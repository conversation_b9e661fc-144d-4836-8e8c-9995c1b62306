# NetSuite ERP Integration Strategy for IonQ PO Creation

## Overview
This document outlines the complete strategy for implementing ERP-driven PO creation in the Didero API, using IonQ's NetSuite integration as the primary use case. The goal is to add intelligent branching to existing workflows that automatically creates POs from NetSuite when they don't exist in our system.

## Current Architecture Context

### Existing PO Creation Workflow
- **Temporal-based**: Uses Temporal workflow orchestration
- **Multi-source**: Supports EMAIL, EXTERNAL_PO_IMPORT, NetSuite integration
- **AI Extraction**: Current flow uses AI to extract PO details from emails/documents
- **Stagehand Integration**: Has branching logic for different extraction methods
- **Team Configuration**: Team-scoped with approval workflows

### ERP Integration Foundation
- **Location**: `didero/integrations/erp/`
- **Architecture**: Abstract base client with NetSuite implementation
- **Authentication**: OAuth 1.0a SOAP API
- **Field Mapping**: IonQ-specific field mappings already configured
- **Team Config**: ERPIntegrationConfig model for team-level settings

## NetSuite Data Analysis

### IonQ Extraction Completeness (PO431 Example)
```json
{
  "total_value": 7291.42,
  "extraction_success": {
    "header_fields": 15,
    "custom_header_fields": 9,
    "line_items": 19,
    "line_custom_fields": 76,
    "expense_lines": 1
  },
  "data_quality": {
    "financial_consistency": "✅ All line items have rate/amount/quantity",
    "date_consistency": "✅ All items have expectedReceiptDate",
    "metadata_richness": "✅ Project codes, departments, custom fields",
    "tracking_integration": "✅ UPS tracking number captured"
  }
}
```

### Critical Field Mappings

#### Header Fields
| NetSuite Field | Didero Model | Status | Notes |
|----------------|--------------|--------|--------|
| `tranId` | `po_number` | ✅ Direct | Core identifier |
| `total` | `total_cost` | ✅ Direct | MoneyField |
| `status` | `order_status` | ⚠️ Mapping needed | Requires status translation |
| `currencyName` | *Missing* | ❌ Model enhancement | Need currency field |
| `memo` | *Missing* | ❌ Model enhancement | Need memo/notes field |

#### Custom Fields (IonQ-Specific)
| NetSuite Field | Purpose | Storage Strategy |
|----------------|---------|------------------|
| `custbody_ionq_tracking_number` | Package tracking | JSON custom_fields |
| `custbody_ionq_po_req_num` | Internal req number | JSON custom_fields |
| `custbody_ionq_sent_date_to_supplier` | Procurement timeline | JSON custom_fields |

#### Line Items
| NetSuite Field | Didero Model | Status |
|----------------|--------------|--------|
| `expectedReceiptDate` | *Missing* | ❌ Need expected_receipt_date |
| `vendorName` | *Missing* | ❌ Need vendor_item_name |
| `line` | *Missing* | ❌ Need netsuite_line_number |

## Model Enhancements Required

### PurchaseOrder Model Extensions
```python
class PurchaseOrder(BaseModel):
    # Existing fields...
    
    # NetSuite Integration Fields
    netsuite_internal_id = CharField(max_length=50, null=True, blank=True)
    currency = CharField(max_length=10, default='USD')
    transaction_date = DateTimeField(null=True, blank=True)
    memo = TextField(blank=True)
    supplier_email = EmailField(blank=True)
    custom_fields = JSONField(default=dict, blank=True)
```

### OrderItem Model Extensions
```python
class OrderItem(BaseModel):
    # Existing fields...
    
    # NetSuite Integration Fields
    netsuite_line_number = IntegerField(null=True, blank=True)
    vendor_item_name = CharField(max_length=255, blank=True)
    expected_receipt_date = DateTimeField(null=True, blank=True)
    netsuite_item_internal_id = CharField(max_length=50, null=True, blank=True)
    line_custom_fields = JSONField(default=dict, blank=True)
```

## Implementation Strategy

### Core Activity: `create_po_from_erp`
```python
@activity.defn(name="create_po_from_erp")
async def create_po_from_erp(
    po_number: str,
    team_id: int,
    source_context: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Create PO by extracting complete data from team's ERP system.
    Uses proven ionq_api_extraction.py approach for field extraction.
    """
    # 1. Get ERP client for team
    # 2. Search for PO in NetSuite
    # 3. Extract complete XML data
    # 4. Parse using proven extraction logic
    # 5. Create PO with all line items
    # 6. Return success/failure with metadata
```

### Workflow Integration Points

#### PO Creation Workflow Enhancement
```python
@workflow.defn(name="po_creation_workflow")
class POCreationWorkflow:
    async def run(self, request: POCreationRequest) -> POCreationResult:
        # STEP 1: Check if PO already exists
        if await check_po_exists(request.po_number, request.team_id):
            return await link_to_existing_po(request)
        
        # STEP 2: Check team ERP configuration
        team_config = await get_team_erp_config(request.team_id)
        
        # STEP 3: Try ERP creation if configured
        if team_config and team_config.enabled:
            erp_result = await create_po_from_erp(...)
            if erp_result.success:
                return success_response(erp_result)
            if not erp_result.fallback_required:
                return failure_response(erp_result)
        
        # STEP 4: Fallback to standard AI extraction
        return await standard_po_creation_flow(request)
```

### Multi-Workflow Integration

#### Order Acknowledgement Integration
- **Trigger**: When processing order ack for non-existent PO
- **Behavior**: Auto-create PO from ERP before processing acknowledgement
- **Configuration**: Team-level setting for auto-creation vs manual confirmation

#### Shipment Processing Integration
- **Trigger**: Unknown shipment notifications
- **Behavior**: Create PO from ERP for tracking/receiving
- **Configuration**: Handle unknown shipments vs ignore

### Team Configuration System

#### Workflow-Specific Settings
```python
class ERPIntegrationConfig(models.Model):
    # Existing fields...
    
    workflow_settings = JSONField(default=dict)
    # Structure:
    # {
    #   "po_creation": {
    #     "enabled": true,
    #     "require_confirmation": false,
    #     "fallback_on_failure": true
    #   },
    #   "order_acknowledgement": {
    #     "enabled": true,
    #     "auto_create_po": true,
    #     "require_confirmation": true
    #   }
    # }
```

#### ERPWorkflowRouter
```python
class ERPWorkflowRouter:
    """Intelligent routing based on team config and workflow type"""
    
    async def should_auto_create_po(self, workflow_type: str) -> bool
    async def should_require_confirmation(self, workflow_type: str) -> bool
    async def should_fallback_on_failure(self, workflow_type: str) -> bool
    async def create_po_if_missing(self, po_number, workflow_type, context) -> Optional[Dict]
```

## Extraction Logic (Proven Approach)

### NetSuite SOAP API Integration
- **Authentication**: OAuth 1.0a with HMAC-SHA256 signatures
- **Search Strategy**: TransactionSearchBasic by tranId
- **Data Retrieval**: Complete PO details via internal ID
- **Field Extraction**: Regex-based XML parsing (proven reliable)

### Data Processing Pipeline
1. **PO Lookup**: Search by PO number to get internal ID
2. **Complete Extraction**: Retrieve full PO XML with all line items
3. **Field Parsing**: Extract header, custom, and line-level fields
4. **Data Mapping**: Convert NetSuite fields to Didero model fields
5. **Entity Creation**: Create PO with all related line items
6. **Context Linking**: Link to source (email, shipment notification)

## Implementation Phases

### Phase 1: Core Infrastructure
- [ ] Model enhancements (PurchaseOrder/OrderItem)
- [ ] `create_po_from_erp` activity implementation
- [ ] NetSuite field parsing logic (from ionq_api_extraction.py)
- [ ] Basic workflow integration

### Phase 2: Multi-Workflow Support
- [ ] Order acknowledgement workflow integration
- [ ] Shipment processing workflow integration
- [ ] ERPWorkflowRouter implementation
- [ ] Team configuration enhancements

### Phase 3: Management & Monitoring
- [ ] Configuration UI for workflow settings
- [ ] PO creation confirmation tasks
- [ ] Error handling and fallback scenarios
- [ ] Monitoring and analytics

## Key Success Metrics

### Technical Success
- **Data Completeness**: >95% of NetSuite fields successfully mapped
- **Integration Reliability**: <5% ERP creation failures
- **Workflow Performance**: <30s end-to-end PO creation time
- **Fallback Success**: 100% fallback to standard extraction on ERP failure

### Business Success  
- **PO Coverage**: Reduction in "PO not found" errors by >80%
- **Manual Intervention**: Reduction in manual PO creation tasks
- **Data Consistency**: Single source of truth for PO data
- **User Experience**: Transparent PO creation (users unaware of source)

## Risk Mitigation

### Technical Risks
- **NetSuite API Changes**: Abstract client pattern allows for API updates
- **Authentication Issues**: Credential refresh and error handling
- **Data Inconsistency**: Comprehensive field validation and error reporting

### Business Risks
- **ERP Downtime**: Automatic fallback to standard extraction
- **Configuration Errors**: Team-level settings with safe defaults
- **Data Quality**: Validation rules and manual confirmation options

## IonQ-Specific Configuration

### Team IDs
- **Test Environment**: Team ID 4
- **Production**: Team ID 173

### Custom Field Mappings
- **Header**: `custbody_ionq_tracking_number` → tracking number
- **Line Items**: `expectedreceiptdate` → expected receipt date
- **Line Custom**: `custcol_ionq_supplierpromisedatefield` → promised ship date

### Rollout Strategy
1. **Development Testing**: Test with Team ID 4 using sandbox NetSuite
2. **Pilot Deployment**: Limited rollout to Team ID 173
3. **Monitoring Phase**: Monitor success rates and error patterns
4. **Full Deployment**: Enable for all configured ERP teams

## Conclusion

This strategy leverages the existing robust ERP integration architecture while adding intelligent PO creation capabilities. The proven NetSuite extraction logic from `ionq_api_extraction.py` provides a solid foundation, and the configurable workflow routing ensures flexibility across different use cases.

The implementation maintains backward compatibility while significantly reducing manual PO creation overhead and improving data consistency across all workflows.