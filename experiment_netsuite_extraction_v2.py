#!/usr/bin/env python3
"""
Experiment 2: Direct mapping to Didero models
Goal: Test creating data structure that maps directly to Didero PO creation
"""

import re
from typing import Dict, Any, List, Optional
from datetime import datetime
from decimal import Decimal


class ExperimentV2:
    """Test direct mapping to Didero model fields"""

    def extract_for_didero_models(self, xml_text: str) -> Dict[str, Any]:
        """Extract and map directly to Didero model fields"""

        result = {
            "po_fields": {},  # Maps to PurchaseOrder model
            "supplier_data": {},  # For supplier matching
            "shipping_address": {},  # Maps to Address model
            "billing_address": {},  # Maps to Address model (sender)
            "order_items": [],  # Maps to OrderItem model
            "metadata": {},  # Additional info
        }

        # Extract PO header fields that map to PurchaseOrder model
        po_mappings = {
            # NetSuite field -> Didero field
            "tranId": "po_number",
            "memo": "vendor_notes",  # or internal_notes
            "tranDate": "placement_time",
        }

        for ns_field, didero_field in po_mappings.items():
            pattern = f"<tranPurch:{ns_field}>([^<]*)</tranPurch:{ns_field}>"
            match = re.search(pattern, xml_text)
            if match:
                value = match.group(1).strip()
                # Handle date conversion
                if "Date" in ns_field or "time" in didero_field:
                    # Parse ISO date
                    result["po_fields"][didero_field] = value  # Keep as string for now
                else:
                    result["po_fields"][didero_field] = value

        # Extract payment terms
        terms_match = re.search(
            r"<tranPurch:terms[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>",
            xml_text,
            re.DOTALL,
        )
        if terms_match:
            result["po_fields"]["payment_terms"] = terms_match.group(1).strip()

        # Extract supplier for matching
        vendor_match = re.search(
            r'<tranPurch:entity[^>]*internalId="([^"]*)"[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>',
            xml_text,
            re.DOTALL,
        )
        if vendor_match:
            result["supplier_data"] = {
                "netsuite_id": vendor_match.group(1),
                "name": vendor_match.group(2).strip(),
                # We'll use match_supplier_by_name with this
            }

        # Extract addresses
        result["billing_address"] = self._extract_address(xml_text, "billingAddress")
        result["shipping_address"] = self._extract_address(xml_text, "shippingAddress")

        # Extract line items
        result["order_items"] = self._extract_order_items(xml_text)

        # Add metadata
        result["metadata"]["currency"] = self._extract_currency(xml_text)
        result["metadata"]["total"] = self._extract_total(xml_text)

        return result

    def _extract_address(self, xml_text: str, address_type: str) -> Dict[str, Any]:
        """Extract address matching Didero Address model"""
        address = {}

        pattern = f"<tranPurch:{address_type}[^>]*>(.*?)</tranPurch:{address_type}>"
        match = re.search(pattern, xml_text, re.DOTALL)
        if not match:
            return address

        addr_xml = match.group(1)

        # Map to Didero Address fields
        field_mappings = {
            "addr1": "line_1",
            "addr2": "line_2",
            "city": "city",
            "state": "state_or_province",
            "zip": "postal_code",
            "country": "country",
            "addressee": "name",
            "addrText": "raw_text",  # For AI parsing
        }

        for ns_field, didero_field in field_mappings.items():
            pattern = f"<platformCommon:{ns_field}>([^<]*)</platformCommon:{ns_field}>"
            field_match = re.search(pattern, addr_xml)
            if field_match:
                address[didero_field] = field_match.group(1).strip()

        return address

    def _extract_order_items(self, xml_text: str) -> List[Dict[str, Any]]:
        """Extract order items matching Didero OrderItem model"""
        items = []

        itemlist_match = re.search(
            r"<tranPurch:itemList>(.*?)</tranPurch:itemList>", xml_text, re.DOTALL
        )
        if not itemlist_match:
            return items

        items_xml = itemlist_match.group(1)
        item_pattern = r"<tranPurch:item>(.*?)</tranPurch:item>"

        for item_match in re.finditer(item_pattern, items_xml, re.DOTALL):
            item_xml = item_match.group(1)

            order_item = {
                "item_data": {},  # For Item model
                "order_data": {},  # For OrderItem model
            }

            # Extract item number (for Item model)
            item_ref = re.search(
                r"<tranPurch:item[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>",
                item_xml,
                re.DOTALL,
            )
            if item_ref:
                order_item["item_data"]["item_number"] = item_ref.group(1).strip()

            # Extract description
            desc_match = re.search(
                r"<tranPurch:description>([^<]*)</tranPurch:description>", item_xml
            )
            if desc_match:
                order_item["item_data"]["description"] = desc_match.group(1).strip()

            # Extract vendor name (might be manufacturer_part_number)
            vendor_name = re.search(
                r"<tranPurch:vendorName>([^<]*)</tranPurch:vendorName>", item_xml
            )
            if vendor_name:
                order_item["item_data"]["supplier_part_number"] = vendor_name.group(
                    1
                ).strip()

            # Extract quantity
            qty_match = re.search(
                r"<tranPurch:quantity>([^<]*)</tranPurch:quantity>", item_xml
            )
            if qty_match:
                try:
                    order_item["order_data"]["quantity"] = float(qty_match.group(1))
                except:
                    order_item["order_data"]["quantity"] = 1.0

            # Extract price
            rate_match = re.search(
                r"<tranPurch:rate>([^<]*)</tranPurch:rate>", item_xml
            )
            if rate_match:
                order_item["order_data"]["price"] = rate_match.group(1).strip()

            # Extract expected receipt date
            date_match = re.search(
                r"<tranPurch:expectedReceiptDate>([^<]*)</tranPurch:expectedReceiptDate>",
                item_xml,
            )
            if date_match:
                order_item["order_data"]["requested_date"] = date_match.group(1).strip()

            items.append(order_item)

        return items

    def _extract_currency(self, xml_text: str) -> str:
        """Extract currency code"""
        match = re.search(
            r"<tranPurch:currencyName>([^<]*)</tranPurch:currencyName>", xml_text
        )
        return match.group(1).strip() if match else "USD"

    def _extract_total(self, xml_text: str) -> Optional[str]:
        """Extract total amount"""
        match = re.search(r"<tranPurch:total>([^<]*)</tranPurch:total>", xml_text)
        return match.group(1).strip() if match else None


def test_didero_mapping():
    """Test mapping to Didero models"""
    sample_xml = """
    <tranPurch:purchaseOrder>
        <tranPurch:tranId>PO431</tranPurch:tranId>
        <tranPurch:memo>WEB NUW1334695</tranPurch:memo>
        <tranPurch:tranDate>2022-10-03T21:00:00.000-07:00</tranPurch:tranDate>
        <tranPurch:total>7291.42</tranPurch:total>
        <tranPurch:currencyName>USD</tranPurch:currencyName>
        <tranPurch:terms internalId="4">
            <platformCore:name>Net 30</platformCore:name>
        </tranPurch:terms>
        <tranPurch:entity internalId="550">
            <platformCore:name>V10072 ThorLabs</platformCore:name>
        </tranPurch:entity>
        <tranPurch:billingAddress>
            <platformCommon:addressee>ThorLabs</platformCommon:addressee>
            <platformCommon:addr1>56 Sparta Avenue</platformCommon:addr1>
            <platformCommon:city>Newton</platformCommon:city>
            <platformCommon:state>NJ</platformCommon:state>
            <platformCommon:zip>07860</platformCommon:zip>
            <platformCommon:country>_unitedStates</platformCommon:country>
        </tranPurch:billingAddress>
        <tranPurch:shippingAddress>
            <platformCommon:addressee>CP Tooling (NI)</platformCommon:addressee>
            <platformCommon:addrText>CP Tooling (NI)\r\nUnited States</platformCommon:addrText>
        </tranPurch:shippingAddress>
        <tranPurch:itemList>
            <tranPurch:item>
                <tranPurch:item internalId="2761">
                    <platformCore:name>502-00097</platformCore:name>
                </tranPurch:item>
                <tranPurch:description>Compact Power and Energy Meter Console</tranPurch:description>
                <tranPurch:quantity>1.0</tranPurch:quantity>
                <tranPurch:rate>1220.57</tranPurch:rate>
                <tranPurch:vendorName>PM100D</tranPurch:vendorName>
                <tranPurch:expectedReceiptDate>2022-10-03T21:00:00.000-07:00</tranPurch:expectedReceiptDate>
            </tranPurch:item>
        </tranPurch:itemList>
    </tranPurch:purchaseOrder>
    """

    extractor = ExperimentV2()
    result = extractor.extract_for_didero_models(sample_xml)

    print("=== Experiment V2 Results ===")
    print(f"PO Fields: {result['po_fields']}")
    print(f"Supplier: {result['supplier_data']}")
    print(f"Billing Address Fields: {list(result['billing_address'].keys())}")
    print(f"Shipping Address: {result['shipping_address']}")
    print(f"Order Items: {len(result['order_items'])}")
    if result["order_items"]:
        print(f"First Item: {result['order_items'][0]}")

    return result


if __name__ == "__main__":
    test_didero_mapping()
