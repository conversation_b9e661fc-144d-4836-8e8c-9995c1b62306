from unittest.mock import MagicMock, Mock, patch

from didero.suppliers.models import Supplier
from didero.suppliers.tasks import generate_supplier_description_task
from didero.testing.cases import TestCase


class TestSupplierDescription(TestCase):
    def setUp(self):
        self.user = self.create_user(
            email="<EMAIL>", first_name="Test", last_name="User"
        )
        self.team = self.user.teams.first()

    @patch("didero.suppliers.tasks.generate_supplier_description_task.apply_async")
    def test_signal_triggers_task_when_website_url_provided_and_no_description(
        self, mock_task: MagicMock
    ):
        """
        Test that the signal triggers the task when a supplier is created
        with a website_url and no description.
        """
        supplier = Supplier.objects.create(
            name="Test Supplier",
            team=self.team,
            website_url="https://example.com",
            # description is None by default
        )

        # Verify the task was called with correct arguments
        mock_task.assert_called_once_with(args=(supplier.id,), countdown=3)

    @patch("didero.ai.supplier_description.supplier_description.requests.get")
    @patch("didero.ai.supplier_description.supplier_description.get_anthropic_client")
    @patch("didero.ai.supplier_description.supplier_description.get_langfuse_prompt")
    def test_task_successful_description_generation(
        self,
        mock_get_prompt: MagicMock,
        mock_get_client: MagicMock,
        mock_requests_get: MagicMock,
    ):
        """
        Test the complete flow from URL to description generation.
        """
        # Mock webpage content
        mock_html = """
        <html>
            <head><title>Acme Industrial Solutions</title></head>
            <body>
                <h1>Welcome to Acme Industrial Solutions</h1>
                <p>We are a leading manufacturer of industrial equipment and machinery.</p>
                <p>Serving the manufacturing industry for over 50 years.</p>
            </body>
        </html>
        """
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.content = mock_html.encode("utf-8")
        mock_requests_get.return_value = mock_response

        # Mock prompt
        mock_prompt = Mock()
        mock_prompt.compile.return_value = "Generate business description"
        mock_get_prompt.return_value = mock_prompt

        # Mock Anthropic client response
        mock_ai_response = Mock()
        mock_ai_response.content = [Mock()]
        mock_ai_response.content[
            0
        ].text = '{"business_description": "Industrial equipment manufacturer", "success": true}'

        mock_client = Mock()
        mock_client.messages.create.return_value = mock_ai_response
        mock_get_client.return_value = mock_client

        # Create supplier and run task
        supplier = Supplier.objects.create(
            name="Acme Solutions",
            team=self.team,
            website_url="https://acme.com",
        )

        # Execute the task
        generate_supplier_description_task(supplier_id=supplier.id)

        # Verify the supplier was updated
        supplier.refresh_from_db()
        self.assertEqual(supplier.description, "Industrial equipment manufacturer")

        mock_get_client.assert_called_once()
        mock_client.messages.create.assert_called_once()
