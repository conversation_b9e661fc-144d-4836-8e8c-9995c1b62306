# Generated by Django 4.2.7 on 2025-07-16 16:28

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("suppliers", "0095_supplier_available_shipping_terms_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="supplier",
            name="available_shipping_terms",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(max_length=50),
                blank=True,
                default=list,
                help_text="List of shipping terms this supplier supports (including custom terms)",
                null=True,
                size=None,
            ),
        ),
        migrations.AlterField(
            model_name="supplier",
            name="default_shipping_terms",
            field=models.CharField(
                blank=True,
                help_text="Default shipping terms for purchase orders with this supplier",
                max_length=50,
                null=True,
            ),
        ),
    ]
