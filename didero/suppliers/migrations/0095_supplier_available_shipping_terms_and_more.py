# Generated by Django 4.2.7 on 2025-06-30 19:37

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("suppliers", "0094_merge_20250619_1111"),
    ]

    operations = [
        migrations.AddField(
            model_name="supplier",
            name="available_shipping_terms",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(
                    choices=[
                        ("EXW", "Ex Works - Buyer picks up"),
                        ("FCA", "Free Carrier - Deliver to carrier"),
                        ("FOB", "Free on Board - Load on vessel"),
                        ("DAP", "Delivered at Place - Seller delivers"),
                        ("DDP", "Delivered Duty Paid - Full service"),
                        ("CPT", "Carriage Paid To"),
                        ("CIP", "Carriage and Insurance Paid To"),
                        ("DPU", "Delivered at Place Unloaded"),
                        ("FAS", "Free Alongside Ship"),
                        ("CFR", "Cost and Freight"),
                        ("CIF", "Cost Insurance and Freight"),
                    ],
                    max_length=10,
                ),
                blank=True,
                default=list,
                help_text="List of shipping terms this supplier supports",
                null=True,
                size=None,
            ),
        ),
        migrations.AddField(
            model_name="supplier",
            name="default_shipping_terms",
            field=models.CharField(
                blank=True,
                choices=[
                    ("EXW", "Ex Works - Buyer picks up"),
                    ("FCA", "Free Carrier - Deliver to carrier"),
                    ("FOB", "Free on Board - Load on vessel"),
                    ("DAP", "Delivered at Place - Seller delivers"),
                    ("DDP", "Delivered Duty Paid - Full service"),
                    ("CPT", "Carriage Paid To"),
                    ("CIP", "Carriage and Insurance Paid To"),
                    ("DPU", "Delivered at Place Unloaded"),
                    ("FAS", "Free Alongside Ship"),
                    ("CFR", "Cost and Freight"),
                    ("CIF", "Cost Insurance and Freight"),
                ],
                help_text="Default shipping terms for purchase orders with this supplier",
                max_length=10,
                null=True,
            ),
        ),
    ]
