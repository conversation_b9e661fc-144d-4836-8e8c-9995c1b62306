from datetime import datetime, timedelta
from unittest.mock import patch

from django.test import TestCase
from zoneinfo import ZoneInfo

from didero.workflows.util.business_hours_utils import (
    calculate_business_aware_delay,
    is_business_day,
    is_business_hour,
    next_business_hour,
)


class TestBusinessHoursUtils(TestCase):
    """Test cases for business hours utility functions."""

    def test_is_business_day_weekdays(self):
        """Test that weekdays are correctly identified as business days."""
        # Monday
        dt = datetime(2024, 1, 8, 10, 0)
        self.assertTrue(is_business_day(dt))

        # Tuesday
        dt = datetime(2024, 1, 9, 10, 0)
        self.assertTrue(is_business_day(dt))

        # Wednesday
        dt = datetime(2024, 1, 10, 10, 0)
        self.assertTrue(is_business_day(dt))

        # Thursday
        dt = datetime(2024, 1, 11, 10, 0)
        self.assertTrue(is_business_day(dt))

        # Friday
        dt = datetime(2024, 1, 12, 10, 0)
        self.assertTrue(is_business_day(dt))

    def test_is_business_day_weekends(self):
        """Test that weekends are not business days."""
        # Saturday
        dt = datetime(2024, 1, 13, 10, 0)
        self.assertFalse(is_business_day(dt))

        # Sunday
        dt = datetime(2024, 1, 14, 10, 0)
        self.assertFalse(is_business_day(dt))

    def test_is_business_day_holidays(self):
        """Test that US holidays are not business days."""
        # Independence Day 2024 (Thursday)
        dt = datetime(2024, 7, 4, 10, 0)
        self.assertFalse(is_business_day(dt))

        # Christmas 2024 (Wednesday)
        dt = datetime(2024, 12, 25, 10, 0)
        self.assertFalse(is_business_day(dt))

        # New Year's Day 2024 (Monday)
        dt = datetime(2024, 1, 1, 10, 0)
        self.assertFalse(is_business_day(dt))

    def test_is_business_day_timezone_aware(self):
        """Test business day detection with timezone-aware datetime."""
        # Create timezone-aware datetime
        tz = ZoneInfo("America/New_York")
        dt = datetime(2024, 1, 8, 10, 0, tzinfo=tz)
        self.assertTrue(is_business_day(dt))

        # Test with different timezone
        tz_la = ZoneInfo("America/Los_Angeles")
        dt_la = datetime(2024, 1, 8, 10, 0, tzinfo=tz_la)
        self.assertTrue(is_business_day(dt_la, timezone="America/Los_Angeles"))

    def test_is_business_hour_within_hours(self):
        """Test that times within business hours are correctly identified."""
        # 9 AM - start of business hours
        dt = datetime(2024, 1, 8, 9, 0)  # Monday
        self.assertTrue(is_business_hour(dt))

        # 12 PM - middle of business hours
        dt = datetime(2024, 1, 8, 12, 0)  # Monday
        self.assertTrue(is_business_hour(dt))

        # 4:59 PM - just before end of business hours
        dt = datetime(2024, 1, 8, 16, 59)  # Monday
        self.assertTrue(is_business_hour(dt))

    def test_is_business_hour_outside_hours(self):
        """Test that times outside business hours are correctly identified."""
        # 8:59 AM - just before business hours
        dt = datetime(2024, 1, 8, 8, 59)  # Monday
        self.assertFalse(is_business_hour(dt))

        # 5 PM - end of business hours
        dt = datetime(2024, 1, 8, 17, 0)  # Monday
        self.assertFalse(is_business_hour(dt))

        # 10 PM - well after business hours
        dt = datetime(2024, 1, 8, 22, 0)  # Monday
        self.assertFalse(is_business_hour(dt))

    def test_is_business_hour_weekend(self):
        """Test that weekend times are never business hours."""
        # Saturday at 10 AM
        dt = datetime(2024, 1, 13, 10, 0)
        self.assertFalse(is_business_hour(dt))

        # Sunday at 2 PM
        dt = datetime(2024, 1, 14, 14, 0)
        self.assertFalse(is_business_hour(dt))

    def test_is_business_hour_custom_hours(self):
        """Test business hours with custom start and end times."""
        dt = datetime(2024, 1, 8, 8, 0)  # Monday 8 AM

        # Default hours (9-5): should be false
        self.assertFalse(is_business_hour(dt))

        # Custom hours (8-4): should be true
        self.assertTrue(is_business_hour(dt, start_hour=8, end_hour=16))

    def test_next_business_hour_already_business_hour(self):
        """Test that current time is returned if already in business hours."""
        dt = datetime(2024, 1, 8, 10, 30)  # Monday 10:30 AM
        result = next_business_hour(dt)
        # Remove timezone info for comparison since the function adds timezone
        self.assertEqual(result.replace(tzinfo=None), dt)

    def test_next_business_hour_before_business_hours(self):
        """Test next business hour when before business hours on a business day."""
        # Monday 7 AM
        dt = datetime(2024, 1, 8, 7, 0)
        result = next_business_hour(dt)
        expected = datetime(2024, 1, 8, 9, 0)
        self.assertEqual(result.replace(tzinfo=None), expected)

    def test_next_business_hour_after_business_hours(self):
        """Test next business hour when after business hours."""
        # Monday 6 PM
        dt = datetime(2024, 1, 8, 18, 0)
        result = next_business_hour(dt)
        # Should be Tuesday 9 AM
        expected = datetime(2024, 1, 9, 9, 0)
        self.assertEqual(result.replace(tzinfo=None), expected)

    def test_next_business_hour_friday_evening(self):
        """Test next business hour from Friday evening (should skip weekend)."""
        # Friday 6 PM
        dt = datetime(2024, 1, 19, 18, 0)
        result = next_business_hour(dt)
        # Should be Monday 9 AM
        expected = datetime(2024, 1, 22, 9, 0)
        self.assertEqual(result.replace(tzinfo=None), expected)

    def test_next_business_hour_saturday(self):
        """Test next business hour from Saturday."""
        # Saturday 2 PM
        dt = datetime(2024, 1, 20, 14, 0)
        result = next_business_hour(dt)
        # Should be Monday 9 AM
        expected = datetime(2024, 1, 22, 9, 0)
        self.assertEqual(result.replace(tzinfo=None), expected)

    def test_next_business_hour_holiday(self):
        """Test next business hour from a holiday."""
        # July 4th (Thursday) 2024 at 10 AM
        dt = datetime(2024, 7, 4, 10, 0)
        result = next_business_hour(dt)
        # Should be Friday July 5th at 9 AM
        expected = datetime(2024, 7, 5, 9, 0)
        self.assertEqual(result.replace(tzinfo=None), expected)

    def test_next_business_hour_holiday_friday(self):
        """Test next business hour from a holiday that falls on Friday."""
        # July 4th 2025 is a Friday
        dt = datetime(2025, 7, 4, 10, 0)
        result = next_business_hour(dt)
        # Should skip to Monday July 7th at 9 AM
        expected = datetime(2025, 7, 7, 9, 0)
        self.assertEqual(result.replace(tzinfo=None), expected)

    def test_calculate_business_aware_delay_business_hours_disabled(self):
        """Test delay calculation when business hours are disabled."""
        delay = calculate_business_aware_delay(
            hours_to_wait=24, business_hours_only=False
        )
        self.assertEqual(delay, 24)

    @patch("didero.workflows.util.business_hours_utils.datetime")
    def test_calculate_business_aware_delay_during_business_hours(self, mock_datetime):
        """Test delay calculation during business hours."""
        # Mock current time as Monday 10 AM
        tz = ZoneInfo("America/New_York")
        mock_now = datetime(2024, 1, 8, 10, 0, tzinfo=tz)

        # Create a proper mock that handles both datetime.now(tz=tz) and datetime constructor
        mock_datetime.now = lambda tz=None: mock_now
        mock_datetime.side_effect = lambda *args, **kwargs: datetime(*args, **kwargs)

        # Request 2 hour delay
        delay = calculate_business_aware_delay(
            hours_to_wait=2, business_hours_only=True
        )

        # Should be 2 hours (landing at Monday 12 PM, still business hours)
        self.assertAlmostEqual(delay, 2, places=1)

    @patch("didero.workflows.util.business_hours_utils.datetime")
    def test_calculate_business_aware_delay_end_of_day(self, mock_datetime):
        """Test delay calculation near end of business day."""
        # Mock current time as Monday 4 PM
        tz = ZoneInfo("America/New_York")
        mock_now = datetime(2024, 1, 8, 16, 0, tzinfo=tz)

        # Create a proper mock that handles both datetime.now(tz=tz) and datetime constructor
        mock_datetime.now = lambda tz=None: mock_now
        mock_datetime.side_effect = lambda *args, **kwargs: datetime(*args, **kwargs)

        # Request 3 hour delay
        delay = calculate_business_aware_delay(
            hours_to_wait=3, business_hours_only=True
        )

        # Monday 4 PM + 3 hours = Monday 7 PM, which becomes Tuesday 9 AM
        # From Monday 4 PM to Tuesday 9 AM is 17 hours
        self.assertAlmostEqual(delay, 17, places=1)

    @patch("didero.workflows.util.business_hours_utils.datetime")
    def test_calculate_business_aware_delay_friday_afternoon(self, mock_datetime):
        """Test delay calculation on Friday afternoon."""
        # Mock current time as Friday 3 PM (Jan 19, 2024)
        tz = ZoneInfo("America/New_York")
        mock_now = datetime(2024, 1, 19, 15, 0, tzinfo=tz)

        # Create a proper mock that handles both datetime.now(tz=tz) and datetime constructor
        mock_datetime.now = lambda tz=None: mock_now
        mock_datetime.side_effect = lambda *args, **kwargs: datetime(*args, **kwargs)

        # Request 4 hour delay
        delay = calculate_business_aware_delay(
            hours_to_wait=4, business_hours_only=True
        )

        # Friday 3 PM + 4 hours = Friday 7 PM, which becomes Monday 9 AM
        # From Friday 3 PM to Monday 9 AM is 66 hours
        self.assertAlmostEqual(delay, 66, places=1)

    @patch("didero.workflows.util.business_hours_utils.datetime")
    def test_calculate_business_aware_delay_weekend(self, mock_datetime):
        """Test delay calculation starting on weekend."""
        # Mock current time as Saturday 2 PM (Jan 20, 2024)
        tz = ZoneInfo("America/New_York")
        mock_now = datetime(2024, 1, 20, 14, 0, tzinfo=tz)

        # Create a proper mock that handles both datetime.now(tz=tz) and datetime constructor
        mock_datetime.now = lambda tz=None: mock_now
        mock_datetime.side_effect = lambda *args, **kwargs: datetime(*args, **kwargs)

        # Request any delay
        delay = calculate_business_aware_delay(
            hours_to_wait=1, business_hours_only=True
        )

        # Saturday 2 PM + 1 hour = Saturday 3 PM, which becomes Monday 9 AM
        # From Saturday 2 PM to Monday 9 AM is 43 hours
        self.assertAlmostEqual(delay, 43, places=1)

    def test_timezone_conversions(self):
        """Test that timezone conversions work correctly."""
        # Test with Pacific time
        dt = datetime(2024, 1, 8, 7, 0)  # 7 AM Pacific = 10 AM Eastern
        tz_pacific = "America/Los_Angeles"

        # 7 AM Pacific on a Monday should not be business hours (before 9 AM)
        self.assertFalse(is_business_hour(dt, timezone=tz_pacific))

        # But 10 AM Pacific should be business hours
        dt = datetime(2024, 1, 8, 10, 0)
        self.assertTrue(is_business_hour(dt, timezone=tz_pacific))
