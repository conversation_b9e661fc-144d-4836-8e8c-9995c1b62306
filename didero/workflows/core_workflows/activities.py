from typing import Any, Dict, Optional

import structlog
from temporalio import activity

from didero.workflows.models import UserWorkflow, WorkflowBehaviorConfig

logger = structlog.get_logger(__name__)


@activity.defn
def load_workflow_config(
    workflow_id: str, config_type: str
) -> Optional[Dict[str, Any]]:
    """
    Load workflow configuration from database as an activity.

    This activity safely loads workflow configuration from Django ORM,
    avoiding threading issues when called from within a Temporal workflow.

    Args:
        workflow_id: The UserWorkflow ID
        config_type: The type of config to load ("order_ack", "po_creation", etc.)
                   Used only for fallback when workflow doesn't exist.

    Returns:
        Dict containing the configuration data, or None if not found
    """
    logger.info(
        f"Loading workflow config for workflow_id: {workflow_id}, type: {config_type}"
    )

    try:
        # Get the UserWorkflow with its behavior config
        user_workflow = UserWorkflow.objects.select_related("behavior_config").get(
            id=int(workflow_id)
        )
        logger.info(f"Loaded UserWorkflow: {user_workflow}")

        # Check if behavior config exists via try/except since pyr<PERSON> doesn't know about related fields
        try:
            behavior_config = WorkflowBehaviorConfig.objects.get(workflow=user_workflow)
            logger.info(f"Found existing behavior config: {behavior_config}")
        except WorkflowBehaviorConfig.DoesNotExist:
            logger.info(f"No behavior config found for workflow {workflow_id}")
            return None

        # Get config as pydantic model (method determines type from workflow type)
        config_model = behavior_config.get_config_as_pydantic()
        logger.info(f"Loaded config as pydantic model: {config_model}")

        return config_model.model_dump()

    except UserWorkflow.DoesNotExist:
        logger.error(f"UserWorkflow with id {workflow_id} not found")
        # Return None - the workflow will handle creating the default
        return None

    except Exception as e:
        logger.error(f"Failed to load workflow config: {str(e)}")
        # Return None - the workflow will handle creating the default
        return None


@activity.defn
def get_didero_ai_user_id(team_id: int) -> Optional[int]:
    """
    Get the Didero AI user ID for a team as an activity.

    This activity safely gets the user ID from Django ORM,
    avoiding threading issues when called from within a Temporal workflow.

    Args:
        team_id: The team ID

    Returns:
        User ID if found, None otherwise
    """
    logger.info(f"Getting Didero AI user for team_id: {team_id}")

    try:
        from didero.users.models.team_models import Team
        from didero.utils.utils import get_didero_ai_user

        team = Team.objects.get(id=team_id)
        didero_ai_user = get_didero_ai_user(team=team)

        if didero_ai_user:
            logger.info(f"Found Didero AI user: {didero_ai_user.id}")
            return didero_ai_user.id
        else:
            logger.warning(f"No Didero AI user found for team {team_id}")
            return None

    except Exception as e:
        logger.error(f"Failed to get Didero AI user: {str(e)}")
        return None


@activity.defn
def get_task_assignment_user_id(purchase_order_id: str) -> Optional[int]:
    """
    Get the user ID for task assignment based on purchase order.

    This activity uses the same logic as the follow-up workflow to determine
    the appropriate user for task assignment.

    Args:
        purchase_order_id: The purchase order ID

    Returns:
        User ID if found, None otherwise
    """
    logger.info(f"Getting task assignment user for PO: {purchase_order_id}")

    try:
        from didero.orders.models import PurchaseOrder
        from didero.tasks.utils import get_task_assignment_user

        purchase_order = PurchaseOrder.objects.get(id=purchase_order_id)
        user = get_task_assignment_user(purchase_order)

        if user:
            logger.info(f"Found task assignment user: {user.pk}")
            return user.pk
        else:
            logger.warning(f"No task assignment user found for PO {purchase_order_id}")
            return None

    except Exception as e:
        logger.error(f"Failed to get task assignment user: {str(e)}")
        return None
