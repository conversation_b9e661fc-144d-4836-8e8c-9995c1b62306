"""
Metrics definitions for PO Creation Workflow
"""

from temporalio import workflow


class POCreationMetrics:
    """Encapsulates all metrics for the PO Creation workflow"""

    def __init__(self):
        meter = workflow.metric_meter()

        # Counter for total workflow attempts
        self.workflow_attempts = meter.create_counter(
            "workflow_po_creations",
            description="Total number of PO workflow attempts",
            unit="1",
        )

        # Counter for validation failures
        self.validation_failures = meter.create_counter(
            "workflow_po_validation_failures",
            description="Number of PO validation failures",
            unit="1",
        )

        # Histogram for PO creation duration
        self.po_creation_duration = meter.create_histogram(
            "workflow_po_creation_duration",
            description="Time taken to create PO",
            unit="ms",
        )

    def record_validation_failure(self, team_id: str, reason: str):
        """Record a validation failure"""
        self.validation_failures.add(1, {"team_id": team_id, "reason": reason})

    def record_workflow_attempt(
        self,
        team_id: str,
        success: bool,
        duration_seconds: float,
        reason: str | None = None,
    ):
        """Record a workflow attempt"""
        labels = {"team_id": team_id, "success": success}
        if not success:
            labels["reason"] = reason or "other"
        self.workflow_attempts.add(1, labels)

        self.po_creation_duration.record(
            int(duration_seconds * 1000), {"success": success}
        )
