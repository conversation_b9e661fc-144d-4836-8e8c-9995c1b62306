from didero.workflows.schemas import WorkflowBehaviorConfigBase
from decimal import Decimal
from typing import List, Optional
from pydantic import Field, field_validator


class POCreationBehaviorConfig(WorkflowBehaviorConfigBase):
    """PO Creation specific configuration"""

    # Core behavior flags
    require_human_validation: bool = (
        True  # Replaces TEAM_PO_CREATION_HUMAN_VALIDATION_ENABLED
    )

    # Validation controls
    validate_duplicate_po: bool = True

    # Auto-approval rules
    auto_approve_threshold: Optional[Decimal] = None
    auto_approve_currency: str = "USD"

    # Extension points for team-specific customizations
    post_extraction_activities: List[str] = Field(default_factory=list)
    pre_creation_activities: List[str] = Field(default_factory=list)
    post_creation_activities: List[str] = Field(default_factory=list)

    # Notification settings
    notification_channels: List[str] = Field(default_factory=lambda: ["email"])
    notify_on_validation_required: bool = True

    @field_validator("auto_approve_currency")
    @classmethod
    def validate_currency(cls, v: str) -> str:
        valid_currencies = ["USD", "EUR", "GBP", "JPY", "CAD"]
        if v not in valid_currencies:
            raise ValueError(f"Currency must be one of {valid_currencies}")
        return v

    class Config:
        json_encoders = {Decimal: str}
