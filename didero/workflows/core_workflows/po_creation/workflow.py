from dataclasses import dataclass
from datetime import timed<PERSON>ta
from enum import Enum
from typing import Any, Dict, List, Optional

from temporalio import workflow
from temporalio.common import RetryPolicy
from temporalio.exceptions import ApplicationError

from didero.workflows.core_workflows.po_creation.metrics import POCreationMetrics
from didero.workflows.retry_utils import get_retry_policy

# Use unsafe imports for activity functions and Django models
with workflow.unsafe.imports_passed_through():
    # Import the DAG activity functions directly
    from didero.ai.purchase_order.schemas import PurchaseOrderDetails
    from didero.workflows.core.nodes.purchase_orders.po_creation.activities import (
        create_po,
        extract_po_details,
        extract_po_from_erp,
        link_email,
    )

    # Import schema types used by DAG activities
    from didero.workflows.core.nodes.purchase_orders.po_creation.schemas import (
        POCreationParamsOptional,
        POExtractionParams,
        POLinkEmailParams,
    )

    # Import shared core workflow activities
    from didero.workflows.core_workflows.activities import load_workflow_config

    # Import config schema
    from didero.workflows.core_workflows.po_creation.schemas import (
        POCreationBehaviorConfig,
    )


@dataclass
class POExtractionResult:
    """Result from PO extraction"""

    success: bool
    po_exists: bool = False
    po_data: Optional[PurchaseOrderDetails] = None
    error_message: Optional[str] = None
    extraction_method: str = "ai"  # "ai", "erp", or "netsuite"
    is_data_sufficient: bool = False  # ⚠️ THIS WAS MISSING!


@dataclass
class POCreationResult:
    """Result from PO creation"""

    success: bool
    po_id: Optional[str] = None
    po_number: Optional[str] = None
    error_message: Optional[str] = None
    created_in_draft: bool = False


@dataclass
class POCreationParams:
    team_id: str
    workflow_id: str  # The UserWorkflow ID for loading configuration
    email_id: Optional[str] = None
    document_id: Optional[str] = None  # For external PO imports
    po_number: Optional[str] = (
        None  # For auto-creation: skip AI extraction, use this PO number directly
    )


class POStatus(str, Enum):
    DRAFT = "DRAFT"
    ISSUED = "ISSUED"


@workflow.defn
class POCreationWorkflow:
    """Core PO Creation workflow implementation with behavior configuration"""

    @workflow.run
    async def run(self, workflow_id: str, params: POCreationParams) -> Dict[str, Any]:
        """
        Main workflow execution

        Args:
            workflow_id: The UserWorkflow ID
            params: Must contain 'email_id', 'team_id', and 'workflow_id'

        Returns:
            Dict with success status and created PO details
        """
        self.logger = workflow.logger
        self.workflow_id = workflow_id

        # Load workflow configuration
        # For document imports without UserWorkflow, use default config
        if params.workflow_id:
            self.config = await self._load_workflow_config(params.workflow_id)
        else:
            self.config = POCreationBehaviorConfig()  # Use default config

        # Initialize metrics
        self._init_metrics()

        email_id = params.email_id
        document_id = params.document_id
        team_id = params.team_id
        po_number = params.po_number

        # For auto-creation: po_number can be provided directly
        # For normal flows: either email_id or document_id is required
        if not team_id or (not email_id and not document_id and not po_number):
            self.metrics.record_workflow_attempt(
                team_id=team_id or "none",
                success=False,
                duration_seconds=(
                    workflow.now() - workflow.info().start_time
                ).total_seconds(),
                reason="missing_parameters",
            )
            raise ApplicationError(
                "Missing required parameters: team_id and either email_id, document_id, or po_number",
                type="ValidationError",
                non_retryable=True,
            )

        # Check if workflow is enabled
        if not self.config.enabled:
            self.metrics.record_workflow_attempt(
                team_id=team_id,
                success=False,
                duration_seconds=(
                    workflow.now() - workflow.info().start_time
                ).total_seconds(),
                reason="workflow_disabled",
            )
            raise ApplicationError(
                "Workflow is disabled for this team",
                type="WorkflowDisabled",
                non_retryable=True,
            )

        success = False
        reason = None

        try:
            # Step 1: Extract PO details from email, document, or directly from po_number
            extraction_result = await self.extract_po_details(
                email_id=email_id,
                document_id=document_id,
                team_id=team_id,
                po_number=po_number,
            )

            if not extraction_result.success:
                self.metrics.record_workflow_attempt(
                    team_id=team_id,
                    success=False,
                    duration_seconds=(
                        workflow.now() - workflow.info().start_time
                    ).total_seconds(),
                    reason="extraction_failed",
                )
                raise ApplicationError(
                    f"Failed to extract PO details: {extraction_result.error_message}",
                    type="ExtractionFailed",
                    non_retryable=True,
                )

            if extraction_result.po_exists:
                self.metrics.record_workflow_attempt(
                    team_id=team_id,
                    success=False,
                    duration_seconds=(
                        workflow.now() - workflow.info().start_time
                    ).total_seconds(),
                    reason="po_already_exists",
                )
                raise ApplicationError(
                    "PO already exists",
                    {"duplicate": True},
                    type="DuplicateResource",
                    non_retryable=True,
                )

            # Step 2: Check configuration for human validation requirement
            human_validation_enabled = await self.get_human_validation_setting(team_id)

            # Run post-extraction extension activities if configured
            if self.config.post_extraction_activities:
                extraction_result.po_data = await self._run_extension_activities(
                    extraction_result.po_data,
                    self.config.post_extraction_activities,
                    "post_extraction",
                )

            # Run pre-creation extension activities if configured
            if self.config.pre_creation_activities:
                extraction_result.po_data = await self._run_extension_activities(
                    extraction_result.po_data,
                    self.config.pre_creation_activities,
                    "pre_creation",
                )

            # Step 4: Create the Purchase Order
            if extraction_result.po_data is None:
                self.metrics.record_workflow_attempt(
                    team_id=team_id,
                    success=False,
                    duration_seconds=(
                        workflow.now() - workflow.info().start_time
                    ).total_seconds(),
                    reason="no_po_data",
                )
                raise ApplicationError(
                    "No PO data extracted",
                    type="ExtractionFailed",
                    non_retryable=True,
                )

            creation_result = await self.create_purchase_order(
                email_id=email_id,
                document_id=document_id,
                team_id=team_id,
                po_data=extraction_result.po_data,
                extraction_method=extraction_result.extraction_method,
                human_validation_enabled=human_validation_enabled,
                is_data_sufficient=extraction_result.is_data_sufficient,
            )

            if not creation_result.success:
                self.metrics.record_workflow_attempt(
                    team_id=team_id,
                    success=False,
                    duration_seconds=(
                        workflow.now() - workflow.info().start_time
                    ).total_seconds(),
                    reason="po_creation_failed",
                )
                raise ApplicationError(
                    f"Failed to create PO: {creation_result.error_message}",
                    type="CreationFailed",
                    non_retryable=True,
                )

            # Step 5: Link email and create tasks (non-critical)
            if creation_result.po_id is None or creation_result.po_number is None:
                self.metrics.record_workflow_attempt(
                    team_id=team_id,
                    success=False,
                    duration_seconds=(
                        workflow.now() - workflow.info().start_time
                    ).total_seconds(),
                    reason="po_missing_details",
                )
                raise ApplicationError(
                    "PO created but missing ID or number",
                    type="CreationIncomplete",
                    non_retryable=True,
                )

            task_id = await self.link_email_and_create_tasks(
                email_id=email_id,
                document_id=document_id,
                team_id=team_id,
                po_id=creation_result.po_id,
                po_number=creation_result.po_number,
                created_in_draft=creation_result.created_in_draft,
            )

            # Run post-creation extension activities if configured
            if self.config.post_creation_activities:
                await self._run_extension_activities(
                    {
                        "po_id": creation_result.po_id,
                        "po_number": creation_result.po_number,
                    },
                    self.config.post_creation_activities,
                    "post_creation",
                )

            success = True
            return {
                "success": True,
                "po_id": creation_result.po_id,
                "po_number": creation_result.po_number,
                "created_in_draft": creation_result.created_in_draft,
                "task_id": task_id,
            }

        except ApplicationError as e:
            # Record failure metrics for non-retryable errors
            self.metrics.record_workflow_attempt(
                team_id=team_id,
                success=False,
                duration_seconds=(
                    workflow.now() - workflow.info().start_time
                ).total_seconds(),
                reason="application_error",
            )

            self.logger.error(f"Workflow failed with non-retryable error: {str(e)}")
            # Re-raise to fail the workflow properly
            raise
        except Exception as e:
            # Record failure metrics for other errors

            self.metrics.record_workflow_attempt(
                team_id=team_id,
                success=success,
                duration_seconds=(
                    workflow.now() - workflow.info().start_time
                ).total_seconds(),
                reason=reason,
            )

            self.logger.error(f"Workflow failed with unexpected error: {str(e)}")
            # Re-raise to let Temporal handle retries for transient errors
            raise

    async def _load_workflow_config(self, workflow_id: str) -> POCreationBehaviorConfig:
        """
        Load workflow configuration using an activity to avoid threading issues.
        """
        try:
            # Call the shared activity to load config
            config_dict = await workflow.execute_activity(
                load_workflow_config,
                args=[workflow_id, "po_creation"],
                start_to_close_timeout=timedelta(seconds=30),
                retry_policy=RetryPolicy(
                    maximum_attempts=3,
                    initial_interval=timedelta(seconds=1),
                    maximum_interval=timedelta(seconds=10),
                    backoff_coefficient=2.0,
                ),
            )

            # Convert dict back to pydantic model
            # If None, create default config
            if config_dict is None:
                return POCreationBehaviorConfig()
            return POCreationBehaviorConfig.model_validate(config_dict)

        except Exception as e:
            self.logger.error(f"Failed to load workflow config via activity: {str(e)}")
            # Return default config on error
            return POCreationBehaviorConfig()

    def _init_metrics(self):
        """
        Initialize workflow-specific metrics
        """
        self.metrics = POCreationMetrics()

    async def extract_po_details(
        self,
        email_id: Optional[str],
        document_id: Optional[str],
        team_id: str,
        po_number: Optional[str] = None,
    ) -> POExtractionResult:
        """
        Extract PO details from email, document, or directly from po_number using ERP or AI

        This method:
        1. If po_number is provided: Skip AI extraction and go directly to ERP extraction
        2. Otherwise: First tries AI extraction to get PO number, then ERP extraction
        3. Falls back to AI extraction if ERP not available or ERP extraction fails
        4. Checks if PO already exists
        5. Determines if data is sufficient for direct creation
        """
        ai_extraction_result = None
        extracted_po_number = po_number  # Use provided PO number if available

        try:
            # Step 1: Skip AI extraction if po_number is provided directly (auto-creation case)
            if po_number:
                self.logger.info(
                    f"PO number provided directly: {po_number}, skipping AI extraction"
                )
                # Use provided PO number, no AI extraction data available
                ai_po_details = None
            else:
                # Step 1: Run AI extraction first to get the PO number
                # This is needed for ERP flow as ERP needs the PO number to fetch data
                params = POExtractionParams(
                    email_id=email_id,
                    document_id=document_id,
                )

                ai_extraction_result = await workflow.execute_activity(
                    extract_po_details,
                    params,
                    start_to_close_timeout=timedelta(minutes=5),
                    retry_policy=RetryPolicy(
                        maximum_attempts=3,
                        initial_interval=timedelta(seconds=10),
                        maximum_interval=timedelta(seconds=60),
                        backoff_coefficient=2.0,
                    ),
                )

                # If AI extraction failed completely, return failure
                if not ai_extraction_result:
                    return POExtractionResult(
                        success=False,
                        error_message="No result returned from AI extraction",
                    )

                # Extract basic info from AI extraction
                ai_po_details = ai_extraction_result.get("po_details")
                extracted_po_number = ai_po_details.po_number if ai_po_details else None

                if not extracted_po_number:
                    return POExtractionResult(
                        success=False,
                        error_message="Could not extract PO number from email/document",
                    )

                self.logger.info(f"AI extracted PO number: {extracted_po_number}")

            # Step 2: IonQ ERP Integration - Try ERP extraction using the extracted PO number
            # This is the core of the ERP integration feature. For IonQ customers (team ID 4, 173),
            # we attempt to fetch complete PO data directly from NetSuite instead of relying on
            # incomplete AI extraction or slow RPA processes.
            try:
                erp_params = {
                    "email_id": email_id,
                    "team_id": team_id,
                    "po_number": extracted_po_number,
                    "po_details": ai_po_details,  # Pass AI data for comparison/validation (None for auto-creation)
                }

                # Execute ERP extraction with appropriate timeouts for NetSuite API calls
                # The ERP activity will return rich PO data with 19+ line items, complete
                # vendor information, and proper address details
                erp_result = await workflow.execute_activity(
                    extract_po_from_erp,
                    erp_params,
                    start_to_close_timeout=timedelta(
                        minutes=5
                    ),  # Allow time for NetSuite API
                    retry_policy=RetryPolicy(
                        maximum_attempts=2,  # Fewer attempts for ERP as fallback exists
                        initial_interval=timedelta(seconds=5),
                        maximum_interval=timedelta(seconds=30),
                        backoff_coefficient=2.0,
                    ),
                )

                if erp_result:
                    self.logger.info(
                        f"ERP extraction successful for PO {extracted_po_number}"
                    )

                    # Use rich ERP-extracted data instead of basic AI extraction
                    # ERP data includes complete line items, pricing, vendor details, and addresses
                    erp_po_details = erp_result.get("po_details")
                    is_data_sufficient = erp_result.get("is_data_sufficient", False)

                    self.logger.info(
                        f"ERP result details: po_details={erp_po_details is not None}, "
                        f"is_data_sufficient={is_data_sufficient}, "
                        f"supplier_name={erp_po_details.supplier_name if erp_po_details else None}, "
                        f"item_count={len(erp_po_details.items) if erp_po_details and erp_po_details.items else 0}"
                    )

                    return POExtractionResult(
                        success=True,
                        po_exists=False,
                        po_data=erp_po_details,
                        extraction_method="erp",
                        is_data_sufficient=is_data_sufficient,  # ⚠️ THIS WAS MISSING!
                    )

            except ApplicationError as e:
                # Check if this is a fallback scenario (ERP not configured or unsupported)
                if (
                    hasattr(e, "details")
                    and e.details
                    and e.details.get("fallback_to_ai")
                ):
                    self.logger.info(
                        f"ERP extraction failed with fallback flag: {str(e)}, using AI extraction"
                    )
                    # Continue to use AI extraction result below
                else:
                    # Check if it's a duplicate PO error
                    if hasattr(e, "type") and "DuplicateResourceError" in e.type:
                        return POExtractionResult(
                            success=False,
                            po_exists=True,
                            error_message=str(e),
                        )
                    # Re-raise non-fallback errors
                    raise
            except Exception as e:
                # For unexpected errors in ERP extraction
                if po_number:
                    # For auto-creation with direct po_number, ERP failure is a real failure (no AI fallback available)
                    self.logger.error(
                        f"ERP extraction failed for auto-creation with PO {extracted_po_number}: {str(e)}"
                    )
                    return POExtractionResult(
                        success=False,
                        error_message=f"ERP extraction failed for PO {extracted_po_number}: {str(e)}",
                    )
                else:
                    # For email-based extraction, fall back to AI
                    self.logger.warning(
                        f"Unexpected error in ERP extraction, falling back to AI: {str(e)}"
                    )

            # Step 3: Use AI extraction result as fallback (only for email-based flows)
            if not po_number and ai_extraction_result:
                self.logger.info(
                    f"Using AI extraction result for PO {extracted_po_number}"
                )

                ai_po_details = ai_extraction_result.get("po_details")
                is_data_sufficient = ai_extraction_result.get(
                    "is_data_sufficient", False
                )

                # Determine extraction method based on data completeness
                extraction_method = "ai" if is_data_sufficient else "netsuite"

                self.logger.info(
                    f"AI fallback result: po_details={ai_po_details is not None}, "
                    f"is_data_sufficient={is_data_sufficient}, "
                    f"extraction_method={extraction_method}"
                )

                return POExtractionResult(
                    success=True,
                    po_exists=False,
                    po_data=ai_po_details,
                    extraction_method=extraction_method,
                    is_data_sufficient=is_data_sufficient,
                )

            # If we reach here, both ERP and AI failed (shouldn't happen for auto-creation case)
            return POExtractionResult(
                success=False,
                error_message=f"All extraction methods failed for PO {extracted_po_number}",
            )

        except ApplicationError as e:
            # Handle non-retryable errors from AI extraction
            self.logger.error(f"Non-retryable error extracting PO details: {str(e)}")

            # Check if it's a duplicate PO error
            if hasattr(e, "type") and "DuplicateResourceError" in e.type:
                return POExtractionResult(
                    success=False,
                    po_exists=True,
                    error_message=str(e),
                )

            return POExtractionResult(success=False, error_message=str(e))
        except Exception as e:
            self.logger.error(f"Failed to extract PO details: {str(e)}")
            return POExtractionResult(success=False, error_message=str(e))

    async def get_human_validation_setting(self, team_id: str) -> bool:
        """Get human validation requirement from team settings"""
        # For now, use the workflow config directly to avoid threading issues
        # The DAG activity will handle the team setting lookup internally
        return self.config.require_human_validation

    async def create_purchase_order(
        self,
        email_id: Optional[str],
        document_id: Optional[str],
        team_id: str,
        po_data: Optional[PurchaseOrderDetails],
        extraction_method: str,
        human_validation_enabled: bool,
        is_data_sufficient: bool,
    ) -> POCreationResult:
        """
        Create a purchase order

        This method:
        1. Determines PO status based on validation settings
        2. Creates or matches supplier
        3. Creates the PO with appropriate status
        4. Creates order items
        5. Handles both direct creation and NetSuite integration
        """
        try:
            # po_data is a PurchaseOrderDetails object from extraction
            po_number = po_data.po_number if po_data else ""

            # Create parameters for the DAG create_po activity
            params: POCreationParamsOptional = {
                "email_id": email_id,
                "po_number": po_number,
                "team_id": int(team_id) if team_id else None,
            }

            # Add document_id if this is a document-based import
            if document_id:
                params["document_id"] = document_id

            # Add optional fields based on extraction result
            if po_data:
                params["po_details"] = po_data  # PurchaseOrderDetails object

            # Use the is_data_sufficient from the extraction result, not hardcoded logic
            params["is_data_sufficient"] = is_data_sufficient

            self.logger.info(
                f"Creating PO with extraction method: {extraction_method}, "
                f"is_data_sufficient: {is_data_sufficient}, "
                f"po_data: {po_data is not None}"
            )

            # Call the DAG create_po activity
            result = await workflow.execute_activity(
                create_po,
                params,
                start_to_close_timeout=timedelta(minutes=5),
                retry_policy=RetryPolicy(
                    maximum_attempts=3,
                    initial_interval=timedelta(seconds=10),
                    maximum_interval=timedelta(seconds=60),
                    backoff_coefficient=2.0,
                ),
            )

            if result:
                # The DAG activity handles the human validation logic internally
                # It will create the PO in DRAFT status if human validation is enabled
                return POCreationResult(
                    success=True,
                    po_id=result["purchase_order_id"],
                    po_number=result["po_number"],
                    created_in_draft=human_validation_enabled,  # Track this based on our setting
                )

            return POCreationResult(
                success=False, error_message="No result returned from PO creation"
            )

        except ApplicationError as e:
            # Handle non-retryable errors
            self.logger.error(f"Non-retryable error creating PO: {str(e)}")
            return POCreationResult(success=False, error_message=str(e))
        except Exception as e:
            self.logger.error(f"Failed to create PO: {str(e)}")
            return POCreationResult(success=False, error_message=str(e))

    async def link_email_and_create_tasks(
        self,
        email_id: Optional[str],
        document_id: Optional[str],
        team_id: str,
        po_id: str,
        po_number: str,
        created_in_draft: bool,
    ) -> Optional[str]:
        """
        Link email or document to PO and create appropriate tasks

        This method:
        1. Links email thread or document to the PO
        2. Creates activity log entries
        3. Creates either confirmation task (draft) or success notification (issued)

        Returns task_id if successful, None if failed (non-critical)
        """
        try:
            # Handle both email and document linking
            if email_id or document_id:
                # Create parameters for the DAG link_email activity
                params: POLinkEmailParams = {
                    "email_id": email_id,
                    "document_id": document_id,
                    "purchase_order_id": po_id,
                }

                # Call the DAG link_email activity which handles:
                # 1. Linking email thread or document to PO
                # 2. Creating activity log
                # 3. Creating confirmation tasks or success notifications
                await workflow.execute_activity(
                    link_email,
                    params,
                    start_to_close_timeout=timedelta(minutes=2),
                    retry_policy=RetryPolicy(
                        maximum_attempts=2, initial_interval=timedelta(seconds=5)
                    ),
                )

            # The DAG activity handles task creation internally
            # Return None since we don't have a specific task_id
            return None

        except ApplicationError as e:
            # Non-retryable errors in linking are non-critical
            self.logger.warning(f"Non-retryable error linking email: {str(e)}")
            return None
        except Exception as e:
            # This is non-critical, so we log but don't fail the workflow
            self.logger.warning(f"Failed to link email or create tasks: {str(e)}")
            return None

    async def get_task_assignee(self, team_id: str) -> str:
        """Get the appropriate task assignee for the team"""
        # Special handling for team 47 (TotalHomeSupply)
        if team_id == "47":
            return "<EMAIL>"

        # Default to AI user
        return "ai_user"

    async def _run_extension_activities(
        self, data: Any, activities: List[str], phase: str
    ) -> Any:
        """
        Run extension activities for customization
        """
        result = data
        for activity_name in activities:
            try:
                # Extension activities would be registered separately
                # For now, log that we would run them
                self.logger.info(
                    f"Would run extension activity '{activity_name}' in phase '{phase}'"
                )
                # In a real implementation:
                # result = await workflow.execute_activity(
                #     activity_name,
                #     {"data": result, "phase": phase},
                #     start_to_close_timeout=timedelta(minutes=2)
                # )
            except Exception as e:
                self.logger.error(
                    f"Extension activity {activity_name} failed: {str(e)}"
                )
                # Continue with other extensions

        return result

    def _get_retry_policy(self) -> RetryPolicy:
        """Get retry policy from configuration"""
        return get_retry_policy(self.config)
