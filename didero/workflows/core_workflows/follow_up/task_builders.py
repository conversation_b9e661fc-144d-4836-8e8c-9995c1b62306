"""
Task builders for follow-up workflow.

These builders create follow-up tasks with enriched context and AI-generated
email content based on the type of follow-up needed.
"""

from typing import Any, Dict, List

import structlog
from asgiref.sync import sync_to_async

from didero.tasks.utils import CreateTaskActionParams
from didero.workflows.core_workflows.follow_up.schemas import (
    <PERSON>ailContent,
    TaskCreationResult,
)

logger = structlog.get_logger(__name__)


class BaseTaskBuilder:
    """Base class for task builders."""

    async def create_task(
        self,
        purchase_order_id: str,
        attempts_made: int,
        max_attempts: int,
        context: Any = None,
    ) -> TaskCreationResult:
        """
        Create a follow-up task using the task builder pattern.

        This orchestrates the full task creation flow:
        1. Get PO and base context
        2. Build enriched context from condition context + email context
        3. Generate email content using enriched context
        4. Create task actions (email vs email+call logic)
        5. Create task with all context
        """
        # Import here to avoid circular imports in Temporal
        from didero.workflows.core_workflows.follow_up.utils import (
            get_purchase_order_with_relations,
        )

        # 1. Get PO and base context
        purchase_order = await sync_to_async(
            get_purchase_order_with_relations, thread_sensitive=True
        )(purchase_order_id)

        # 2. Build enriched context from condition context + email context
        enriched_context = await self._build_enriched_context(
            purchase_order, context, attempts_made, max_attempts
        )

        # 3. Generate email content using enriched context
        email_content = await self._generate_email_content(enriched_context)

        # 4. Create task actions (email vs email+call logic here)
        actions = await self._create_task_actions(enriched_context, email_content)

        # 5. Create task with all context
        from didero.workflows.core_workflows.follow_up.utils import (
            create_task_with_context,
        )

        return await sync_to_async(create_task_with_context, thread_sensitive=True)(
            purchase_order, actions, enriched_context
        )

    # Subclasses must implement these type-specific methods
    async def _build_enriched_context(
        self, purchase_order: Any, context: Any, attempts_made: int, max_attempts: int
    ) -> Dict[str, Any]:
        """Build enriched context specific to this follow-up type."""
        raise NotImplementedError

    async def _generate_email_content(
        self, enriched_context: Dict[str, Any]
    ) -> EmailContent:
        """Generate email content using enriched context."""
        raise NotImplementedError

    async def _create_task_actions(
        self, enriched_context: Dict[str, Any], email_content: EmailContent
    ) -> List[CreateTaskActionParams]:
        """Create task actions based on context (email vs email+call logic)."""
        raise NotImplementedError


class OATaskBuilder(BaseTaskBuilder):
    """Creates Order Acknowledgement follow-up tasks."""

    async def _build_enriched_context(
        self, purchase_order: Any, context: Any, attempts_made: int, max_attempts: int
    ) -> Dict[str, Any]:
        """Build enriched context for OA follow-up."""
        from didero.emails.utils.email_utils import get_po_email_context
        from didero.workflows.core_workflows.follow_up.utils import build_base_context

        # Build base context
        base_context = build_base_context(
            purchase_order, attempts_made, max_attempts, "oa"
        )

        # Get email context
        po_email_context = await sync_to_async(
            get_po_email_context, thread_sensitive=True
        )(purchase_order)

        # Build context for prompt formatting
        retry_info = base_context.get("retry_info", {})
        po_details = base_context.get("po_details", {})

        # OA-specific enriched context with prompt-ready fields
        enriched_context = {
            **base_context,
            "po_email_context": po_email_context,
            "condition_context": context,
            "purchase_order": purchase_order,
            # Prompt-ready fields
            "attempt_number": retry_info.get("attempts_made", 0) + 1,
            "placement_time": po_details.get("placement_time", ""),
            "total_cost": po_details.get("total_cost", 0),
            "order_status": po_details.get("order_status", ""),
            "email_history_count": len(po_email_context.email_history)
            if po_email_context
            else 0,
        }

        return enriched_context

    async def _generate_email_content(
        self, enriched_context: Dict[str, Any]
    ) -> EmailContent:
        """Generate OA email content using AI."""
        from asgiref.sync import sync_to_async

        from didero.ai.email_generation.email_generation import ai_generate_email

        return await sync_to_async(ai_generate_email, thread_sensitive=True)(
            enriched_context, "oa_follow_up"
        )

    async def _create_task_actions(
        self, enriched_context: Dict[str, Any], email_content: EmailContent
    ) -> List[CreateTaskActionParams]:
        """Create OA task actions (email + call on final attempt)."""
        from didero.tasks.utils import create_call_supplier_action, create_email_action

        actions = []

        # Always add email action
        po_email_context = enriched_context.get("po_email_context", {})
        supplier_email = (
            getattr(po_email_context, "supplier_email", "") if po_email_context else ""
        )
        actions.append(create_email_action(email_content, supplier_email))

        # Add call action on final attempt
        is_final_attempt = enriched_context.get("retry_info", {}).get(
            "is_final_attempt", False
        )
        if is_final_attempt:
            purchase_order = enriched_context.get("purchase_order")
            supplier_phone = ""
            if purchase_order and purchase_order.supplier:
                supplier_phone = getattr(purchase_order.supplier, "phone", "") or ""
            # Only add call action if the function returns a valid action (not None)
            call_action = create_call_supplier_action(supplier_phone)
            if call_action:
                actions.append(call_action)

        return actions


class ShipDateTaskBuilder(BaseTaskBuilder):
    """Creates ship date follow-up tasks."""

    async def _build_enriched_context(
        self, purchase_order: Any, context: Any, attempts_made: int, max_attempts: int
    ) -> Dict[str, Any]:
        """Build enriched context for ship date follow-up."""
        from didero.emails.utils.email_utils import get_po_email_context
        from didero.workflows.core_workflows.follow_up.utils import build_base_context

        # Build base context
        base_context = build_base_context(
            purchase_order, attempts_made, max_attempts, "ship_dates"
        )

        # Get email context
        po_email_context = await sync_to_async(
            get_po_email_context, thread_sensitive=True
        )(purchase_order)

        # Get OA context to find incomplete items using OAManager
        from didero.orders.models import OrderAcknowledgement

        oa_context = await sync_to_async(
            OrderAcknowledgement.objects.get_oa_context, thread_sensitive=True
        )(purchase_order)

        # Build context for prompt formatting
        retry_info = base_context.get("retry_info", {})
        po_details = base_context.get("po_details", {})

        # Ship date-specific enriched context
        enriched_context = {
            **base_context,
            "po_email_context": po_email_context,
            "oa_context": oa_context,
            "condition_context": context,
            "purchase_order": purchase_order,
            # Prompt-ready fields
            "attempt_number": retry_info.get("attempts_made", 0) + 1,
            "placement_time": po_details.get("placement_time", ""),
            "total_cost": po_details.get("total_cost", 0),
            "email_history_count": len(po_email_context.email_history)
            if po_email_context
            else 0,
            "incomplete_items": ", ".join(oa_context.incomplete_items)
            if oa_context.incomplete_items
            else "multiple items",
        }

        return enriched_context

    async def _generate_email_content(
        self, enriched_context: Dict[str, Any]
    ) -> EmailContent:
        """Generate ship date email content using AI."""
        from asgiref.sync import sync_to_async

        from didero.ai.email_generation.email_generation import ai_generate_email

        return await sync_to_async(ai_generate_email, thread_sensitive=True)(
            enriched_context, "ship_date_follow_up"
        )

    async def _create_task_actions(
        self, enriched_context: Dict[str, Any], email_content: EmailContent
    ) -> List[CreateTaskActionParams]:
        """Create ship date task actions (email + call on final attempt)."""
        from didero.tasks.utils import create_call_supplier_action, create_email_action

        actions = []

        # Always add email action
        po_email_context = enriched_context.get("po_email_context", {})
        supplier_email = (
            getattr(po_email_context, "supplier_email", "") if po_email_context else ""
        )
        actions.append(create_email_action(email_content, supplier_email))

        # Add call action on final attempt
        is_final_attempt = enriched_context.get("retry_info", {}).get(
            "is_final_attempt", False
        )
        if is_final_attempt:
            purchase_order = enriched_context.get("purchase_order")
            supplier_phone = ""
            if purchase_order and purchase_order.supplier:
                supplier_phone = getattr(purchase_order.supplier, "phone", "") or ""
            # Only add call action if the function returns a valid action (not None)
            call_action = create_call_supplier_action(supplier_phone)
            if call_action:
                actions.append(call_action)

        return actions


class ShipmentTaskBuilder(BaseTaskBuilder):
    """Creates shipment follow-up tasks with rich context support."""

    async def _build_enriched_context(
        self, purchase_order: Any, context: Any, attempts_made: int, max_attempts: int
    ) -> Dict[str, Any]:
        """Build enriched context for shipment follow-up."""
        from didero.emails.utils.email_utils import get_po_email_context
        from didero.workflows.core_workflows.follow_up.utils import build_base_context

        # Build base context
        base_context = build_base_context(
            purchase_order, attempts_made, max_attempts, "shipment"
        )

        # Get email context
        po_email_context = await sync_to_async(
            get_po_email_context, thread_sensitive=True
        )(purchase_order)

        # Extract shipment context from condition checker
        shipment_items = []
        group_context = {}
        if context and "items" in context:
            # Filter out the special _group_context item
            for item in context["items"]:
                if "_group_context" in item:
                    group_context = item["_group_context"]
                else:
                    shipment_items.append(item)

        # Build context for prompt formatting
        retry_info = base_context.get("retry_info", {})
        po_details = base_context.get("po_details", {})

        # Determine overdue status and strategy from group context
        is_overdue = group_context.get("is_overdue", False)
        strategy = group_context.get("strategy", context.get("strategy", "balanced"))
        overdue_days = group_context.get("overdue_days", 0)
        days_until_ship = group_context.get("days_until_ship", 0)

        # Build shipment context description
        if is_overdue:
            shipment_context = (
                f"Items are {overdue_days} days overdue. "
                f"Promised ship dates: {group_context.get('group_min_date')} to {group_context.get('group_max_date')}. "
                f"Total unshipped items: {len(shipment_items)}"
            )
            overdue_status = f"{overdue_days} days overdue"
        else:
            shipment_context = (
                f"Scheduled follow-up {days_until_ship} days before ship date. "
                f"Promised ship dates: {group_context.get('group_min_date')} to {group_context.get('group_max_date')}. "
                f"Total unshipped items: {len(shipment_items)}"
            )
            overdue_status = "Not overdue - proactive follow-up"

        # Shipment-specific enriched context
        enriched_context = {
            **base_context,
            "po_email_context": po_email_context,
            "shipment_items": shipment_items,
            "group_context": group_context,
            "condition_context": context,
            "purchase_order": purchase_order,
            # Prompt-ready fields
            "attempt_number": retry_info.get("attempts_made", 0) + 1,
            "placement_time": po_details.get("placement_time", ""),
            "total_cost": po_details.get("total_cost", 0),
            "email_history_count": len(po_email_context.email_history)
            if po_email_context
            else 0,
            "strategy": strategy,
            "overdue_status": overdue_status,
            "shipment_context": shipment_context,
            "is_overdue": is_overdue,
        }

        return enriched_context

    async def _generate_email_content(
        self, enriched_context: Dict[str, Any]
    ) -> EmailContent:
        """Generate shipment email content using AI."""
        from asgiref.sync import sync_to_async

        from didero.ai.email_generation.email_generation import ai_generate_email

        return await sync_to_async(ai_generate_email, thread_sensitive=True)(
            enriched_context, "shipment_follow_up"
        )

    async def _create_task_actions(
        self, enriched_context: Dict[str, Any], email_content: EmailContent
    ) -> List[CreateTaskActionParams]:
        """Create shipment task actions (email only for pre-ship, email+call for overdue)."""
        from didero.tasks.utils import create_call_supplier_action, create_email_action

        actions = []

        # Always add email action
        po_email_context = enriched_context.get("po_email_context", {})
        supplier_email = (
            getattr(po_email_context, "supplier_email", "") if po_email_context else ""
        )
        actions.append(create_email_action(email_content, supplier_email))

        # Add call action if overdue OR on final attempt
        is_overdue = enriched_context.get("is_overdue", False)
        is_final_attempt = enriched_context.get("retry_info", {}).get(
            "is_final_attempt", False
        )

        if is_overdue or is_final_attempt:
            purchase_order = enriched_context.get("purchase_order")
            supplier_phone = ""
            if purchase_order and purchase_order.supplier:
                supplier_phone = getattr(purchase_order.supplier, "phone", "") or ""
            # Only add call action if the function returns a valid action (not None)
            call_action = create_call_supplier_action(supplier_phone)
            if call_action:
                actions.append(call_action)

        return actions
