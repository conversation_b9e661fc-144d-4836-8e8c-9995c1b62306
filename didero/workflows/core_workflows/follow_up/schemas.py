from datetime import date, datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel


class FollowUpStatus(str, Enum):
    COMPLETED = "completed"
    ERROR = "error"


class FollowUpType(str, Enum):
    OA = "oa"
    SHIP_DATES = "ship_dates"
    SHIPMENT = "shipment"


class FollowUpResult(BaseModel):
    success: bool
    status: FollowUpStatus
    message: str
    purchase_order_id: str
    activities_run: List[str]  # Which activities were executed
    successful_activities: List[str]  # Which activities succeeded
    failed_activities: List[str]  # Which activities failed


# Response schemas for utility functions
class EmailInfo(BaseModel):
    subject: Optional[str] = None
    body: Optional[str] = None
    comm_time: Optional[datetime] = None
    email_from: Optional[str] = None
    direction: Optional[str] = None


class POItem(BaseModel):
    item_name: str
    quantity: float
    price: Optional[float] = None
    requested_date: Optional[Union[date, datetime]] = None


class POEmailContext(BaseModel):
    po_number: Optional[str] = None
    po_status: str
    placement_time: datetime
    expected_delivery_date: Optional[datetime] = None
    supplier_name: Optional[str] = None
    supplier_email: Optional[str] = None
    total_cost: Optional[float] = None
    items: List[POItem]
    latest_email: Optional[EmailInfo] = None
    email_history: List[EmailInfo]


class EmailContent(BaseModel):
    email_subject: str
    email_body: str
    email_type: str


class TaskParams(BaseModel):
    supplier_name: str
    po_number: Optional[str] = None
    follow_up_type: str
    followup_config: Dict[str, Any]
    attempts_made: int
    max_attempts: int
    is_final_attempt: bool
    email_subject: str
    email_body: str
    email_type: str
    context: POEmailContext


class ActivityResult(BaseModel):
    success: bool
    message: str


class TaskCreationResult(ActivityResult):
    task_id: str


# Schemas for OA context and ship date follow-up
class OAItem(BaseModel):
    item_name: str
    promised_ship_date: Optional[datetime] = None
    promised_delivery_date: Optional[datetime] = None
    has_ship_date: bool


class OAContext(BaseModel):
    existing_oas: List[Dict[str, Any]]
    incomplete_items: List[str]
    has_all_ship_dates: bool


# Legacy phase schemas removed - using independent activities now
