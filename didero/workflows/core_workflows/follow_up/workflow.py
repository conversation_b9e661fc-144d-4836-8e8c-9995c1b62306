from datetime import timed<PERSON><PERSON>

from temporalio import workflow

with workflow.unsafe.imports_passed_through():
    import structlog

    from didero.users.utils.team_setting_utils import TeamFollowupConfig
    from didero.workflows.core_workflows.follow_up.activities import (
        oa_follow_up_activity,
        ship_date_follow_up_activity,
        shipment_follow_up_activity,
    )
    from didero.workflows.core_workflows.follow_up.schemas import (
        ActivityResult,
        FollowUpResult,
        FollowUpStatus,
    )
    from didero.workflows.shared_activities.follow_up_data import (
        get_followup_config_activity,
    )
    from didero.workflows.shared_activities.purchase_order_operations import (
        get_purchase_order_activity,
    )


logger = structlog.get_logger(__name__)


@workflow.defn
class FollowUpWorkflow:
    @workflow.run
    async def run(self, workflow_id: str, params: dict) -> FollowUpResult:
        """
        Independent follow-up workflow that runs team-configured activities.

        Based on team settings, this workflow can run any combination of:
        1. Order Acknowledgement follow-up
        2. Ship dates follow-up
        3. Shipping follow-up

        Args:
            workflow_id: ID of the UserWorkflow instance
            params: Dictionary containing purchase_order_id and email_object_id

        Returns:
            FollowUpResult containing workflow outcome
        """
        # Extract parameters from params dict
        purchase_order_id = params.get("purchase_order_id")
        email_object_id = params.get("email_object_id", "")

        if not purchase_order_id:
            raise ValueError("purchase_order_id is required in params")

        logger.info(
            "starting follow-up workflow",
            workflow_id=workflow_id,
            purchase_order_id=purchase_order_id,
            email_object_id=email_object_id,
        )

        # Get PO and followup config to determine which activities to run
        purchase_order = await workflow.execute_activity(
            get_purchase_order_activity,
            args=[purchase_order_id],
            schedule_to_close_timeout=timedelta(minutes=5),
            retry_policy=None,
        )

        followup_config_dict = await workflow.execute_activity(
            get_followup_config_activity,
            args=[purchase_order["team_id"], purchase_order["supplier_id"]],
            schedule_to_close_timeout=timedelta(minutes=5),
            retry_policy=None,
        )

        # Reconstruct Pydantic object from dict (Temporal serializes Pydantic v2 to dict)
        followup_config = TeamFollowupConfig(**followup_config_dict)

        # Determine which activities to run based on team configuration
        activities_to_run = []
        if followup_config.oa_followup.enabled:
            activities_to_run.append("oa")
            # Ship dates are part of OA follow-up - only enable if OA is enabled
            activities_to_run.append("ship_dates")
        if followup_config.shipment_followup.enabled:
            activities_to_run.append("shipment")

        logger.info(
            "determined activities to run",
            purchase_order_id=purchase_order_id,
            activities_to_run=activities_to_run,
        )

        # Execute enabled activities independently
        activity_results = {}

        for activity_name in activities_to_run:
            logger.info(
                "executing follow-up activity",
                purchase_order_id=purchase_order_id,
                activity=activity_name,
            )

            try:
                if activity_name == "oa":
                    result = await workflow.execute_activity(
                        oa_follow_up_activity,
                        schedule_to_close_timeout=timedelta(
                            hours=followup_config.oa_followup.initial_wait_hours
                            + followup_config.oa_followup.max_attempts_before_task
                            * followup_config.oa_followup.repeat_every_hours
                            + 1
                        ),
                        args=[purchase_order_id, followup_config.oa_followup],
                        retry_policy=None,
                    )
                elif activity_name == "ship_dates":
                    result = await workflow.execute_activity(
                        ship_date_follow_up_activity,
                        schedule_to_close_timeout=timedelta(
                            hours=followup_config.oa_followup.initial_wait_hours
                            + followup_config.oa_followup.max_attempts_before_task
                            * followup_config.oa_followup.repeat_every_hours
                            + 1
                        ),
                        args=[purchase_order_id, followup_config.oa_followup],
                        retry_policy=None,
                    )
                elif activity_name == "shipment":
                    result = await workflow.execute_activity(
                        shipment_follow_up_activity,
                        schedule_to_close_timeout=timedelta(
                            hours=followup_config.shipment_followup.time_based_wait_hours
                            + followup_config.shipment_followup.max_attempts_before_task
                            * followup_config.shipment_followup.time_based_repeat_hours
                            + 1
                        ),
                        args=[purchase_order_id, followup_config.shipment_followup],
                        retry_policy=None,
                    )
                else:
                    logger.warning(
                        "unknown activity type",
                        purchase_order_id=purchase_order_id,
                        activity=activity_name,
                    )
                    continue

                activity_results[activity_name] = result

                logger.info(
                    "activity completed",
                    purchase_order_id=purchase_order_id,
                    activity=activity_name,
                    success=result.success,
                    message=result.message,
                )

            except Exception as e:
                logger.error(
                    "activity failed",
                    purchase_order_id=purchase_order_id,
                    activity=activity_name,
                    error=str(e),
                )
                activity_results[activity_name] = ActivityResult(
                    success=False,
                    message=f"Activity failed: {str(e)}",
                )

        # Build summary result
        successful_activities = [
            name for name, result in activity_results.items() if result.success
        ]
        failed_activities = [
            name for name, result in activity_results.items() if not result.success
        ]

        return FollowUpResult(
            success=len(failed_activities) == 0,
            status=FollowUpStatus.COMPLETED
            if len(failed_activities) == 0
            else FollowUpStatus.ERROR,
            message=f"Completed {len(successful_activities)}/{len(activities_to_run)} follow-up activities",
            purchase_order_id=purchase_order_id,
            activities_run=activities_to_run,
            successful_activities=successful_activities,
            failed_activities=failed_activities,
        )
