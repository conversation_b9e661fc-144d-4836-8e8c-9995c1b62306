from datetime import date, datetime, timedelta
from typing import Any, Dict, List, Tuple

import structlog

from didero.orders.models import (
    OrderAcknowledgement,
    OrderAcknowledgementItem,
    PurchaseOrder,
    ShipmentStatus,
)
from didero.tasks.utils import CreateTaskActionParams
from didero.workflows.core_workflows.follow_up.schemas import TaskCreationResult

logger = structlog.get_logger(__name__)


# Strategy-based follow-up frequency configuration
SHIPMENT_FOLLOWUP_STRATEGY_CONFIG = {
    "aggressive": {"min_followups": 3, "max_followups": 5, "frequency_factor": 1.5},
    "balanced": {"min_followups": 3, "max_followups": 4, "frequency_factor": 2.0},
    "conservative": {"min_followups": 2, "max_followups": 3, "frequency_factor": 3.0},
}


def get_purchase_order_with_relations(purchase_order_id: str) -> PurchaseOrder:
    """
    Utility to get purchase order with only the relations we actually use.

    Args:
        purchase_order_id: UUID of the purchase order

    Returns:
        PurchaseOrder: Purchase order with supplier and team loaded
    """
    return PurchaseOrder.objects.select_related("supplier", "team").get(
        pk=purchase_order_id
    )


def get_start_date_for_shipment_follow_up(
    purchase_order: PurchaseOrder, latest_oa: OrderAcknowledgement
) -> datetime:
    """
    Get the start date for follow-up calculation.

    For first group: Use OA received date
    For subsequent groups: Use latest shipment date (when previous group completed)

    Args:
        purchase_order: The purchase order
        latest_oa: The latest order acknowledgment

    Returns:
        Start datetime for follow-up calculation
    """
    # Check if any shipments exist with actual shipment dates
    latest_shipment = (
        purchase_order.shipments.filter(
            status__in=[ShipmentStatus.SHIPPED.value, ShipmentStatus.RECEIVED.value],
            shipment_date__isnull=False,
        )
        .order_by("-shipment_date")
        .first()
    )

    if latest_shipment and latest_shipment.shipment_date:
        # There are completed shipments - use the latest shipment date
        # Convert date to datetime at start of day
        start_date = datetime.combine(
            latest_shipment.shipment_date, datetime.min.time()
        )

        logger.info(
            "using latest shipment date as start date for follow-up",
            purchase_order_id=purchase_order.id,
            po_number=purchase_order.po_number,
            latest_shipment_date=latest_shipment.shipment_date.isoformat(),
            start_date=start_date.isoformat(),
        )

        return start_date
    else:
        # No shipments yet - this is the first group, use OA date
        logger.info(
            "no shipments found, using OA received date as start date",
            purchase_order_id=purchase_order.id,
            po_number=purchase_order.po_number,
            oa_received_date=latest_oa.created_at.isoformat(),
        )

        return latest_oa.created_at


def calculate_shipment_followup_dates(
    ship_date: date,
    strategy: str,
    start_date: datetime,
    initial_wait_hours: int = 24,
    business_hours_only: bool = True,
    timezone: str = "America/New_York",
) -> List[date]:
    """
    Calculate follow-up dates for shipment using practical day-based strategy.

    Logic:
    - Start: 1 day after today (or after initial_wait_hours from start_date, whichever is later)
    - End: 1 day before ship_date
    - Frequency: Based on strategy (conservative=less frequent, aggressive=more frequent)
    - Business hours: If enabled, ensures follow-ups land on business days

    Args:
        ship_date: Promised ship date from OA
        strategy: "aggressive", "balanced", or "conservative"
        start_date: Start date for follow-up calculation (OA date or latest shipment date)
        initial_wait_hours: Hours to wait after start_date before first follow-up
        business_hours_only: Whether to ensure follow-ups land on business days
        timezone: Timezone for business hours calculation

    Returns:
        List of follow-up dates sorted chronologically
    """
    if strategy not in SHIPMENT_FOLLOWUP_STRATEGY_CONFIG:
        logger.warning(f"Unknown strategy {strategy}, using balanced")
        strategy = "balanced"

    config = SHIPMENT_FOLLOWUP_STRATEGY_CONFIG[strategy]
    today = date.today()

    # Calculate the earliest we can start following up
    initial_followup = start_date + timedelta(hours=initial_wait_hours)
    start_day = max(today + timedelta(days=1), initial_followup.date())

    # End 1 day before ship date
    end_day = ship_date - timedelta(days=1)

    # If start_day is after or equal to end_day, no follow-ups possible
    if start_day >= end_day:
        return []

    # Calculate available window and number of follow-ups
    available_window = (end_day - start_day).days + 1

    # Determine number of follow-ups based on strategy
    num_followups = max(
        config["min_followups"],
        min(
            config["max_followups"],
            max(2, int(available_window / config["frequency_factor"])),
        ),
    )

    # If only one follow-up possible, do it 1 day before ship date
    if num_followups <= 1 or available_window <= 1:
        return [end_day]

    # Distribute follow-ups evenly across the available window
    followup_dates = []
    if num_followups == 2:
        followup_dates = [start_day, end_day]
    else:
        gap = available_window / (num_followups - 1)
        for i in range(num_followups - 1):
            followup_day = start_day + timedelta(days=round(i * gap))
            if followup_day not in followup_dates and followup_day <= end_day:
                followup_dates.append(followup_day)
        followup_dates.append(end_day)

    # Ensure dates are unique, sorted, and within bounds
    unique_dates = sorted(list(set(followup_dates)))
    valid_dates = [d for d in unique_dates if start_day <= d <= end_day]

    # Apply business hours logic if enabled
    if business_hours_only:
        from didero.workflows.util.business_hours_utils import next_business_hour

        business_dates = []
        used_business_days = set()

        for follow_date in valid_dates:
            # Convert to business hour (9 AM on business day)
            follow_datetime = datetime.combine(follow_date, datetime.min.time())
            business_hour_dt = next_business_hour(follow_datetime, timezone)
            business_date = business_hour_dt.date()

            # Skip if we already have a follow-up on this business day
            if business_date not in used_business_days:
                business_dates.append(business_date)
                used_business_days.add(business_date)

                if business_date != follow_date:
                    logger.info(
                        "adjusted follow-up date for business hours",
                        original_date=follow_date.isoformat(),
                        business_date=business_date.isoformat(),
                        strategy=strategy,
                    )

        return business_dates

    return valid_dates


def group_items_by_ship_date(
    unshipped_items: List[Tuple[OrderAcknowledgementItem, date, int]],
    tolerance_days: int = 3,
) -> List[List[Tuple[OrderAcknowledgementItem, date, int]]]:
    """
    Group unshipped items by ship date with tolerance.

    Items with ship dates within tolerance_days of each other are grouped together.
    Groups are sorted by earliest ship date (earliest first).
    """
    if not unshipped_items:
        return []

    # Sort items by ship date
    sorted_items = sorted(unshipped_items, key=lambda x: x[1])

    groups = []
    current_group = [sorted_items[0]]
    current_base_date = sorted_items[0][1]

    for item in sorted_items[1:]:
        item_ship_date = item[1]
        days_diff = (item_ship_date - current_base_date).days

        if days_diff <= tolerance_days:
            # Item is within tolerance, add to current group
            current_group.append(item)
        else:
            # Item is beyond tolerance, start new group
            groups.append(current_group)
            current_group = [item]
            current_base_date = item_ship_date

    # Add the last group
    if current_group:
        groups.append(current_group)

    return groups


def check_shipment_follow_up_needed(
    purchase_order: PurchaseOrder,
    strategy: str,
    initial_wait_hours: int = 24,
    business_hours_only: bool = False,
    timezone: str = "America/New_York",
) -> Tuple[bool, str, List[dict]]:
    """
    Check if shipment follow-up is needed today for the given PO.

    Enhanced logic with rich context for email drafting:
    1. Check first unshipped group (earliest ship dates)
    2. Use min/max dates within group for proper timing
    3. Pre-ship-date: Strategy-based follow-ups (email only)
    4. Post-ship-date: Urgent follow-ups (email + call)
    5. Rich context for email drafting with item details

    Args:
        purchase_order: PurchaseOrder instance
        strategy: Follow-up strategy ("aggressive", "balanced", "conservative")
        initial_wait_hours: Hours to wait after OA before first follow-up

    Returns:
        Tuple of (needs_followup, reason_message, context_items)
        context_items: List of dicts with item details for email drafting
    """
    today = date.today()

    # Get unshipped items with ship dates using OAManager
    unshipped_items = (
        OrderAcknowledgement.objects.get_unshipped_oa_items_with_ship_dates(
            purchase_order
        )
    )

    if not unshipped_items:
        # This means either all items are shipped OR there are no ship dates
        # The caller should validate ship dates exist before calling this function
        return False, "All items have been shipped", []

    # Group items by ship date (within ±3 days)
    item_groups = group_items_by_ship_date(unshipped_items)

    # Check only the first group (earliest ship dates)
    first_group = item_groups[0]

    # Get min and max ship dates within the first group
    group_ship_dates = [
        promised_ship_date
        for oa_item, promised_ship_date, unshipped_quantity in first_group
    ]
    min_ship_date = min(group_ship_dates)
    max_ship_date = max(group_ship_dates)

    # Build rich context for all items in the first group
    context_items = []
    for oa_item, promised_ship_date, unshipped_quantity in first_group:
        item_context = {
            "oa_item_id": oa_item.id,
            "item_name": (
                oa_item.item.description
                if oa_item.item
                else oa_item.item_description or "Unknown Item"
            ),
            "promised_ship_date": promised_ship_date,
            "unshipped_quantity": unshipped_quantity,
            "total_quantity": oa_item.quantity,
            "shipped_quantity": oa_item.quantity - unshipped_quantity,
            "days_overdue": (today - promised_ship_date).days
            if promised_ship_date < today
            else None,
            "days_until_ship": (promised_ship_date - today).days
            if promised_ship_date >= today
            else None,
        }
        context_items.append(item_context)

    # Check if this group is overdue (max date has passed)
    if max_ship_date < today:
        # Post-ship-date: Create urgent follow-up (email + call)
        overdue_days = (today - max_ship_date).days
        reason = (
            f"Ship dates {min_ship_date} to {max_ship_date} have passed "
            f"({overdue_days} days overdue) - urgent follow-up needed"
        )

        # Add group-level context for task builder
        group_context = {
            "_group_context": {
                "is_overdue": True,
                "group_min_date": min_ship_date,
                "group_max_date": max_ship_date,
                "strategy": strategy,
                "overdue_days": overdue_days,
                "total_items": len(first_group),
            }
        }

        return True, reason, context_items + [group_context]

    # Group is future - check strategy timing using min date
    # Get OA received date for timing calculations
    latest_oa = (
        OrderAcknowledgement.objects.filter(purchase_order=purchase_order)
        .order_by("-created_at")
        .first()
    )
    if not latest_oa:
        return False, "No OA found for shipment follow-up", []

    # Get start date for this group (OA date for first group, latest shipment for subsequent)
    start_date = get_start_date_for_shipment_follow_up(purchase_order, latest_oa)

    # Calculate follow-up dates for this group using earliest ship date
    followup_dates = calculate_shipment_followup_dates(
        ship_date=min_ship_date,
        strategy=strategy,
        start_date=start_date,
        initial_wait_hours=initial_wait_hours,
        business_hours_only=business_hours_only,
        timezone=timezone,
    )

    # Check if today is a follow-up date for this group
    if today in followup_dates:
        # Pre-ship-date: Create scheduled follow-up (email only)
        days_until_ship = (min_ship_date - today).days
        reason = (
            f"Scheduled follow-up - {days_until_ship} days until earliest ship date"
        )

        # Add group-level context for task builder
        group_context = {
            "_group_context": {
                "is_overdue": False,
                "group_min_date": min_ship_date,
                "group_max_date": max_ship_date,
                "strategy": strategy,
                "days_until_ship": days_until_ship,
                "total_items": len(first_group),
            }
        }

        return True, reason, context_items + [group_context]

    return False, "No follow-up needed today", []


# =============================================================================
# TASK CREATION UTILITIES - Reusable across workflows
# =============================================================================


def build_base_context(
    purchase_order: PurchaseOrder,
    attempts_made: int,
    max_attempts: int,
    follow_up_type: str,
) -> Dict[str, Any]:
    """
    Build base context shared across all follow-up types.

    Args:
        purchase_order: PurchaseOrder instance
        attempts_made: Number of attempts already made
        max_attempts: Maximum attempts allowed
        follow_up_type: Type of follow-up

    Returns:
        dict: Base context with common fields
    """
    supplier = purchase_order.supplier
    if not supplier:
        raise ValueError("Purchase order must have a supplier")

    return {
        "supplier_name": supplier.name,
        "po_number": purchase_order.po_number,
        "follow_up_type": follow_up_type,
        "retry_info": {
            "attempts_made": attempts_made,
            "max_attempts": max_attempts,
            "is_final_attempt": attempts_made >= max_attempts - 1,
        },
        "po_details": {
            "placement_time": purchase_order.placement_time.strftime("%Y-%m-%d")
            if purchase_order.placement_time
            else "",
            "total_cost": purchase_order.total_cost or 0,
            "order_status": purchase_order.order_status,
        },
    }


def create_task_with_context(
    purchase_order: PurchaseOrder,
    actions: List[CreateTaskActionParams],
    enriched_context: Dict[str, Any],
) -> TaskCreationResult:
    """
    Create a task with enriched context - utility for any workflow.

    Args:
        purchase_order: PurchaseOrder instance
        actions: List of task actions
        enriched_context: Full context for the task

    Returns:
        TaskCreationResult: Result of task creation
    """
    from django.contrib.contenttypes.models import ContentType

    from didero.tasks.schemas import TaskContextPanelType, TaskType

    # Get user for task assignment
    from didero.tasks.utils import create_task_v2, get_task_assignment_user

    user = get_task_assignment_user(purchase_order)

    # Get content type for the model
    content_type = ContentType.objects.get_for_model(purchase_order)

    # Build task details from enriched context
    retry_info = enriched_context.get("retry_info", {})
    attempt_number = retry_info.get("attempts_made", 0) + 1
    max_attempts = retry_info.get("max_attempts", 1)
    is_final_attempt = retry_info.get("is_final_attempt", False)

    # Create human-readable details based on follow-up type and context
    follow_up_type = enriched_context.get("follow_up_type", "follow-up")

    # Create proper follow-up type display names
    follow_up_display_names = {
        "oa": "Order Acknowledgement",
        "ship_dates": "Ship Date Information",
        "shipment": "Shipment Status",
    }

    # Get supplier name and PO number for context
    supplier_name = enriched_context.get("supplier_name", "the supplier")
    po_number = enriched_context.get("po_number", "")

    # Build concise, action-oriented details
    if follow_up_type == "oa":
        task_details = f"Order acknowledgement has not been received from {supplier_name} for PO {po_number}."
    elif follow_up_type == "ship_dates":
        task_details = f"Ship dates were not provided by {supplier_name} in the order acknowledgement for PO {po_number}."
    elif follow_up_type == "shipment":
        shipment_context = enriched_context.get("shipment_context", "")
        if shipment_context:
            task_details = f"{shipment_context.rstrip('.')} for PO {po_number} from {supplier_name}."
        else:
            task_details = f"Shipment tracking has not been received from {supplier_name} for PO {po_number}."
    else:
        # Default for unknown follow-up types
        task_details = f"Follow-up required with {supplier_name} for PO {po_number}."

    # Add attempt information only if it's the final attempt
    if max_attempts > 1 and is_final_attempt:
        task_details += (
            f" This is the final attempt ({attempt_number} of {max_attempts})."
        )

    # Create follow-up task
    task = create_task_v2(
        task_type=TaskType.FOLLOW_UP,
        user=user,
        model_type=content_type,
        model_id=str(purchase_order.pk),
        task_type_params={
            "supplier_name": enriched_context.get("supplier_name"),
            "po_number": enriched_context.get("po_number"),
            "follow_up_type": follow_up_display_names.get(
                follow_up_type, follow_up_type.title()
            ),
            "details": task_details,
        },
        context_panels=[
            {
                "panel_type": TaskContextPanelType.PO_DETAILS,
                "param_values": {"purchaseOrderId": str(purchase_order.pk)},
            }
        ],
        actions=actions,
    )

    logger.info(
        "follow-up task created with enriched context",
        purchase_order_id=str(purchase_order.pk),
        follow_up_type=enriched_context.get("follow_up_type"),
        task_id=task.pk,
        context_keys=list(enriched_context.keys()),
    )

    return TaskCreationResult(
        success=True,
        task_id=str(task.pk),
        message=f"{enriched_context.get('follow_up_type')} follow-up task created successfully",
    )
