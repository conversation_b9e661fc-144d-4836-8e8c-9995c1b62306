"""
Condition checkers for follow-up workflow.

These checkers determine if follow-up actions are needed based on the current
state of purchase orders, order acknowledgements, and shipments.
"""

from typing import Any, <PERSON><PERSON>

import structlog
from asgiref.sync import sync_to_async

from didero.workflows.core_workflows.follow_up.utils import (
    check_shipment_follow_up_needed,
)

logger = structlog.get_logger(__name__)


class BaseConditionChecker:
    """Base class for condition checkers."""

    async def check(self, purchase_order_id: str) -> <PERSON>ple[bool, str, Any]:
        """
        Check if the follow-up condition is satisfied.

        Returns:
            Tuple of (condition_met: bool, reason: str, context: Any)
        """
        raise NotImplementedError


class OAConditionChecker(BaseConditionChecker):
    """Checks if Order Acknowledgement exists for ISSUED purchase orders."""

    async def check(self, purchase_order_id: str) -> Tuple[bool, str, Any]:
        from didero.orders.schemas import PurchaseOrderStatus
        from didero.workflows.core_workflows.follow_up.utils import (
            get_purchase_order_with_relations,
        )

        # Get the purchase order
        purchase_order = await sync_to_async(
            get_purchase_order_with_relations, thread_sensitive=True
        )(purchase_order_id)

        # Verify PO status is ISSUED
        if purchase_order.order_status != PurchaseOrderStatus.ISSUED.value:
            raise ValueError(
                f"OA follow-up can only be performed on ISSUED purchase orders. "
                f"Current status: {purchase_order.order_status}"
            )

        # Check if OA exists using OAManager
        from didero.orders.models import OrderAcknowledgement

        oa_exists = await sync_to_async(
            OrderAcknowledgement.objects.check_oa_exists_for_po, thread_sensitive=True
        )(purchase_order_id)

        if oa_exists:
            return True, "Order acknowledgement found", None
        else:
            return False, "Order acknowledgement missing", {"po_id": purchase_order_id}


class ShipDateConditionChecker(BaseConditionChecker):
    """Checks if ship dates are complete in OA for ISSUED or AWAITING_SHIPMENT purchase orders."""

    async def check(self, purchase_order_id: str) -> Tuple[bool, str, Any]:
        from didero.orders.schemas import PurchaseOrderStatus
        from didero.workflows.core_workflows.follow_up.utils import (
            get_purchase_order_with_relations,
        )

        # Get the purchase order
        purchase_order = await sync_to_async(
            get_purchase_order_with_relations, thread_sensitive=True
        )(purchase_order_id)

        # Verify PO status is ISSUED or AWAITING_SHIPMENT
        valid_statuses = [
            PurchaseOrderStatus.ISSUED.value,
            PurchaseOrderStatus.AWAITING_SHIPMENT.value,
        ]
        if purchase_order.order_status not in valid_statuses:
            raise ValueError(
                f"Ship date follow-up can only be performed on ISSUED or AWAITING_SHIPMENT purchase orders. "
                f"Current status: {purchase_order.order_status}"
            )

        # Check if OA exists using OAManager
        from didero.orders.models import OrderAcknowledgement

        oa_exists = await sync_to_async(
            OrderAcknowledgement.objects.check_oa_exists_for_po, thread_sensitive=True
        )(purchase_order_id)
        if not oa_exists:
            raise ValueError(
                "Ship date follow-up requires an Order Acknowledgement to exist. "
                "No OA found for this purchase order."
            )

        # Check if ship dates are complete using OAManager
        ship_dates_complete = await sync_to_async(
            OrderAcknowledgement.objects.check_oa_ship_dates_complete,
            thread_sensitive=True,
        )(purchase_order_id)

        if ship_dates_complete:
            return True, "All ship dates complete in order acknowledgement", None
        else:
            return (
                False,
                "Ship dates incomplete in order acknowledgement",
                {"po_id": purchase_order_id},
            )


class ShipmentConditionChecker(BaseConditionChecker):
    """Checks if shipment follow-up is needed with full strategy logic."""

    async def check(self, purchase_order_id: str) -> Tuple[bool, str, Any]:
        """
        Check if shipment follow-up is needed TODAY based on strategy and ship dates.

        Returns:
            Tuple of (condition_met: bool, reason: str, context: Any)
            - condition_met: True if no follow-up needed, False if follow-up needed today
            - reason: Description of why follow-up is/isn't needed
            - context: Dict with items and follow-up details for task creation
        """
        from didero.orders.schemas import PurchaseOrderStatus
        from didero.users.utils.team_setting_utils import (
            get_followup_config_for_supplier,
        )
        from didero.workflows.core_workflows.follow_up.utils import (
            get_purchase_order_with_relations,
        )

        # Get the purchase order
        purchase_order = await sync_to_async(
            get_purchase_order_with_relations, thread_sensitive=True
        )(purchase_order_id)

        # Verify PO status is AWAITING_SHIPMENT
        if purchase_order.order_status != PurchaseOrderStatus.AWAITING_SHIPMENT.value:
            raise ValueError(
                f"Shipment follow-up can only be performed on AWAITING_SHIPMENT purchase orders. "
                f"Current status: {purchase_order.order_status}"
            )

        # Check if ship dates are complete (prerequisite for shipment follow-up)
        from didero.orders.models import OrderAcknowledgement

        ship_dates_complete = await sync_to_async(
            OrderAcknowledgement.objects.check_oa_ship_dates_complete,
            thread_sensitive=True,
        )(purchase_order_id)

        if not ship_dates_complete:
            raise ValueError(
                f"Cannot perform shipment follow-up: Ship dates are not complete for PO {purchase_order.po_number}. "
                f"Ship dates must be obtained through ship date follow-up first."
            )

        # Get follow-up configuration
        followup_config = await sync_to_async(
            get_followup_config_for_supplier, thread_sensitive=True
        )(purchase_order.team, purchase_order.supplier)

        # Check if shipment follow-up is needed TODAY using full strategy logic
        needs_followup, reason, items = await sync_to_async(
            check_shipment_follow_up_needed, thread_sensitive=True
        )(
            purchase_order,
            followup_config.shipment_followup.date_based_followup_strategy,
            followup_config.shipment_followup.time_based_wait_hours,
        )

        logger.info(
            "shipment condition check complete with strategy",
            purchase_order_id=purchase_order_id,
            needs_followup=needs_followup,
            reason=reason,
            items_count=len(items),
            strategy=followup_config.shipment_followup.date_based_followup_strategy,
        )

        # Prepare context for task builder
        context = {
            "items": items,
            "strategy": followup_config.shipment_followup.date_based_followup_strategy,
            "po_id": purchase_order_id,
        }

        if needs_followup:
            return False, reason, context  # False = condition NOT met, need follow-up
        else:
            return True, reason, None  # True = condition met, no follow-up needed
