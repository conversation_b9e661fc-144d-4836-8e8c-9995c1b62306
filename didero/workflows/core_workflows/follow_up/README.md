# Follow-Up Workflow System

## Overview

The Follow-Up Workflow System provides intelligent, AI-powered follow-up communications for purchase orders. It orchestrates retry logic, condition checking, and task creation across three main follow-up types.

## Architecture

```mermaid
graph TB
    A[Temporal Workflow] -->|Executes| B[Follow-up Activities]
    B --> C{Condition Check}
    C -->|Not Met| D[Task Builder]
    C -->|Met| E[Success]
    D --> F[AI Email Generation]
    F --> G[Task Creation]
    
    H[Team Settings] -.->|Config| A
    I[Langfuse Prompts] -.->|Templates| F
```

## Workflow Flow

```mermaid
flowchart LR
    A[PO Created] --> B[Initial Wait]
    B --> C{Check Condition}
    C -->|Condition Met| D[Success]
    C -->|Not Met| E[Create Task]
    E --> F{Max Attempts?}
    F -->|No| G[Wait & Retry]
    F -->|Yes| H[Final Task]
    G --> C
```

## Follow-Up Types

### 1. **Order Acknowledgement (OA)**
- **Trigger**: PO status = ISSUED
- **Condition**: OA exists
- **Strategy**: Retry until OA received

### 2. **Ship Dates**  
- **Trigger**: PO status = ISSUED or AWAITING_SHIPMENT
- **Prerequisite**: OA must exist
- **Condition**: All OA items have ship dates
- **Strategy**: Retry until dates complete

### 3. **Shipment**
- **Trigger**: PO status = AWAITING_SHIPMENT
- **Prerequisite**: OA with ship dates
- **Condition**: Items shipped per strategy (time-based/date-based/hybrid)
- **Strategy**: Follow configured strategy

## Configuration

```yaml
oa_followup:
  enabled: true
  initial_wait_hours: 24
  max_attempts_before_task: 3
  repeat_every_hours: 48

shipment_followup:
  enabled: true
  time_based_wait_hours: 168
  max_attempts_before_task: 2
  repeat_every_hours: 72
  date_based_followup_strategy: "strict"  # strict|lenient|hybrid
```

## Adding New Follow-Up Types

### 1. Create Condition Checker
```python
class NewTypeConditionChecker(BaseConditionChecker):
    async def check(self, purchase_order_id: str) -> Tuple[bool, str, Any]:
        # Return (condition_met, reason, context)
        pass
```

### 2. Create Task Builder
```python
class NewTypeTaskBuilder(BaseTaskBuilder):
    def get_follow_up_type(self) -> str:
        return "new_type"
    
    def get_prompt_key(self) -> str:
        return "new_type_follow_up"
    
    async def _build_enriched_context(self, purchase_order, context, attempts, max_attempts):
        # Build context for AI
        pass
```

### 3. Add Activity
```python
@activity.defn
async def new_type_follow_up_activity(purchase_order_id: str, config: Any) -> ActivityResult:
    return await orchestrate_follow_up("new_type", purchase_order_id, config)
```

### 4. Register in Handler Registry
```python
FOLLOW_UP_REGISTRY = {
    "oa": (OAConditionChecker(), OATaskBuilder()),
    "ship_dates": (ShipDateConditionChecker(), ShipDateTaskBuilder()),
    "shipment": (ShipmentConditionChecker(), ShipmentTaskBuilder()),
    "new_type": (NewTypeConditionChecker(), NewTypeTaskBuilder()),  # Add here
}
```

### 5. Update Workflow
```python
# In workflow.py
if followup_config.new_type_followup.enabled:
    activities_to_run.append("new_type")

# Add activity execution
elif activity_name == "new_type":
    result = await workflow.execute_activity(
        new_type_follow_up_activity,
        schedule_to_close_timeout=calculate_timeout(config),
        args=[purchase_order_id, followup_config.new_type_followup],
    )
```

### 6. Add AI Prompt
```yaml
# In prompts_config.yaml
new_type_follow_up:
  name: new_type_follow_up
  tags: [email_generation, follow_up, new_type]
  system_prompt: |
    You are a procurement specialist...
  user_prompt: |
    Generate email for {{context}}
```

## Prerequisites & Assumptions

### Prerequisites (Will Fail Without These)

#### **All Follow-Up Types**
- ✅ Purchase Order must exist
- ✅ PO must have a supplier (`purchase_order.supplier` cannot be None)
- ❌ Team configuration must have all required fields - **TODO: Add validation**
- ❌ Supplier must have valid email address - **TODO: Add validation**

#### **OA Follow-Up**
- ✅ PO status must be `ISSUED`
- ✅ No existing OA (condition check)

#### **Ship Date Follow-Up**  
- ✅ PO status must be `ISSUED` or `AWAITING_SHIPMENT`
- ❌ **Order Acknowledgement MUST exist** - **TODO: Add prerequisite check**
- ❌ OA must have items (`orderacknowledgmentitem_set`) - **TODO: Add validation**
- ❌ OA items must have ship date fields - **TODO: Add structure validation**

#### **Shipment Follow-Up**
- ✅ PO status must be `AWAITING_SHIPMENT`
- ❌ **Order Acknowledgement MUST exist** - **TODO: Add prerequisite check**
- ❌ **OA items MUST have ship dates** - **TODO: Add validation**
- ❌ Ship dates must be valid dates (not null) - **TODO: Add validation**

### Runtime Assumptions

### ✅ **Validated**
- PO status validation in each condition checker
- Supplier existence check in task creation
- Database state re-validation on each retry

### ❌ **Not Validated (TODO)**
- **Team config completeness** - TODO: Schema validation for required fields
- **AI service availability** - TODO: Startup validation for OpenAI/Langfuse
- **Context serialization** - TODO: Handle non-serializable data gracefully
- **Email template variables** - TODO: Validate all required vars present

## Failure Scenarios & Mitigations

### ✅ **Mitigated**
1. **AI Generation Failures**
   - Mitigation: Fallback to template emails
   
2. **Missing Supplier Data**
   - Mitigation: Validation with placeholders

3. **PO Status Changes**
   - Mitigation: Re-validate on each retry

### ❌ **Not Mitigated (TODO)**
4. **Invalid Configuration**
   - Issue: Missing required fields cause crashes
   - TODO: Runtime config validation

6. **Temporal Timeouts**
   - Issue: Long retries exceed activity timeout
   - TODO: Dynamic timeout calculation

7. **Config Changes Mid-Flight**
   - Issue: Team changes settings during execution
   - TODO: Snapshot config at start

## Error Handling

```mermaid
graph TD
    A[Error] --> B{Type}
    B -->|AI Failure| C[Use Template]
    B -->|Missing Data| D[Use Placeholder]
    B -->|Invalid Status| E[Skip Activity]
    B -->|Network| F[Retry]
```

## Monitoring Points

- Activity success/failure rates
- AI generation performance
- Configuration validation errors
- Task creation success rates

## Development Guidelines

### Testing - TODO
- Unit test condition checkers independently
- Integration test full activity flows
- Test AI prompt compilation
### Debugging
- Follow Langfuse trace IDs for AI operations
- Check Temporal UI for activity history
- Use structured logging with consistent fields