"""
Follow-up Handler System for Temporal workflows.

This provides a clean way to handle different follow-up types while working
properly with Temporal's activity system.
"""

from typing import Any, Dict

import structlog

from didero.workflows.core_workflows.follow_up.condition_checkers import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ShipDateCond<PERSON><PERSON><PERSON><PERSON>,
    ShipmentConditionChecker,
)
from didero.workflows.core_workflows.follow_up.schemas import ActivityResult
from didero.workflows.core_workflows.follow_up.task_builders import (
    OATaskBuilder,
    ShipDateTaskBuilder,
    ShipmentTaskBuilder,
)

logger = structlog.get_logger(__name__)


# =============================================================================
# FOLLOW-UP HANDLER REGISTRY - Maps follow-up types to their handlers
# =============================================================================


class FollowUpHandlerRegistry:
    """Registry of all follow-up handlers."""

    _handlers = {
        "oa": {
            "condition_checker": OAConditionChecker(),
            "task_builder": OATaskBuilder(),
            "success_message": "Order acknowledgement found, no follow-up needed",
            "task_message": "Order acknowledgement not found, follow-up task created",
        },
        "ship_dates": {
            "condition_checker": ShipDateConditionChecker(),
            "task_builder": ShipDateTaskBuilder(),
            "success_message": "All ship dates found in order acknowledgement",
            "task_message": "Ship dates incomplete, follow-up task created",
        },
        "shipment": {
            "condition_checker": ShipmentConditionChecker(),
            "task_builder": ShipmentTaskBuilder(),
            "success_message": "Items have shipped according to schedule",
            "task_message": "Items have not shipped, follow-up task created",
        },
    }

    @classmethod
    def get_handler_config(cls, follow_up_type: str) -> Dict[str, Any]:
        """Get the handler configuration for a follow-up type."""
        if follow_up_type not in cls._handlers:
            raise ValueError(f"Unknown follow-up type: {follow_up_type}")
        return cls._handlers[follow_up_type]


# =============================================================================
# FOLLOW-UP HANDLER FUNCTION - Called by individual activities
# =============================================================================


async def attempt_follow_up(
    follow_up_type: str,
    purchase_order_id: str,
    attempts_made: int = 0,
    max_attempts: int = 3,
) -> ActivityResult:
    """
    Single follow-up attempt using the handler system.

    Called by orchestrate_follow_up for each retry attempt. Uses the registry
    to get the appropriate handler for the follow-up type.

    Args:
        follow_up_type: Type of follow-up ("oa", "ship_dates", "shipment")
        purchase_order_id: UUID of the purchase order
        attempts_made: Number of attempts already made
        max_attempts: Maximum attempts allowed

    Returns:
        ActivityResult: Result of the follow-up attempt
    """
    logger.info(
        "attempting follow-up",
        follow_up_type=follow_up_type,
        purchase_order_id=purchase_order_id,
        attempts_made=attempts_made,
        max_attempts=max_attempts,
    )

    # Get handler configuration for this follow-up type
    handler_config = FollowUpHandlerRegistry.get_handler_config(follow_up_type)

    condition_checker = handler_config["condition_checker"]
    task_builder = handler_config["task_builder"]

    # Step 1: Check if condition is already met
    condition_met, reason, context = await condition_checker.check(purchase_order_id)

    if condition_met:
        logger.info(
            "follow-up condition satisfied",
            follow_up_type=follow_up_type,
            purchase_order_id=purchase_order_id,
            reason=reason,
        )
        return ActivityResult(
            success=True,
            message=reason,
        )

    # Step 2: Condition not met, create task
    logger.info(
        "follow-up condition not met, creating task",
        follow_up_type=follow_up_type,
        purchase_order_id=purchase_order_id,
        reason=reason,
    )

    await task_builder.create_task(
        purchase_order_id, attempts_made, max_attempts, context
    )

    return ActivityResult(
        success=False,
        message=handler_config["task_message"],
    )
