from typing import Any

import structlog
from temporalio import activity

from didero.workflows.core_workflows.follow_up.schemas import (
    ActivityResult,
    FollowUpType,
)

# TODO: change type oa_config, ship_date_config, shipment_config from any to followup config
# It is a dict now because temporal serializes the config as a dict

logger = structlog.get_logger(__name__)

# Time multiplier for testing - set to 1 for fast testing (1 hour = 1 seconds)
# Set to 3600 for production (1 hour = 3600 seconds)
# TODO: Make this a BehaviouralConfig setting
HOURS_TO_SECONDS_MULTIPLIER = 3600


async def orchestrate_follow_up(
    follow_up_type: FollowUpType,
    purchase_order_id: str,
    config: Any,
) -> ActivityResult:
    """
    Orchestrate follow-up process including timing, condition checking, and task creation.

    Args:
        follow_up_type: Type of follow-up ("oa", "ship_dates", "shipment")
        purchase_order_id: UUID of the purchase order
        config: Team follow-up configuration object

    Returns:
        ActivityResult: success=True if condition met, False if max retries reached
    """
    import asyncio

    from didero.workflows.core_workflows.follow_up.follow_up_handlers import (
        attempt_follow_up,
    )

    # Determine config field names based on follow-up type
    if follow_up_type == FollowUpType.SHIPMENT:
        initial_wait_field = "time_based_wait_hours"
        repeat_field = "time_based_repeat_hours"
    else:
        initial_wait_field = "initial_wait_hours"
        repeat_field = "repeat_every_hours"

    # Validate required config fields
    required_fields = [
        initial_wait_field,
        "max_attempts_before_task",
        repeat_field,
    ]
    for field in required_fields:
        if not hasattr(config, field):
            raise ValueError(f"Team settings missing required field: {field}")

    initial_wait_hours = getattr(config, initial_wait_field)
    repeat_hours = getattr(config, repeat_field)

    logger.info(
        f"starting {follow_up_type} follow-up activity with retry logic",
        follow_up_type=follow_up_type,
        purchase_order_id=purchase_order_id,
        initial_wait_hours=initial_wait_hours,
        max_attempts=config.max_attempts_before_task,
    )

    # Initial wait before first attempt
    # Check if we need business hours calculation
    from didero.workflows.util.business_hours_utils import (
        calculate_business_aware_delay,
    )

    # Get business hours settings from config
    business_hours_only = False
    timezone = "America/New_York"

    # Check if config has global_config with the required fields
    if hasattr(config, "global_config") and config.global_config:
        global_config = config.global_config
        if hasattr(global_config, "business_hours_only"):
            business_hours_only = global_config.business_hours_only
        if hasattr(global_config, "timezone"):
            timezone = global_config.timezone

    # Calculate delay with business hours if enabled
    actual_wait_hours = calculate_business_aware_delay(
        initial_wait_hours, timezone=timezone, business_hours_only=business_hours_only
    )

    # Convert hours to seconds using the multiplier
    initial_wait_seconds = actual_wait_hours * HOURS_TO_SECONDS_MULTIPLIER

    logger.info(
        f"waiting {initial_wait_seconds} seconds before first {follow_up_type} attempt",
        business_hours_only=business_hours_only,
        timezone=timezone,
        original_hours=initial_wait_hours,
        actual_hours=actual_wait_hours,
    )
    await asyncio.sleep(initial_wait_seconds)

    # Retry loop
    result = ActivityResult(success=False, message="No attempts made")
    for attempt in range(config.max_attempts_before_task):
        logger.info(
            f"{follow_up_type} follow-up attempt",
            follow_up_type=follow_up_type,
            purchase_order_id=purchase_order_id,
            attempt=attempt + 1,
            max_attempts=config.max_attempts_before_task,
        )

        # Check condition and create task if needed
        result = await attempt_follow_up(
            follow_up_type, purchase_order_id, attempt, config.max_attempts_before_task
        )

        # If condition is met, stop retrying
        if result.success:
            logger.info(
                f"{follow_up_type} condition satisfied, stopping retries",
                follow_up_type=follow_up_type,
                purchase_order_id=purchase_order_id,
                attempts_made=attempt + 1,
            )
            return result

        # Wait before next retry (if not last attempt)
        if attempt < config.max_attempts_before_task - 1:
            # Calculate delay with business hours if enabled
            actual_repeat_hours = calculate_business_aware_delay(
                repeat_hours, timezone=timezone, business_hours_only=business_hours_only
            )

            # Convert hours to seconds using the multiplier
            wait_seconds = actual_repeat_hours * HOURS_TO_SECONDS_MULTIPLIER

            logger.info(
                f"waiting before next {follow_up_type} retry",
                follow_up_type=follow_up_type,
                purchase_order_id=purchase_order_id,
                wait_seconds=wait_seconds,
                remaining_attempts=config.max_attempts_before_task - attempt - 1,
                original_hours=repeat_hours,
                actual_hours=actual_repeat_hours,
            )
            await asyncio.sleep(wait_seconds)

    # Max retries reached - task was created on last attempt
    logger.info(
        f"{follow_up_type} follow-up max retries reached, task created",
        follow_up_type=follow_up_type,
        purchase_order_id=purchase_order_id,
        max_attempts=config.max_attempts_before_task,
    )

    return result  # success=False, task was created by attempt_follow_up


# =============================================================================
# INDEPENDENT FOLLOW-UP ACTIVITIES
# =============================================================================


@activity.defn
async def oa_follow_up_activity(
    purchase_order_id: str,
    oa_config: Any,
) -> ActivityResult:
    """
    Independent Order Acknowledgement follow-up activity with retry logic.

    This activity:
    1. Validates team configuration
    2. Waits initial period based on config
    3. Retries checking OA condition and creating tasks until condition met or max retries
    4. Returns success=True if OA exists, False if max retries reached with task created

    Args:
        purchase_order_id: UUID of the purchase order
        oa_config: Team OA follow-up configuration object (dict from Temporal)

    Returns:
        ActivityResult: success=True if OA exists, False if max retries reached
    """
    # Convert dict back to Pydantic object if needed
    if isinstance(oa_config, dict):
        from didero.users.utils.team_setting_utils import OAFollowupConfig

        oa_config = OAFollowupConfig(**oa_config)

    return await orchestrate_follow_up(FollowUpType.OA, purchase_order_id, oa_config)


@activity.defn
async def ship_date_follow_up_activity(
    purchase_order_id: str,
    ship_date_config: Any,
) -> ActivityResult:
    """
    Independent ship date follow-up activity with retry logic.

    This activity:
    1. Waits for initial period before checking
    2. Checks if OA exists and has complete ship dates
    3. If not, creates a ship date follow-up task
    4. Retries based on configuration
    5. Returns success=True if ship dates complete, False if max retries reached

    Args:
        purchase_order_id: UUID of the purchase order
        ship_date_config: Team ship date follow-up configuration object (dict from Temporal)

    Returns:
        ActivityResult: success=True if ship dates complete, False if max retries reached
    """
    # Convert dict back to Pydantic object if needed
    if isinstance(ship_date_config, dict):
        from didero.users.utils.team_setting_utils import OAFollowupConfig

        ship_date_config = OAFollowupConfig(**ship_date_config)

    return await orchestrate_follow_up(
        FollowUpType.SHIP_DATES, purchase_order_id, ship_date_config
    )


@activity.defn
async def shipment_follow_up_activity(
    purchase_order_id: str,
    shipment_config: Any,
) -> ActivityResult:
    """
    Independent shipment follow-up activity with retry logic.

    This activity:
    1. Waits for initial period before checking
    2. Checks if items have shipped according to strategy and ship dates
    3. If not, creates a shipment follow-up task (email or email+call based on context)
    4. Retries based on configuration and follow-up schedule
    5. Returns success=True if shipment complete, False if max retries reached

    Args:
        purchase_order_id: UUID of the purchase order
        shipment_config: Team shipment follow-up configuration object (dict from Temporal)

    Returns:
        ActivityResult: success=True if shipment complete, False if max retries reached
    """
    # Convert dict back to Pydantic object if needed
    if isinstance(shipment_config, dict):
        from didero.users.utils.team_setting_utils import ShipmentFollowupConfig

        shipment_config = ShipmentFollowupConfig(**shipment_config)

    return await orchestrate_follow_up(
        FollowUpType.SHIPMENT, purchase_order_id, shipment_config
    )
