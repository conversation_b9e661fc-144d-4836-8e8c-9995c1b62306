# This is the definition for the purchase order follow-up workflow.
# Use Django Shell to create the workflow for a team.

from didero.workflows.schemas import (
    WorkflowTrigger,
    WorkflowType,
)

workflow_type = WorkflowType.PURCHASE_ORDER_FOLLOW_UP
trigger = WorkflowTrigger.ON_PURCHASE_ORDER_FOLLOW_UP


# Import this function in the Django shell to create the workflow for a team.
def create_purchase_order_follow_up_workflow_for_team(team_id: str):
    from didero.workflows.utils import create_or_update_core_workflow

    """Create a core PO Follow-up workflow for a team.

    Args:
        team_id: The team ID
        
    Note: Follow-up workflow uses team settings directly, no separate config class needed.
    """

    # Follow-up workflow doesn't have a separate config class - it uses team settings
    return create_or_update_core_workflow(workflow_type, trigger, team_id, config=None)
