from dataclasses import dataclass
from datetime import timed<PERSON><PERSON>
from typing import Any, Dict, List, Optional

from temporalio import workflow
from temporalio.common import RetryPolicy
from temporalio.exceptions import ApplicationError

from didero.workflows.retry_utils import get_retry_policy

# Use unsafe imports for activity functions and Django models
with workflow.unsafe.imports_passed_through():
    from didero.ai.purchase_order.schemas import PurchaseOrderDetails
    from didero.documents.schemas import DocumentType
    from didero.invoices.schemas import Invoice as InvoiceSchema
    from didero.invoices.schemas import InvoiceStatus
    from didero.workflows.core_workflows.activities import load_workflow_config
    from didero.workflows.core_workflows.invoice_processing.activities import (
        store_invoice_activity,
        update_invoice_po_link_activity,
    )
    from didero.workflows.core_workflows.invoice_processing.schemas import (
        InvoiceProcessingBehaviorConfig,
    )
    from didero.workflows.shared_activities.document_matching import (
        create_document_match_review_task_activity,
        match_invoice_to_po_activity,
    )
    from didero.workflows.shared_activities.document_retrieval import (
        RetrievalStatus,
        retrieve_document_activity,
    )
    from didero.workflows.shared_activities.invoice_operations import (
        extract_invoice_details,
    )
    from didero.workflows.shared_activities.purchase_order_operations import (
        get_purchase_order_details_activity,
        get_purchase_order_id_from_po_number_activity,
    )
    from didero.workflows.shared_activities.schemas import (
        DocumentInfo,
        DocumentMatchContext,
        MatchingResult,
        TaskAssignment,
        TaskMetadata,
    )


@dataclass
class InvoiceExtractionResult:
    """Result from invoice extraction"""

    success: bool
    invoice_data: Optional[InvoiceSchema] = None
    error_message: Optional[str] = None


@dataclass
class DocumentMatchingResult:
    """Result from document matching/validation"""

    success: bool
    match_result: Optional[str] = None
    matching_score: float = 0.0
    summary: str = ""
    header_comparison: Optional[Dict[str, Any]] = None
    line_items_comparison: Optional[List[Dict[str, Any]]] = None
    error_message: Optional[str] = None

    @property
    def exact_match(self) -> bool:
        return self.match_result == "exact_match"

    @property
    def within_tolerance(self) -> bool:
        return self.match_result == "within_tolerance"

    @property
    def has_critical_issues(self) -> bool:
        return self.match_result == "critical_issues"

    @property
    def acceptable(self) -> bool:
        return self.match_result in ["exact_match", "within_tolerance"]

    @property
    def requires_manual_review(self) -> bool:
        return self.match_result == "critical_issues"


@dataclass
class InvoiceStorageResult:
    """Result from invoice storage"""

    success: bool
    invoice_id: Optional[str] = None
    error_message: Optional[str] = None


@dataclass
class InvoiceProcessingParams:
    email_id: str
    team_id: str
    workflow_id: str  # The UserWorkflow ID for loading configuration


@workflow.defn
class InvoiceProcessingWorkflow:
    """Core Invoice Processing workflow implementation with behavior configuration"""

    def __init__(self):
        # Signal state management
        self.document_received = False
        self.document_data: Optional[Dict[str, Any]] = None  # Basic PO info from signal
        self.document_error: Optional[str] = None

    @workflow.signal
    def document_uploaded_signal(self, document: Dict[str, Any]):
        """Document successfully uploaded and processed"""
        self.document_received = True
        self.document_data = document
        workflow.logger.info("Received document upload signal")

    @workflow.signal
    def document_upload_failed_signal(self, error_message: str):
        """Document upload/processing failed"""
        self.document_error = error_message
        workflow.logger.info(
            f"Received document upload failure signal: {error_message}"
        )

    @workflow.run
    async def run(
        self,
        workflow_id: str,
        params: InvoiceProcessingParams,
    ) -> Dict[str, Any]:
        """
        Main invoice processing workflow execution

        Args:
            workflow_id: The UserWorkflow ID for loading configuration
            params: InvoiceProcessingParams containing email_id and team_id

        Returns:
            Dict with success status and processing details
        """
        # Extract params
        email_id = params.email_id
        team_id_str = params.team_id
        team_id = int(team_id_str)  # Convert to int for activities

        self.logger = workflow.logger
        self.supplier_info = {}  # Initialize supplier info
        self.email_id = email_id  # Store email_id for later use
        self.workflow_id = workflow_id

        self.logger.info("Invoice processing workflow started")
        self.logger.info(f"Processing email {email_id}, team_id={team_id}")

        # Load workflow configuration from database
        self.config = await self._load_workflow_config(workflow_id)

        self.logger.info(
            f"Workflow config loaded: manual_validation={self.config.require_manual_validation}, "
            f"auto_approve_threshold={self.config.auto_approve_threshold}"
        )

        # Initialize metrics
        self._init_metrics()

        # Check if workflow is enabled
        if not self.config.enabled:
            return self._create_error_response(
                "Invoice processing workflow is disabled for this team"
            )

        try:
            # Step 1: Extract invoice details from email
            extraction_result = await self.extract_invoice_details(email_id)

            if not extraction_result.success or not extraction_result.invoice_data:
                return self._create_error_response(
                    f"Failed to extract invoice details: {extraction_result.error_message}"
                )

            # Type narrowing: after this check, invoice_data is guaranteed to be not None
            invoice_data = extraction_result.invoice_data

            # Debug: Log the extracted invoice data to check for missing fields
            self.logger.info(
                f"Extracted invoice data: invoice_number={getattr(invoice_data, 'invoice_number', 'NOT_SET')}"
            )

            # Step 2: Store invoice record first (so we can use it as context for document retrieval)
            storage_result = await self.store_invoice(
                invoice_data,
                None,  # No document_id yet since we haven't retrieved the PO
                team_id,
            )

            if not storage_result.success or not storage_result.invoice_id:
                return self._create_error_response(
                    f"Failed to store invoice: {storage_result.error_message}"
                )

            # Type narrowing for invoice ID
            invoice_id = storage_result.invoice_id

            # Step 3: Retrieve matching document (based on config) using invoice as context
            # For now we only support PO matching, but this is extensible
            document_type = self.config.match_against_document_type

            if document_type != DocumentType.PO:
                return self._create_error_response(
                    f"Document type {document_type.value} not yet supported"
                )

            # Extract PO number from invoice (may be None)
            po_number = invoice_data.po_number if invoice_data else None

            # Use generic document retrieval activity with invoice context for rich task display
            retrieval_result = await workflow.execute_activity(
                retrieve_document_activity,
                args=[
                    team_id,  # team_id
                    "purchase_order",  # document_type
                    {  # search_criteria
                        "po_number": po_number
                        or "unknown",  # Pass unknown if no PO number
                        "supplier_name": self.supplier_info.get("supplier_name", ""),
                        "invoice_number": invoice_data.invoice_number,  # For context
                    },
                    "invoice_processing",  # requesting_workflow
                    "invoice",  # context_object_type (use invoice for rich frontend display)
                    invoice_id,  # context_object_id (invoice ID for context linking)
                    email_id,  # email_id (for audit trail)
                    workflow.info().workflow_id,  # Pass current workflow ID for signals
                    {  # behavior_config
                        "assigned_user_ids": self.config.assigned_user_ids,
                    },
                ],
                start_to_close_timeout=timedelta(
                    minutes=5
                ),  # Increased timeout for task creation
                retry_policy=self._get_retry_policy(),
            )

            self.logger.info(
                f"Document retrieval completed: status={retrieval_result.status}, "
                f"source={retrieval_result.source}"
            )

            # Initialize variables that will be used later
            document_details = None
            document_id = None

            # Handle retrieval results
            if retrieval_result.status == RetrievalStatus.FOUND:
                # Document found - we now get PurchaseOrderDetails directly with strict typing
                document_details = retrieval_result.document
                if not isinstance(document_details, PurchaseOrderDetails):
                    raise ValueError(
                        f"Expected PurchaseOrderDetails, got {type(document_details)}"
                    )

                # Get the document database ID
                document_id = await self._get_document_id_from_details(
                    document_details, team_id
                )

                # Update the stored invoice to link it to the found PO
                await self.update_invoice_po_link(invoice_id, document_id, team_id)

            elif retrieval_result.status == RetrievalStatus.UPLOAD_TASK_CREATED:
                # Upload task created successfully - wait for signal
                self.logger.info(
                    f"Upload task created (task_id: {retrieval_result.task_id}), waiting for document upload signal"
                )

                # Wait for either document upload success or failure signal
                await workflow.wait_condition(
                    lambda: self.document_received or self.document_error is not None,
                    timeout=timedelta(days=7),
                )

                if self.document_error:
                    # Document upload failed
                    return self._create_error_response(
                        f"Document upload failed: {self.document_error}",
                        retrieval_result.task_id,
                        invoice_id,
                    )
                elif self.document_received:
                    # Document uploaded successfully - continue processing
                    po_basic_info = self.document_data

                    # Signal data contains basic PO info - use po_id for efficient retrieval
                    try:
                        if isinstance(po_basic_info, dict):
                            po_number = po_basic_info.get("po_number")
                            po_id = po_basic_info.get("po_id")

                            if not po_id or not po_number:
                                raise ValueError(
                                    f"Missing required PO info: po_id={po_id}, po_number={po_number}"
                                )

                            # Fetch complete PO details using PO ID (more efficient) - returns PurchaseOrderDetails schema
                            # Activity will throw ApplicationError if PO not found
                            po_details_data = await workflow.execute_activity(
                                get_purchase_order_details_activity,
                                args=[str(po_id)],  # Use PO ID for efficient lookup
                                start_to_close_timeout=timedelta(minutes=1),
                                retry_policy=self._get_retry_policy(),
                            )

                            # Create PurchaseOrderDetails from the returned data
                            document_details = PurchaseOrderDetails.model_validate(
                                po_details_data
                            )
                            document_id = int(po_id)

                            self.logger.info(
                                "Fetched complete PO details from database",
                                po_number=po_number,
                                po_id=document_id,
                            )
                        else:
                            raise ValueError(
                                f"Unexpected signal data type: {type(po_basic_info)}"
                            )

                    except Exception as e:
                        self.logger.error(f"Failed to process signal data: {str(e)}")
                        return self._create_error_response(
                            f"Failed to parse signal data: {str(e)}",
                            retrieval_result.task_id,
                            invoice_id,
                        )

                    # Validate that we have the document_id and document_details
                    if not document_id or not document_details:
                        return self._create_error_response(
                            "PO creation workflow completed but PO details not available",
                            retrieval_result.task_id,
                            invoice_id,
                        )

                    # Update the stored invoice to link it to the uploaded PO
                    await self.update_invoice_po_link(invoice_id, document_id, team_id)

                    self.logger.info(
                        "Document uploaded and processed via signal, continuing workflow"
                    )
                else:
                    # Timeout reached without signal
                    return self._create_error_response(
                        "Document upload timeout - no document uploaded within 7 days",
                        retrieval_result.task_id,
                        invoice_id,
                    )

            else:
                # Error occurred
                return self._create_error_response(
                    f"Document retrieval failed: {retrieval_result.message}"
                )

            # Ensure we have the required data for validation
            if not document_details or not document_id:
                return self._create_error_response(
                    "Missing document details or document ID for validation"
                )

            # Step 4: Validate invoice against document (both as schemas)
            validation_result = await self.validate_invoice_against_document(
                invoice_data,  # Invoice schema
                document_details,  # Document schema (PO, etc.)
                team_id,  # Pass as int for activities
            )

            if not validation_result.success:
                return self._create_error_response(
                    f"Validation failed: {validation_result.error_message}"
                )

            # Step 5: Handle validation results and create tasks
            result = await self._handle_validation_results(
                validation_result,
                invoice_id,
                document_details,
                document_id,
                team_id,
                invoice_data,
            )

            # Record success metrics
            duration = (workflow.now() - workflow.info().start_time).total_seconds()
            self.invoice_processed_counter.add(
                1,
                {
                    "team_id": team_id,
                    "match_result": validation_result.match_result or "unknown",
                    "matching_score": str(validation_result.matching_score),
                },
            )
            self.invoice_processing_duration.record(
                int(duration * 1000), {"team_id": team_id, "success": "true"}
            )

            return {
                "success": True,
                "invoice_id": invoice_id,
                "match_result": validation_result.match_result,
                "matching_score": validation_result.matching_score,
                "summary": validation_result.summary,
                "header_comparison": validation_result.header_comparison or {},
                "line_items_comparison": validation_result.line_items_comparison or [],
                **result,
            }

        except ApplicationError as e:
            # Record failure metrics for non-retryable business logic errors
            duration = (workflow.now() - workflow.info().start_time).total_seconds()
            self.invoice_processing_duration.record(
                int(duration * 1000),
                {
                    "team_id": team_id,
                    "success": "false",
                    "error_type": type(e).__name__,
                },
            )

            self.logger.error(
                f"Invoice processing workflow failed with business logic error: {str(e)}"
            )
            # Re-raise to fail the workflow properly
            raise

        except Exception as e:
            # Record failure metrics for unexpected errors
            duration = (workflow.now() - workflow.info().start_time).total_seconds()
            self.invoice_processing_duration.record(
                int(duration * 1000),
                {
                    "team_id": team_id,
                    "success": "false",
                    "error_type": type(e).__name__,
                },
            )

            self.logger.error(
                f"Invoice processing workflow failed with unexpected error: {str(e)}"
            )
            # Re-raise to let Temporal handle retries for transient errors
            raise

    async def _load_workflow_config(
        self, workflow_id: str
    ) -> InvoiceProcessingBehaviorConfig:
        """Load workflow configuration from database"""
        try:
            self.logger.info(f"Loading config for workflow {workflow_id}")

            # Use activity to load config from database
            config_dict = await workflow.execute_activity(
                load_workflow_config,
                args=[workflow_id, "invoice_processing"],
                start_to_close_timeout=timedelta(seconds=30),
                retry_policy=self._get_retry_policy(use_defaults=True),
            )

            if config_dict:
                # Create Pydantic config from loaded dict
                config = InvoiceProcessingBehaviorConfig(**config_dict)
                return config
            else:
                self.logger.warning(
                    f"No config found for workflow {workflow_id}, using defaults"
                )
                default_config = InvoiceProcessingBehaviorConfig()
                return default_config

        except Exception as e:
            self.logger.error(f"Config load failed: {str(e)}")
            default_config = InvoiceProcessingBehaviorConfig()
            return default_config

    def _init_metrics(self):
        """Initialize workflow-specific metrics"""
        meter = workflow.metric_meter()
        self.invoice_processed_counter = meter.create_counter(
            "invoice_processed_total",
            description="Total number of invoices processed",
            unit="1",
        )
        self.validation_failures = meter.create_counter(
            "invoice_validation_failures_total",
            description="Number of invoice validation failures",
            unit="1",
        )
        self.invoice_processing_duration = meter.create_histogram(
            "invoice_processing_duration_seconds",
            description="Time taken to process invoice",
            unit="s",
        )

    async def extract_invoice_details(self, email_id: str) -> InvoiceExtractionResult:
        """Extract invoice details from email using AI"""
        try:
            # Use existing activity for invoice extraction
            from didero.workflows.core_workflows.invoice_processing.schemas import (
                ExtractInvoiceDetailsParams,
            )

            params = ExtractInvoiceDetailsParams(email_id=email_id)

            error, result = await workflow.execute_activity(
                extract_invoice_details,
                args=[params],
                start_to_close_timeout=timedelta(minutes=5),
                retry_policy=self._get_retry_policy(),
            )

            if error:
                return InvoiceExtractionResult(
                    success=False,
                    error_message=f"Extraction failed: {error}",
                )

            if result and "invoice" in result:
                # Convert dict back to InvoiceSchema if needed
                invoice_data = result["invoice"]
                if isinstance(invoice_data, dict):
                    # Temporal serialized the Pydantic model to dict, convert it back
                    from didero.invoices.schemas import Invoice as InvoiceSchema

                    invoice_data = InvoiceSchema.model_validate(invoice_data)

                # Store supplier info for later use
                self.supplier_info = result.get("supplier_info", {})

                return InvoiceExtractionResult(
                    success=True,
                    invoice_data=invoice_data,
                )

            return InvoiceExtractionResult(
                success=False,
                error_message="No invoice data extracted",
            )

        except Exception as e:
            self.logger.error(f"Failed to extract invoice details: {str(e)}")
            return InvoiceExtractionResult(success=False, error_message=str(e))

    async def validate_invoice_against_document(
        self, invoice_data: InvoiceSchema, document_data: Any, team_id: int
    ) -> DocumentMatchingResult:
        """Validate invoice against document using document matching"""
        try:
            if not self.config.validate_invoice_amounts:
                # Skip validation if disabled - assume exact match
                return DocumentMatchingResult(
                    success=True,
                    match_result="exact_match",
                    matching_score=1.0,
                    summary="Validation skipped - assumed exact match",
                    header_comparison={},
                    line_items_comparison=[],
                )

            # Convert tolerance config to dict for activity
            tolerance_config_dict = {
                "type": self.config.tolerance_config.type.value,
                "value": self.config.tolerance_config.value,  # Now float, JSON serializable
                "currency": self.config.tolerance_config.currency,
            }

            # Use the appropriate document matching activity based on document type
            document_type = self.config.match_against_document_type

            if document_type == DocumentType.PO:
                # Use Temporal's built-in retry for document matching
                comparison_result = await workflow.execute_activity(
                    match_invoice_to_po_activity,
                    args=[
                        invoice_data,  # Invoice schema
                        document_data,  # Document schema (PO, etc.)
                        team_id,
                        tolerance_config_dict,
                    ],
                    start_to_close_timeout=timedelta(minutes=3),
                    retry_policy=self._get_retry_policy(),
                )

                if not comparison_result.get("success", False):
                    return DocumentMatchingResult(
                        success=False,
                        error_message=comparison_result.get(
                            "error", "Document matching failed"
                        ),
                    )

                self.logger.info("Document matching completed successfully")

                return DocumentMatchingResult(
                    success=True,
                    match_result=comparison_result.get("match_result"),
                    matching_score=comparison_result.get("matching_score", 0.0),
                    summary=comparison_result.get("summary", ""),
                    header_comparison=comparison_result.get("header_comparison", {}),
                    line_items_comparison=comparison_result.get(
                        "line_items_comparison", []
                    ),
                )

            else:
                return DocumentMatchingResult(
                    success=False,
                    error_message=f"Document matching for {document_type.value} not yet implemented",
                )

        except Exception as e:
            self.logger.error(f"Failed to validate invoice: {str(e)}")
            return DocumentMatchingResult(success=False, error_message=str(e))

    async def store_invoice(
        self,
        invoice_data: InvoiceSchema,
        document_id: Optional[int],
        team_id: int,
    ) -> InvoiceStorageResult:
        """Store invoice record in database using activity"""
        try:
            # Convert complete invoice schema to dict for activity
            invoice_dict = invoice_data.model_dump()

            # Use the activity to store invoice
            result = await workflow.execute_activity(
                store_invoice_activity,
                args=[
                    invoice_dict,
                    document_id,  # This is purchase_order_id (can be None)
                    self.email_id,  # email_id from workflow
                    team_id,
                ],
                start_to_close_timeout=timedelta(minutes=2),
                retry_policy=self._get_retry_policy(),
            )

            if not result.get("success", False):
                return InvoiceStorageResult(
                    success=False,
                    error_message=result.get("error_message", "Unknown error"),
                )

            return InvoiceStorageResult(
                success=True,
                invoice_id=result["invoice_id"],
            )

        except Exception as e:
            self.logger.error(f"Failed to store invoice: {str(e)}")
            return InvoiceStorageResult(success=False, error_message=str(e))

    async def update_invoice_po_link(
        self,
        invoice_id: str,
        purchase_order_id: int,
        team_id: int,
    ) -> None:
        """Update an existing invoice to link it to a purchase order"""
        try:
            self.logger.info(
                f"Updating invoice {invoice_id} to link with PO {purchase_order_id}"
            )

            # Create a simple activity to update the PO link directly
            await workflow.execute_activity(
                update_invoice_po_link_activity,
                args=[
                    invoice_id,
                    purchase_order_id,
                    team_id,
                ],
                start_to_close_timeout=timedelta(minutes=1),
                retry_policy=self._get_retry_policy(),
            )

            self.logger.info(
                f"Successfully linked invoice {invoice_id} to PO {purchase_order_id}"
            )

        except Exception as e:
            self.logger.error(f"Failed to update invoice PO link: {str(e)}")
            # Don't raise - this is not critical enough to fail the workflow

    async def _handle_validation_results(
        self,
        validation_result: DocumentMatchingResult,
        invoice_id: str,
        document_details: Any,
        document_id: int,
        team_id: int,
        invoice_data: InvoiceSchema,
    ) -> Dict[str, Any]:
        """Handle validation results and create appropriate tasks with matching score-weighted decisions"""
        result = {}

        self.logger.info(
            f"Processing invoice {invoice_id}: {validation_result.match_result} (score: {validation_result.matching_score:.2f})"
        )

        # Use matching score-weighted decision making
        should_auto_approve = self._should_auto_approve(validation_result)

        # Simplified task creation logic:
        # Auto-approve ONLY when: manual validation OFF + high matching score + acceptable match result
        # Create task in ALL other cases: manual validation ON, OR low matching score, OR critical issues, OR document not found

        task_created = False

        if validation_result.match_result in ["exact_match", "within_tolerance"]:
            if self.config.require_manual_validation:
                # Human validation required - always create task regardless of matching score
                self.logger.info(
                    f"Creating task for manual validation (score: {validation_result.matching_score:.2f})"
                )
                result["requires_validation"] = True
                result["status"] = InvoiceStatus.AWAITING_DOCUMENT.value
                result["reason"] = "Manual validation required by policy"

                await self._create_document_review_task(
                    validation_result,
                    invoice_id,
                    document_details,
                    document_id,
                    team_id,
                    review_reason="Manual validation required by policy",
                    invoice_data=invoice_data,
                )
                task_created = True

            elif should_auto_approve:
                # No manual validation required + high matching score = auto-approve
                matching_score_threshold = self.config.auto_approve_threshold or 0.9
                self.logger.info(
                    f"Auto-approving invoice: {validation_result.match_result} (score: {validation_result.matching_score:.2f} >= {matching_score_threshold})"
                )
                result["auto_approved"] = True
                result["status"] = InvoiceStatus.MATCHED.value

            else:
                # No manual validation required + low matching score = always create task
                matching_score_threshold = self.config.auto_approve_threshold or 0.9
                self.logger.info(
                    f"Low matching score: {validation_result.matching_score:.2f} < {matching_score_threshold}"
                )

                # Always create task for low matching score
                self.logger.info(
                    f"Creating task for low matching score: {validation_result.matching_score:.2f} < {matching_score_threshold}"
                )
                result["requires_validation"] = True
                result["status"] = InvoiceStatus.AWAITING_DOCUMENT.value
                result["reason"] = (
                    f"Low AI matching score ({validation_result.matching_score:.1%}) below threshold ({matching_score_threshold:.1%})"
                )

                await self._create_document_review_task(
                    validation_result,
                    invoice_id,
                    document_details,
                    document_id,
                    team_id,
                    review_reason=f"Low AI matching score ({validation_result.matching_score:.1%}) requires manual verification",
                    invoice_data=invoice_data,
                )
                task_created = True

        else:  # critical_issues
            self.logger.info(
                f"Critical issues found (score: {validation_result.matching_score:.2f})"
            )
            result["requires_validation"] = True
            result["mismatch_found"] = True
            result["status"] = InvoiceStatus.MISMATCH.value

            # Always create task for critical issues
            self.logger.info("Creating task due to critical issues")
            await self._create_document_review_task(
                validation_result,
                invoice_id,
                document_details,
                document_id,
                team_id,
                review_reason="Critical issues found requiring manual review",
                invoice_data=invoice_data,
            )
            task_created = True

        # Log final decision
        if task_created:
            self.logger.info("Task created for manual review")
        else:
            self.logger.info("Invoice auto-approved")

        # Include matching score and match result info
        result["matching_score"] = validation_result.matching_score
        result["match_result"] = validation_result.match_result
        result["header_comparison"] = validation_result.header_comparison
        result["line_items_comparison"] = validation_result.line_items_comparison

        return result

    def _get_matching_threshold(self) -> float:
        """Get the configured matching score threshold"""
        return self.config.auto_approve_threshold or 0.9

    def _create_error_response(
        self,
        error: str,
        task_id: Optional[str] = None,
        invoice_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Create standardized error response"""
        response = {"success": False, "error": error}
        if task_id:
            response["task_id"] = task_id
        if invoice_id:
            response["invoice_id"] = invoice_id
        return response

    async def _get_document_id_from_details(
        self, document_details: Any, team_id: int
    ) -> int:
        """Get document ID from document details"""
        if isinstance(document_details, PurchaseOrderDetails):
            # For PO details, look up the database ID
            document_id = await workflow.execute_activity(
                get_purchase_order_id_from_po_number_activity,
                args=[document_details.po_number, team_id],
                start_to_close_timeout=timedelta(minutes=1),
                retry_policy=self._get_retry_policy(),
            )
            if document_id is None:
                raise ValueError(
                    f"PO not found in database: {document_details.po_number}"
                )
            return document_id
        else:
            raise ValueError(f"Unsupported document type: {type(document_details)}")

    def _should_auto_approve(self, validation_result: DocumentMatchingResult) -> bool:
        """Determine if invoice should be auto-approved based on match result and matching score"""

        # Manual validation overrides all auto-approval
        if self.config.require_manual_validation:
            return False

        # Critical issues never auto-approve
        if validation_result.match_result == "critical_issues":
            return False

        # Check matching score threshold
        if validation_result.matching_score < self._get_matching_threshold():
            return False

        # Exact matches and within tolerance can auto-approve if matching score is high enough
        return validation_result.match_result in ["exact_match", "within_tolerance"]

    async def _create_document_review_task(
        self,
        validation_result: DocumentMatchingResult,
        invoice_id: str,
        document_details: Any,
        document_id: int,
        team_id: int,
        review_reason: str,
        invoice_data: InvoiceSchema,
    ) -> None:
        """Create a document review task using the task creation activity"""
        try:
            self.logger.info(f"Creating task for invoice {invoice_id}: {review_reason}")

            # Extract document information
            document_type = self.config.match_against_document_type
            document_reference = self._get_document_reference_from_details(
                document_details, document_type
            )

            # Extract supplier name with cascading fallback
            supplier_name = (
                self.supplier_info.get(
                    "supplier_name"
                )  # From communication/invoice extraction
                or getattr(document_details, "supplier_name", None)  # From PO details
                or self._get_supplier_from_po_database(document_id)  # From database PO
                or "Unknown Supplier"  # Final fallback
            )
            total_amount = getattr(document_details, "total_amount", None) or "0.00"

            # Use the document_id that was passed from the retrieval result
            document2_id = str(document_id)

            # Prepare document data
            document2_data = (
                document_details.model_dump()
                if hasattr(document_details, "model_dump")
                else getattr(document_details, "__dict__", {})
            )

            # Construct parameter objects for the new activity
            context = DocumentMatchContext(
                model_id=invoice_id,
                model_type_name="Invoice",
                team_id=team_id,
                document1=DocumentInfo(
                    doc_type="invoice",
                    reference=f"INV-{invoice_id}",
                    doc_id=invoice_id,
                    data=invoice_data.model_dump(),
                ),
                document2=DocumentInfo(
                    doc_type=document_type.value,
                    reference=document_reference,
                    doc_id=document2_id,
                    data=document2_data,
                ),
            )

            matching_result = MatchingResult(
                match_result=validation_result.match_result or "unknown",
                matching_score=validation_result.matching_score,
                review_reason=review_reason,
                comparison_summary=validation_result.summary,
                comparison_data={
                    "header_comparison": validation_result.header_comparison or {},
                    "line_items_comparison": validation_result.line_items_comparison
                    or [],
                },
            )

            metadata = TaskMetadata(
                supplier_name=supplier_name,
                total_amount=str(total_amount),
            )

            assignment = TaskAssignment(
                user_ids=self.config.assigned_user_ids,
                user_group_names=self.config.assigned_user_group_names,
            )

            # Call the task creation activity with cleaner parameters
            task_result = await workflow.execute_activity(
                create_document_match_review_task_activity,
                args=[context, matching_result, metadata, assignment, "INVOICE"],
                start_to_close_timeout=timedelta(minutes=2),
                retry_policy=self._get_retry_policy(),
            )

            if task_result.get("success"):
                self.logger.info(f"Created task(s): {task_result.get('task_ids')}")
            else:
                self.logger.error(
                    f"Task creation failed: {task_result.get('error_message')}"
                )

        except Exception as e:
            self.logger.error(
                f"Exception creating document review task: {str(e)}", exc_info=True
            )

    def _get_document_reference_from_details(
        self, document_details: Any, document_type: DocumentType
    ) -> str:
        """Get document reference from document details based on document type"""
        if document_type == DocumentType.PO:
            return getattr(document_details, "po_number", "unknown")
        # Add other document types as needed
        return "unknown"

    def _get_supplier_from_po_database(self, document_id: int) -> Optional[str]:
        """Get supplier name from database PO as fallback"""
        try:
            from didero.orders.models import PurchaseOrder

            po = PurchaseOrder.objects.select_related("supplier").get(id=document_id)
            return po.supplier.name if po.supplier else None
        except Exception as e:
            self.logger.warning(f"Could not get supplier from PO database: {str(e)}")
            return None

    def _get_retry_policy(self, use_defaults: bool = False) -> RetryPolicy:
        """Get retry policy from configuration"""
        if use_defaults or not hasattr(self, "config"):
            return get_retry_policy(None, use_defaults=True)
        return get_retry_policy(self.config, use_defaults)
