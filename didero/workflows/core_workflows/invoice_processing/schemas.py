from decimal import Decimal
from typing import List, Optional, TypedDict

from pydantic import Field

from didero.documents.schemas import DocumentType
from didero.invoices.schemas import Invoice
from didero.workflows.schemas import ToleranceConfig, WorkflowBehaviorConfigBase


class ExtractInvoiceDetailsParams(TypedDict):
    email_id: str


class InvoicePostValidationActionsParams(TypedDict):
    email_id: str
    purchase_order_id: str | int
    invoice: Invoice


class ValidateInvoiceInfoParams(TypedDict):
    invoice: Invoice
    purchase_order_id: str | int
    email_id: str


class ValidateInvoiceAmountParams(TypedDict):
    invoice: Invoice
    purchase_order_id: str | int
    email_id: str


class ValidateInvoiceItemsParams(TypedDict):
    invoice: Invoice
    purchase_order_id: str | int
    email_id: str


class ValidateInvoiceChargesParams(TypedDict):
    invoice: Invoice
    purchase_order_id: str | int
    email_id: str


class InvoiceProcessingBehaviorConfig(WorkflowBehaviorConfigBase):
    """Invoice Processing specific configuration"""

    config_name: str = Field(
        default="Invoice PO Matching",
        description="The name of the configuration",
    )
    match_against_document_type: DocumentType = Field(
        default=DocumentType.PO,
        description="The type of document to match against",
    )

    # Core behavior flags
    enable_matching: bool = True  # store invoice directly
    require_manual_validation: bool = True
    auto_match_document: bool = True

    # Validation controls
    validate_invoice_amounts: bool = True
    tolerance_config: ToleranceConfig = Field(
        default_factory=ToleranceConfig,
        description="Tolerance configuration for amount differences",
    )
    require_line_item_match: bool = True

    # Auto-approval rules
    # manual validation turns off auto approval
    auto_approve_threshold: Optional[float] = Field(
        default=None,
        ge=0.0,
        le=1.0,
        description="AI matching score threshold for auto-approval (0.0-1.0). If None, defaults to 0.9",
    )

    # Note: Tasks are always created except when auto-approving
    # (manual validation OFF + high matching score + acceptable match result)

    # Task assignment configuration
    assigned_user_ids: List[int] = Field(
        default_factory=list,
        description="Specific user IDs to assign invoice review tasks to",
    )
    assigned_user_group_names: List[str] = Field(
        default_factory=list,
        description="User group names to assign invoice review tasks to (e.g., ['invoice_reviewers'])",
    )

    # Notification settings
    notification_channels: List[str] = Field(default_factory=lambda: ["email", "task"])
    notify_on_discrepancy: bool = True
    notify_on_document_not_found: bool = True

    class Config:
        json_encoders = {Decimal: str}
