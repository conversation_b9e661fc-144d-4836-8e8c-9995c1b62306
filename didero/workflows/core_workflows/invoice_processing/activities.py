"""
Invoice processing workflow-specific activities.
"""

from decimal import Decimal
from typing import Any, Dict, List, Optional

from django.contrib.contenttypes.models import ContentType
from temporalio import activity

from didero.addresses.models import Address
from didero.addresses.schemas import AddressSchema
from didero.ai.utils.utils import match_item_by_number
from didero.documents.models import DocumentLink
from didero.documents.schemas import DocumentType
from didero.invoices.models import Invoice as InvoiceModel
from didero.invoices.models import InvoiceItem
from didero.items.models import Item
from didero.orders.models import PurchaseOrder
from didero.orders.schemas import OrderAcknowledgementOrderItem
from didero.suppliers.models import Communication
from didero.users.models.team_models import Team
from didero.utils.date_utils import parse_date_flexible
from didero.workflows.core_workflows.invoice_processing.errors import (
    PORetrievalError,
)


@activity.defn
def store_invoice_activity(
    invoice_data: Dict[str, Any],
    purchase_order_id: Optional[int],
    email_id: Optional[str],
    team_id: int,
) -> Dict[str, Any]:
    """
    Store complete invoice record in database including financial fields, line items, and billing address.

    Args:
        invoice_data: Complete invoice data dictionary with all fields
        purchase_order_id: The PO ID to link to (optional)
        email_id: Source email ID (communication ID, optional)
        team_id: The team ID

    Returns:
        Dict with success status and invoice ID or error message
    """
    try:
        activity.logger.info(
            f"Storing invoice for PO ID {purchase_order_id}, team {team_id}"
        )
        activity.logger.info(f"Invoice data: {invoice_data}")

        # Get the purchase order if provided
        purchase_order = None
        if purchase_order_id is not None:
            try:
                purchase_order = PurchaseOrder.objects.get(id=purchase_order_id)
                activity.logger.info(f"Found PO: {purchase_order.po_number}")
            except PurchaseOrder.DoesNotExist:
                activity.logger.error(
                    f"PurchaseOrder with ID {purchase_order_id} not found"
                )
                raise PORetrievalError(
                    f"PurchaseOrder with ID {purchase_order_id} not found"
                )
        else:
            activity.logger.info("No PO ID provided - storing invoice without PO link")

        # Communication already retrieved above for supplier lookup

        # Create or find billing address
        billing_address = None
        if invoice_data.get("billing_address"):
            billing_address = _create_billing_address(
                invoice_data["billing_address"], team_id
            )
            activity.logger.info(
                f"Created/found billing address: {billing_address.id if billing_address else None}"
            )

        # Parse dates flexibly
        invoice_date = parse_date_flexible(invoice_data.get("invoice_date"))
        due_date = parse_date_flexible(invoice_data.get("due_date"))

        activity.logger.info(
            f"Parsed invoice_date: {invoice_date} (from: {invoice_data.get('invoice_date')})"
        )
        activity.logger.info(
            f"Parsed due_date: {due_date} (from: {invoice_data.get('due_date')})"
        )

        # Get communication and supplier from email_id if available
        communication = None
        supplier = None
        if email_id:
            try:
                communication = Communication.objects.get(id=email_id)
                supplier = communication.supplier
                activity.logger.info(
                    f"Found supplier from communication: {supplier.name if supplier else 'None'}"
                )
            except Communication.DoesNotExist:
                activity.logger.warning(f"Communication with ID {email_id} not found")

        # Fallback to PO supplier if no communication supplier
        if not supplier and purchase_order:
            supplier = purchase_order.supplier

        # Create invoice record with all available fields
        invoice_record = InvoiceModel.objects.create(
            purchase_order=purchase_order,
            supplier=supplier,
            team_id=team_id,
            billing_address=billing_address,
            invoice_number=invoice_data.get("invoice_number"),
            invoice_date=invoice_date,
            due_date=due_date,
            payment_terms=invoice_data.get("payment_terms", ""),
            notes=invoice_data.get("notes", ""),
            special_instructions=invoice_data.get("special_instructions", ""),
            # Store all financial fields
            subtotal=invoice_data.get("subtotal"),
            tax_rate=invoice_data.get("tax_rate"),
            tax_amount=invoice_data.get("tax_amount"),
            shipping_or_freight_charges=invoice_data.get("shipping_or_freight_charges"),
            other_special_charges=invoice_data.get("other_special_charges"),
            payment_status=invoice_data.get("payment_status"),
            status=invoice_data.get("status"),
        )

        # Link invoice to email thread (following PO pattern)
        if communication:
            from didero.invoices.utils import link_invoice_to_email_thread

            link, created = link_invoice_to_email_thread(invoice_record, communication)
            if created:
                activity.logger.info(
                    f"Linked invoice {invoice_record.id} to email thread {communication.email_thread.id if communication.email_thread else 'None'}"
                )

        # Store invoice line items
        items_created = 0
        if invoice_data.get("order_items"):
            # Get team instance for item matching
            team = Team.objects.get(id=team_id)

            items_created = _create_invoice_items(
                invoice_record,
                invoice_data["order_items"],
                supplier,  # Use the supplier we determined above
                team,
            )
            activity.logger.info(f"Created {items_created} invoice items")

        # Link invoice document directly to the invoice record for better performance
        document_linked = _link_invoice_document(invoice_record, communication)
        if document_linked:
            activity.logger.info(
                f"Successfully linked invoice document directly to invoice {invoice_record.pk}"
            )

        activity.logger.info(
            f"Successfully created invoice record with ID: {invoice_record.pk}"
        )

        return {
            "success": True,
            "invoice_id": str(invoice_record.pk),
            "items_created": items_created,
        }

    except PORetrievalError as e:
        activity.logger.error(f"PO retrieval error: {str(e)}")
        return {"success": False, "error_message": str(e)}
    except Exception as e:
        activity.logger.error(f"Failed to store invoice: {str(e)}")
        return {"success": False, "error_message": f"Failed to store invoice: {str(e)}"}


def _create_billing_address(address_data: Dict[str, Any], team_id: int) -> Address:
    """
    Create or find billing address from address data.

    Args:
        address_data: Address data dictionary
        team_id: The team ID

    Returns:
        Address instance
    """
    try:
        # Convert dict to AddressSchema if needed
        if isinstance(address_data, dict):
            address_schema = AddressSchema.model_validate(address_data)
        else:
            address_schema = address_data

        # Try to find existing address first
        address, created = Address.objects.get_or_create(
            line_1=address_schema.line_1 or "",
            line_2=address_schema.line_2 or "",
            city=address_schema.city or "",
            state_or_province=address_schema.state_or_province or "",
            postal_code=address_schema.postal_code or "",
            country=address_schema.country or "",
            team_id=team_id,
        )

        return address
    except Exception as e:
        activity.logger.error(f"Failed to create billing address: {str(e)}")
        return None


def _create_invoice_items(
    invoice: InvoiceModel, order_items: List[Dict[str, Any]], supplier, team: Team
) -> int:
    """
    Create invoice items from order items data with catalog item matching.

    Args:
        invoice: Invoice instance
        order_items: List of order item dictionaries
        supplier: Supplier instance for item matching
        team: Team instance for item matching

    Returns:
        Number of items created
    """
    items_created = 0

    try:
        for item_data in order_items:
            try:
                # Convert dict to OrderAcknowledgementOrderItem if needed
                if isinstance(item_data, dict):
                    item_schema = OrderAcknowledgementOrderItem.model_validate(
                        item_data
                    )
                else:
                    item_schema = item_data

                # Convert unit price to Decimal for catalog matching
                unit_price = None
                if item_schema.unit_price:
                    try:
                        # Extract amount from MoneyField if needed
                        if hasattr(item_schema.unit_price, "amount"):
                            unit_price = Decimal(str(item_schema.unit_price.amount))
                        else:
                            unit_price = Decimal(str(item_schema.unit_price))
                    except (ValueError, TypeError) as e:
                        activity.logger.warning(
                            f"Failed to convert unit price to Decimal for item {item_schema.item_number}: {str(e)}"
                        )

                # Use the utility function to match or create the catalog item
                catalog_item = match_item_by_number(
                    item_number=item_schema.item_number,
                    supplier=supplier,
                    team=team,
                    create_if_not_found=True,
                    description=item_schema.item_description,
                    unit_price=unit_price,
                )

                # Create InvoiceItem record linked to catalog item
                InvoiceItem.objects.create(
                    invoice=invoice,
                    item=catalog_item,  # Link to catalog item
                    item_number=item_schema.item_number,
                    item_description=item_schema.item_description,
                    quantity=float(item_schema.quantity),
                    unit_of_measure=item_schema.unit_of_measure,
                    unit_price=item_schema.unit_price,
                    total_price=item_schema.total_price,
                )
                items_created += 1

                activity.logger.info(
                    f"Created invoice item for {item_schema.item_number}, "
                    f"linked to catalog item {catalog_item.id if catalog_item else 'None'}"
                )

            except Exception as e:
                activity.logger.error(
                    f"Failed to create invoice item for {item_data.get('item_number', 'unknown')}: {str(e)}"
                )
                # Continue processing other items rather than failing entirely

    except Exception as e:
        activity.logger.error(f"Failed to create invoice items: {str(e)}")
        # Don't raise exception, just log and continue with partial success

    return items_created


@activity.defn
def update_invoice_po_link_activity(
    invoice_id: str,
    purchase_order_id: int,
    team_id: int,
) -> Dict[str, Any]:
    """
    Update an existing invoice to link it to a purchase order.

    Args:
        invoice_id: The invoice ID to update
        purchase_order_id: The PO ID to link to
        team_id: The team ID

    Returns:
        Dict with success status
    """
    try:
        activity.logger.info(
            f"Updating invoice {invoice_id} to link with PO {purchase_order_id}"
        )

        # Get the existing invoice
        try:
            invoice = InvoiceModel.objects.get(id=invoice_id, team_id=team_id)
        except InvoiceModel.DoesNotExist:
            activity.logger.error(f"Invoice {invoice_id} not found for team {team_id}")
            return {
                "success": False,
                "error_message": f"Invoice {invoice_id} not found",
            }

        # Get the purchase order
        try:
            purchase_order = PurchaseOrder.objects.get(id=purchase_order_id)
        except PurchaseOrder.DoesNotExist:
            activity.logger.error(f"PurchaseOrder {purchase_order_id} not found")
            return {
                "success": False,
                "error_message": f"PurchaseOrder {purchase_order_id} not found",
            }

        # Update the invoice to link to the PO
        invoice.purchase_order = purchase_order
        invoice.save()

        activity.logger.info(
            f"Successfully linked invoice {invoice_id} to PO {purchase_order_id}"
        )

        return {"success": True}

    except Exception as e:
        activity.logger.error(f"Failed to update invoice PO link: {str(e)}")
        return {"success": False, "error_message": str(e)}


def _link_invoice_document(invoice: InvoiceModel, communication: Communication) -> bool:
    """
    Create a direct DocumentLink from the invoice document to the invoice record.
    Uses EmailThread pattern to find documents from all related communications.

    This provides faster document access and makes invoices independent of
    purchase orders for document retrieval.

    Args:
        invoice: Invoice instance to link document to
        communication: Communication instance that contains the document

    Returns:
        True if document was successfully linked, False otherwise
    """
    if not communication:
        activity.logger.info("No communication provided for document linking")
        return False

    try:
        # Get all communications from the email thread (following PO pattern)
        from didero.invoices.utils import get_related_communications_for_invoice

        related_comms = get_related_communications_for_invoice(invoice)

        # If no EmailThread links exist yet, fall back to the current communication
        if not related_comms.exists():
            related_comms = Communication.objects.filter(id=communication.id)

        comm_content_type = ContentType.objects.get_for_model(Communication)
        comm_doc_link = None

        # First try to find documents already classified as INVOICE
        comm_doc_link = DocumentLink.objects.filter(
            parent_object_type=comm_content_type,
            parent_object_id__in=[
                str(pk) for pk in related_comms.values_list("id", flat=True)
            ],
            document__doc_type=DocumentType.INVOICE,
        ).first()

        # If no INVOICE document found, look for common misclassifications
        if not comm_doc_link:
            # Check PROCESSING first (normal case), then PO (misclassification case)
            for doc_type in [DocumentType.PROCESSING, DocumentType.PO]:
                comm_doc_link = DocumentLink.objects.filter(
                    parent_object_type=comm_content_type,
                    parent_object_id__in=[
                        str(pk) for pk in related_comms.values_list("id", flat=True)
                    ],
                    document__doc_type=doc_type,
                ).first()

                if comm_doc_link:
                    # Log misclassifications for analysis
                    if doc_type == DocumentType.PO:
                        activity.logger.warning(
                            f"Found invoice document {comm_doc_link.document.id} misclassified as PO, "
                            f"should investigate classification logic for communication {communication.id}"
                        )
                    break

            # If we found a non-INVOICE document, update it to INVOICE type
            if comm_doc_link and comm_doc_link.document:
                old_type = comm_doc_link.document.doc_type
                comm_doc_link.document.doc_type = DocumentType.INVOICE
                comm_doc_link.document.save()
                activity.logger.info(
                    f"Updated document {comm_doc_link.document.id} type from {old_type} to INVOICE"
                )

        if not comm_doc_link or not comm_doc_link.document:
            activity.logger.info(
                f"No invoice document found linked to email thread communications for invoice {invoice.id}"
            )
            return False

        # Check if direct link already exists to avoid duplicates
        invoice_content_type = ContentType.objects.get_for_model(InvoiceModel)
        existing_link = DocumentLink.objects.filter(
            parent_object_type=invoice_content_type,
            parent_object_id=str(invoice.id),
            document=comm_doc_link.document,
        ).exists()

        if existing_link:
            activity.logger.info(
                f"Document already directly linked to invoice {invoice.id}"
            )
            return True

        # Create direct link from document to invoice
        DocumentLink.objects.create(
            document=comm_doc_link.document,
            parent_object=invoice,
            parent_object_type=invoice_content_type,
            parent_object_id=str(invoice.id),
        )

        activity.logger.info(
            f"Created direct link from document {comm_doc_link.document.id} to invoice {invoice.id}"
        )
        return True

    except Exception as e:
        activity.logger.error(
            f"Failed to link invoice document to invoice {invoice.id}: {str(e)}"
        )
        return False
