"""
Definition for the invoice processing workflow.
"""

from typing import Optional

from didero.workflows.core_workflows.invoice_processing.schemas import (
    InvoiceProcessingBehaviorConfig,
)
from didero.workflows.models import UserWorkflow, WorkflowBehaviorConfig
from didero.workflows.schemas import (
    WorkflowTrigger,
    WorkflowType,
)


def create_invoice_processing_workflow_for_team(
    team_id: str,
    config: Optional[InvoiceProcessingBehaviorConfig] = None,
) -> UserWorkflow:
    """
    Create the invoice processing workflow for a team.

    Args:
        team_id: The ID of the team to create the workflow for
        config: Optional configuration for the workflow. If not provided, uses defaults.

    Returns:
        The created workflow instance
    """
    from didero.users.models.team_models import Team

    # Get the team
    team = Team.objects.get(pk=team_id)

    # Create default config if none provided
    if config is None:
        config = InvoiceProcessingBehaviorConfig()

    # Create or update workflow for this team
    workflow, created = UserWorkflow.objects.get_or_create(
        team=team,
        workflow_type=WorkflowType.INVOICE_PROCESSING,
        trigger=WorkflowTrigger.ON_INVOICE_EMAIL_RECEIVED,
    )

    # Create or update behavior config
    WorkflowBehaviorConfig.objects.update_or_create(
        workflow=workflow,
        defaults={"config": config.model_dump()},
    )

    return workflow
