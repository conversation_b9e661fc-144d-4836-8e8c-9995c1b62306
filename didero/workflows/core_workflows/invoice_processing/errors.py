from enum import StrEnum
from typing import Any

from pydantic import BaseModel


class InvoiceWorkflowErrorType(StrEnum):
    COMMUNICATION_NOT_FOUND = "communication_not_found"
    PURCHASE_ORDER_NOT_FOUND = "purchase_order_not_found"
    INVOICE_SCHEMA_EXTRACTION_FAILED = "invoice_schema_extraction_failed"

    # These error types map to Tasks that are created to notify the ops team
    TOTAL_PRICE_MISMATCH = "total_price_mismatch"
    UNKNOWN_INVOICE_ITEMS = "unknown_invoice_items"
    MISSING_PURCHASE_ORDER_ITEMS = "missing_purchase_order_items"
    MISMATCHED_PRICE_OR_QUANTITY = "mismatched_price_or_quantity"
    GENERAL_INFO_VALIDATION_ERROR = "general_info_validation_error"
    TAX_AMOUNT_MISMATCH = "tax_amount_mismatch"
    SHIPPING_CHARGES_MISMATCH = "shipping_charges_mismatch"
    OTHER_CHARGES_MISMATCH = "other_charges_mismatch"


class InvoiceWorkflowError(BaseModel):
    error_type: InvoiceWorkflowErrorType
    error_details: dict[str, Any]


class PORetrievalError(Exception):
    """Custom exception for PO retrieval errors"""

    pass


class InvoiceStorageError(Exception):
    """Custom exception for invoice storage errors"""

    pass


TASK_CREATION_ERROR_TYPES: list[InvoiceWorkflowErrorType] = [
    InvoiceWorkflowErrorType.TOTAL_PRICE_MISMATCH,
    InvoiceWorkflowErrorType.UNKNOWN_INVOICE_ITEMS,
    InvoiceWorkflowErrorType.MISSING_PURCHASE_ORDER_ITEMS,
    InvoiceWorkflowErrorType.MISMATCHED_PRICE_OR_QUANTITY,
    InvoiceWorkflowErrorType.GENERAL_INFO_VALIDATION_ERROR,
    InvoiceWorkflowErrorType.TAX_AMOUNT_MISMATCH,
    InvoiceWorkflowErrorType.SHIPPING_CHARGES_MISMATCH,
    InvoiceWorkflowErrorType.OTHER_CHARGES_MISMATCH,
]
