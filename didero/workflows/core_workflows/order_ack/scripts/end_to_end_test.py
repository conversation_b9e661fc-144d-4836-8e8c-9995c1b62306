#!/usr/bin/env python
"""
End-to-End Test for Order Acknowledgement Workflow

This script provides modular functions to test the OA workflow:
1. Setup test data (PO, OrderItems, Supplier)
2. Create OA email from supplier
3. Trigger OA workflow using standard categorization
4. Monitor workflow execution
5. Verify OA creation with order_item links
6. Check follow-up workflow integration
7. Cleanup all created data

Usage:
    source .env && DJANGO_SETTINGS_MODULE=didero.settings.common uv run python didero/workflows/core_workflows/order_ack/scripts/end_to_end_test.py
"""

import os
import sys
import time
import uuid
from decimal import Decimal
from typing import Dict, List, Optional, Tuple

# Setup Django environment before any imports to avoid conflicts
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")

# Add the project directory to Python path - go to actual project root
current_file = os.path.abspath(__file__)
# Go up 6 levels: scripts -> order_ack -> core_workflows -> workflows -> didero -> didero-api (project root)
project_root = os.path.dirname(
    os.path.dirname(
        os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_file))))
    )
)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    import django

    django.setup()
except Exception as e:
    print(f"Django setup error: {e}")
    print(
        "Make sure you're running this with: source .env && DJANGO_SETTINGS_MODULE=didero.settings.common uv run python didero/workflows/core_workflows/order_ack/scripts/end_to_end_test.py"
    )
    sys.exit(1)

# Now import Django models
from django.utils import timezone

from didero.addresses.models import Address
from didero.ai.email_categorization import (
    ai_categorize_email,
    trigger_workflows_for_email_category,
)
from didero.emails.models import EmailThread
from didero.emails.schemas import EmailCategory
from didero.items.models import Item
from didero.orders.models import (
    OrderAcknowledgement,
    OrderItem,
    PurchaseOrder,
)
from didero.suppliers.models import Communication, Supplier
from didero.users.models.team_models import Team
from didero.workflows.models import WorkflowRun

# Configuration
DEFAULT_TEAM_ID = 1
DEFAULT_WAIT_TIME = 180  # Allow for Temporal retries and workflow completion (increased due to async activities)


class OAWorkflowTester:
    """Modular OA workflow end-to-end tester"""

    def __init__(self, team_id: int = DEFAULT_TEAM_ID):
        self.team_id = team_id
        self.team = Team.objects.get(id=team_id)
        self.unique_suffix = uuid.uuid4().hex[:8]
        self.po_number = f"OA-CHECK-PO-{self.unique_suffix}"

        # Track created objects for cleanup
        self.created_objects = {
            "supplier": None,
            "address": None,
            "items": [],
            "purchase_order": None,
            "order_items": [],
            "email_thread": None,
            "email": None,
            "order_acknowledgement": None,
        }

        print(
            f"🔧 Initialized OA Workflow Tester for team: {self.team.name} (ID: {self.team.id})"
        )

    def cleanup_existing_data(self):
        """Clean up any existing test data"""
        print("\n🧹 Cleaning up existing test data...")

        # Delete existing test data
        deleted_pos = PurchaseOrder.objects.filter(
            po_number__startswith="OA-CHECK-PO-", team=self.team
        ).delete()[0]

        deleted_items = Item.objects.filter(
            item_number__startswith="OA-CHECK-ITEM-", team=self.team
        ).delete()[0]

        deleted_suppliers = Supplier.objects.filter(
            name="OA Check Supplier Inc.", team=self.team
        ).delete()[0]

        print(
            f"   Deleted {deleted_pos} POs, {deleted_items} items, {deleted_suppliers} suppliers"
        )

    def create_supplier_and_address(self) -> Tuple[Supplier, Address]:
        """Create test supplier and address"""
        print("\n🏢 Creating supplier and address...")

        unique_website = f"https://oa-check-supplier-{self.unique_suffix}.example.com"

        supplier = Supplier.objects.create(
            name="OA Check Supplier Inc.",
            team=self.team,
            website_url=unique_website,
            description="Supplier for OA workflow checking",
        )

        address = Address.objects.create(
            supplier=supplier,
            team=self.team,
            line_1="789 Check Street",
            city="Seattle",
            state_or_province="WA",
            postal_code="98101",
            country="US",
            is_default=True,
        )

        self.created_objects["supplier"] = supplier
        self.created_objects["address"] = address

        print(f"   ✅ Created supplier: {supplier.name} (ID: {supplier.id})")
        print(f"   ✅ Created address: {address}")

        return supplier, address

    def create_catalog_items(self, item_configs: List[Dict] = None) -> List[Item]:
        """Create test catalog items

        Args:
            item_configs: List of dicts with 'number', 'description' keys
                         Defaults to 2 standard test items
        """
        print("\n📦 Creating catalog items...")

        if item_configs is None:
            item_configs = [
                {"number": "OA-CHECK-ITEM-1", "description": "Check Widget Pro"},
                {"number": "OA-CHECK-ITEM-2", "description": "Check Gadget Deluxe"},
            ]

        items = []
        for config in item_configs:
            item = Item.objects.create(
                item_number=config["number"],
                description=config["description"],
                team=self.team,
            )
            items.append(item)
            self.created_objects["items"].append(item)
            print(f"   ✅ Created item: {item.item_number} - {item.description}")

        return items

    def create_purchase_order(
        self,
        supplier: Supplier,
        address: Address = None,
        order_items_config: List[Dict] = None,
    ) -> Tuple[PurchaseOrder, List[OrderItem]]:
        """Create test purchase order with order items

        Args:
            supplier: Supplier for the PO
            order_items_config: List of dicts with 'item_number', 'quantity', 'price' keys
        """
        print("\n📋 Creating purchase order...")

        if order_items_config is None:
            order_items_config = [
                {
                    "item_number": "OA-CHECK-ITEM-1",
                    "quantity": 5.0,
                    "price": Decimal("100.00"),
                },
                {
                    "item_number": "OA-CHECK-ITEM-2",
                    "quantity": 3.0,
                    "price": Decimal("200.00"),
                },
            ]

        # Calculate total cost
        total_cost = sum(
            Decimal(str(config["quantity"])) * config["price"]
            for config in order_items_config
        )

        purchase_order = PurchaseOrder.objects.create(
            po_number=self.po_number,
            team=self.team,
            supplier=supplier,
            order_status="pending_acceptance",
            total_cost=total_cost,
            placement_time=timezone.now(),
            source="MANUAL",
            shipping_address=address,  # Add shipping address to match OA email
        )

        self.created_objects["purchase_order"] = purchase_order
        print(f"   ✅ Created PO: {purchase_order.po_number} (ID: {purchase_order.id})")

        # Create OrderItems
        order_items = []
        for config in order_items_config:
            # Find the item
            item = Item.objects.get(item_number=config["item_number"], team=self.team)

            order_item = OrderItem.objects.create(
                purchase_order=purchase_order,
                item=item,
                quantity=config["quantity"],
                price=config["price"],
                unit_of_measure="Each",
            )
            order_items.append(order_item)
            self.created_objects["order_items"].append(order_item)
            print(
                f"   ✅ Created OrderItem: {order_item.item.item_number} x {order_item.quantity} @ ${order_item.price}"
            )

        return purchase_order, order_items

    def create_oa_email(
        self,
        supplier: Supplier,
        purchase_order: PurchaseOrder,
        order_items: List[OrderItem],
        email_config: Dict = None,
    ) -> Communication:
        """Create test OA email

        Args:
            supplier: Supplier sending the OA
            purchase_order: PO being acknowledged
            order_items: OrderItems to acknowledge
            email_config: Dict with email customization options
        """
        print("\n📧 Creating order acknowledgement email...")

        if email_config is None:
            email_config = {
                "ship_days_offset": 10,  # Ship in 10 days
                "delivery_days_offset": 15,  # Deliver in 15 days
                "include_shipping": False,  # No extra charges to match PO total
                "include_tax": False,
            }

        # Build email content
        ship_date = (
            timezone.now() + timezone.timedelta(days=email_config["ship_days_offset"])
        ).strftime("%Y-%m-%d")
        delivery_date = (
            timezone.now()
            + timezone.timedelta(days=email_config["delivery_days_offset"])
        ).strftime("%Y-%m-%d")

        items_section = ""
        subtotal = Decimal("0.00")

        for i, order_item in enumerate(order_items, 1):
            extended_price = Decimal(str(order_item.quantity)) * Decimal(
                str(order_item.price.amount)
            )
            subtotal += extended_price

            items_section += f"""
{i}. Item Number: {order_item.item.item_number}
   Description: {order_item.item.description}
   Quantity Ordered: {order_item.quantity}
   Unit Price: ${order_item.price}
   Extended Price: ${extended_price}
   Promised Ship Date: {ship_date}
"""

        # Calculate totals
        tax = (
            subtotal * Decimal("0.08")
            if email_config.get("include_tax")
            else Decimal("0.00")
        )
        shipping = (
            Decimal("35.00")
            if email_config.get("include_shipping")
            else Decimal("0.00")
        )
        total = subtotal + tax + shipping

        email_body = f"""
Dear Customer,

Thank you for your purchase order {purchase_order.po_number}. We are pleased to acknowledge receipt and confirm acceptance of your order.

ORDER ACKNOWLEDGEMENT / SALES ORDER CONFIRMATION

Customer: {self.team.name}
Order Date: {timezone.now().strftime("%Y-%m-%d")}
Our Sales Order Number: SO-{self.unique_suffix}

ITEMS CONFIRMED:{items_section}

SUMMARY:
Subtotal: ${subtotal}
Tax: ${tax}
Shipping: ${shipping}
Total: ${total}

SHIPPING INFORMATION:
Ship To: {purchase_order.shipping_address.line_1}, {purchase_order.shipping_address.city}, {purchase_order.shipping_address.state_or_province}, {purchase_order.shipping_address.postal_code}
Expected Ship Date: {ship_date}
Shipping Method: UPS Ground
Estimated Delivery: {delivery_date}

We appreciate your business and will provide tracking information once the order ships.

Best regards,
{supplier.name}
Sales Department
"""

        # Create email thread
        email_thread = EmailThread.objects.create(
            team=self.team, thread_id=f"oa-check-thread-{uuid.uuid4()}"
        )
        self.created_objects["email_thread"] = email_thread

        # Create the OA email
        email = Communication.objects.create(
            team=self.team,
            supplier=supplier,
            email_thread=email_thread,
            email_subject=f"Order Acknowledgement - PO {purchase_order.po_number}",
            email_content=email_body,
            direction="INBOUND",
            email_from=f"sales@{supplier.name.lower().replace(' ', '-')}.com",
            email_message_id=f"oa-check-{uuid.uuid4()}@example.com",
            comm_time=timezone.now(),
        )

        self.created_objects["email"] = email
        print(f"   ✅ Created OA email: {email.email_subject} (ID: {email.pk})")

        return email

    def trigger_workflow(
        self, email: Communication, force_category: bool = False
    ) -> EmailCategory:
        """Trigger the OA workflow

        Args:
            email: Email to categorize and trigger workflow for
            force_category: If True, force PURCHASE_ORDER_ACKNOWLEDGEMENT category
        """
        print("\n🚀 Triggering OA workflow...")

        if force_category:
            category = EmailCategory.PURCHASE_ORDER_ACKNOWLEDGEMENT
            print(f"   🔧 Forcing category: {category}")
        else:
            print(f"   🤖 AI categorizing email ID: {email.pk}")
            category = ai_categorize_email(email)
            print(f"   📊 Email categorized as: {category}")

        if category == EmailCategory.PURCHASE_ORDER_ACKNOWLEDGEMENT:
            print("   ✅ Correct category - triggering workflow")
        else:
            print(f"   ⚠️  Unexpected category {category} - forcing OA workflow")
            category = EmailCategory.PURCHASE_ORDER_ACKNOWLEDGEMENT

        trigger_workflows_for_email_category(email, category)
        print("   🎯 Workflow triggered successfully")

        return category

    def wait_for_execution(
        self, wait_time: int = DEFAULT_WAIT_TIME, purchase_order: PurchaseOrder = None
    ):
        """Wait for workflow execution with periodic status checks"""
        print(f"\n⏳ Waiting {wait_time} seconds for workflow execution...")

        for i in range(wait_time):
            if i % 30 == 0 and i > 0:  # Print status every 30 seconds
                print(f"   ⏱️  {i}/{wait_time} seconds elapsed...")

                # Check if OA has been created yet
                if purchase_order:
                    oas = OrderAcknowledgement.objects.filter(
                        purchase_order=purchase_order
                    )
                    if oas.exists():
                        print(f"   🎉 OrderAcknowledgement created at {i} seconds!")
                        break
                    else:
                        print(f"   ⏳ No OA yet at {i} seconds, continuing to wait...")

            time.sleep(1)

        print("   ✅ Wait period completed")

    def check_oa_creation(
        self, purchase_order: PurchaseOrder
    ) -> Optional[OrderAcknowledgement]:
        """Check if OA was created and analyze it"""
        print("\n🔍 Checking OrderAcknowledgement creation...")

        oas = OrderAcknowledgement.objects.filter(purchase_order=purchase_order)
        if not oas.exists():
            print("   ❌ No OrderAcknowledgement found!")
            return None

        oa = oas.first()
        self.created_objects["order_acknowledgement"] = oa

        print(f"   ✅ OrderAcknowledgement created (ID: {oa.pk})")
        print(f"      Status: {oa.oa_status}")
        print(f"      Supplier Order #: {oa.order_number}")
        print(f"      Items count: {oa.items.count()}")

        return oa

    def analyze_oa_items(self, oa: OrderAcknowledgement) -> Dict:
        """Analyze OA items and their relationships"""
        print("\n📊 Analyzing OA Items relationships...")

        oa_items = oa.items.all()

        total_items = oa_items.count()
        items_with_order_item = oa_items.filter(order_item__isnull=False).count()
        items_with_fallback = oa_items.filter(
            order_item__isnull=True, item__isnull=False
        ).count()
        orphaned_items = oa_items.filter(
            order_item__isnull=True, item__isnull=True
        ).count()

        analysis = {
            "total_items": total_items,
            "items_with_order_item": items_with_order_item,
            "items_with_fallback": items_with_fallback,
            "orphaned_items": orphaned_items,
            "success_rate": (items_with_order_item / total_items * 100)
            if total_items > 0
            else 0,
        }

        print("   📈 Analysis Results:")
        print(f"      Total OA Items: {total_items}")
        print(f"      ✅ Items with order_item link: {items_with_order_item}")
        print(f"      ⚠️  Items using fallback: {items_with_fallback}")
        print(f"      ❌ Orphaned items: {orphaned_items}")
        print(f"      🎯 Success Rate: {analysis['success_rate']:.1f}%")

        # Detailed item analysis
        print("\n   📝 Detailed Item Analysis:")
        for i, oa_item in enumerate(oa_items, 1):
            print(f"      {i}. {oa_item.item_number} - {oa_item.item_description}")
            print(f"         Qty: {oa_item.quantity}, Price: ${oa_item.unit_price}")

            if oa_item.order_item:
                print(f"         ✅ → OrderItem {oa_item.order_item.id}")
                qty_match = (
                    abs(float(oa_item.quantity) - float(oa_item.order_item.quantity))
                    < 0.01
                )
                price_match = (
                    abs(
                        float(oa_item.unit_price.amount)
                        - float(oa_item.order_item.price.amount)
                    )
                    < 0.01
                )
                print(
                    f"         Qty match: {'✅' if qty_match else '❌'}, Price match: {'✅' if price_match else '❌'}"
                )
            elif oa_item.item:
                print(f"         ⚠️  → Fallback Item {oa_item.item.item_number}")
            else:
                print("         ❌ → No links")

            if oa_item.promised_ship_date:
                print(f"         📅 Ship: {oa_item.promised_ship_date}")

        return analysis

    def check_follow_up_integration(self, purchase_order: PurchaseOrder) -> Dict:
        """Check follow-up workflow integration"""
        print("\n🔄 Checking follow-up workflow integration...")

        try:
            from didero.workflows.core_workflows.follow_up.utils import (
                check_shipment_follow_up_needed,
            )

            needs_followup, reason, context_items = check_shipment_follow_up_needed(
                purchase_order=purchase_order,
                strategy="balanced",
                initial_wait_hours=24,
                business_hours_only=False,
            )

            result = {
                "success": True,
                "needs_followup": needs_followup,
                "reason": reason,
                "context_items_count": len(context_items),
                "context_items": context_items,
            }

            print("   ✅ Follow-up integration working!")
            print(f"      Needs follow-up: {needs_followup}")
            print(f"      Reason: {reason}")
            print(f"      Context items: {len(context_items)}")

            if context_items:
                print("      📊 Sample context items:")
                for item in context_items[:2]:  # Show first 2 items
                    if "_group_context" not in item:
                        print(
                            f"         - {item.get('item_name', 'Unknown')}: {item.get('unshipped_quantity', 0)} unshipped"
                        )

            return result

        except Exception as e:
            print(f"   ❌ Follow-up integration failed: {e}")
            return {"success": False, "error": str(e)}

    def check_workflow_execution(self, email: Communication) -> Optional[Dict]:
        """Check workflow execution details"""
        print("\n📋 Checking workflow execution...")

        workflow_runs = WorkflowRun.objects.filter(
            params__contains=f'"email_id": "{email.pk}"'
        ).order_by("-start_time")

        if not workflow_runs.exists():
            print("   ❌ No workflow run found")
            return None

        workflow_run = workflow_runs.first()
        print("   ✅ Workflow run found:")
        print(f"      ID: {workflow_run.id}")
        print(f"      Temporal ID: {workflow_run.temporal_id}")
        print(f"      Type: {workflow_run.workflow_type}")
        print(
            f"      Overall state: {workflow_run.state.get('overall_state', 'Unknown')}"
        )

        # Show additional state details
        state = workflow_run.state
        if isinstance(state, dict):
            for key, value in state.items():
                if key != "overall_state":
                    print(f"      {key}: {value}")

        return {
            "workflow_run": workflow_run,
            "state": state,
        }

    def cleanup_all_data(self):
        """Clean up all created test data"""
        print("\n🧹 Cleaning up all created test data...")

        cleanup_count = 0

        # Delete in reverse order of creation
        if self.created_objects["order_acknowledgement"]:
            self.created_objects["order_acknowledgement"].delete()
            cleanup_count += 1
            print("   🗑️  Deleted OrderAcknowledgement")

        if self.created_objects["email"]:
            self.created_objects["email"].delete()
            cleanup_count += 1
            print("   🗑️  Deleted email")

        if self.created_objects["email_thread"]:
            self.created_objects["email_thread"].delete()
            cleanup_count += 1
            print("   🗑️  Deleted email thread")

        for order_item in self.created_objects["order_items"]:
            order_item.delete()
            cleanup_count += 1
        print(f"   🗑️  Deleted {len(self.created_objects['order_items'])} OrderItems")

        if self.created_objects["purchase_order"]:
            self.created_objects["purchase_order"].delete()
            cleanup_count += 1
            print("   🗑️  Deleted PurchaseOrder")

        for item in self.created_objects["items"]:
            item.delete()
            cleanup_count += 1
        print(f"   🗑️  Deleted {len(self.created_objects['items'])} Items")

        if self.created_objects["address"]:
            self.created_objects["address"].delete()
            cleanup_count += 1
            print("   🗑️  Deleted Address")

        if self.created_objects["supplier"]:
            self.created_objects["supplier"].delete()
            cleanup_count += 1
            print("   🗑️  Deleted Supplier")

        print(f"   ✅ Cleanup completed: {cleanup_count} objects deleted")


def run_full_oa_test(
    team_id: int = DEFAULT_TEAM_ID, wait_time: int = DEFAULT_WAIT_TIME
) -> Dict:
    """Run the complete OA workflow test"""

    print("=" * 80)
    print("ORDER ACKNOWLEDGEMENT WORKFLOW END-TO-END TEST")
    print("=" * 80)

    tester = OAWorkflowTester(team_id=team_id)

    try:
        # Step 1: Setup
        tester.cleanup_existing_data()
        supplier, address = tester.create_supplier_and_address()
        tester.create_catalog_items()
        purchase_order, order_items = tester.create_purchase_order(supplier, address)

        # Step 2: Create email but DON'T trigger workflow yet
        email = tester.create_oa_email(supplier, purchase_order, order_items)

        # Step 2.5: Check if OA exists BEFORE triggering workflow
        print("\n🔍 Checking if OA exists BEFORE triggering workflow...")
        oa_before = tester.check_oa_creation(purchase_order)
        if oa_before:
            print(
                f"   ⚠️  OA already exists BEFORE workflow trigger! ID: {oa_before.pk}"
            )
            return {
                "success": False,
                "oa_created_before_workflow": True,
                "early_exit": True,
            }
        else:
            print("   ✅ No OA exists before workflow - good!")

        # Step 3: Now trigger workflow
        tester.trigger_workflow(email)

        # Step 4: Wait and check results
        tester.wait_for_execution(wait_time, purchase_order)
        oa = tester.check_oa_creation(purchase_order)

        if oa:
            # Step 4: Analyze results
            analysis = tester.analyze_oa_items(oa)
            followup_result = tester.check_follow_up_integration(purchase_order)
            workflow_result = tester.check_workflow_execution(email)

            # Summary
            success = analysis["items_with_order_item"] > 0

            print("\n" + "=" * 80)
            print("TEST SUMMARY")
            print("=" * 80)

            if success:
                print("🎉 SUCCESS: OA workflow executed successfully!")
                print("   ✅ OrderAcknowledgement created")
                print(
                    f"   ✅ {analysis['items_with_order_item']}/{analysis['total_items']} items linked to OrderItems"
                )
                print(f"   ✅ Success rate: {analysis['success_rate']:.1f}%")
                print(
                    f"   ✅ Follow-up integration: {'✅' if followup_result['success'] else '❌'}"
                )
            else:
                print("❌ FAILURE: Issues detected")

            return {
                "success": success,
                "oa_created": True,
                "analysis": analysis,
                "followup_result": followup_result,
                "workflow_result": workflow_result,
            }
        else:
            print("\n❌ FAILURE: No OrderAcknowledgement created")
            return {
                "success": False,
                "oa_created": False,
                "analysis": None,
                "followup_result": None,
                "workflow_result": tester.check_workflow_execution(email),
            }

    finally:
        # Always cleanup
        tester.cleanup_all_data()
        print("\n✅ All test data cleaned up")


def run_custom_oa_test():
    """Example of running custom variations"""

    print("=" * 80)
    print("CUSTOM OA TEST VARIATIONS")
    print("=" * 80)

    tester = OAWorkflowTester(team_id=1)

    try:
        # Setup base data
        tester.cleanup_existing_data()
        supplier, address = tester.create_supplier_and_address()

        # Custom items with different configurations
        tester.create_catalog_items(
            [
                {"number": "CUSTOM-ITEM-A", "description": "Custom Widget Alpha"},
                {"number": "CUSTOM-ITEM-B", "description": "Custom Widget Beta"},
                {"number": "CUSTOM-ITEM-C", "description": "Custom Widget Gamma"},
            ]
        )

        # Custom order with different quantities and prices
        custom_order_config = [
            {
                "item_number": "CUSTOM-ITEM-A",
                "quantity": 10.0,
                "price": Decimal("50.00"),
            },
            {
                "item_number": "CUSTOM-ITEM-B",
                "quantity": 5.0,
                "price": Decimal("75.00"),
            },
            {
                "item_number": "CUSTOM-ITEM-C",
                "quantity": 2.0,
                "price": Decimal("125.00"),
            },
        ]

        purchase_order, order_items = tester.create_purchase_order(
            supplier, address, custom_order_config
        )

        # Custom email configuration
        custom_email_config = {
            "ship_days_offset": 7,  # Ship in 7 days
            "delivery_days_offset": 10,  # Deliver in 10 days
            "include_shipping": False,  # No shipping charges
            "include_tax": True,
        }

        email = tester.create_oa_email(
            supplier, purchase_order, order_items, custom_email_config
        )

        # Force the category to test specific path
        tester.trigger_workflow(email, force_category=True)

        tester.wait_for_execution(180, purchase_order)  # Use longer wait time

        oa = tester.check_oa_creation(purchase_order)
        if oa:
            analysis = tester.analyze_oa_items(oa)
            tester.check_follow_up_integration(purchase_order)

            print(
                f"\n🎯 Custom test completed with {analysis['success_rate']:.1f}% success rate"
            )

    finally:
        tester.cleanup_all_data()


def run_mismatch_test():
    """Test workflow with intentional mismatches to trigger document review tasks"""

    print("=" * 80)
    print("ORDER ACKNOWLEDGEMENT MISMATCH TEST")
    print("=" * 80)

    tester = OAWorkflowTester(team_id=1)

    try:
        # Setup base data
        tester.cleanup_existing_data()
        supplier, address = tester.create_supplier_and_address()
        tester.create_catalog_items()

        # Create PO with specific quantities and prices
        po_config = [
            {
                "item_number": "OA-CHECK-ITEM-1",
                "quantity": 5.0,
                "price": Decimal("100.00"),
            },
            {
                "item_number": "OA-CHECK-ITEM-2",
                "quantity": 3.0,
                "price": Decimal("200.00"),
            },
        ]
        purchase_order, order_items = tester.create_purchase_order(
            supplier, address, po_config
        )

        # Now manually create OA email with MISMATCHED quantities and prices
        print("\n📧 Creating MISMATCHED order acknowledgement email...")

        ship_date = (timezone.now() + timezone.timedelta(days=10)).strftime("%Y-%m-%d")
        delivery_date = (timezone.now() + timezone.timedelta(days=15)).strftime(
            "%Y-%m-%d"
        )

        # Create email with intentional mismatches
        mismatched_email_body = f"""
Dear Customer,

Thank you for your purchase order {purchase_order.po_number}. We are pleased to acknowledge receipt and confirm acceptance of your order.

ORDER ACKNOWLEDGEMENT / SALES ORDER CONFIRMATION

Customer: {tester.team.name}
Order Date: {timezone.now().strftime("%Y-%m-%d")}
Our Sales Order Number: SO-{tester.unique_suffix}

ITEMS CONFIRMED:
1. Item Number: OA-CHECK-ITEM-1
   Description: Check Widget Pro
   Quantity Ordered: 7.0
   Unit Price: $120.00
   Extended Price: $840.00
   Promised Ship Date: {ship_date}

2. Item Number: OA-CHECK-ITEM-2  
   Description: Check Gadget Deluxe
   Quantity Ordered: 2.0
   Unit Price: $250.00
   Extended Price: $500.00
   Promised Ship Date: {ship_date}

SUMMARY:
Subtotal: $1340.00
Tax: $0.00
Shipping: $0.00
Total: $1340.00

SHIPPING INFORMATION:
Ship To: {purchase_order.shipping_address.line_1}, {purchase_order.shipping_address.city}, {purchase_order.shipping_address.state_or_province}, {purchase_order.shipping_address.postal_code}
Expected Ship Date: {ship_date}
Shipping Method: UPS Ground
Estimated Delivery: {delivery_date}

We appreciate your business and will provide tracking information once the order ships.

Best regards,
{supplier.name}
Sales Department
"""

        # Create email thread and communication
        from didero.emails.models import EmailThread
        from didero.suppliers.models import Communication

        email_thread = EmailThread.objects.create(
            team=tester.team, thread_id=f"oa-mismatch-thread-{uuid.uuid4()}"
        )

        email = Communication.objects.create(
            team=tester.team,
            supplier=supplier,
            email_thread=email_thread,
            email_subject=f"Order Acknowledgement - PO {purchase_order.po_number} [MISMATCHED]",
            email_content=mismatched_email_body,
            direction="INBOUND",
            email_from=f"sales@{supplier.name.lower().replace(' ', '-')}.com",
            email_message_id=f"oa-mismatch-{uuid.uuid4()}@example.com",
            comm_time=timezone.now(),
        )

        print(
            f"   ✅ Created MISMATCHED OA email: {email.email_subject} (ID: {email.pk})"
        )
        print("   🔍 Mismatches introduced:")
        print("      - Item 1: Qty 5→7, Price $100→$120")
        print("      - Item 2: Qty 3→2, Price $200→$250")

        # Trigger workflow
        tester.trigger_workflow(email, force_category=True)

        # Wait for processing
        tester.wait_for_execution(180, purchase_order)

        # Check results
        oa = tester.check_oa_creation(purchase_order)
        if oa:
            analysis = tester.analyze_oa_items(oa)

            # Check for tasks created due to mismatches
            print("\n📋 Checking for document review tasks...")
            from django.contrib.contenttypes.models import ContentType

            from didero.tasks.models import Task

            oa_content_type = ContentType.objects.get_for_model(oa.__class__)
            tasks = Task.objects.filter(
                model_type=oa_content_type,
                model_id=oa.id,
                task_type_v2__name__icontains="REVIEW",
            ).order_by("-created_at")

            print(f"   📊 Found {tasks.count()} review tasks")
            for task in tasks:
                print(f"      Task ID: {task.id}")
                print(
                    f"      Type: {task.task_type_v2.name if task.task_type_v2 else 'Unknown'}"
                )
                print(f"      Status: {task.status}")
                print(
                    f"      Assigned to: {task.user.username if task.user else 'No user'}"
                )

            print(
                f"\n🎯 Mismatch test completed with {analysis['success_rate']:.1f}% matching rate"
            )
            return {
                "success": True,
                "oa_created": True,
                "tasks_created": tasks.count(),
                "analysis": analysis,
            }
        else:
            print("\n❌ FAILURE: No OrderAcknowledgement created")
            return {"success": False, "oa_created": False}

    finally:
        tester.cleanup_all_data()


if __name__ == "__main__":
    # Run mismatch test only
    mismatch_result = run_mismatch_test()

    # Uncomment to run other tests
    # result = run_full_oa_test()
    # run_custom_oa_test()
