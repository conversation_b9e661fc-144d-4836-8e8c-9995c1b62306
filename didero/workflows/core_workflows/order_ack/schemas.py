from typing import List
from pydantic import Field
from didero.workflows.schemas import WorkflowBehaviorConfigBase
from decimal import Decimal


class OrderAcknowledgementBehaviorConfig(WorkflowBehaviorConfigBase):
    """Order Acknowledgement specific configuration"""

    # Core behavior flags
    require_human_validation: bool = (
        True  # Replaces TEAM_ORDER_ACK_HUMAN_VALIDATION_ENABLED
    )

    # Validation controls
    validate_duplicate_oa: bool = True
    validate_info: bool = True  # Enable/disable info validation step
    validate_items: bool = True  # Enable/disable items validation step
    validate_freight_costs: bool = True  # Enable/disable freight costs validation step

    # Extension points for team-specific customizations
    post_extraction_activities: List[str] = Field(default_factory=list)
    pre_validation_activities: List[str] = Field(default_factory=list)
    post_validation_activities: List[str] = Field(default_factory=list)

    # Notification settings
    notification_channels: List[str] = Field(default_factory=lambda: ["email"])
    notify_on_validation_required: bool = True

    class Config:
        json_encoders = {Decimal: str}
