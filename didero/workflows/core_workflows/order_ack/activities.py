"""
Order Acknowledgement workflow activities using document comparison framework.
"""

from typing import Any, Dict, Optional, TypedDict

import structlog
from temporalio import activity
from temporalio.exceptions import ApplicationError

from didero.ai.document_matching.document_matching import ai_match_documents
from didero.ai.document_matching.schemas import DocumentComparisonResult
from didero.documents.schemas import DocumentType
from didero.orders.models import OrderAcknowledgement as DjangoOrderAcknowledgement
from didero.orders.models import PurchaseOrder
from didero.suppliers.models import Communication
from didero.workflows.serialization_utils import serialize_purchase_order


class OAComparisonResult(TypedDict):
    """Return type for OA to PO comparison activity."""

    comparison_result: DocumentComparisonResult
    po_data: Dict[str, Any]


class OASaveResult(TypedDict):
    """Return type for OA save activity."""

    success: bool
    oa_id: int
    oa_status: str
    has_mismatches: bool


logger = structlog.get_logger(__name__)


@activity.defn
def compare_oa_to_po_activity(
    params: Dict[str, Any],
) -> OAComparisonResult:
    """
    Compare Order Acknowledgement to Purchase Order using document matching framework.

    Args:
        params: Dict containing:
            - order_acknowledgement: OrderAcknowledgement schema
            - purchase_order_id: PO ID
            - team_id: Team ID
            - tolerance_config: Optional tolerance configuration

    Returns:
        Dict containing:
            - comparison_result: DocumentComparisonResult with validation results
            - po_data: Serialized PO data for task creation
    """
    logger.info(
        "Starting OA to PO document comparison",
        purchase_order_id=params["purchase_order_id"],
        team_id=params["team_id"],
    )

    try:
        # Get PO and serialize it using existing utility
        purchase_order = PurchaseOrder.objects.get(id=params["purchase_order_id"])
        po_data = serialize_purchase_order(purchase_order)

        # Use document comparison framework with acknowledge-by-omission logic for OA validation
        from pathlib import Path

        from didero.ai.utils.utils import get_langfuse_prompt

        # Get OA-specific instructions from prompts config
        prompts_path = (
            Path(__file__).parent.parent.parent.parent
            / "ai"
            / "document_matching"
            / "prompts_config.yaml"
        )
        oa_instructions_prompt = get_langfuse_prompt(
            prompt_key="oa_instructions", prompts_path=prompts_path
        )
        oa_instructions = str(oa_instructions_prompt.compile())

        comparison_result = ai_match_documents(
            document1=params["order_acknowledgement"],
            document2=po_data,
            team_id=int(params["team_id"]),
            document1_type=DocumentType.ORDER_ACKNOWLEDGEMENT,
            document2_type=DocumentType.PO,
            tolerance_config=params.get("tolerance_config", {}),
            additional_context=oa_instructions,
            context_type="ORDER_ACKNOWLEDGEMENT_VALIDATION",
        )

        logger.info(
            "OA to PO comparison completed",
            purchase_order_id=params["purchase_order_id"],
            match_result=comparison_result.match_result.value,
            matching_score=comparison_result.matching_score,
        )

        # Return both comparison result and PO data for task creation
        return {
            "comparison_result": comparison_result,
            "po_data": po_data,
        }

    except Exception as e:
        logger.error(
            "Failed to compare OA to PO",
            error=str(e),
            purchase_order_id=params["purchase_order_id"],
            team_id=params["team_id"],
        )
        raise


@activity.defn
def save_oa_activity(
    params: Dict[str, Any],
) -> OASaveResult:
    """
    Save Order Acknowledgement - placeholder for future PR.

    TODO: Implement smart merge logic in next PR:
    - Copy PO values for fields with no mismatch (acknowledge by omission)
    - Copy OA values for fields with mismatch (supplier override)
    - Always preserve promised ship/delivery dates from OA (supplier commitment)
    - Set acknowledgement status on order items (acknowledged/mismatched)

    Args:
        params: Dict containing:
            - order_acknowledgement: OrderAcknowledgement schema
            - comparison_result: DocumentComparisonResult
            - purchase_order_id: PO ID
            - email_id: Source email ID

    Returns:
        Dict with saved OA info and status
    """
    logger.info(
        "Saving OA - placeholder implementation",
        purchase_order_id=params["purchase_order_id"],
    )

    # TODO: Implement smart merge logic in next PR using comparison results
    # For now, use existing OA creation logic without comparison-based saving
    try:
        purchase_order = PurchaseOrder.objects.get(id=params["purchase_order_id"])
        email = Communication.objects.get(id=params["email_id"])

        # Convert dict back to Pydantic objects (Temporal serializes Pydantic objects to dicts)
        from didero.orders.schemas import (
            OrderAcknowledgement as OrderAcknowledgementSchema,
        )

        oa_data = params["order_acknowledgement"]
        if isinstance(oa_data, dict):
            # Convert dict to Pydantic object
            oa_schema = OrderAcknowledgementSchema.model_validate(oa_data)
        else:
            # Already a Pydantic object
            oa_schema = oa_data

        # Also convert comparison_result from dict to Pydantic object
        comparison_data = params["comparison_result"]
        if isinstance(comparison_data, dict):
            # Convert dict to Pydantic object
            comparison_result = DocumentComparisonResult.model_validate(comparison_data)
        else:
            # Already a Pydantic object
            comparison_result = comparison_data

        # Use OA manager to handle all saving logic
        django_oa = DjangoOrderAcknowledgement.objects.create_from_extraction(
            extraction_data=oa_schema,
            purchase_order=purchase_order,
            source_email=email,
            comparison_result=comparison_result,
        )

        # Update PO status to OA_MISMATCH if there are mismatches requiring manual review
        if comparison_result.requires_manual_review:
            from didero.orders.schemas import PurchaseOrderStatus

            if purchase_order.order_status != PurchaseOrderStatus.OA_MISMATCH:
                purchase_order.order_status = PurchaseOrderStatus.OA_MISMATCH
                purchase_order.save(update_fields=["order_status"])

                logger.info(
                    "Updated PO status to OA_MISMATCH due to validation mismatches",
                    po_id=purchase_order.id,
                    po_number=purchase_order.po_number,
                    oa_id=django_oa.pk,
                    oa_status=django_oa.oa_status,
                )

        return {
            "success": True,
            "oa_id": django_oa.pk,
            "oa_status": django_oa.oa_status,
            "has_mismatches": comparison_result.requires_manual_review,
        }

    except Exception as e:
        logger.error(
            "Failed to save OA",
            error=str(e),
            purchase_order_id=params["purchase_order_id"],
        )
        raise