# Order Acknowledgement (OA) Workflow

## Overview

The Order Acknowledgement workflow processes supplier confirmations of purchase orders, validating them against the original PO and storing the acknowledgement details for follow-up workflows.

## Workflow Trigger

The workflow is triggered when:
1. An email is received from a supplier
2. The email is categorized as an Order Acknowledgement
3. The email contains a reference to a valid Purchase Order

## Workflow Steps

### 1. Extract Order Acknowledgement Details (`extract_order_acknowledgement_details`)
- Uses AI to extract structured data from the email and attachments
- Extracts key fields:
  - PO number reference
  - Supplier's order/sales order number
  - Item details with quantities and prices
  - Promised ship dates and delivery dates
  - Shipping address and terms
  - Payment terms
  - Special charges (shipping, freight, etc.)

### 2. Compare OA to PO (`compare_oa_to_po_activity`)
- Uses the document comparison framework to validate OA against PO
- Applies acknowledge-by-omission logic:
  - Missing fields in OA are assumed to match PO values
  - Present fields in OA represent supplier's updates/confirmations
- Checks for mismatches in critical fields:
  - Item quantities
  - Prices
  - Delivery dates
- Returns a `DocumentComparisonResult` with validation findings

### 3. Save Order Acknowledgement (`save_oa_activity`)
- Creates the OrderAcknowledgement record using comparison results
- Determines OA status based on validation:
  - `EXTRACTED`: No mismatches found, ready for processing
  - `PENDING_CLARIFICATION`: Mismatches found, needs review
- Updates PO status to `OA_MISMATCH` if mismatches detected
- Links OA to email and documents (see below)

### 4. Post Validation Actions (`order_acknowledgement_post_validation_actions`)
- Creates human validation task if enabled for the team
- OR automatically accepts OA and moves PO to `AWAITING_SHIPMENT`
- Adds comments to the PO for audit trail
- Triggers follow-up workflows

## How Order Acknowledgements are Saved

### 1. OA Saving Flow Overview

The OA saving process follows different paths based on document comparison results:

```
Email → AI Extraction → Document Comparison → Save Decision
                                           ↓
                        ┌─────────────────────────────────┐
                        │     Has Mismatches?             │
                        └─────────────┬───────────────────┘
                                     │
                          ┌──────────┴──────────┐
                          │                     │
                    YES   │                     │   NO
                          ▼                     ▼
            ┌─────────────────────┐    ┌─────────────────────┐
            │ PENDING_CLARIFICATION│    │     EXTRACTED       │
            │ Status              │    │     Status          │
            │                     │    │                     │
            │ • Store AI data     │    │ • Store AI data     │
            │ • Mark for review   │    │ • Ready for use     │
            │ • Set PO to         │    │ • PO stays current  │
            │   OA_MISMATCH       │    │   status            │
            └─────────────────────┘    └─────────────────────┘
                          │                     │
                          │                     │
                          └──────────┬──────────┘
                                     │
                                     ▼
                        ┌─────────────────────────────────┐
                        │   Create OA with Links          │
                        │ • Email thread link             │
                        │ • Document links                │
                        │ • Item matching via cache       │
                        └─────────────────────────────────┘
```

### 2. Mismatch vs Non-Mismatch Handling

#### Non-Mismatch Scenario (EXTRACTED Status):
- **Document Comparison**: No critical differences found between OA and PO
- **OA Status**: Set to `EXTRACTED`
- **PO Status**: Remains unchanged (typically `ISSUED`)
- **Data Storage**: Save only AI-extracted fields using minimal approach
- **User Experience**: Flows to human validation or auto-acceptance
- **Next Steps**: Ready for completion when PO moves to `AWAITING_SHIPMENT`

#### Mismatch Scenario (PENDING_CLARIFICATION Status):
- **Document Comparison**: Critical differences detected (prices, quantities, dates)
- **OA Status**: Set to `PENDING_CLARIFICATION`
- **PO Status**: Updated to `OA_MISMATCH` for dashboard visibility
- **Data Storage**: Save AI-extracted fields + store comparison metadata
- **User Experience**: Creates high-priority task for manual review
- **Next Steps**: Requires human intervention to resolve discrepancies

### 3. OA Status Lifecycle and Updates

#### Initial OA Creation (`save_oa_activity`):
```python
# Determine status based on comparison results
if comparison_result.requires_manual_review:
    oa_status = "PENDING_CLARIFICATION"
    # Update PO to signal attention needed
    purchase_order.order_status = PurchaseOrderStatus.OA_MISMATCH
else:
    oa_status = "EXTRACTED" 
    # PO status unchanged
```

#### OA Completion via PO Status Change:
When PO status changes to `AWAITING_SHIPMENT`, the `complete_acknowledgements_for_po` method is triggered:

```python
def complete_acknowledgements_for_po(self, purchase_order):
    """Complete OAs when PO moves to AWAITING_SHIPMENT"""
    for oa in purchase_order.order_acknowledgements.filter(oa_status="EXTRACTED"):
        # Apply acknowledge-by-omission logic
        self._apply_po_defaults_to_oa(oa, purchase_order)
```

#### Human Validation Updates:
- **Confirm Action**: User accepts OA → PO status changes to `AWAITING_SHIPMENT` → OA becomes `COMPLETE/INCOMPLETE`
- **Reject Action**: User rejects OA → PO status remains unchanged → OA stays `PENDING_CLARIFICATION`

### 4. OA Creation Process (`create_oa_from_extraction`)

The OA creation uses a "minimal approach" - only AI-extracted data is stored:

```python
# Key steps in OA creation:
1. Map AI-extracted fields to OA model fields
2. Handle shipping address (only if valid with country)
3. Determine OA status based on comparison results
4. Create OA record with minimal field mappings
5. Create OA items with PO item linking via DocumentItemMatch
6. Apply global dates to items missing specific dates
7. Create line items for special charges
8. Link OA to source email and documents
```

### 5. Acknowledge-by-Omission Logic

When OA is completed (status changes to `COMPLETE`):
- **Missing fields**: Copy values from PO (supplier acknowledges PO terms)
- **Present fields**: Keep OA values (supplier provides updates)
- **Critical fields**: Always preserve OA values:
  - Promised ship/delivery dates
  - Supplier order number
  - Special terms or instructions

### 6. Status Change Triggers

#### Automatic Status Changes:
- **AI Processing**: `null` → `EXTRACTED` or `PENDING_CLARIFICATION`
- **PO Status Change**: `EXTRACTED` → `COMPLETE` when PO becomes `AWAITING_SHIPMENT`
- **Mismatch Detection**: PO status → `OA_MISMATCH` when validation fails

#### Manual Status Changes:
- **User Confirmation**: Any status → `COMPLETE` via task action
- **User Rejection**: `PENDING_CLARIFICATION` → remains same, PO reset
- **Admin Override**: Direct status change via Django admin

### 2. Item Creation and Matching
- OA items are linked to PO items using `DocumentItemMatch` table
- Handles cases where supplier uses different item numbers
- Matches items by:
  1. Item number (using cached AI matches)
  2. Quantity and price (for ambiguous cases)
- Preserves promised dates at item level

### 3. Global Date Handling
If the OA has global promised dates:
- `global_promised_ship_date`
- `global_promised_delivery_date`

These are applied to individual items that don't have specific dates.

### 4. Email and Document Linking
The `link_oa_to_email_and_documents` utility:
- Links the OA to the email thread via `EmailThreadToOrderAcknowledgementLink`
- Links any PDF/documents from the email via `DocumentLink`
- Provides audit trail of source documents

## Status Determination Logic

### OA Status:
- **COMPLETE**: Has order number, PO number, and all items have ship dates
- **INCOMPLETE**: Has basic info but missing some item dates
- **PENDING_CLARIFICATION**: Missing critical information

### PO Status Updates:
- **OA_MISMATCH**: Set when validation finds mismatches
- **AWAITING_SHIPMENT**: Set after OA is accepted (manual or auto)

## Acknowledge-by-Omission Logic

The workflow implements "acknowledge-by-omission" validation:
- If a field is present in the OA, it represents the supplier's value
- If a field is missing from the OA, it's assumed to match the PO
- This reduces false positives for incomplete OAs

## Human Validation vs Auto-Acceptance

Controlled by team setting: `TEAM_ORDER_ACK_HUMAN_VALIDATION_ENABLED`

### Human Validation Flow:
1. Create task for user review
2. User sees OA details and mismatches
3. User can:
   - Confirm: Moves PO to AWAITING_SHIPMENT
   - Reject: Keeps PO in current state

### Auto-Acceptance Flow:
1. Skip human validation task
2. Automatically move PO to AWAITING_SHIPMENT
3. Create notification for user awareness

## OA Completion from PO Status Change

When a PO status changes to `AWAITING_SHIPMENT` from any state:
1. `complete_acknowledgements_for_po` is triggered
2. For OAs in EXTRACTED status:
   - Copy missing data from PO (acknowledge-by-omission)
   - Mark OA as COMPLETE
   - Apply item-level acknowledgements
3. This ensures OAs are properly completed for follow-up workflows

## Error Handling

- **Address Validation**: Skip invalid addresses (missing country)
- **Item Matching**: Log warnings but continue if items can't be matched
- **Document Linking**: Wrapped in try-catch to prevent workflow failure
- **Temporal Retries**: Activities are retryable for transient errors

## Key Models and Relationships

```
PurchaseOrder
  └── OrderAcknowledgement (via purchase_order FK)
       ├── OrderAcknowledgementItem (via order_acknowledgement FK)
       │    └── Links to PO's OrderItem (via order_item FK)
       ├── OrderAcknowledgmentLineItem (special charges)
       └── Linked to:
            ├── EmailThread (via EmailThreadToOrderAcknowledgementLink)
            └── Documents (via DocumentLink)
```

## Configuration

- Workflow ID: `purchase_order_acknowledgement`
- Queue: `order_ack_queue`
- Team settings:
  - `TEAM_ORDER_ACK_HUMAN_VALIDATION_ENABLED`: Enable/disable human review

## Testing

### Method 1: Using Real Email
1. Ensure Temporal workers are running
2. Use an email with OA content and PO reference
3. Trigger workflow via email categorization
4. Verify OA creation and document linking
5. Check PO status updates

### Method 2: End-to-End Test Script
Run the comprehensive test script that creates test data and triggers the workflow:

```bash
source .env && DJANGO_SETTINGS_MODULE=didero.settings.common uv run python didero/workflows/core_workflows/order_ack/scripts/end_to_end_test.py
```

This script:
- Creates a test PO with items
- Creates a test email with OA content
- Triggers the OA workflow
- Monitors workflow execution
- Verifies OA creation and item matching
- Cleans up test data

### Method 3: Manual Workflow Trigger
```python
from didero.workflows.utils import run_workflow
from didero.suppliers.models import Communication

# Get the email
email = Communication.objects.get(id=YOUR_EMAIL_ID)

# Prepare workflow parameters
params = {
    "email_id": str(email.pk),
}

# Run the workflow (workflow ID 57 is purchase_order_acknowledgement)
result = run_workflow(workflow_id=57, params=params)
```

Note: Restart Temporal workers after code changes for updates to take effect.