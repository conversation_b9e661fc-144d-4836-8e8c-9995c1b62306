from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Dict, Optional

from pydantic import BaseModel
from temporalio import workflow
from temporalio.common import RetryPolicy
from temporalio.exceptions import ApplicationError

from didero.workflows.retry_utils import get_retry_policy

# Use unsafe imports for activity functions and Django models
with workflow.unsafe.imports_passed_through():
    import structlog

    # Import comparison result type
    from didero.ai.document_matching.schemas import DocumentComparisonResult

    # Import Django models needed in workflow
    # Import DAG OA activity for post-validation (still needed)
    from didero.workflows.core.nodes.purchase_orders.order_acknowledgement.order_ack import (
        order_acknowledgement_post_validation_actions,
    )

    # Import DAG OA schemas (still needed for post-validation)
    from didero.workflows.core.nodes.purchase_orders.order_acknowledgement.schemas import (
        OrderAcknowledgementPostValidationActionsParams,
        ValidateOrderAcknowledgementInfoParams,
    )

    # Import shared core workflow activities
    from didero.workflows.core_workflows.activities import (
        get_task_assignment_user_id,
        load_workflow_config,
    )

    # Import document comparison activities
    from didero.workflows.core_workflows.order_ack.activities import (
        OASaveResult,
        compare_oa_to_po_activity,
        save_oa_activity,
    )
    
    # Import unified supplier document operations
    from didero.workflows.shared_activities.supplier_document_operations import (
        SupplierDocumentExtractionResult,
        extract_and_resolve_supplier_document,
    )

    # Import config schema
    from didero.workflows.core_workflows.order_ack.schemas import (
        OrderAcknowledgementBehaviorConfig,
    )

    # Import shared document matching activity
    from didero.workflows.shared_activities.document_matching import (
        create_document_match_review_task_activity,
    )

    # Import shared activity schemas
    from didero.workflows.shared_activities.schemas import (
        DocumentInfo,
        DocumentMatchContext,
        MatchingResult,
        TaskAssignment,
        TaskMetadata,
    )

    logger = structlog.get_logger(__name__)


class OrderAcknowledgementParams(BaseModel):
    email_id: str
    team_id: str
    workflow_id: str  # The UserWorkflow ID for loading configuration


class OrderAcknowledgementResult(BaseModel):
    success: bool
    error: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


@workflow.defn
class OrderAcknowledgementWorkflow:
    """Core Order Acknowledgement workflow implementation with behavior configuration"""

    @workflow.run
    async def run(
        self, workflow_id: str, params: OrderAcknowledgementParams
    ) -> Dict[str, Any]:
        self.workflow_id = workflow_id

        # Load workflow configuration (generic for now)
        self.config = await self._load_workflow_config(params.workflow_id)

        email_id = params.email_id
        team_id = params.team_id

        self._init_metrics()

        if not email_id or not team_id:
            self._record_workflow_attempt(
                team_id=team_id, success=False, reason="missing_parameters"
            )
            raise ApplicationError(
                "Missing required parameters: email_id and team_id",
                type="ValidationError",
                non_retryable=True,
            )

        # Variables for workflow tracking (removed unused variables)

        try:
            # Step 1: Extract OA details from email with smart PO resolution
            extraction_params = {
                "document_type": "order_acknowledgement",
                "email_id": email_id,
                "team_id": int(team_id),
                "enable_po_auto_creation": self.config.enable_po_auto_creation,
            }

            try:
                extracted_result = await workflow.execute_activity(
                    extract_and_resolve_supplier_document,
                    extraction_params,
                    start_to_close_timeout=timedelta(
                        minutes=10
                    ),  # Longer timeout for potential auto-creation
                    retry_policy=self._get_retry_policy(),
                )

                # Convert the result to the format expected by the rest of the workflow
                extracted = {
                    "order_acknowledgement": extracted_result["document_details"],
                    "purchase_order_id": extracted_result["purchase_order_id"],
                    "email_id": extracted_result["email_id"],
                }

                # Log if PO was auto-created
                if extracted_result.get("auto_created_po"):
                    logger.info(
                        f"PO was auto-created for order acknowledgement",
                        po_id=extracted["purchase_order_id"],
                        email_id=email_id,
                    )

            except ApplicationError as e:
                # Non-retryable error (validation, not found, etc.)
                logger.error(f"Failed to extract OA details or resolve PO: {e}")
                self._record_workflow_attempt(
                    team_id=team_id, success=False, reason="extraction_failed"
                )
                # Re-raise to fail the workflow properly
                raise

            # Step 2: Compare OA to PO using document comparison framework
            comparison_params = {
                "order_acknowledgement": extracted["order_acknowledgement"],
                "purchase_order_id": extracted["purchase_order_id"],
                "team_id": team_id,
                "tolerance_config": {},  # Use default tolerance config
            }

            try:
                comparison_data = await workflow.execute_activity(
                    compare_oa_to_po_activity,
                    comparison_params,
                    start_to_close_timeout=timedelta(minutes=5),
                    retry_policy=self._get_retry_policy(),
                )
                comparison_result_data = comparison_data["comparison_result"]
                po_data = comparison_data["po_data"]

                # Convert dict back to DocumentComparisonResult if needed
                if isinstance(comparison_result_data, dict):
                    comparison_result = DocumentComparisonResult.model_validate(
                        comparison_result_data
                    )
                else:
                    comparison_result = comparison_result_data
            except ApplicationError as e:
                logger.error(f"Failed to compare OA to PO: {e}")
                self._record_workflow_attempt(
                    team_id=team_id, success=False, reason="comparison_failed"
                )
                raise

            # Step 3: Save OA based on comparison results
            save_params = {
                "order_acknowledgement": extracted["order_acknowledgement"],
                "comparison_result": comparison_result,
                "purchase_order_id": extracted["purchase_order_id"],
                "email_id": extracted["email_id"],
            }

            try:
                save_result = await workflow.execute_activity(
                    save_oa_activity,
                    save_params,
                    start_to_close_timeout=timedelta(minutes=5),
                    retry_policy=self._get_retry_policy(),
                )
            except ApplicationError as e:
                logger.error(f"Failed to save OA: {e}")
                self._record_workflow_attempt(
                    team_id=team_id, success=False, reason="save_failed"
                )
                raise

            # Step 4: Handle validation results and create appropriate tasks
            # Two types of tasks can be created:
            # 1. Document review tasks (when there are mismatches)
            # 2. Human validation tasks (when everything matches but human approval is required)

            if comparison_result.requires_manual_review:
                # Create document review task for mismatches
                await self._create_document_review_task(
                    extracted, save_result, comparison_result, team_id, po_data
                )
            elif self.config.require_human_validation:
                # Create human validation task for approval (following original workflow pattern)
                await self._create_human_validation_task(
                    extracted, save_result, comparison_result, team_id
                )
            # If no mismatches and no human validation required, auto-approve (no task needed)

            # Build details with the result
            details = {
                "oa_id": save_result["oa_id"],
                "oa_status": save_result["oa_status"],
                "has_mismatches": save_result["has_mismatches"],
                "match_result": comparison_result.match_result.value,
                "matching_score": comparison_result.matching_score,
                "requires_manual_review": comparison_result.requires_manual_review,
            }

            # Record successful workflow completion
            self._record_workflow_attempt(team_id=team_id, success=True, reason=None)

            return OrderAcknowledgementResult(
                success=True, details=details
            ).model_dump()

        except Exception as e:
            logger.error(f"Order Acknowledgement Workflow failed: {str(e)}")
            # Re-raise to let Temporal handle retries for transient errors
            raise

    async def _load_workflow_config(
        self, workflow_id: str
    ) -> OrderAcknowledgementBehaviorConfig:
        """
        Load workflow configuration using an activity to avoid threading issues.
        """
        try:
            # Call the shared activity to load config
            config_dict = await workflow.execute_activity(
                load_workflow_config,
                args=[workflow_id, "order_ack"],
                start_to_close_timeout=timedelta(seconds=30),
                retry_policy=RetryPolicy(
                    maximum_attempts=3,
                    initial_interval=timedelta(seconds=1),
                    maximum_interval=timedelta(seconds=10),
                    backoff_coefficient=2.0,
                ),
            )

            # Convert dict back to pydantic model
            # If None, create default config
            if config_dict is None:
                return OrderAcknowledgementBehaviorConfig()
            return OrderAcknowledgementBehaviorConfig.model_validate(config_dict)

        except Exception as e:
            logger.error(f"Failed to load workflow config via activity: {str(e)}")
            # Return default config on error
            return OrderAcknowledgementBehaviorConfig()

    def _get_retry_policy(self) -> RetryPolicy:
        return get_retry_policy(self.config)

    async def _create_document_review_task(
        self,
        extracted: ValidateOrderAcknowledgementInfoParams,
        save_result: OASaveResult,
        comparison_result: DocumentComparisonResult,
        team_id: str,
        po_data: Dict[str, Any],
    ) -> None:
        """Create a document review task for mismatches"""
        # Prepare context for task creation following invoice pattern
        context = DocumentMatchContext(
            model_id=str(save_result["oa_id"]),
            model_type_name="OrderAcknowledgement",
            team_id=int(team_id),
            document1=DocumentInfo(
                doc_type="ORDER_ACKNOWLEDGEMENT",
                reference=getattr(
                    extracted["order_acknowledgement"], "order_number", "OA-Unknown"
                ),
                doc_id=str(save_result["oa_id"]),
                data=extracted["order_acknowledgement"].model_dump()
                if hasattr(extracted["order_acknowledgement"], "model_dump")
                else {},
            ),
            document2=DocumentInfo(
                doc_type="PURCHASE_ORDER",
                reference=f"PO-{extracted['purchase_order_id']}",
                doc_id=str(extracted["purchase_order_id"]),
                data=po_data,
            ),
        )

        matching_result = MatchingResult(
            match_result=comparison_result.match_result.value,
            matching_score=comparison_result.matching_score,
            review_reason=comparison_result.summary,
            comparison_summary=comparison_result.summary,
            comparison_data={
                "header_comparison": comparison_result.header_comparison.model_dump(),
                "line_items_comparison": [
                    item.model_dump()
                    for item in comparison_result.line_items_comparison
                ],
            },
        )

        # Extract supplier name and total amount from the data
        supplier_name = "Supplier"  # Placeholder until we can get from PO
        total_amount = "0.00"

        # Get total amount from OrderAcknowledgement schema
        try:
            if hasattr(extracted["order_acknowledgement"], "total_amount"):
                total_amount = str(
                    extracted["order_acknowledgement"].total_amount or "0.00"
                )
            elif (
                isinstance(extracted["order_acknowledgement"], dict)
                and "total_amount" in extracted["order_acknowledgement"]
            ):
                total_amount = str(
                    extracted["order_acknowledgement"]["total_amount"] or "0.00"
                )
        except Exception:
            total_amount = "0.00"

        metadata = TaskMetadata(
            supplier_name=supplier_name,
            total_amount=total_amount,
        )

        # Configure task assignment - get user for this PO
        user_id = await workflow.execute_activity(
            get_task_assignment_user_id,
            args=[str(extracted["purchase_order_id"])],
            start_to_close_timeout=timedelta(seconds=30),
            retry_policy=self._get_retry_policy(),
        )

        if user_id is None:
            logger.warning(
                f"No task assignment user found for PO {extracted['purchase_order_id']}, skipping document review task"
            )
            return

        user_ids = [user_id]
        user_group_names = []  # Not using user groups for now

        assignment = TaskAssignment(
            user_ids=user_ids,
            user_group_names=user_group_names,
        )

        try:
            task_result = await workflow.execute_activity(
                create_document_match_review_task_activity,
                args=[context, matching_result, metadata, assignment, "PURCHASE_ORDER"],
                start_to_close_timeout=timedelta(minutes=5),
                retry_policy=self._get_retry_policy(),
            )

            if not task_result.get("success"):
                logger.error(
                    f"Failed to create document review task: {task_result.get('error_message')}"
                )

        except ApplicationError as e:
            logger.error(f"Failed to create document review task: {e}")
            # Don't fail the workflow if task creation fails

    async def _create_human_validation_task(
        self,
        extracted: ValidateOrderAcknowledgementInfoParams,
        save_result: OASaveResult,
        comparison_result: DocumentComparisonResult,
        team_id: str,
    ) -> None:
        """Create a human validation task for approval (following original workflow pattern)"""
        # Use the original post-validation actions that creates proper human validation task
        post_validation_params = OrderAcknowledgementPostValidationActionsParams(
            order_acknowledgement=extracted["order_acknowledgement"],
            purchase_order_id=extracted["purchase_order_id"],
            email_id=extracted["email_id"],
        )

        try:
            human_validation_bypassed = await workflow.execute_activity(
                order_acknowledgement_post_validation_actions,
                post_validation_params,
                start_to_close_timeout=timedelta(minutes=5),
                retry_policy=self._get_retry_policy(),
            )

            logger.info(
                f"Human validation task created, bypassed: {human_validation_bypassed}"
            )

        except ApplicationError as e:
            logger.error(f"Failed to create human validation task: {e}")
            # Don't fail the workflow if task creation fails

    def _init_metrics(self):
        """
        Initialize workflow-specific metrics
        """
        meter = workflow.metric_meter()

        # Counter for total workflow attempts
        self.workflow_attempts = meter.create_counter(
            "workflow_order_acks",
            description="Total number of order ack workflow attempts",
            unit="1",
        )

        self.order_ack_duration = meter.create_histogram(
            "workflow_order_ack_duration",
            description="Time taken to acknowledge an order",
            unit="ms",
        )

    def _record_workflow_attempt(
        self,
        team_id: str,
        success: bool,
        reason: str | None = None,
    ):
        """Record a workflow attempt"""
        labels = {"team_id": team_id, "success": success}
        if not success:
            labels["reason"] = reason or "other"
        self.workflow_attempts.add(1, labels)

        self.order_ack_duration.record(
            int((workflow.now() - workflow.info().start_time).total_seconds() * 1000),
            {"success": success},
        )
