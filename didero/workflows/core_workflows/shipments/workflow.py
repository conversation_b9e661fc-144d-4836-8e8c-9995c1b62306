from dataclasses import dataclass
from datetime import timedelta
from typing import Any, Dict, Optional, Union

from temporalio import workflow
from temporalio.common import RetryPolicy
from temporalio.exceptions import ApplicationError

from didero.workflows.retry_utils import get_retry_policy

# Use unsafe imports for activity functions and Django models
with workflow.unsafe.imports_passed_through():
    # Import DAG activities that are still needed
    from didero.workflows.core.nodes.purchase_orders.shipments import (
        ParseShipmentParams,
        ShipmentDetails,
        check_notification_type,
        create_shipment_from_details,
        process_pickup_notification,
    )
    from didero.workflows.core_workflows.activities import load_workflow_config

    # Import unified supplier document operations
    from didero.workflows.shared_activities.supplier_document_operations import (
        SupplierDocumentExtractionResult,
        extract_and_resolve_supplier_document,
    )
    from didero.workflows.core_workflows.shipments.schemas import ShipmentBehaviorConfig


@dataclass
class ShipmentWorkflowParams:
    """Parameters for shipment workflow"""

    email_id: str
    team_id: str
    workflow_id: str
    notification_type: Optional[str] = None  # "shipped" or "pickup"


@dataclass
class ShipmentWorkflowResult:
    """Result from shipment workflow execution"""

    success: bool
    notification_type: str
    error_message: Optional[str] = None
    shipment_id: Optional[str] = None
    po_number: Optional[str] = None


@workflow.defn
class ShipmentWorkflow:
    """Core Shipment workflow implementation with behavior configuration"""

    @workflow.run
    async def run(self, workflow_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main workflow execution

        Args:
            workflow_id: The UserWorkflow ID
            params: Must contain 'email_id', 'team_id', 'workflow_id', and optional 'notification_type'

        Returns:
            Dict with success status and shipment details
        """
        self.logger = workflow.logger
        self.workflow_id = workflow_id

        # Load workflow configuration
        # Use the workflow_id parameter passed to run method
        self.config = await self._load_workflow_config(workflow_id)

        # Initialize metrics
        self._init_metrics()

        email_id = params.get("email_id")
        team_id = params.get("team_id")
        notification_type = params.get("notification_type", "shipped")

        # Store team_id for use in error handling
        self.team_id = team_id or "unknown"

        if not email_id or not team_id:
            self._record_workflow_attempt(
                team_id=team_id or "none", success=False, reason="missing_parameters"
            )
            return {
                "success": False,
                "error": "Missing required parameters: email_id and team_id",
            }

        # Check if workflow is enabled
        if not self.config.enabled:
            self._record_workflow_attempt(
                team_id=team_id, success=False, reason="workflow_disbaled"
            )
            return {
                "success": False,
                "error": "Workflow is disabled for this team",
            }

        success = False
        reason = None
        notification_type_result = None

        try:
            # Step 1: Determine notification type (shipped vs pickup)
            is_pickup = await self._check_notification_type(email_id, notification_type)

            if is_pickup:
                # Handle pickup notification
                result = await self._handle_pickup_notification(email_id, team_id)
                notification_type_result = "pickup"
            else:
                # Handle shipment notification
                result = await self._handle_shipment_notification(email_id, team_id)
                notification_type_result = "shipment"

            # Set success directly based on what we know from the handler methods
            if result.get("success", False):
                success = True
            else:
                success = False
                reason = result.get("error_type", "processing_failed")

            return result

        except ApplicationError as e:
            # Record failure metrics for non-retryable errors
            duration = (workflow.now() - workflow.info().start_time).total_seconds()
            self.shipment_processing_duration.record(
                int(duration * 1000),
                {
                    "team_id": team_id,
                    "success": "false",
                    "error_type": "ApplicationError",
                },
            )
            self._record_workflow_attempt(
                team_id=team_id,
                success=False,
                reason="ApplicationError",
            )

            self.logger.error(f"Workflow failed with non-retryable error: {str(e)}")
            # Re-raise to fail the workflow
            raise
        except Exception as e:
            # Record failure metrics for other errors
            duration = (workflow.now() - workflow.info().start_time).total_seconds()
            self.shipment_processing_duration.record(
                int(duration * 1000),
                {
                    "team_id": team_id,
                    "success": "false",
                    "error_type": type(e).__name__,
                },
            )
            self._record_workflow_attempt(
                team_id=team_id,
                success=success,
                reason=reason,
                notification_type=notification_type_result,
            )

            self.logger.error(f"Workflow failed with unexpected error: {str(e)}")
            # Re-raise to let Temporal handle retries
            raise

    async def _load_workflow_config(self, workflow_id: str) -> ShipmentBehaviorConfig:
        """
        Load workflow configuration using an activity to avoid threading issues.
        """
        try:
            # Call the shared activity to load config
            config_dict = await workflow.execute_activity(
                load_workflow_config,
                args=[workflow_id, "shipments"],
                start_to_close_timeout=timedelta(seconds=30),
                retry_policy=RetryPolicy(
                    maximum_attempts=3,
                    initial_interval=timedelta(seconds=1),
                    maximum_interval=timedelta(seconds=10),
                    backoff_coefficient=2.0,
                ),
            )

            # Convert dict back to pydantic model
            # If None, create default config
            if config_dict is None:
                return ShipmentBehaviorConfig()
            return ShipmentBehaviorConfig.model_validate(config_dict)

        except Exception as e:
            self.logger.error(f"Failed to load workflow config via activity: {str(e)}")
            # Return default config on error
            return ShipmentBehaviorConfig()

    def _init_metrics(self):
        """
        Initialize workflow-specific metrics
        """
        meter = workflow.metric_meter()
        self.shipment_processed_counter = meter.create_counter(
            "workflow_shipments_processed",
            description="Total number of shipment processing workflow attempts",
            unit="1",
        )
        self.validation_failures = meter.create_counter(
            "workflow_shipment_validation_failures",
            description="Number of shipment validation failures",
            unit="1",
        )
        self.shipment_extraction_errors = meter.create_counter(
            "workflow_shipment_extraction_errors",
            description="Number of shipment extraction or PO resolution errors",
            unit="1",
        )
        self.shipment_processing_duration = meter.create_histogram(
            "workflow_shipment_processing_duration",
            description="Time taken to process shipment",
            unit="ms",
        )

    def _record_workflow_attempt(
        self,
        team_id: str,
        success: bool,
        reason: str | None = None,
        notification_type: str | None = None,
    ):
        """Record a workflow attempt"""
        labels = {"team_id": team_id, "success": success}
        if success:
            labels["notification_type"] = notification_type
        else:
            labels["reason"] = reason or "other"
        self.shipment_processed_counter.add(1, labels)

        self.shipment_processing_duration.record(
            int((workflow.now() - workflow.info().start_time).total_seconds() * 1000),
            {"success": success},
        )

    async def _check_notification_type(
        self, email_id: str, notification_type: str
    ) -> bool:
        """
        Check if this is a pickup notification

        Returns:
            True if pickup, False if shipment
        """
        # Create params for the activity
        params = {"email_id": email_id, "notification_type": notification_type}

        # Execute the check_notification_type activity
        result = await workflow.execute_activity(
            check_notification_type,
            params,
            start_to_close_timeout=timedelta(seconds=30),
            retry_policy=RetryPolicy(maximum_attempts=2),
        )

        # Handle both dict and object returns
        if isinstance(result, dict):
            return result["condition_met"]
        else:
            return result.condition_met  # type: ignore

    async def _handle_pickup_notification(
        self, email_id: str, team_id: str
    ) -> Dict[str, Any]:
        """
        Handle pickup notification workflow path
        """
        try:
            # Process pickup notification
            params = {"email_id": email_id}

            await workflow.execute_activity(
                process_pickup_notification,
                params,
                start_to_close_timeout=timedelta(minutes=3),
                retry_policy=RetryPolicy(
                    maximum_attempts=3,
                    initial_interval=timedelta(seconds=10),
                    maximum_interval=timedelta(seconds=60),
                    backoff_coefficient=2.0,
                ),
            )

            return {
                "success": True,
                "notification_type": "pickup",
                "message": "Pickup notification processed successfully",
            }

        except ApplicationError as e:
            # Handle non-retryable errors
            self.logger.error(
                f"Non-retryable error processing pickup notification: {str(e)}"
            )
            # Re-raise to fail the workflow properly
            raise
        except Exception as e:
            # Transient errors will be retried by Temporal
            self.logger.error(f"Failed to process pickup notification: {str(e)}")
            raise  # Let Temporal handle retry

    async def _handle_shipment_notification(
        self, email_id: str, team_id: str
    ) -> Dict[str, Any]:
        """
        Handle shipment notification workflow path with smart PO resolution
        """
        try:
            # Step 1: Extract shipment details and resolve PO with optional auto-creation
            extraction_params = {
                "document_type": "shipment",
                "email_id": email_id,
                "team_id": int(team_id),
                "enable_po_auto_creation": self.config.enable_po_auto_creation,
            }

            try:
                extraction_result = await workflow.execute_activity(
                    extract_and_resolve_supplier_document,
                    extraction_params,
                    start_to_close_timeout=timedelta(
                        minutes=10
                    ),  # Longer timeout for potential auto-creation
                    retry_policy=self._get_retry_policy(),
                )

                shipment_details = extraction_result["document_details"]

                # Log if PO was auto-created
                if extraction_result.get("auto_created_po"):
                    self.logger.info(
                        f"PO was auto-created for shipment",
                        po_id=extraction_result["purchase_order_id"],
                        email_id=email_id,
                    )

            except ApplicationError as e:
                # Non-retryable error (validation, not found, etc.)
                self.logger.error(
                    f"Failed to extract shipment details or resolve PO: {e}"
                )

                # Track as extraction failure
                self.shipment_extraction_errors.add(
                    1,
                    {
                        "team_id": team_id,
                        "error_type": e.type if hasattr(e, "type") else "unknown",
                    },
                )

                raise

            # Step 2: Validate based on configuration
            # Handle both dict and object cases
            tracking_number = (
                shipment_details.get("tracking_number")
                if isinstance(shipment_details, dict)
                else getattr(shipment_details, "tracking_number", None)
            )

            if self.config.validate_tracking_number and not tracking_number:
                self.validation_failures.add(
                    1,
                    {
                        "team_id": team_id,
                        "validation_type": "tracking_number_missing",
                    },
                )
                # Raise ApplicationError for validation failure
                raise ApplicationError(
                    "Tracking number is required but missing",
                    type="ValidationError",
                    non_retryable=True,
                )

            # Step 3: Create shipment records
            await self._create_shipment(shipment_details)

            # Get PO number handling both dict and object
            po_number = (
                shipment_details.get("purchase_order_number")
                if isinstance(shipment_details, dict)
                else getattr(shipment_details, "purchase_order_number", None)
            )

            return {
                "success": True,
                "notification_type": "shipment",
                "message": "Shipment processed successfully",
                "po_number": po_number,
            }

        except ApplicationError:
            # Re-raise ApplicationError to fail the workflow
            raise
        except Exception as e:
            # Transient errors will be retried by Temporal
            self.logger.error(f"Failed to process shipment notification: {str(e)}")
            raise  # Let Temporal handle retry

    async def _parse_shipment_details(self, email_id: str) -> Dict[str, Any]:
        """
        Parse shipment details from email
        """
        try:
            params = ParseShipmentParams(email_id=email_id)

            # Execute the parse activity
            shipment_details = await workflow.execute_activity(
                parse_shipment_details_from_email,
                params,
                start_to_close_timeout=timedelta(minutes=5),
                retry_policy=RetryPolicy(
                    maximum_attempts=3,
                    initial_interval=timedelta(seconds=10),
                    maximum_interval=timedelta(seconds=60),
                    backoff_coefficient=2.0,
                ),
            )

            return {"success": True, "shipment_details": shipment_details}

        except ApplicationError as e:
            # Handle non-retryable errors (validation, resource not found, etc.)
            self.logger.error(f"Non-retryable error parsing shipment details: {str(e)}")

            # Track validation failures
            error_type = (
                "validation_error"
                if "ValidationError" in e.type
                else "resource_not_found"
            )
            self.validation_failures.add(
                1,
                {
                    "team_id": self.team_id if hasattr(self, "team_id") else "unknown",
                    "error_type": error_type,
                },
            )

            # Re-raise to fail the workflow properly
            raise

        except Exception as e:
            # Transient errors will be retried by Temporal
            self.logger.error(f"Failed to parse shipment details: {str(e)}")
            raise  # Let Temporal handle retry

    async def _create_shipment(
        self, shipment_details: Union[Dict[str, Any], ShipmentDetails]
    ) -> Dict[str, Any]:
        """
        Create shipment records
        """
        try:
            # Execute the create shipment activity
            # The activity handles human validation checks internally
            await workflow.execute_activity(
                create_shipment_from_details,
                shipment_details,  # type: ignore
                start_to_close_timeout=timedelta(minutes=5),
                retry_policy=RetryPolicy(
                    maximum_attempts=3,
                    initial_interval=timedelta(seconds=10),
                    maximum_interval=timedelta(seconds=60),
                    backoff_coefficient=2.0,
                ),
            )

            return {"success": True}

        except ApplicationError as e:
            # Handle non-retryable errors (duplicate shipment, resource not found)
            self.logger.error(f"Non-retryable error creating shipment: {str(e)}")

            # Check if it's a duplicate shipment error
            if hasattr(e, "type") and "DuplicateResourceError" in e.type:
                # Duplicate shipment is not necessarily a failure - workflow can continue
                self.logger.info("Shipment already exists, continuing workflow")
                return {"success": True}

            # Re-raise other non-retryable errors to fail the workflow
            raise
        except Exception as e:
            # Transient errors will be retried by Temporal
            self.logger.error(f"Failed to create shipment: {str(e)}")
            raise  # Let Temporal handle retry

    def _get_retry_policy(self) -> RetryPolicy:
        """Get retry policy from configuration"""
        return get_retry_policy(self.config)
