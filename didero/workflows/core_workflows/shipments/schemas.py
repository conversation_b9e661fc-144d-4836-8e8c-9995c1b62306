from typing import Literal

from pydantic import Field

from didero.workflows.schemas import WorkflowBehaviorConfigBase


class ShipmentBehaviorConfig(WorkflowBehaviorConfigBase):
    """Shipment workflow specific configuration"""

    # Core behavior flags
    require_human_validation: bool = (
        False  # Replaces TEAM_SHIPMENT_HUMAN_VALIDATION_ENABLED
    )

    # Validation controls
    validate_tracking_number: bool = True  # Whether tracking number is required
    validate_carrier: bool = True  # Whether to validate carrier name
    validate_order_items: bool = True  # Whether to validate order items match
    validate_shipment_date: bool = True  # Whether shipment date is required

    # Team-specific requirements
    tracking_number_required_for_teams: list[str] = Field(
        default_factory=list
    )  # Team IDs that require tracking numbers

    # Extension points for team-specific customizations
    post_parse_activities: list[str] = Field(default_factory=list)
    pre_shipment_creation_activities: list[str] = Field(default_factory=list)
    post_shipment_creation_activities: list[str] = Field(default_factory=list)

    # ERP Integration
    enable_erp_sync: bool = Field(
        default=False, description="Enable automatic ERP synchronization"
    )

    erp_sync_mode: Literal["automatic", "manual"] = Field(
        default="manual",
        description="When to trigger ERP sync: automatic (immediately) or manual (on human confirmation)",
    )

    erp_sync_timeout_seconds: int = Field(
        default=30, description="Timeout for ERP sync operations"
    )
