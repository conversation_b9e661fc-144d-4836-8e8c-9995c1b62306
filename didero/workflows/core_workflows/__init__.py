"""
Core workflow implementations and mappings.
This module contains the mapping of workflow types to their core workflow classes.
"""

from typing import Dict, Set, Type

from didero.workflows.core_workflows.follow_up.workflow import FollowUpWorkflow
from didero.workflows.core_workflows.invoice_processing.schemas import (
    InvoiceProcessingBehaviorConfig,
)
from didero.workflows.core_workflows.invoice_processing.workflow import (
    InvoiceProcessingWorkflow,
)
from didero.workflows.core_workflows.order_ack.schemas import (
    OrderAcknowledgementBehaviorConfig,
)
from didero.workflows.core_workflows.order_ack.workflow import (
    OrderAcknowledgementWorkflow,
)
from didero.workflows.core_workflows.po_creation.schemas import (
    POCreationBehaviorConfig,
)
from didero.workflows.core_workflows.po_creation.workflow import POCreationWorkflow
from didero.workflows.core_workflows.shipments.schemas import (
    ShipmentBehaviorConfig,
)
from didero.workflows.core_workflows.shipments.workflow import ShipmentWorkflow
from didero.workflows.schemas import (
    WorkflowBehaviorConfigBase,
    WorkflowType,
)

# Mapping of workflow types to their configuration classes
# Note: Follow-up workflow uses team settings directly, no separate config class needed
WORKFLOW_CONFIG_CLASSES: Dict[WorkflowType, Type[WorkflowBehaviorConfigBase]] = {
    WorkflowType.PURCHASE_ORDER_CREATION: POCreationBehaviorConfig,
    WorkflowType.PURCHASE_ORDER_ACKNOWLEDGEMENT: OrderAcknowledgementBehaviorConfig,
    WorkflowType.PURCHASE_ORDER_SHIPPED: ShipmentBehaviorConfig,
    # WorkflowType.PURCHASE_ORDER_FOLLOW_UP: No config class - uses team settings
    WorkflowType.INVOICE_PROCESSING: InvoiceProcessingBehaviorConfig,
}

# Set of workflow types that have core implementations
CORE_WORKFLOW_TYPES: Set[WorkflowType] = {
    WorkflowType.PURCHASE_ORDER_CREATION,
    WorkflowType.PURCHASE_ORDER_ACKNOWLEDGEMENT,
    WorkflowType.PURCHASE_ORDER_SHIPPED,
    WorkflowType.PURCHASE_ORDER_FOLLOW_UP,
    WorkflowType.INVOICE_PROCESSING,
    # Add other core workflow types as we implement them
}
# Static mapping of workflow types to their core workflow classes
CORE_WORKFLOW_CLASSES = {
    WorkflowType.PURCHASE_ORDER_CREATION: POCreationWorkflow,
    WorkflowType.PURCHASE_ORDER_ACKNOWLEDGEMENT: OrderAcknowledgementWorkflow,
    WorkflowType.PURCHASE_ORDER_SHIPPED: ShipmentWorkflow,
    WorkflowType.PURCHASE_ORDER_FOLLOW_UP: FollowUpWorkflow,
    WorkflowType.INVOICE_PROCESSING: InvoiceProcessingWorkflow,
}
