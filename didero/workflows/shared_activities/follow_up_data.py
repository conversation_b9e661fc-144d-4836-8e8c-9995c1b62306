# Database access activities for follow-up workflows
from temporalio import activity


@activity.defn
async def get_followup_config_activity(team_id: str, supplier_id: str):
    """Get followup config for supplier as a Temporal activity."""
    from asgiref.sync import sync_to_async

    from didero.suppliers.models import Supplier
    from didero.users.models import Team
    from didero.users.utils.team_setting_utils import get_followup_config_for_supplier

    activity.logger.info(
        f"Getting followup config for team {team_id}, supplier {supplier_id}"
    )

    # Get the actual objects
    team = await sync_to_async(Team.objects.get)(pk=team_id)
    supplier = (
        await sync_to_async(Supplier.objects.get)(pk=supplier_id)
        if supplier_id
        else None
    )

    config = await sync_to_async(
        get_followup_config_for_supplier, thread_sensitive=True
    )(team, supplier)

    activity.logger.info(
        "returning config from activity",
        config_type=type(config).__name__,
        config_class=config.__class__.__module__ + "." + config.__class__.__name__,
        has_oa_followup=hasattr(config, "oa_followup"),
        oa_enabled=config.oa_followup.enabled
        if hasattr(config, "oa_followup")
        else "no_attr",
    )

    return config
