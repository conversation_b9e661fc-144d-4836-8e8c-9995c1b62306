import asyncio
import time
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Dict, Optional

from temporalio import activity, workflow
from temporalio.common import RetryPolicy
from temporalio.exceptions import ApplicationError

from didero.workflows.errors import ResourceNotFoundError, ValidationError

# Use unsafe imports for Django models
with workflow.unsafe.imports_passed_through():
    from django.db import OperationalError

    from didero.ai.purchase_order.schemas import PurchaseOrderDetails
    from didero.orders.models import PurchaseOrder
    from didero.workflows.core.nodes.purchase_orders.shipments import (
        get_purchase_order_from_po_number,
    )


@activity.defn
def retrieve_purchase_order_activity(po_number: str, team_id: int) -> Dict[str, Any]:
    """
    Retrieve purchase order from database.

    Args:
        po_number: The PO number to search for
        team_id: The team ID

    Returns:
        Dict with success status and PurchaseOrderDetails or error message
    """
    try:
        activity.logger.info(
            f"Starting PO retrieval - Raw input: '{po_number}' for team {team_id}"
        )

        if not po_number:
            activity.logger.warning(
                "No PO number provided to retrieve_purchase_order_activity"
            )
            raise ValidationError(
                "PO number is required",
                field="po_number",
                team_id=team_id,
            )

        # Look up PO in database using the same logic as get_purchase_order_from_po_number
        # Sanitize the PO number by removing any whitespace
        original_po_number = po_number
        po_number = po_number.strip()

        activity.logger.info(
            f"PO number after strip: '{po_number}' (original was '{original_po_number}')"
        )

        purchase_order = None

        # First attempt: try with "PO-" prefix
        prefixed_po = f"PO-{po_number}"
        activity.logger.info(f"First attempt: searching for '{prefixed_po}'")

        try:
            purchase_order = PurchaseOrder.objects.get(
                po_number=prefixed_po, team_id=team_id, archived_at__isnull=True
            )
            activity.logger.info(
                f"✓ Found PO with prefixed search: {purchase_order.po_number} (ID: {purchase_order.pk})"
            )
        except PurchaseOrder.DoesNotExist:
            activity.logger.info(f"✗ No PO found with '{prefixed_po}'")

            # Second attempt: try exact match
            activity.logger.info(
                f"Second attempt: searching for exact match '{po_number}'"
            )
            try:
                purchase_order = PurchaseOrder.objects.get(
                    po_number=po_number, team_id=team_id, archived_at__isnull=True
                )
                activity.logger.info(
                    f"✓ Found PO with exact match: {purchase_order.po_number} (ID: {purchase_order.pk})"
                )
            except PurchaseOrder.DoesNotExist:
                activity.logger.info(f"✗ No PO found with '{po_number}'")

                # Log what POs actually exist for debugging
                similar_pos = PurchaseOrder.objects.filter(
                    po_number__icontains=po_number[-4:]
                    if len(po_number) >= 4
                    else po_number,  # Last 4 chars
                    team_id=team_id,
                    archived_at__isnull=True,
                )[:5]

                if similar_pos:
                    activity.logger.info(
                        f"Found {similar_pos.count()} POs with similar numbers containing '{po_number[-4:] if len(po_number) >= 4 else po_number}': "
                        + ", ".join([f"'{po.po_number}'" for po in similar_pos])
                    )
                else:
                    activity.logger.info(
                        f"No similar POs found containing '{po_number[-4:] if len(po_number) >= 4 else po_number}'"
                    )

                # Also log total PO count for the team
                total_pos = PurchaseOrder.objects.filter(
                    team_id=team_id, archived_at__isnull=True
                ).count()
                activity.logger.info(
                    f"Total active POs for team {team_id}: {total_pos}"
                )

                # Raise non-retryable error for PO not found
                raise ResourceNotFoundError(
                    resource_type="PurchaseOrder",
                    resource_id=po_number,
                    team_id=team_id,
                    similar_pos=[po.po_number for po in similar_pos]
                    if similar_pos
                    else [],
                )

        if purchase_order:
            activity.logger.info(
                f"Successfully retrieved PO: {purchase_order.po_number} (ID: {purchase_order.pk}) for team {team_id}"
            )

            # Convert Django model to PurchaseOrderDetails schema
            po_details = PurchaseOrderDetails.from_model(purchase_order)

            # Return with typed PO details
            return {
                "success": True,
                "po_found": True,
                "purchase_order_id": purchase_order.pk,
                "purchase_order_details": po_details.model_dump(),  # Serialize for Temporal
            }

        # This shouldn't happen, but adding for completeness
        activity.logger.warning(
            "Unexpected state: purchase_order is None after searches"
        )
        raise ResourceNotFoundError(
            resource_type="PurchaseOrder",
            resource_id=po_number,
            team_id=team_id,
            error_detail="PO object is None after searches",
        )

    except ValidationError as e:
        activity.logger.warning(f"Validation error: {str(e)}")
        return {
            "success": False,
            "po_found": False,
            "error_message": str(e),
        }
    except ResourceNotFoundError as e:
        activity.logger.info(f"PO not found: {str(e)}")
        return {
            "success": False,
            "po_found": False,
            "error_message": str(e),
        }
    except OperationalError as e:
        # Database connection errors should be retried by Temporal
        activity.logger.error(f"Database connection error: {str(e)}")
        raise  # Let Temporal retry
    except Exception as e:
        # For other unexpected errors, let Temporal handle the retry
        activity.logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        raise  # Let Temporal retry based on retry policy


@activity.defn
async def get_purchase_order_activity(purchase_order_id: str):
    """Get purchase order with relations as a Temporal activity."""
    from asgiref.sync import sync_to_async

    from didero.orders.models import PurchaseOrder
    from didero.workflows.core_workflows.follow_up.utils import (
        get_purchase_order_with_relations,
    )
    from didero.workflows.serialization_utils import serialize_purchase_order

    activity.logger.info(
        f"Getting purchase order with relations for ID: {purchase_order_id}"
    )

    try:
        po = await sync_to_async(
            get_purchase_order_with_relations, thread_sensitive=True
        )(purchase_order_id)
    except PurchaseOrder.DoesNotExist:
        activity.logger.error(f"Purchase order not found: {purchase_order_id}")
        # Raise ApplicationError for business logic failure (non-retryable)
        raise ApplicationError(
            f"Purchase order not found: {purchase_order_id}",
            type="PurchaseOrderNotFound",
            non_retryable=True,
        )

    activity.logger.info(f"Successfully retrieved PO: {po.po_number} (ID: {po.pk})")

    # Add the IDs we need for further processing
    serialized_po = await sync_to_async(
        serialize_purchase_order, thread_sensitive=True
    )(po)
    serialized_po.update(
        {
            "id": str(po.pk),
            "team_id": str(po.team.pk),
            "supplier_id": str(po.supplier.pk) if po.supplier else None,
        }
    )

    activity.logger.info("Serialized PO data for follow-up processing")
    return serialized_po


@activity.defn
def get_purchase_order_id_from_po_number_activity(
    po_number: str, team_id: int
) -> Optional[int]:
    """Activity wrapper to get PO ID from PO number for use in workflows."""
    po = get_purchase_order_from_po_number(po_number, team_id)
    return po.pk if po else None


@activity.defn
async def get_purchase_order_details_activity(purchase_order_id: str) -> Dict[str, Any]:
    """Get purchase order details as PurchaseOrderDetails schema for invoice processing."""
    from asgiref.sync import sync_to_async

    from didero.orders.models import PurchaseOrder
    from didero.workflows.core_workflows.follow_up.utils import (
        get_purchase_order_with_relations,
    )

    activity.logger.info(
        f"Getting purchase order details for invoice processing: {purchase_order_id}"
    )

    try:
        po = await sync_to_async(
            get_purchase_order_with_relations, thread_sensitive=True
        )(purchase_order_id)
    except PurchaseOrder.DoesNotExist:
        activity.logger.error(f"Purchase order not found: {purchase_order_id}")
        # Raise ApplicationError for business logic failure (non-retryable)
        raise ApplicationError(
            f"Purchase order not found: {purchase_order_id}",
            type="PurchaseOrderNotFound",
            non_retryable=True,
        )

    # Convert Django model to PurchaseOrderDetails schema (same as retrieve_purchase_order_activity)
    # Must use sync_to_async because from_model() makes ORM calls
    po_details = await sync_to_async(
        PurchaseOrderDetails.from_model, thread_sensitive=True
    )(po)

    activity.logger.info(
        f"Successfully retrieved PO details: {po.po_number} (ID: {po.pk})"
    )

    # Return the schema data (serialized for Temporal)
    return po_details.model_dump()


# Chapter 2: Smart PO Auto-Creation Functions
def team_has_erp_auto_creation_capability(team_id: int) -> bool:
    """
    Check if team has technical capability for auto-creating POs from ERP.

    This checks the technical prerequisites - whether the team has an
    active ERP integration that supports PO creation.

    Note: This function checks capability only. Permission is controlled
    by WorkflowBehaviorConfig.enable_po_auto_creation.

    Args:
        team_id: Team ID to check

    Returns:
        True if team has ERP integration capable of creating POs
    """
    from didero.integrations.models import ERPIntegrationConfig

    try:
        # Check if ERP is configured and enabled
        # This leverages the IonQ NetSuite integration from Chapter 1
        return ERPIntegrationConfig.objects.filter(
            team_id=team_id,
            enabled=True,
            erp_type="netsuite",  # Currently only NetSuite supported
        ).exists()
    except Exception as e:
        # Log the error and return False instead of hardcoded fallback
        import structlog
        logger = structlog.get_logger(__name__)
        logger.warning(
            f"Error checking ERP capability for team {team_id}: {str(e)}"
        )
        # Only fallback to hardcoded teams if it's a connection error
        if "connection" in str(e).lower():
            return team_id in [4, 173]
        return False


def should_auto_create_po_for_team(
    team_id: int, enable_auto_creation: bool = False
) -> bool:
    """
    Check if team should auto-create missing POs.

    This combines permission (from workflow config) with capability (from ERP config).
    Both conditions must be true:
    1. Workflow config enables it (permission)
    2. ERP integration is configured with NetSuite (capability)

    This enables just-in-time PO creation for teams with ERP integration.

    Args:
        team_id: Team ID to check
        enable_auto_creation: Whether workflow config permits auto-creation

    Returns:
        True if both permission and capability are present
    """
    # Check workflow-level permission first
    if not enable_auto_creation:
        return False

    # Then check technical capability
    return team_has_erp_auto_creation_capability(team_id)


def get_po_auto_creation_eligibility(team_id: int) -> Dict[str, Any]:
    """
    Get detailed information about a team's PO auto-creation eligibility.

    This helper function provides visibility into why auto-creation may or may not
    be available for a team, useful for debugging and configuration.

    Args:
        team_id: Team ID to check

    Returns:
        Dict with eligibility details including capability status and requirements
    """
    from didero.integrations.models import ERPIntegrationConfig

    result = {
        "team_id": team_id,
        "has_capability": False,
        "erp_configured": False,
        "erp_type": None,
        "erp_enabled": False,
        "missing_requirements": [],
    }

    try:
        # Check ERP configuration
        erp_config = ERPIntegrationConfig.objects.filter(team_id=team_id).first()

        if erp_config:
            result["erp_configured"] = True
            result["erp_type"] = erp_config.erp_type
            result["erp_enabled"] = erp_config.enabled

            if not erp_config.enabled:
                result["missing_requirements"].append("ERP integration is disabled")
            if erp_config.erp_type != "netsuite":
                result["missing_requirements"].append(
                    f"ERP type '{erp_config.erp_type}' not supported (only NetSuite)"
                )
        else:
            result["missing_requirements"].append("No ERP integration configured")

        # Check overall capability
        result["has_capability"] = team_has_erp_auto_creation_capability(team_id)

    except Exception as e:
        result["error"] = str(e)
        result["missing_requirements"].append(f"Error checking configuration: {str(e)}")

    return result


async def enqueue_po_auto_creation_workflow(
    team_id: int,
    po_number: str,
    source_email_id: Optional[str] = None,
    source_context: str = "unknown",
) -> Dict[str, Any]:
    """
    Enqueue PO auto-creation using POCreationWorkflow with direct PO number.

    This leverages the modified POCreationWorkflow that can accept a po_number
    directly and skip AI extraction, going straight to NetSuite ERP integration
    from Chapter 1 to create missing POs.

    Args:
        team_id: Team ID for which to create the PO
        po_number: PO number to create (extracted from email by AI)
        source_email_id: Optional source email ID that triggered auto-creation
        source_context: Context of the trigger ("order_acknowledgement", "shipment", etc.)

    Returns:
        Dict with success status and creation details
    """
    from didero.workflows.core_workflows.po_creation.workflow import (
        POCreationParams,
        POCreationWorkflow,
    )
    from didero.workflows.queue_config import get_queue_for_workflow_type
    from didero.workflows.schemas import WorkflowType
    from didero.workflows.utils import get_temporal_client

    try:
        activity.logger.info(
            f"Starting PO auto-creation with direct PO number {po_number}, team_id={team_id}, source_context={source_context}, source_email_id={source_email_id}"
        )

        # Generate unique workflow ID following existing patterns
        workflow_id = f"auto-po-creation-{po_number}-{team_id}-{source_context}-{int(time.time())}"

        # Prepare workflow parameters for auto-creation with direct PO number
        workflow_params = POCreationParams(
            team_id=str(team_id),
            email_id=source_email_id,  # Optional: source email for context
            workflow_id="",  # Empty to use default config (following trigger_po_creation_workflow_for_document pattern)
            po_number=po_number,  # NEW: Direct PO number to skip AI extraction
        )

        # Get Temporal client and queue following existing patterns
        client = await get_temporal_client()
        task_queue = get_queue_for_workflow_type(WorkflowType.PURCHASE_ORDER_CREATION)

        activity.logger.info(
            f"Starting POCreationWorkflow with direct PO number, workflow_id={workflow_id}, task_queue={task_queue}, team_id={team_id}, po_number={po_number}"
        )

        # Start the child workflow using the correct signature pattern from trigger_po_creation_workflow_for_document
        workflow_handle = await client.start_workflow(
            POCreationWorkflow.run,
            args=(
                "",
                workflow_params,
            ),  # Empty workflow_id as first arg, params as second
            id=workflow_id,
            task_queue=task_queue,
            retry_policy=RetryPolicy(
                maximum_attempts=2,  # Fewer attempts since this is auto-creation
                initial_interval=timedelta(seconds=10),
                maximum_interval=timedelta(seconds=60),
                backoff_coefficient=2.0,
            ),
        )

        activity.logger.info(
            f"Started POCreationWorkflow {workflow_id}, temporal_id={workflow_handle.id}, po_number={po_number}, team_id={team_id}"
        )

        # Wait for completion with timeout to prevent hanging parent workflows
        try:
            result = await asyncio.wait_for(
                workflow_handle.result(),
                timeout=300,  # 5 minute timeout for PO creation
            )

            activity.logger.info(
                f"PO auto-creation completed successfully, workflow_id={workflow_id}, po_number={po_number}, team_id={team_id}, success={result.get('success', False)}"
            )

            return {
                "success": result.get("success", False),
                "po_id": result.get("po_id"),
                "po_number": result.get("po_number"),
                "workflow_id": workflow_id,
                "temporal_id": workflow_handle.id,
                "created_in_draft": result.get("created_in_draft", False),
            }

        except asyncio.TimeoutError:
            activity.logger.error(
                f"PO auto-creation workflow timed out, workflow_id={workflow_id}, po_number={po_number}, team_id={team_id}, timeout_seconds=300"
            )
            return {
                "success": False,
                "error_message": "PO auto-creation workflow timed out after 5 minutes",
                "workflow_id": workflow_id,
                "temporal_id": workflow_handle.id,
            }

    except Exception as e:
        activity.logger.error(
            f"Failed to enqueue PO auto-creation workflow, po_number={po_number}, team_id={team_id}, source_context={source_context}, error={str(e)}"
        )
        return {
            "success": False,
            "error_message": f"Failed to start auto-creation workflow: {str(e)}",
        }


@activity.defn
def resolve_purchase_order_with_auto_creation(
    po_number: str,
    team_id: int,
    source_email_id: Optional[str] = None,
    source_context: str = "unknown",
    enable_auto_creation: bool = False,
) -> Dict[str, Any]:
    """
    Enhanced PO resolution with auto-creation for configured teams.

    This activity provides intelligent PO resolution that automatically creates
    missing POs from ERP systems when they're referenced in order acknowledgements
    or shipment notifications. This eliminates workflow failures and provides
    seamless integration between NetSuite and Didero for IonQ customers.

    Flow:
    1. Standard PO lookup (reuse existing retrieve_purchase_order_activity pattern)
    2. If not found + auto-creation enabled + ERP configured → Trigger child PO creation workflow
    3. Wait for completion and retry lookup
    4. Return enhanced result with auto-creation context

    Args:
        po_number: PO number to resolve
        team_id: Team ID for the lookup
        source_email_id: Optional email ID that triggered this resolution
        source_context: Context of the trigger ("order_acknowledgement", "shipment", etc.)
        enable_auto_creation: Whether auto-creation is enabled (from workflow config)

    Returns:
        {
            "success": bool,
            "purchase_order": PurchaseOrder model data | None,
            "purchase_order_id": str | None,
            "auto_created": bool,
            "creation_details": Dict | None,
            "auto_creation_attempted": bool,
            "auto_creation_error": str | None
        }
    """
    activity.logger.info(
        f"Starting smart PO resolution for {po_number}, team_id={team_id}, source_context={source_context}, "
        f"source_email_id={source_email_id}, enable_auto_creation={enable_auto_creation}"
    )

    # Step 1: Standard PO lookup (reuse existing pattern)
    try:
        standard_result = retrieve_purchase_order_activity(po_number, team_id)

        if standard_result["success"] and standard_result["po_found"]:
            activity.logger.info(
                f"PO {po_number} found via standard lookup, team_id={team_id}, po_id={standard_result.get('purchase_order_id')}"
            )
            return {
                **standard_result,
                "purchase_order_id": standard_result.get("purchase_order_id"),
                "auto_created": False,
                "creation_details": None,
                "auto_creation_attempted": False,
                "auto_creation_error": None,
            }
    except Exception as e:
        activity.logger.error(
            f"Error in standard PO lookup: {str(e)}, po_number={po_number}, team_id={team_id}"
        )
        # Continue to auto-creation attempt
        standard_result = {"success": False, "error_message": str(e)}

    # Step 2: Check if auto-creation is enabled (permission from workflow config)
    if not enable_auto_creation:
        activity.logger.info(
            f"PO {po_number} not found and auto-creation disabled by workflow config, team_id={team_id}, source_context={source_context}"
        )
        return {
            **standard_result,
            "auto_creation_attempted": False,
            "auto_creation_error": "Auto-creation disabled by workflow configuration",
        }

    # Step 3: Check if team has ERP configured for auto-creation (capability)
    if not team_has_erp_auto_creation_capability(team_id):
        activity.logger.info(
            f"PO {po_number} not found and team {team_id} does not have ERP configured for auto-creation, source_context={source_context}"
        )
        return {
            **standard_result,
            "auto_creation_attempted": False,
            "auto_creation_error": "ERP integration not configured for auto-creation",
        }

    # Step 4: Both permission and capability present - trigger auto-creation workflow
    activity.logger.info(
        f"PO {po_number} not found, auto-creation enabled and team has ERP capability, triggering creation, team_id={team_id}, source_context={source_context}"
    )

    try:
        from asgiref.sync import async_to_sync

        # Use the child workflow pattern to create the missing PO
        creation_result = async_to_sync(enqueue_po_auto_creation_workflow)(
            team_id=team_id,
            po_number=po_number,
            source_email_id=source_email_id,
            source_context=source_context,
        )

        if creation_result["success"]:
            activity.logger.info(
                f"Auto-creation successful, retrying PO lookup, po_number={po_number}, team_id={team_id}, created_po_id={creation_result.get('po_id')}"
            )

            # Step 5: Retry standard lookup after auto-creation
            try:
                retry_result = retrieve_purchase_order_activity(po_number, team_id)
                if retry_result["success"] and retry_result["po_found"]:
                    activity.logger.info(
                        f"Successfully auto-created and retrieved PO {po_number}, team_id={team_id}, po_id={retry_result.get('purchase_order_id')}, auto_creation_workflow={creation_result.get('workflow_id')}"
                    )
                    return {
                        **retry_result,
                        "purchase_order_id": retry_result.get("purchase_order_id"),
                        "auto_created": True,
                        "creation_details": creation_result,
                        "auto_creation_attempted": True,
                        "auto_creation_error": None,
                    }
                else:
                    activity.logger.error(
                        f"Auto-creation succeeded but PO {po_number} still not retrievable, team_id={team_id}, creation_result={creation_result}"
                    )
                    return {
                        "success": False,
                        "error_message": "PO creation succeeded but PO still not retrievable",
                        "auto_creation_attempted": True,
                        "auto_creation_error": "PO not found after successful creation",
                        "creation_details": creation_result,
                    }
            except Exception as e:
                activity.logger.error(
                    f"Error retrying PO lookup after auto-creation: {str(e)}, po_number={po_number}, team_id={team_id}"
                )
                return {
                    "success": False,
                    "error_message": f"Error retrying lookup after auto-creation: {str(e)}",
                    "auto_creation_attempted": True,
                    "auto_creation_error": str(e),
                    "creation_details": creation_result,
                }
        else:
            # Auto-creation failed, return original error with context
            activity.logger.warning(
                f"Auto-creation failed for PO {po_number}, team_id={team_id}, creation_error={creation_result.get('error_message')}, source_context={source_context}"
            )
            return {
                **standard_result,
                "auto_creation_attempted": True,
                "auto_creation_error": creation_result.get("error_message"),
                "creation_details": creation_result,
            }

    except Exception as e:
        activity.logger.error(
            f"Unexpected error during PO auto-creation, po_number={po_number}, team_id={team_id}, error={str(e)}"
        )
        return {
            **standard_result,
            "auto_creation_attempted": True,
            "auto_creation_error": str(e),
        }
