"""
Unified supplier document operations for workflow activities.

This module provides a generic activity for extracting and resolving
supplier documents (Order Acknowledgements, Shipments, etc.) with
optional PO auto-creation support.
"""

from dataclasses import dataclass
from typing import Any, Callable, Dict, Protocol, TypedDict

import structlog
from django.db import OperationalError
from temporalio import activity
from temporalio.exceptions import ApplicationError

from didero.orders.models import PurchaseOrder
from didero.suppliers.models import Communication

logger = structlog.get_logger(__name__)


# Type definitions
class SupplierDocumentExtractionResult(TypedDict):
    """
    Generic return type for supplier document extraction with PO resolution.
    
    Used by the unified extract_and_resolve_supplier_document activity
    for all supplier document types (Order Acknowledgements, Shipments, etc.)
    """
    
    document_type: str  # "order_acknowledgement", "shipment", "invoice"
    document_details: Dict[str, Any]  # The extracted document data
    purchase_order_id: str  # Resolved PO ID
    email_id: str  # Source email ID
    auto_created_po: bool  # Whether PO was auto-created from ERP


class DocumentExtractor(Protocol):
    """Protocol for document extractors."""
    def __call__(self, email: Communication) -> Any: ...


@dataclass
class DocumentTypeConfig:
    """Configuration for each document type."""
    extractor: DocumentExtractor
    po_number_getter: Callable[[Any], str]
    log_prefix: str
    source_context: str  # For auto-creation tracking


# Document type configurations
def _get_document_type_configs() -> Dict[str, DocumentTypeConfig]:
    """
    Get document type configurations.
    
    Imports are inside function to avoid circular dependencies.
    """
    from didero.ai.order_acknowledgment.order_ack import (
        ai_extract_order_acknowledgement_details,
    )
    from didero.ai.email_processing.shipment_info import (
        get_shipment_info_for_email,
    )
    
    return {
        "order_acknowledgement": DocumentTypeConfig(
            extractor=ai_extract_order_acknowledgement_details,
            po_number_getter=lambda doc: doc.po_number,
            log_prefix="Supplier Document (OA)",
            source_context="order_acknowledgement",
        ),
        "shipment": DocumentTypeConfig(
            extractor=get_shipment_info_for_email,
            po_number_getter=lambda doc: doc.purchase_order_number,
            log_prefix="Supplier Document (Shipment)",
            source_context="shipment",
        ),
        # Future: Add invoice, etc.
    }


@activity.defn
def extract_and_resolve_supplier_document(
    params: Dict[str, Any],
) -> SupplierDocumentExtractionResult:
    """
    Extract supplier document details and resolve PO with optional auto-creation.
    
    This unified activity replaces document-specific extraction activities
    for teams using core workflows. It supports all supplier document types
    and includes smart PO resolution logic.
    
    DESIGN DECISION: Mixed Responsibilities
    This activity intentionally combines document extraction with PO resolution,
    violating the Single Responsibility Principle. This design choice was made to:
    1. Simplify workflow code - single activity call vs orchestrating multiple
    2. Ensure atomic operations - extraction and resolution succeed or fail together
    3. Reduce network calls - one activity execution vs multiple
    4. Match existing patterns - similar to how invoice processing works
    
    TODO: Future Refactoring Options (if needed):
    - Option 1: Split into extract_supplier_document + resolve_purchase_order
      - Pros: Better composability, single responsibility, easier testing
      - Cons: More complex workflows, potential consistency issues
    - Option 2: Create a workflow-level orchestrator that calls focused activities
      - Pros: Keeps workflows simple while having focused activities
      - Cons: Another layer of abstraction
    - Option 3: Keep as-is but create additional focused activities for special cases
      - Pros: Backward compatible, gradual migration
      - Cons: Multiple ways to do the same thing
    
    For now, keeping combined approach aligns with team's preference for
    pragmatic simplicity over strict architectural principles.
    
    Args:
        params: Dict containing:
            - document_type: Type of document ("order_acknowledgement", "shipment", etc.)
            - email_id: Email ID to extract from
            - team_id: Team ID for PO lookup
            - enable_po_auto_creation: Whether to auto-create missing POs
    
    Returns:
        SupplierDocumentExtractionResult with extracted document, resolved PO ID, and auto-creation flag
        
    Raises:
        ApplicationError: For non-retryable errors (not found, validation failures)
    """
    from didero.workflows.core.nodes.purchase_orders.shipments import (
        get_purchase_order_from_po_number,
    )
    from didero.workflows.shared_activities.purchase_order_operations import (
        resolve_purchase_order_with_auto_creation,
    )
    
    # Extract parameters
    document_type = params["document_type"]
    email_id = params["email_id"]
    team_id = params["team_id"]
    enable_po_auto_creation = params.get("enable_po_auto_creation", False)
    
    # Get configuration for this document type
    configs = _get_document_type_configs()
    if document_type not in configs:
        raise ApplicationError(
            f"Unsupported document type: {document_type}",
            type="ValidationError",
            non_retryable=True,
        )
    
    config = configs[document_type]
    
    logger.info(
        f"{config.log_prefix}: Extracting and resolving document",
        email_id=email_id,
        team_id=team_id,
        document_type=document_type,
        enable_po_auto_creation=enable_po_auto_creation,
    )
    
    # Step 1: Get the email
    try:
        email = Communication.objects.select_related(
            "team", "supplier", "email_thread"
        ).get(id=email_id)
    except Communication.DoesNotExist:
        logger.error(
            f"{config.log_prefix}: Communication not found",
            email_id=email_id,
        )
        raise ApplicationError(
            f"Communication not found: {email_id}",
            type="ResourceNotFoundError",
            non_retryable=True,
        )
    except OperationalError:
        # Database connection errors should be retried
        raise
    
    # Step 2: Extract document details using AI
    try:
        document_details = config.extractor(email)
        po_number = config.po_number_getter(document_details)
        logger.info(
            f"{config.log_prefix}: Extracted document details",
            email_id=email_id,
            po_number=po_number,
        )
    except Exception as e:
        logger.error(
            f"AI extraction failed for email {email_id}: {str(e)}",
        )
        # AI errors might be transient, let them retry
        raise
    
    # Step 3: Try standard PO lookup first
    purchase_order = get_purchase_order_from_po_number(po_number, team_id)
    
    auto_created_po = False
    
    # Step 4: If not found and auto-creation enabled, trigger it
    if not purchase_order and enable_po_auto_creation:
        logger.info(
            f"{config.log_prefix}: PO not found, attempting auto-creation",
            po_number=po_number,
            team_id=team_id,
        )
        
        po_resolution = resolve_purchase_order_with_auto_creation(
            po_number=po_number,
            team_id=team_id,
            source_email_id=email_id,
            source_context=config.source_context,
            enable_auto_creation=True,  # Explicitly enabled by workflow config
        )
        
        if po_resolution["success"] and po_resolution.get("purchase_order_id"):
            # Get the auto-created PO
            try:
                purchase_order = PurchaseOrder.objects.get(
                    id=po_resolution["purchase_order_id"]
                )
                auto_created_po = po_resolution.get("auto_created", False)
                logger.info(
                    f"{config.log_prefix}: Successfully auto-created PO",
                    po_id=purchase_order.id,
                    po_number=purchase_order.po_number,
                )
            except PurchaseOrder.DoesNotExist:
                logger.error(
                    f"{config.log_prefix}: Auto-created PO not found in database",
                    po_id=po_resolution["purchase_order_id"],
                )
        else:
            logger.warning(
                f"{config.log_prefix}: Auto-creation failed",
                po_number=po_number,
                error=po_resolution.get("error_message"),
            )
    
    # Step 5: If still no PO, raise error
    if not purchase_order:
        logger.error(
            f"{config.log_prefix}: Purchase order not found and auto-creation failed or disabled",
            po_number=po_number,
            team_id=team_id,
            auto_creation_enabled=enable_po_auto_creation,
        )
        raise ApplicationError(
            f"Purchase order not found: {po_number}",
            type="ResourceNotFoundError",
            non_retryable=True,
        )
    
    # Return the extracted document and resolved PO
    return {
        "document_type": document_type,
        "document_details": document_details.model_dump(),
        "purchase_order_id": str(purchase_order.id),
        "email_id": email_id,
        "auto_created_po": auto_created_po,
    }

