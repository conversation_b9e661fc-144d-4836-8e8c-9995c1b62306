"""
Shared invoice operations activities that can be used across multiple workflows.
"""

from enum import StrEnum
from typing import Any, Dict, Optional, <PERSON>ple

import structlog
from temporalio import activity

from didero.ai.invoice.extraction import ai_extract_invoice_details
from didero.emails.models import EmailThreadToPurchaseOrderLink
from didero.suppliers.models import Communication
from didero.workflows.core.nodes.purchase_orders.shipments import (
    get_purchase_order_from_po_number,
)
from didero.workflows.core_workflows.invoice_processing.schemas import (
    ExtractInvoiceDetailsParams,
)

logger = structlog.get_logger(__name__)


# Define error types locally to avoid circular import
class InvoiceWorkflowErrorType(StrEnum):
    COMMUNICATION_NOT_FOUND = "communication_not_found"
    PURCHASE_ORDER_NOT_FOUND = "purchase_order_not_found"
    INVOICE_SCHEMA_EXTRACTION_FAILED = "invoice_schema_extraction_failed"


@activity.defn
def extract_invoice_details(
    params: ExtractInvoiceDetailsParams,
) -> <PERSON><PERSON>[Optional[str], Optional[Dict[str, Any]]]:
    """
    Extract invoice details from an email communication.

    This activity:
    1. Retrieves the email communication
    2. Extracts invoice details using AI
    3. Looks up the matching purchase order
    4. Links the email thread to the PO if found

    Args:
        params: ExtractInvoiceDetailsParams containing email_id

    Returns:
        Tuple of (error_type, result_dict) where result_dict contains:
        - invoice: The extracted invoice schema
        - purchase_order_id: The matching PO ID
        - email_id: The original email ID
    """
    email_id = params["email_id"]
    logger.info(
        "Invoice Workflow: Extracting invoice details",
        email_id=email_id,
    )
    try:
        email = Communication.objects.get(id=email_id)
        logger.info(
            "Invoice Workflow: Found communication",
            email_id=email_id,
        )
    except Communication.DoesNotExist:
        logger.error(
            "Invoice Workflow: Communication not found",
            email_id=email_id,
        )
        return InvoiceWorkflowErrorType.COMMUNICATION_NOT_FOUND.value, None

    # Extract invoice details
    logger.info(
        "Invoice Workflow: Matching invoice to purchase order",
        email_id=email_id,
    )
    invoice = ai_extract_invoice_details(email)

    if not invoice:
        logger.error(
            "Invoice Workflow: Failed to extract invoice details",
            email_id=email_id,
        )
        return InvoiceWorkflowErrorType.INVOICE_SCHEMA_EXTRACTION_FAILED.value, None

    logger.info(
        "Invoice Workflow: Successfully extracted invoice details",
        email_id=email_id,
        invoice_purchase_order_number=invoice.po_number,
    )

    # Retrieve purchase order ID
    # TODO: In the future, we are going to get the PO where it checks ERP or didero
    # and creates a task to create the PO if none is found
    purchase_order = get_purchase_order_from_po_number(invoice.po_number, email.team.id)

    if purchase_order:
        logger.info(
            "Invoice Workflow: Purchase order found",
            email_id=email_id,
            purchase_order_id=purchase_order.pk,
        )
        # Link the communication to the purchase order
        logger.info(
            "Invoice Workflow: Linking communication to purchase order",
            email_id=email_id,
            purchase_order_id=purchase_order.pk,
        )
        email_id = params["email_id"]
        email = Communication.objects.get(id=email_id)
        link, created = EmailThreadToPurchaseOrderLink.objects.get_or_create(
            email_thread=email.email_thread,
            purchase_order=purchase_order,
        )
        if created:
            logger.info(
                "Invoice Workflow: Linked email thread to purchase order",
                email_id=email_id,
                purchase_order_id=purchase_order.pk,
            )

        # Include supplier information from the email itself
        supplier_info = {
            "supplier_id": str(email.supplier.id) if email.supplier else None,
            "supplier_name": email.supplier.name
            if email.supplier
            else "Unknown Supplier",
        }

        result = {
            "invoice": invoice,
            "purchase_order_id": str(purchase_order.pk),
            "email_id": email_id,
            "supplier_info": supplier_info,
        }
        return None, result

    logger.info(
        "Invoice Workflow: Purchase order not found, returning invoice data without PO",
        email_id=email_id,
        invoice_po_number=invoice.po_number,
    )

    # Return invoice data without PO - let the workflow handle the missing PO
    # Include supplier information from the email itself
    supplier_info = {
        "supplier_id": str(email.supplier.id) if email.supplier else None,
        "supplier_name": email.supplier.name if email.supplier else "Unknown Supplier",
    }

    result = {
        "invoice": invoice,
        "purchase_order_id": None,
        "email_id": email_id,
        "supplier_info": supplier_info,
    }
    return None, result


def get_invoice_workflow_parameters(email: Communication) -> Dict[str, Any]:
    """
    Helper function to get workflow parameters from an email.
    Returns parameters compatible with InvoiceProcessingParams.
    """
    return {
        "email_id": str(email.pk),
        "team_id": str(email.team.id),  # Return as str to match InvoiceProcessingParams
    }
