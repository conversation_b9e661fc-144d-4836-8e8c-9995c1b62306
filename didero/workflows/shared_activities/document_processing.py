"""
Shared activities for generic document processing and extraction.
"""

from typing import Any, Dict

from temporalio import activity


@activity.defn
def extract_document_by_type_activity(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generic document extraction activity that works for any document type.

    Args:
        params: Dictionary containing:
            - document_id: ID of the document to extract from
            - document_type: Type of document ("po", "shipment", "invoice", etc.)
            - schema_name: Name of the schema class to use for extraction

    Returns:
        Dictionary containing:
            - success: Boolean indicating if extraction succeeded
            - extracted_data: Dictionary of extracted data (if success=True)
            - document_type: The document type that was processed
            - document_id: The document ID that was processed
            - error: Error message (if success=False)
    """
    document_id = params["document_id"]
    document_type = params["document_type"]  # "po", "shipment", "invoice"
    schema_name = params["schema_name"]  # "PurchaseOrderDetails", "ShipmentDetails"

    activity.logger.info(
        f"Starting document extraction for {document_type}",
        document_id=document_id,
        schema_name=schema_name,
    )

    # Map document types to their corresponding schema modules and classes
    SCHEMA_MAPPING = {
        "po": "didero.ai.purchase_order.schemas.PurchaseOrderDetails",
        "shipment": "didero.ai.shipment.schemas.ShipmentDetails",
        "invoice": "didero.ai.invoice.schemas.InvoiceDetails",
        "order_ack": "didero.ai.order_ack.schemas.OrderAckDetails",
    }

    # Map document types to their AI prompt keys
    PROMPT_MAPPING = {
        "po": "ANTHROPIC_PURCHASE_ORDER_SCHEMA_EXTRACTION",
        "shipment": "ANTHROPIC_SHIPMENT_SCHEMA_EXTRACTION",
        "invoice": "ANTHROPIC_INVOICE_SCHEMA_EXTRACTION",
        "order_ack": "ANTHROPIC_ORDER_ACK_SCHEMA_EXTRACTION",
    }

    try:
        # Get the schema class path for this document type
        schema_path = SCHEMA_MAPPING.get(document_type)
        if not schema_path:
            available_types = list(SCHEMA_MAPPING.keys())
            raise ValueError(
                f"Unknown document type: {document_type}. Available types: {available_types}"
            )

        # Dynamically import the schema class
        module_path, class_name = schema_path.rsplit(".", 1)
        module = __import__(module_path, fromlist=[class_name])
        schema_class = getattr(module, class_name)

        activity.logger.info(f"Successfully imported schema class: {schema_class}")

        # Import required modules for document processing
        from pathlib import Path

        from didero.ai.extraction.extract_from_document import (
            extract_schema_from_document,
        )
        from didero.ai.schemas import AIPromptKey
        from didero.documents.models import Document

        # Get the document from the database
        document = Document.objects.get(id=document_id)
        activity.logger.info(f"Retrieved document: {document.name}")

        # Get the prompt key for this document type
        prompt_key = PROMPT_MAPPING.get(document_type)
        if not prompt_key:
            activity.logger.warning(
                f"No prompt mapping found for {document_type}, using default"
            )
            prompt_key = (
                "ANTHROPIC_PURCHASE_ORDER_SCHEMA_EXTRACTION"  # Default fallback
            )

        # Use the existing document extraction infrastructure
        extracted_data = extract_schema_from_document(
            doc_type=document_type.title(),  # "po" -> "Po"
            schema=schema_class,
            document=document,
            email_content="",  # No email context for uploaded documents
            ctx=None,  # Will create default context in the function
            prompts_path=Path(__file__).parent.parent.parent
            / "ai"
            / "purchase_order"
            / "prompts_config.yaml",  # Use PO prompts as base
            prompts_key=prompt_key,
        )

        activity.logger.info(
            "Document extraction completed successfully",
            document_id=document_id,
            document_type=document_type,
            extracted_data_available=extracted_data is not None,
        )

        return {
            "success": True,
            "extracted_data": extracted_data.model_dump() if extracted_data else None,
            "document_type": document_type,
            "document_id": document_id,
        }

    except Exception as e:
        activity.logger.error(
            f"Document extraction failed: {str(e)}",
            document_id=document_id,
            document_type=document_type,
            error_type=type(e).__name__,
        )
        return {
            "success": False,
            "error": str(e),
            "document_type": document_type,
            "document_id": document_id,
        }


@activity.defn
def process_uploaded_document_activity(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generic activity for complete uploaded document processing.

    This activity handles the full pipeline:
    1. Extracts structured data from uploaded document
    2. Creates appropriate database record from extracted data
    3. Links document to created record for traceability

    Args:
        params: Dictionary containing:
            - document_id: ID of uploaded document
            - document_processing_config: Configuration dict with:
                - document_type: "po", "shipment", "invoice", etc.
                - extraction_schema: Schema class name for extraction
                - creation_activity: Activity name for record creation
            - team_id: Team ID for record creation
            - context_data: Optional context data for creation

    Returns:
        Dictionary containing:
            - success: Boolean indicating if processing succeeded
            - created_record_id: ID of created database record (if success=True)
            - created_record_type: Type of record created
            - document_id: ID of source document
            - error: Error message (if success=False)
    """
    document_id = params["document_id"]
    document_processing_config = params["document_processing_config"]
    team_id = params["team_id"]
    context_data = params.get("context_data", {})

    document_type = document_processing_config["document_type"]

    activity.logger.info(
        f"Starting complete document processing for {document_type}",
        document_id=document_id,
        team_id=team_id,
        document_type=document_type,
    )

    try:
        # Step 1: Extract structured data from document
        extraction_result = extract_document_by_type_activity(
            {
                "document_id": document_id,
                "document_type": document_type,
                "schema_name": document_processing_config["extraction_schema"],
            }
        )

        if not extraction_result["success"]:
            return {
                "success": False,
                "error": f"Document extraction failed: {extraction_result.get('error')}",
                "document_id": document_id,
            }

        activity.logger.info(
            f"Document extraction successful for {document_type}",
            document_id=document_id,
            has_extracted_data=extraction_result.get("extracted_data") is not None,
        )

        # Step 2: Create database record from extracted data
        creation_activity_name = document_processing_config["creation_activity"]
        extracted_data = extraction_result["extracted_data"]

        # Handle different document types with direct calls to working functions
        if creation_activity_name == "create_po_from_extracted_data_activity":
            # Call the working PO creation function directly
            from didero.ai.purchase_order.schemas import PurchaseOrderDetails
            from didero.workflows.core.nodes.purchase_orders.po_creation.activities import (
                _create_po_from_extracted_data_sync,
            )
            from didero.workflows.core.nodes.purchase_orders.po_creation.schemas import (
                CreatePOFromExtractedDataParams,
            )

            try:
                # Convert extracted data to PurchaseOrderDetails
                po_details = PurchaseOrderDetails.model_validate(extracted_data)

                # Create parameters for the sync function
                creation_params: CreatePOFromExtractedDataParams = {
                    "po_details": po_details,
                    "document_id": document_id,
                    "team_id": team_id,
                    # No email_id for document uploads
                }

                # Call the working sync function
                result = _create_po_from_extracted_data_sync(creation_params)

                # Convert to expected format
                creation_result = {
                    "success": True,
                    "record_id": result["purchase_order_id"],
                    "po_number": result["po_number"],
                    "record_data": result,
                }

            except Exception as e:
                creation_result = {
                    "success": False,
                    "error": f"PO creation failed: {str(e)}",
                }

        elif creation_activity_name == "create_shipment_from_extracted_data_activity":
            # Placeholder for future shipment implementation
            creation_result = {
                "success": False,
                "error": "Shipment creation not yet implemented",
            }
        elif creation_activity_name == "create_invoice_from_extracted_data_activity":
            # Placeholder for future invoice implementation
            creation_result = {
                "success": False,
                "error": "Invoice creation not yet implemented",
            }
        else:
            available_activities = [
                "create_po_from_extracted_data_activity",
                "create_shipment_from_extracted_data_activity",
                "create_invoice_from_extracted_data_activity",
            ]
            return {
                "success": False,
                "error": f"Unknown creation activity: {creation_activity_name}. Available: {available_activities}",
                "document_id": document_id,
            }

        if not creation_result["success"]:
            return {
                "success": False,
                "error": f"Record creation failed: {creation_result.get('error')}",
                "document_id": document_id,
            }

        activity.logger.info(
            "Database record created successfully",
            document_type=document_type,
            record_id=creation_result.get("record_id"),
            document_id=document_id,
        )

        return {
            "success": True,
            "created_record_id": creation_result.get("record_id"),
            "created_record_type": document_type,
            "created_record_data": creation_result.get("record_data", {}),
            "document_id": document_id,
            "message": f"Successfully processed {document_type} document and created record",
        }

    except Exception as e:
        activity.logger.error(
            f"Complete document processing failed: {str(e)}",
            document_id=document_id,
            document_type=document_type,
            team_id=team_id,
            error_type=type(e).__name__,
        )
        return {"success": False, "error": str(e), "document_id": document_id}
