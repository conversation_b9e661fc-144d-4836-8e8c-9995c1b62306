"""
Shared document matching activities that can be used across multiple workflows.
"""

from typing import Any, Dict, Optional

import structlog
from django.contrib.contenttypes.models import ContentType
from temporalio import activity

from didero.ai.document_matching.document_matching import ai_match_documents
from didero.ai.document_matching.schemas import DocumentComparisonResult
from didero.ai.purchase_order.schemas import PurchaseOrderDetails
from didero.documents.schemas import DocumentType
from didero.invoices.schemas import Invoice as InvoiceSchema
from didero.suppliers.models import Supplier
from didero.tasks.schemas import TaskActionButtonType
from didero.tasks.schemas import TaskActionType as TaskActionTypeName
from didero.tasks.schemas import TaskType as TaskTypeName
from didero.tasks.utils import create_task_v2
from didero.users.models.team_models import Team
from didero.users.models.user_models import User
from didero.workflows.shared_activities.schemas import (
    DocumentMatchContext,
    MatchingResult,
    TaskAssignment,
    TaskMetadata,
)

logger = structlog.get_logger(__name__)


@activity.defn
def match_invoice_to_po_activity(
    invoice_data: InvoiceSchema,
    po_data: PurchaseOrderDetails,
    team_id: int,
    tolerance_config: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Activity to match an invoice against a purchase order using document matching.

    Args:
        invoice_data: Invoice schema
        po_data: PurchaseOrderDetails schema
        team_id: Team ID for context
        tolerance_config: Tolerance configuration dict

    Returns:
        Dict containing matching results and analysis
    """
    # Get activity info for retry tracking
    activity_info = activity.info()
    attempt_number = activity_info.attempt

    logger.info(
        "Starting invoice to PO matching",
        po_number=po_data.po_number,
        team_id=team_id,
        tolerance_config=tolerance_config,
        attempt_number=attempt_number,
        activity_id=activity_info.activity_id,
    )

    # Get invoice-specific instructions from prompts config
    from pathlib import Path

    from didero.ai.utils.utils import get_langfuse_prompt

    prompts_path = (
        Path(__file__).parent.parent.parent
        / "ai"
        / "document_matching"
        / "prompts_config.yaml"
    )
    invoice_instructions_prompt = get_langfuse_prompt(
        prompt_key="invoice_instructions", prompts_path=prompts_path
    )
    invoice_instructions = str(invoice_instructions_prompt.compile())

    # Use the existing document matching function
    comparison_result: DocumentComparisonResult = ai_match_documents(
        document1=invoice_data,
        document2=po_data,
        team_id=team_id,
        document1_type=DocumentType.INVOICE,
        document2_type=DocumentType.PO,
        tolerance_config=tolerance_config,
        additional_context=invoice_instructions,
        context_type="INVOICE_VALIDATION",
    )

    # Log the raw comparison result for debugging
    logger.info(
        "Raw comparison result from AI",
        po_number=po_data.po_number,
        match_result=comparison_result.match_result.value,
        matching_score=comparison_result.matching_score,
        summary=comparison_result.summary,
        header_field_count=len(comparison_result.header_comparison.field_comparisons),
        line_items_count=len(comparison_result.line_items_comparison),
    )

    # Extract field comparison data for frontend
    header_fields = {}
    for (
        field_name,
        field_comparison,
    ) in comparison_result.header_comparison.field_comparisons.items():
        header_fields[field_name] = {
            "status": field_comparison.status.value,
            "document1_value": field_comparison.document1_value,
            "document2_value": field_comparison.document2_value,
        }

    # Extract line item data for frontend
    line_items = []
    for line_item in comparison_result.line_items_comparison:
        item_fields = {}
        for field_name, field_comparison in line_item.field_comparisons.items():
            item_fields[field_name] = {
                "status": field_comparison.status.value,
                "document1_value": field_comparison.document1_value,
                "document2_value": field_comparison.document2_value,
            }

        line_items.append(
            {
                "item_index": line_item.item_index,
                "description": line_item.description,
                "overall_status": line_item.overall_status.value,
                "field_comparisons": item_fields,
            }
        )

    # Validate that comparison data is complete before returning success
    has_header_data = bool(header_fields and len(header_fields) > 0)

    # For line items, also check if they have field_comparisons
    line_items_have_details = False
    if line_items:
        for item in line_items:
            if isinstance(item, dict) and item.get("field_comparisons"):
                line_items_have_details = True
                break

    # If comparison data is incomplete, fail the activity to trigger Temporal retry
    if not (has_header_data and line_items_have_details):
        error_msg = f"Incomplete comparison data: header_data={has_header_data}, line_items_with_details={line_items_have_details}"
        logger.error(
            "Document matching returned incomplete data, failing activity for retry",
            po_number=po_data.po_number,
            attempt_number=attempt_number,
            activity_id=activity_info.activity_id,
            has_header_data=has_header_data,
            line_items_have_details=line_items_have_details,
            header_fields=header_fields,
            line_items=line_items,
            comparison_result_summary=comparison_result.summary,
        )
        raise Exception(error_msg)

    result = {
        "success": True,
        "match_result": comparison_result.match_result.value,
        "matching_score": comparison_result.matching_score,
        "summary": comparison_result.summary,
        "header_comparison": header_fields,
        "line_items_comparison": line_items,
    }

    logger.info(
        "Invoice to PO matching completed with complete data",
        po_number=po_data.po_number,
        match_result=comparison_result.match_result.value,
        matching_score=comparison_result.matching_score,
    )

    return result


def _prepare_clarification_email_params(
    team: Team,
    supplier_name: str,
    document1_reference: str,
    document2_reference: str,
    clarification_reason: str,
) -> Dict[str, Any]:
    """
    Prepare email parameters for document clarification requests.

    Args:
        team: Team instance for context
        supplier_name: Name of the supplier to contact
        document1_reference: Reference of first document (e.g., INV-123)
        document2_reference: Reference of second document (e.g., PO-456)
        clarification_reason: Reason clarification is needed

    Returns:
        Dict with email parameters (email_type, email_to, email_subject, email_body, etc.)
    """
    # TODO: Look up EmailTemplate with email_type="DOCUMENT_CLARIFICATION" when email types are implemented
    # For now, use static template fallback

    # Try to get supplier's default email
    email_to = ""
    try:
        supplier = Supplier.objects.filter(name=supplier_name, team=team).first()
        if supplier and supplier.default_contact and supplier.default_contact.email:
            email_to = supplier.default_contact.email
        else:
            email_to = f"<{supplier_name}_email>"
    except Exception:
        email_to = f"<{supplier_name}_email>"

    # Static email template
    email_subject = f"Document Clarification Required - {document1_reference} vs {document2_reference}"

    email_body = (
        f"Dear {supplier_name},\n\n"
        f"We need clarification regarding the document matching between "
        f"{document1_reference} and {document2_reference}.\n\n"
        f"Issue: {clarification_reason}\n\n"
        f"Please review these documents and provide clarification or corrections "
        f"at your earliest convenience.\n\n"
        f"Best regards,\n"
        f"{team.name} Team"
    )

    return {
        "email_type": "DOCUMENT_CLARIFICATION",  # For future template lookup
        "email_to": email_to,
        "email_cc": "",  # Empty for now
        "email_bcc": "",  # Empty for now
        "email_subject": email_subject,
        "email_body": email_body,
    }


@activity.defn
def create_document_match_review_task_activity(
    context: DocumentMatchContext,
    matching_result: MatchingResult,
    metadata: TaskMetadata,
    assignment: TaskAssignment,
    category: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Create a task for manual document match review with cleaner parameter structure.

    Args:
        context: Document matching context with model and document information
        matching_result: Results from the matching process
        metadata: Additional task metadata
        assignment: Task assignment information
        category: Optional category for the task

    Returns:
        Dict with success status and task IDs or error message
    """
    try:
        activity.logger.info(
            f"Creating document match review task for {context.model_type_name} {context.model_id} in team {context.team_id}"
        )
        activity.logger.info(
            f"Received user_ids: {assignment.user_ids}, user_group_names: {assignment.user_group_names}"
        )

        # Get the team and users
        team = Team.objects.get(id=context.team_id)
        users = User.objects.none()  # Start with empty queryset

        # Add users from specific user IDs if provided
        if assignment.user_ids:
            users = users.union(
                User.objects.filter(id__in=assignment.user_ids, teams=team)
            )

        # Add users from user groups if provided
        if assignment.user_group_names:
            for group_name in assignment.user_group_names:
                group_users = User.objects.filter(
                    teams=team, user_groups__name__iexact=group_name
                )
                users = users.union(group_users)

        # If no users found and no configuration provided, return error
        if not users.exists():
            activity.logger.warning(f"No valid users found for team {context.team_id}")
            return {
                "success": False,
                "error_message": "No valid users found to assign task",
            }

        # Get content type dynamically based on model_type_name
        try:
            from django.apps import apps

            # Try to get the model from the appropriate app
            if context.model_type_name == "Invoice":
                model_class = apps.get_model("invoices", context.model_type_name)
            else:
                model_class = apps.get_model("orders", context.model_type_name)
            content_type = ContentType.objects.get_for_model(model_class)
        except Exception as e:
            activity.logger.error(
                f"Failed to get content type for {context.model_type_name}: {str(e)}"
            )
            return {
                "success": False,
                "error_message": f"Invalid model type: {context.model_type_name}",
            }

        # Task parameters
        task_params = {
            "document1_type": context.document1.doc_type,
            "document1_reference": context.document1.reference,
            "document1_id": context.document1.doc_id,
            "document2_type": context.document2.doc_type,
            "document2_reference": context.document2.reference,
            "document2_id": context.document2.doc_id,
            "match_result": matching_result.match_result,
            "matching_score": f"{matching_result.matching_score:.2f}",
            "review_reason": matching_result.review_reason,
            "supplier_name": metadata.supplier_name,
            "total_amount": metadata.total_amount,
            "comparison_summary": matching_result.comparison_summary,
            "document1_data": context.document1.data,
            "document2_data": context.document2.data,
            "comparison_data": matching_result.comparison_data,
        }

        # Context panels for document comparison
        from didero.tasks.schemas import TaskContextPanelType
        from didero.tasks.utils import ContextPanelDefinition

        context_panels = [
            ContextPanelDefinition(
                panel_type=TaskContextPanelType.DOCUMENT_COMPARE,
                param_values={
                    "documentIds": [context.model_id, context.document2.doc_id]
                },
            )
        ]

        # Task actions - using CreateTaskActionParams format
        from didero.tasks.utils import CreateTaskActionParams

        actions = [
            CreateTaskActionParams(
                action_type=TaskActionTypeName.APPROVE_DOCUMENT_MATCH,
                action_params={},
                action_execution_params={
                    "document1_id": context.document1.doc_id,
                    "document1_type": context.document1.doc_type,
                    "document2_id": context.document2.doc_id,
                    "document2_type": context.document2.doc_type,
                },
                button_type=TaskActionButtonType.GREEN,
            ),
            CreateTaskActionParams(
                action_type=TaskActionTypeName.REQUEST_DOCUMENT_CLARIFICATION,
                action_params={},
                action_execution_params=_prepare_clarification_email_params(
                    team=team,
                    supplier_name=metadata.supplier_name,
                    document1_reference=context.document1.reference,
                    document2_reference=context.document2.reference,
                    clarification_reason=matching_result.review_reason,
                ),
                button_type=TaskActionButtonType.STANDARD,
            ),
        ]

        tasks_created = []
        for user in users:
            task = create_task_v2(
                task_type=TaskTypeName.DOCUMENT_MATCH_REVIEW,
                user=user,
                model_type=content_type,
                model_id=context.model_id,
                task_type_params=task_params,
                actions=actions,
                context_panels=context_panels,
            )
            tasks_created.append(task.pk)

        activity.logger.info(
            f"Created {len(tasks_created)} document match review tasks: {tasks_created}"
        )

        return {
            "success": True,
            "task_ids": tasks_created,
            "message": f"Created {len(tasks_created)} document match review tasks",
        }

    except Exception as e:
        activity.logger.error(
            f"Failed to create document match review task: {str(e)}", exc_info=True
        )
        return {
            "success": False,
            "error_message": f"Failed to create document match review task: {str(e)}",
        }
