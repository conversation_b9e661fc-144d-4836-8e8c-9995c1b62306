import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from django.test import TestCase

from temporalio.common import RetryPolicy

from didero.workflows.shared_activities.purchase_order_operations import (
    enqueue_po_auto_creation_workflow,
    resolve_purchase_order_with_auto_creation,
    should_auto_create_po_for_team,
)


class TestShouldAutoCreatePOForTeam(TestCase):
    """Test the team configuration check for auto-creation."""

    @patch("didero.integrations.models.ERPIntegrationConfig")
    def test_should_auto_create_po_for_team_enabled(self, mock_config):
        """Test that auto-creation is enabled when ERP config is properly set."""
        # Mock the query to return a configuration
        mock_config.objects.filter.return_value.exists.return_value = True

        result = should_auto_create_po_for_team(4)  # IonQ team ID

        self.assertTrue(result)
        mock_config.objects.filter.assert_called_once_with(
            team_id=4, enabled=True, auto_create_missing_pos=True, erp_type="netsuite"
        )

    @patch("didero.integrations.models.ERPIntegrationConfig")
    def test_should_auto_create_po_for_team_disabled(self, mock_config):
        """Test that auto-creation is disabled when ERP config is not set."""
        # Mock the query to return no configuration
        mock_config.objects.filter.return_value.exists.return_value = False

        result = should_auto_create_po_for_team(999)  # Non-IonQ team ID

        self.assertFalse(result)
        mock_config.objects.filter.assert_called_once_with(
            team_id=999, enabled=True, auto_create_missing_pos=True, erp_type="netsuite"
        )

    @patch("didero.integrations.models.ERPIntegrationConfig")
    def test_should_auto_create_po_for_team_fallback_ionq(self, mock_config):
        """Test fallback to hardcoded IonQ teams when database fails."""
        # Mock the query to raise an exception
        mock_config.objects.filter.side_effect = Exception("Database error")

        # Test IonQ teams (4, 173) - should return True
        self.assertTrue(should_auto_create_po_for_team(4))
        self.assertTrue(should_auto_create_po_for_team(173))

        # Test non-IonQ team - should return False
        self.assertFalse(should_auto_create_po_for_team(999))


class TestEnqueuePOAutoCreationWorkflow(TestCase):
    """Test the child workflow orchestration."""

    @patch(
        "didero.workflows.shared_activities.purchase_order_operations.get_temporal_client"
    )
    @patch(
        "didero.workflows.shared_activities.purchase_order_operations.get_queue_for_workflow_type"
    )
    def test_enqueue_po_auto_creation_workflow_success(
        self, mock_get_queue, mock_get_client
    ):
        """Test successful PO auto-creation workflow enqueuing."""

        async def async_test():
            # Mock dependencies
            mock_client = AsyncMock()
            mock_get_client.return_value = mock_client
            mock_get_queue.return_value = "po_creation_queue"

            # Mock workflow handle
            mock_handle = AsyncMock()
            mock_handle.id = "test-workflow-id"
            mock_handle.result.return_value = {
                "success": True,
                "po_id": "123",
                "po_number": "PO431",
                "created_in_draft": False,
            }
            mock_client.start_workflow.return_value = mock_handle

            # Execute the function
            result = await enqueue_po_auto_creation_workflow(
                team_id=4,
                po_number="PO431",
                source_email_id="email123",
                source_context="order_acknowledgement",
            )

            # Verify results
            self.assertTrue(result["success"])
            self.assertEqual(result["po_id"], "123")
            self.assertEqual(result["po_number"], "PO431")
            self.assertTrue(
                result["workflow_id"].startswith(
                    "auto-po-creation-PO431-4-order_acknowledgement-"
                )
            )
            self.assertEqual(result["temporal_id"], "test-workflow-id")

            # Verify workflow was started correctly
            mock_client.start_workflow.assert_called_once()
            call_args = mock_client.start_workflow.call_args
            self.assertEqual(call_args[1]["task_queue"], "po_creation_queue")
            self.assertIsInstance(call_args[1]["retry_policy"], RetryPolicy)

        asyncio.run(async_test())

    @patch(
        "didero.workflows.shared_activities.purchase_order_operations.get_temporal_client"
    )
    @patch(
        "didero.workflows.shared_activities.purchase_order_operations.get_queue_for_workflow_type"
    )
    def test_enqueue_po_auto_creation_workflow_timeout(
        self, mock_get_queue, mock_get_client
    ):
        """Test timeout handling in PO auto-creation workflow."""

        async def async_test():
            # Mock dependencies
            mock_client = AsyncMock()
            mock_get_client.return_value = mock_client
            mock_get_queue.return_value = "po_creation_queue"

            # Mock workflow handle that times out
            mock_handle = AsyncMock()
            mock_handle.id = "test-workflow-id"
            mock_handle.result.side_effect = asyncio.TimeoutError()
            mock_client.start_workflow.return_value = mock_handle

            # Execute the function
            result = await enqueue_po_auto_creation_workflow(
                team_id=4,
                po_number="PO431",
                source_email_id="email123",
                source_context="shipment",
            )

            # Verify timeout handling
            self.assertFalse(result["success"])
            self.assertIn("timed out", result["error_message"])
            self.assertEqual(result["temporal_id"], "test-workflow-id")

        asyncio.run(async_test())


class TestResolvePurchaseOrderWithAutoCreation(TestCase):
    """Test the main smart PO resolution activity."""

    @patch(
        "didero.workflows.shared_activities.purchase_order_operations.retrieve_purchase_order_activity"
    )
    def test_resolve_po_found_immediately(self, mock_retrieve):
        """Test when PO is found on first lookup - no auto-creation needed."""

        async def async_test():
            # Mock successful standard lookup
            mock_retrieve.return_value = {
                "success": True,
                "po_found": True,
                "purchase_order_id": "123",
                "purchase_order_details": {"po_number": "PO431"},
            }

            result = await resolve_purchase_order_with_auto_creation(
                po_number="PO431",
                team_id=4,
                source_email_id="email123",
                source_context="order_acknowledgement",
            )

            # Verify no auto-creation attempted
            self.assertTrue(result["success"])
            self.assertFalse(result["auto_created"])
            self.assertFalse(result["auto_creation_attempted"])
            self.assertEqual(result["purchase_order_id"], "123")

        asyncio.run(async_test())

    @patch(
        "didero.workflows.shared_activities.purchase_order_operations.should_auto_create_po_for_team"
    )
    @patch(
        "didero.workflows.shared_activities.purchase_order_operations.retrieve_purchase_order_activity"
    )
    def test_resolve_po_not_found_auto_creation_disabled(
        self, mock_retrieve, mock_should_auto_create
    ):
        """Test when PO not found and auto-creation is disabled for team."""

        async def async_test():
            # Mock failed lookup and disabled auto-creation
            mock_retrieve.return_value = {
                "success": False,
                "error_message": "PO not found",
            }
            mock_should_auto_create.return_value = False

            result = await resolve_purchase_order_with_auto_creation(
                po_number="PO431",
                team_id=999,  # Non-IonQ team
                source_email_id="email123",
                source_context="shipment",
            )

            # Verify fallback to standard error
            self.assertFalse(result["success"])
            self.assertFalse(result["auto_creation_attempted"])
            self.assertEqual(
                result["auto_creation_error"], "Auto-creation not enabled for team"
            )

        asyncio.run(async_test())

    @patch(
        "didero.workflows.shared_activities.purchase_order_operations.enqueue_po_auto_creation_workflow"
    )
    @patch(
        "didero.workflows.shared_activities.purchase_order_operations.should_auto_create_po_for_team"
    )
    @patch(
        "didero.workflows.shared_activities.purchase_order_operations.retrieve_purchase_order_activity"
    )
    def test_resolve_po_successful_auto_creation(
        self, mock_retrieve, mock_should_auto_create, mock_enqueue
    ):
        """Test successful auto-creation and retry flow."""

        async def async_test():
            # Mock failed initial lookup
            mock_retrieve.side_effect = [
                {"success": False, "error_message": "PO not found"},  # First call
                {  # Second call after auto-creation
                    "success": True,
                    "po_found": True,
                    "purchase_order_id": "456",
                    "purchase_order_details": {"po_number": "PO431"},
                },
            ]

            # Mock enabled auto-creation
            mock_should_auto_create.return_value = True

            # Mock successful workflow creation
            mock_enqueue.return_value = {
                "success": True,
                "po_id": "456",
                "po_number": "PO431",
                "workflow_id": "auto-workflow-123",
            }

            result = await resolve_purchase_order_with_auto_creation(
                po_number="PO431",
                team_id=4,  # IonQ team
                source_email_id="email123",
                source_context="order_acknowledgement",
            )

            # Verify successful auto-creation
            self.assertTrue(result["success"])
            self.assertTrue(result["auto_created"])
            self.assertTrue(result["auto_creation_attempted"])
            self.assertEqual(result["purchase_order_id"], "456")
            self.assertEqual(
                result["creation_details"]["workflow_id"], "auto-workflow-123"
            )

            # Verify retrieve was called twice (initial + retry)
            self.assertEqual(mock_retrieve.call_count, 2)

        asyncio.run(async_test())


class TestIntegrationScenarios(TestCase):
    """Test realistic integration scenarios."""

    def test_ionq_order_acknowledgement_missing_po_scenario(self):
        """Test realistic IonQ order acknowledgement with missing PO."""

        async def async_test():
            with patch.multiple(
                "didero.workflows.shared_activities.purchase_order_operations",
                retrieve_purchase_order_activity=MagicMock(
                    side_effect=[
                        {"success": False, "error_message": "PO not found"},
                        {"success": True, "po_found": True, "purchase_order_id": "789"},
                    ]
                ),
                should_auto_create_po_for_team=MagicMock(return_value=True),
                enqueue_po_auto_creation_workflow=AsyncMock(
                    return_value={
                        "success": True,
                        "po_id": "789",
                        "po_number": "PO431",
                        "workflow_id": "auto-po-creation-PO431-4-order_acknowledgement",
                    }
                ),
            ):
                result = await resolve_purchase_order_with_auto_creation(
                    po_number="PO431",
                    team_id=4,  # IonQ team
                    source_email_id="oa_email_123",
                    source_context="order_acknowledgement",
                )

                self.assertTrue(result["success"])
                self.assertTrue(result["auto_created"])
                self.assertEqual(result["purchase_order_id"], "789")

        asyncio.run(async_test())

    def test_non_ionq_team_missing_po_scenario(self):
        """Test non-IonQ team with missing PO - should not attempt auto-creation."""

        async def async_test():
            with patch.multiple(
                "didero.workflows.shared_activities.purchase_order_operations",
                retrieve_purchase_order_activity=MagicMock(
                    return_value={"success": False, "error_message": "PO not found"}
                ),
                should_auto_create_po_for_team=MagicMock(return_value=False),
            ):
                result = await resolve_purchase_order_with_auto_creation(
                    po_number="PO999",
                    team_id=999,  # Non-IonQ team
                    source_email_id="email_456",
                    source_context="shipment",
                )

                self.assertFalse(result["success"])
                self.assertFalse(result["auto_creation_attempted"])
                self.assertEqual(
                    result["auto_creation_error"], "Auto-creation not enabled for team"
                )

        asyncio.run(async_test())
