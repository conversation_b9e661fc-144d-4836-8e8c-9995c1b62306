"""
Generic document retrieval activity for Temporal workflows.

This activity provides a unified interface for retrieving documents from:
- Didero database (Phase 1)
- ERP systems (Phase 2)
- User uploads via tasks (Phase 1)

Phase 1: Database search + upload task creation
Phase 2: Add extraction to signal handler
Phase 3: Add polling for extracted documents
"""

from datetime import timedelta
from enum import StrEnum
from typing import Any, Dict, Optional, Type, Union

import structlog
from pydantic import BaseModel
from temporalio import activity, workflow

from didero.ai.purchase_order.schemas import PurchaseOrderDetails

# Import document types and schemas
from didero.documents.schemas import DocumentType
from didero.invoices.schemas import Invoice

# Future imports:
# from didero.ai.shipment.schemas import ShipmentDetails
# from didero.ai.contract.schemas import ContractDetails
# from didero.ai.order_acknowledgement.schemas import OrderAcknowledgementDetails

logger = structlog.get_logger(__name__)


class DocumentSource(StrEnum):
    """Source of the document."""

    DIDERO = "didero"
    ERP = "erp"
    UPLOAD = "upload"


class SearchStatus(StrEnum):
    """Status of document search operation."""

    FOUND = "found"
    NOT_FOUND = "not_found"
    ERROR = "error"


class RetrievalStatus(StrEnum):
    """Status of document retrieval operation."""

    FOUND = "found"
    UPLOAD_TASK_CREATED = "upload_task_created"
    ERROR = "error"
    NOT_FOUND = "not_found"
    TIMEOUT = "timeout"


# Define all retrievable document types as a union
RetrievableDocument = Union[
    PurchaseOrderDetails,
    Invoice,
    # Add more as implemented:
    # ShipmentDetails,
    # ContractDetails,
    # OrderAcknowledgementDetails,
]

# Map DocumentType enum to their schema classes
DOCUMENT_TYPE_TO_SCHEMA: Dict[DocumentType, Type[BaseModel]] = {
    DocumentType.PO: PurchaseOrderDetails,
    DocumentType.INVOICE: Invoice,
    # Add more mappings as schemas are created:
    # DocumentType.SHIPPING_ORDER: ShipmentDetails,
    # DocumentType.CONTRACT: ContractDetails,
    # DocumentType.ORDER_ACKNOWLEDGEMENT: OrderAcknowledgementDetails,
}

# Supported document types for retrieval (subset of all DocumentType values)
RETRIEVABLE_DOCUMENT_TYPES = list(DOCUMENT_TYPE_TO_SCHEMA.keys())


class SearchResult(BaseModel):
    """Result of document search operation."""

    status: SearchStatus
    document: Optional[RetrievableDocument] = None
    message: Optional[str] = None
    source: Optional[DocumentSource] = None


class RetrievalResult(BaseModel):
    """Result of document retrieval operation."""

    status: RetrievalStatus
    document: Optional[RetrievableDocument] = None
    task_id: Optional[str] = None
    message: Optional[str] = None
    source: Optional[DocumentSource] = None


@activity.defn
async def search_document_activity(
    team_id: int, document_type: str, search_criteria: Dict[str, Any]
) -> SearchResult:
    """
    Pure document search activity - no side effects.

    Args:
        team_id: Team ID for scoping the search
        document_type: Type of document to search for (po, invoice, shipment, etc.)
        search_criteria: Criteria for finding the document (po_number, invoice_number, etc.)

    Returns:
        SearchResult indicating if document was found

    Phase 1: Database search only
    Phase 2: Add ERP system search
    """

    logger.info(
        "Document search requested",
        team_id=team_id,
        document_type=document_type,
        search_criteria=search_criteria,
    )

    try:
        # Phase 1: Search for document in database
        document = await _find_document_in_database(
            team_id, document_type, search_criteria
        )

        if document:
            logger.info(
                "Document found in database",
                team_id=team_id,
                document_type=document_type,
                source="didero",
            )

            return SearchResult(
                status=SearchStatus.FOUND,
                document=document,
                source=DocumentSource.DIDERO,
            )

        # TODO Phase 2: Add ERP system search
        # if not document:
        #     document = await _find_document_in_erp(team_id, document_type, search_criteria)
        #     if document:
        #         return SearchResult(status="found", document=document, source="erp")

        logger.info(
            "Document not found in database",
            team_id=team_id,
            document_type=document_type,
            search_criteria=search_criteria,
        )

        return SearchResult(
            status=SearchStatus.NOT_FOUND, message="Document not found in database"
        )

    except Exception as e:
        logger.error(
            "Error in document search activity",
            team_id=team_id,
            document_type=document_type,
            error=str(e),
            exc_info=True,
        )

        return SearchResult(
            status=SearchStatus.ERROR, message=f"Document search failed: {str(e)}"
        )


@activity.defn
async def retrieve_document_activity(
    team_id: int,
    document_type: str,
    search_criteria: Dict[str, Any],
    requesting_workflow: str,
    context_object_type: str,
    context_object_id: str,
    email_id: str,
    workflow_id: str,
    behavior_config: Optional[Dict[str, Any]] = None,
) -> RetrievalResult:
    """
    Generic document retrieval activity that uses invoice as context object.

    Args:
        team_id: Team ID for scoping the search
        document_type: Type of document to retrieve (po, invoice, shipment, etc.)
        search_criteria: Criteria for finding the document (po_number, invoice_number, etc.)
        requesting_workflow: Name of the workflow requesting the document
        context_object_type: Type of context object (invoice, communication, etc.)
        context_object_id: ID of context object for task linking
        email_id: Email ID for audit trail
        workflow_id: ID of the workflow for signal routing
        behavior_config: Optional configuration for retrieval behavior

    Returns:
        RetrievalResult indicating success, failure, or need for upload

    Phase 1: Search database first, create upload tasks when not found
    Phase 2: Signal-based approach - workflow waits for upload completion signals
    """

    logger.info(
        "Generic document retrieval requested",
        team_id=team_id,
        document_type=document_type,
        requesting_workflow=requesting_workflow,
        context_object_type=context_object_type,
        context_object_id=context_object_id,
        email_id=email_id,
        search_criteria=search_criteria,
        behavior_config=behavior_config,
    )

    try:
        # Phase 1: Use search activity for pure document search
        search_result = await search_document_activity(
            team_id=team_id,
            document_type=document_type,
            search_criteria=search_criteria,
        )

        if search_result.status == SearchStatus.FOUND:
            logger.info(
                "Document found via search activity",
                team_id=team_id,
                document_type=document_type,
                source=search_result.source,
            )

            return RetrievalResult(
                status=RetrievalStatus.FOUND,
                document=search_result.document,
                source=search_result.source,
            )

        elif search_result.status == SearchStatus.ERROR:
            logger.error(
                "Search activity failed",
                team_id=team_id,
                document_type=document_type,
                error=search_result.message,
            )
            return RetrievalResult(
                status=RetrievalStatus.ERROR,
                message=f"Document search failed: {search_result.message}",
            )

        # Document not found - create upload task with context object
        logger.info(
            "Document not found, creating upload task with context",
            team_id=team_id,
            document_type=document_type,
            context_object_type=context_object_type,
            context_object_id=context_object_id,
            search_criteria=search_criteria,
        )

        upload_result = await _create_upload_task_with_workflow_id(
            team_id=team_id,
            document_type=document_type,
            search_criteria=search_criteria,
            requesting_workflow=requesting_workflow,
            context_object_type=context_object_type,
            context_object_id=context_object_id,
            email_id=email_id,
            workflow_id=workflow_id,
            behavior_config=behavior_config,
        )

        # Return immediately - workflow will wait for signal
        return upload_result

    except Exception as e:
        logger.error(
            "Error in document retrieval activity",
            team_id=team_id,
            document_type=document_type,
            requesting_workflow=requesting_workflow,
            error=str(e),
            exc_info=True,
        )

        return RetrievalResult(
            status=RetrievalStatus.ERROR, message=f"Document retrieval failed: {str(e)}"
        )


async def _find_document_in_database(
    team_id: int, document_type: str, search_criteria: Dict[str, Any]
) -> Optional[Dict[str, Any]]:
    """Search Didero database for existing documents - Phase 1."""

    with workflow.unsafe.imports_passed_through():
        from asgiref.sync import sync_to_async

        from didero.invoices.models import Invoice
        from didero.orders.models import PurchaseOrder
        from didero.workflows.core.nodes.purchase_orders.shipments import (
            get_purchase_order_from_po_number,
        )

    try:
        # Convert string to DocumentType enum if needed
        if isinstance(document_type, str):
            # Map common string values to DocumentType enum
            doc_type_mapping = {
                "po": DocumentType.PO,
                "purchase_order": DocumentType.PO,
                "invoice": DocumentType.INVOICE,
                # Add more mappings as needed
            }
            doc_type_enum = doc_type_mapping.get(document_type.lower())
        else:
            doc_type_enum = document_type

        # Handle different document types
        if doc_type_enum == DocumentType.PO:
            # Search for PO using existing logic
            po_number = search_criteria.get("po_number")
            if not po_number or po_number == "unknown":
                logger.info(
                    "No PO number provided in search criteria - will create upload task",
                    search_criteria=search_criteria,
                )
                return None  # This will cause upload task creation

            # Use existing function to avoid duplication
            @sync_to_async(thread_sensitive=True)
            def get_po():
                from didero.ai.purchase_order.schemas import PurchaseOrderDetails

                po = get_purchase_order_from_po_number(po_number, team_id)
                if po:
                    # Return PurchaseOrderDetails directly for strict typing
                    return PurchaseOrderDetails.from_model(po)
                return None

            return await get_po()

        elif doc_type_enum == DocumentType.INVOICE:
            # Search for invoice by number
            invoice_number = search_criteria.get("invoice_number")
            if not invoice_number:
                return None

            @sync_to_async
            def get_invoice():
                try:
                    invoice_model = InvoiceModel.objects.filter(
                        team_id=team_id, invoice_number=invoice_number
                    ).first()

                    if invoice_model:
                        # Convert to Invoice schema format for consistency
                        return Invoice.from_model(invoice_model)
                    return None
                except Exception as e:
                    logger.error(
                        "Error fetching invoice from database",
                        invoice_number=invoice_number,
                        team_id=team_id,
                        error=str(e),
                    )
                    return None

            return await get_invoice()

        # TODO: Add support for other document types (shipment, receiving, etc.)

    except Exception as e:
        logger.error(
            "Error searching database for document",
            team_id=team_id,
            document_type=document_type,
            search_criteria=search_criteria,
            error=str(e),
            exc_info=True,
        )

    return None


def _determine_task_assignee(
    team_id: int, behavior_config: Optional[Dict[str, Any]] = None
) -> tuple[Optional["User"], Optional["DideroUserGroup"]]:
    """
    Determine task assignee based on behavior config with fallback to contractor admin.

    Priority order:
    1. User group from behavior_config
    2. Individual user from behavior_config
    3. Contractor admin as fallback

    Returns:
        Tuple of (assigned_user, assigned_user_group) - one will be None
    """
    with workflow.unsafe.imports_passed_through():
        from didero.users.models import DideroUserGroup, Team, User

    assigned_user_group_names = (
        behavior_config.get("assigned_user_group_names", []) if behavior_config else []
    )
    assigned_user_ids = (
        behavior_config.get("assigned_user_ids", []) if behavior_config else []
    )

    # Try user group first
    if assigned_user_group_names:
        try:
            team = Team.objects.get(id=team_id)
            assigned_user_group = DideroUserGroup.objects.get(
                name=assigned_user_group_names[0], team=team
            )
            logger.info(
                "Assigning task to user group",
                team_id=team_id,
                user_group_name=assigned_user_group.name,
                user_group_id=assigned_user_group.id,
            )
            return None, assigned_user_group
        except (Team.DoesNotExist, DideroUserGroup.DoesNotExist) as e:
            logger.error(
                "Could not find configured user group, trying individual user",
                user_group_name=assigned_user_group_names[0],
                team_id=team_id,
                error=str(e),
            )

    # Try individual user
    if assigned_user_ids:
        try:
            assigned_user = User.objects.get(id=assigned_user_ids[0])
            logger.info(
                "Assigning task to configured user",
                team_id=team_id,
                user_id=assigned_user.id,
                username=assigned_user.username,
            )
            return assigned_user, None
        except User.DoesNotExist as e:
            logger.error(
                "Could not find configured user, using contractor fallback",
                user_id=assigned_user_ids[0],
                team_id=team_id,
                error=str(e),
            )

    # Fallback to contractor admin
    try:
        team = Team.objects.get(id=team_id)
        contractor_username = f"contractors+{team.name}"
        assigned_user = User.objects.get(username=contractor_username)
        logger.info(
            "Using contractor admin fallback for task assignment",
            team_id=team_id,
            contractor_username=contractor_username,
            contractor_user_id=assigned_user.id,
        )
        return assigned_user, None
    except (Team.DoesNotExist, User.DoesNotExist) as e:
        logger.error(
            "Could not find contractor admin fallback",
            team_id=team_id,
            error=str(e),
        )
        return None, None


def _get_context_object_info(
    context_object_type: str,
    context_object_id: str,
    team_id: int,
    search_criteria: Dict[str, Any],
) -> tuple[Optional["ContentType"], Optional[str], str, str, str]:
    """
    Get context object information for task linking.

    Returns:
        Tuple of (content_type, model_id, supplier_name, reference_number, reference_document_type)
    """
    with workflow.unsafe.imports_passed_through():
        from django.contrib.contenttypes.models import ContentType

        from didero.suppliers.models import Communication
        from didero.users.models import Team

    if context_object_type == "invoice":
        from didero.invoices.models import Invoice as InvoiceModel

        try:
            invoice = InvoiceModel.objects.get(id=context_object_id)
            content_type = ContentType.objects.get_for_model(InvoiceModel)
            model_id = str(context_object_id)

            # Get supplier from PO if available, otherwise from search criteria
            if invoice.purchase_order and invoice.purchase_order.supplier:
                supplier_name = invoice.purchase_order.supplier.name
            else:
                supplier_name = search_criteria.get("supplier_name", "Unknown Supplier")

            reference_number = invoice.invoice_number or "Unknown"
            reference_document_type = "Invoice"

            logger.info(
                "Using invoice as context object for task",
                invoice_id=context_object_id,
                invoice_number=reference_number,
                supplier_name=supplier_name,
            )

            return (
                content_type,
                model_id,
                supplier_name,
                reference_number,
                reference_document_type,
            )

        except InvoiceModel.DoesNotExist:
            logger.error(
                "Invoice not found, falling back to team context",
                invoice_id=context_object_id,
            )
            # Fall through to team fallback

    elif context_object_type == "communication":
        content_type = ContentType.objects.get_for_model(Communication)
        model_id = str(context_object_id)
        supplier_name = search_criteria.get("supplier_name", "Unknown Supplier")
        reference_number = search_criteria.get("invoice_number") or search_criteria.get(
            "po_number", "Unknown"
        )
        reference_document_type = (
            "Invoice" if search_criteria.get("invoice_number") else "PO"
        )

        return (
            content_type,
            model_id,
            supplier_name,
            reference_number,
            reference_document_type,
        )

    # Fallback to team context
    content_type = ContentType.objects.get_for_model(Team)
    model_id = str(team_id)
    supplier_name = search_criteria.get("supplier_name", "Unknown Supplier")
    reference_number = search_criteria.get("invoice_number") or search_criteria.get(
        "po_number", "Unknown"
    )
    reference_document_type = (
        "Invoice" if search_criteria.get("invoice_number") else "Document"
    )

    return (
        content_type,
        model_id,
        supplier_name,
        reference_number,
        reference_document_type,
    )


def _build_task_params(
    document_type: str,
    reference_document_type: str,
    supplier_name: str,
    reference_number: str,
    requesting_workflow: str,
    team_id: int,
    email_id: str,
    workflow_id: str,
    search_criteria: Dict[str, Any],
) -> Dict[str, Any]:
    """Build task type parameters for upload task."""
    return {
        # Required params for DocumentUploadRequestParams
        "document_type_required": document_type,
        "reference_document_type": reference_document_type,
        "supplier_name": supplier_name,
        "reference_number": reference_number,
        # Additional context for extraction/storage when task is completed
        "requesting_workflow": requesting_workflow,
        "team_id": team_id,
        "email_id": email_id,
        "workflow_id": workflow_id,  # Store workflow_id for signal routing
        **search_criteria,  # Include search criteria for extraction context
    }


def _create_task_with_upload_action(
    assigned_user: Optional["User"],
    assigned_user_group: Optional["DideroUserGroup"],
    content_type: "ContentType",
    model_id: str,
    task_type_params: Dict[str, Any],
    document_type: str,
) -> str:
    """Create task with upload action and return task ID."""
    with workflow.unsafe.imports_passed_through():
        from didero.tasks.models import (
            Task,
            TaskAction,
            TaskActionType,
            TaskTypeName,
            TaskTypeV2,
        )
        from didero.tasks.schemas import (
            ActionExecutionType,
            TaskActionButtonType,
            TaskActionStatus,
        )
        from didero.tasks.utils import replace_placeholders

    # Get TaskTypeV2 for DOCUMENT_UPLOAD_REQUEST
    task_type = TaskTypeV2.objects.get(name=TaskTypeName.DOCUMENT_UPLOAD_REQUEST)

    # Build task config using TaskTypeV2 templates
    task_config = {
        "title": replace_placeholders(task_type.title, task_type_params),
        "description": replace_placeholders(task_type.description, task_type_params),
        "task_type": task_type.name,
        "preview_title": replace_placeholders(
            task_type.preview_title, task_type_params
        ),
        "preview_description": replace_placeholders(
            task_type.preview_description, task_type_params
        ),
        "category": task_type.category if task_type.category else "GENERAL",
        "task_params": task_type_params,
        "context_panels": [],
    }

    # Create the task
    task = Task.objects.create(
        user=assigned_user,
        user_group=assigned_user_group,
        task_type_v2=task_type,
        task_config=task_config,
        status="PENDING",
        model_type=content_type,
        model_id=model_id,
    )

    # Create the UPLOAD_DOCUMENT action
    upload_action_type = TaskActionType.objects.get(name="UPLOAD_DOCUMENT")
    TaskAction.objects.create(
        task=task,
        action_type=upload_action_type,
        title="Upload Document",
        sub_text=f"Upload the missing {document_type}",
        status=TaskActionStatus.PENDING,
        execution_type=ActionExecutionType.CLIENT,
        button_type=TaskActionButtonType.STANDARD,
        execution_params={},  # Empty - frontend reads from task_params
    )

    logger.info(
        "Created upload task with action",
        task_id=task.id,
        document_type=document_type,
        assigned_to_user=assigned_user.username if assigned_user else None,
        assigned_to_group=assigned_user_group.name if assigned_user_group else None,
    )

    return str(task.id)


async def _create_upload_task_with_workflow_id(
    team_id: int,
    document_type: str,
    search_criteria: Dict[str, Any],
    requesting_workflow: str,
    context_object_type: str,
    context_object_id: str,
    email_id: str,
    workflow_id: str,
    behavior_config: Optional[Dict[str, Any]] = None,
) -> RetrievalResult:
    """Create an upload task for the missing document with context object linking and workflow_id for signal routing."""

    with workflow.unsafe.imports_passed_through():
        from asgiref.sync import sync_to_async

    logger.info(
        "Creating upload task for missing document with context",
        team_id=team_id,
        document_type=document_type,
        requesting_workflow=requesting_workflow,
        context_object_type=context_object_type,
        context_object_id=context_object_id,
        email_id=email_id,
        search_criteria=search_criteria,
    )

    try:

        @sync_to_async
        def create_task():
            try:
                # 1. Determine task assignee
                assigned_user, assigned_user_group = _determine_task_assignee(
                    team_id, behavior_config
                )

                # 2. Get context object information
                (
                    content_type,
                    model_id,
                    supplier_name,
                    reference_number,
                    reference_document_type,
                ) = _get_context_object_info(
                    context_object_type, context_object_id, team_id, search_criteria
                )

                # 3. Build task parameters
                task_type_params = _build_task_params(
                    document_type,
                    reference_document_type,
                    supplier_name,
                    reference_number,
                    requesting_workflow,
                    team_id,
                    email_id,
                    workflow_id,
                    search_criteria,
                )

                # 4. Create task with upload action
                return _create_task_with_upload_action(
                    assigned_user,
                    assigned_user_group,
                    content_type,
                    model_id,
                    task_type_params,
                    document_type,
                )

            except Exception as e:
                logger.error(
                    "Failed to create upload task in database",
                    team_id=team_id,
                    document_type=document_type,
                    context_object_type=context_object_type,
                    context_object_id=context_object_id,
                    error=str(e),
                    exc_info=True,
                )
                raise

        task_id = await create_task()

        logger.info(
            "Upload task created successfully with context",
            team_id=team_id,
            document_type=document_type,
            requesting_workflow=requesting_workflow,
            context_object_type=context_object_type,
            context_object_id=context_object_id,
            task_id=task_id,
        )

        return RetrievalResult(
            status=RetrievalStatus.UPLOAD_TASK_CREATED,
            task_id=task_id,
            message=f"Upload task created for {document_type} with {context_object_type} context",
            source=DocumentSource.UPLOAD,
        )

    except Exception as e:
        logger.error(
            "Failed to create upload task with context",
            team_id=team_id,
            document_type=document_type,
            requesting_workflow=requesting_workflow,
            context_object_type=context_object_type,
            context_object_id=context_object_id,
            error=str(e),
            exc_info=True,
        )

        return RetrievalResult(
            status=RetrievalStatus.ERROR,
            message=f"Failed to create upload task: {str(e)}",
        )
