"""
Follow-up Handler System for Temporal workflows.

This provides a clean way to handle different follow-up types while working
properly with Temporal's activity system.
"""

import structlog
from asgiref.sync import sync_to_async
from temporalio import activity

from didero.workflows.core.nodes.follow_up.schemas import (
    ActivityResult,
    TaskCreationResult,
)
from didero.workflows.core.nodes.follow_up.utils import (
    check_oa_exists_for_po,
    check_oa_ship_dates_complete,
)

logger = structlog.get_logger(__name__)


# =============================================================================
# CONDITION CHECKERS - Simple functions that check if follow-up is needed
# =============================================================================


class BaseConditionChecker:
    """Base class for condition checkers."""

    async def check(self, purchase_order_id: str) -> bool:
        """Check if the follow-up condition is satisfied."""
        raise NotImplementedError


class OAConditionChecker(BaseConditionChecker):
    """Checks if Order Acknowledgement exists."""

    async def check(self, purchase_order_id: str) -> bool:
        return await sync_to_async(check_oa_exists_for_po, thread_sensitive=True)(
            purchase_order_id
        )


class ShipDateConditionChecker(BaseConditionChecker):
    """Checks if ship dates are complete in OA."""

    async def check(self, purchase_order_id: str) -> bool:
        return await sync_to_async(check_oa_ship_dates_complete, thread_sensitive=True)(
            purchase_order_id
        )


class ShipmentConditionChecker(BaseConditionChecker):
    """Checks shipment status based on ship date settings."""

    async def check(self, purchase_order_id: str) -> bool:
        """
        Check if shipment follow-up is needed based on promised ship dates vs actual shipments.

        Returns True if follow-up is needed (items haven't shipped), False if no follow-up needed.
        """
        from didero.users.utils.team_setting_utils import (
            get_followup_config_for_supplier,
        )
        from didero.workflows.core.nodes.follow_up.utils import (
            check_shipment_follow_up_needed,
            get_purchase_order_with_relations,
        )

        # Get PO with relations
        purchase_order = await sync_to_async(
            get_purchase_order_with_relations, thread_sensitive=True
        )(purchase_order_id)

        # Get follow-up configuration
        followup_config = await sync_to_async(
            get_followup_config_for_supplier, thread_sensitive=True
        )(purchase_order.team, purchase_order.supplier)

        # Check if shipment follow-up is needed today
        needs_followup, reason, items = await sync_to_async(
            check_shipment_follow_up_needed, thread_sensitive=True
        )(
            purchase_order=purchase_order,
            strategy=followup_config.shipment_followup.date_based_followup_strategy,
            initial_wait_hours=followup_config.shipment_followup.time_based_wait_hours,
        )

        logger.info(
            "shipment condition check complete",
            purchase_order_id=purchase_order_id,
            needs_followup=needs_followup,
            reason=reason,
            items_count=len(items),
        )

        # Return True if follow-up is needed (condition NOT met)
        # Return False if no follow-up needed (condition IS met)
        return not needs_followup


# =============================================================================
# TASK BUILDERS - Create tasks with appropriate context for each type
# =============================================================================


class BaseTaskBuilder:
    """Base class for task builders."""

    async def create_task(
        self, purchase_order_id: str, attempts_made: int, max_attempts: int
    ) -> TaskCreationResult:
        """Create a follow-up task for this type."""
        raise NotImplementedError


class OATaskBuilder(BaseTaskBuilder):
    """Creates Order Acknowledgement follow-up tasks."""

    async def create_task(
        self, purchase_order_id: str, attempts_made: int, max_attempts: int
    ) -> TaskCreationResult:
        # Import here to avoid circular imports in Temporal
        from didero.workflows.core.nodes.follow_up.activities_v2 import (
            create_follow_up_task,
        )

        return await create_follow_up_task(
            purchase_order_id, "Order acknowledgement", attempts_made, max_attempts
        )


class ShipDateTaskBuilder(BaseTaskBuilder):
    """Creates ship date follow-up tasks."""

    async def create_task(
        self, purchase_order_id: str, attempts_made: int, max_attempts: int
    ) -> TaskCreationResult:
        # Import here to avoid circular imports in Temporal
        from didero.workflows.core.nodes.follow_up.activities_v2 import (
            create_follow_up_task,
        )

        return await create_follow_up_task(
            purchase_order_id, "Ship dates", attempts_made, max_attempts
        )


class ShipmentTaskBuilder(BaseTaskBuilder):
    """Creates shipment follow-up tasks with shipment-specific context."""

    async def create_task(
        self, purchase_order_id: str, attempts_made: int, max_attempts: int
    ) -> TaskCreationResult:
        # Import here to avoid circular imports in Temporal
        from didero.workflows.core.nodes.follow_up.activities_v2 import (
            create_follow_up_task,
        )

        # For now, use the same task creation - later can be customized for shipment
        return await create_follow_up_task(
            purchase_order_id, "Shipment", attempts_made, max_attempts
        )


# =============================================================================
# FOLLOW-UP HANDLER REGISTRY - Maps follow-up types to their handlers
# =============================================================================


class FollowUpHandlerRegistry:
    """Registry of all follow-up handlers."""

    _handlers = {
        "oa": {
            "condition_checker": OAConditionChecker(),
            "task_builder": OATaskBuilder(),
            "success_message": "Order acknowledgement found, no follow-up needed",
            "task_message": "Order acknowledgement not found, follow-up task created",
        },
        "ship_dates": {
            "condition_checker": ShipDateConditionChecker(),
            "task_builder": ShipDateTaskBuilder(),
            "success_message": "All ship dates found in order acknowledgement",
            "task_message": "Ship dates incomplete, follow-up task created",
        },
        "shipment": {
            "condition_checker": ShipmentConditionChecker(),
            "task_builder": ShipmentTaskBuilder(),
            "success_message": "Items have shipped according to schedule",
            "task_message": "Items have not shipped, follow-up task created",
        },
    }

    @classmethod
    def get_handler_config(cls, follow_up_type: str) -> dict:
        """Get the handler configuration for a follow-up type."""
        if follow_up_type not in cls._handlers:
            raise ValueError(f"Unknown follow-up type: {follow_up_type}")
        return cls._handlers[follow_up_type]


# =============================================================================
# TEMPORAL ACTIVITY - The actual activity that workflows call
# =============================================================================


@activity.defn
async def execute_follow_up_handler(
    follow_up_type: str,
    purchase_order_id: str,
    attempts_made: int = 0,
    max_attempts: int = 3,
) -> ActivityResult:
    """
    Temporal activity that executes follow-up logic using the handler system.

    This is the single activity that workflows call. It uses the registry
    to get the appropriate handler for the follow-up type.

    Args:
        follow_up_type: Type of follow-up ("oa", "ship_dates", "shipment")
        purchase_order_id: UUID of the purchase order
        attempts_made: Number of attempts already made
        max_attempts: Maximum attempts allowed

    Returns:
        ActivityResult: Result of the follow-up attempt
    """
    logger.info(
        "executing follow-up handler activity",
        follow_up_type=follow_up_type,
        purchase_order_id=purchase_order_id,
        attempts_made=attempts_made,
        max_attempts=max_attempts,
    )

    # Get handler configuration for this follow-up type
    handler_config = FollowUpHandlerRegistry.get_handler_config(follow_up_type)

    condition_checker = handler_config["condition_checker"]
    task_builder = handler_config["task_builder"]

    # Step 1: Check if condition is already met
    condition_met = await condition_checker.check(purchase_order_id)

    if condition_met:
        logger.info(
            "follow-up condition satisfied",
            follow_up_type=follow_up_type,
            purchase_order_id=purchase_order_id,
        )
        return ActivityResult(
            success=True,
            message=handler_config["success_message"],
        )

    # Step 2: Condition not met, create task
    logger.info(
        "follow-up condition not met, creating task",
        follow_up_type=follow_up_type,
        purchase_order_id=purchase_order_id,
    )

    await task_builder.create_task(purchase_order_id, attempts_made, max_attempts)

    return ActivityResult(
        success=False,  # False means continue following up
        message=handler_config["task_message"],
    )
