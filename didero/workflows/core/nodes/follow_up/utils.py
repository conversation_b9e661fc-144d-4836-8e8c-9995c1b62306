from datetime import date, datetime, timedelta
from typing import List, <PERSON><PERSON>

import structlog

from didero.emails.models import EmailThreadToPurchaseOrderLink
from didero.orders.models import (
    OrderAcknowledgement,
    OrderAcknowledgementItem,
    PurchaseOrder,
    Shipment,
)
from didero.suppliers.models import Communication
from didero.users.utils.team_setting_utils import get_followup_config_for_supplier
from didero.utils.utils import get_didero_ai_user
from didero.workflows.core.nodes.follow_up.schemas import (
    EmailContent,
    EmailInfo,
    OAContext,
    POEmailContext,
    POItem,
    TaskParams,
)

logger = structlog.get_logger(__name__)


def get_purchase_order_with_relations(purchase_order_id: str) -> PurchaseOrder:
    """
    Utility to get purchase order with only the relations we actually use.

    Args:
        purchase_order_id: UUID of the purchase order

    Returns:
        PurchaseOrder: Purchase order with supplier and team loaded
    """
    return PurchaseOrder.objects.select_related("supplier", "team").get(
        pk=purchase_order_id
    )


def get_task_assignment_user(purchase_order: PurchaseOrder):
    """
    Utility to get the user to assign the task to.

    Args:
        purchase_order: PurchaseOrder instance

    Returns:
        User: User to assign the task to
    """
    user = get_didero_ai_user(purchase_order.team)
    if user is None:
        raise ValueError("No user found to assign the task to")
    return user


def check_oa_exists_for_po(purchase_order_id: str) -> bool:
    """
    Check if an Order Acknowledgement exists for the given purchase order.

    Args:
        purchase_order_id: UUID of the purchase order

    Returns:
        bool: True if OA exists, False otherwise
    """
    try:
        oa_exists = OrderAcknowledgement.objects.filter(
            purchase_order_id=purchase_order_id
        ).exists()

        logger.info(
            "order acknowledgement existence check complete",
            purchase_order_id=purchase_order_id,
            oa_exists=oa_exists,
        )

        return oa_exists

    except Exception as e:
        logger.error(
            "error checking order acknowledgement existence",
            purchase_order_id=purchase_order_id,
            error=str(e),
        )
        return False


def get_po_related_communications(purchase_order: PurchaseOrder) -> List[Communication]:
    """
    Get all communications related to this PO through email thread links.

    Args:
        purchase_order: PurchaseOrder instance

    Returns:
        list: List of Communication objects related to this PO
    """
    # Get email thread IDs linked to this PO
    email_thread_ids = EmailThreadToPurchaseOrderLink.objects.filter(
        purchase_order=purchase_order
    ).values_list("email_thread_id", flat=True)

    # Get all communications in these threads
    related_communications = Communication.objects.filter(
        email_thread__id__in=email_thread_ids
    ).order_by("-comm_time")

    return list(related_communications)


def get_po_email_context(purchase_order: PurchaseOrder) -> POEmailContext:
    """
    Get core PO context including status, communications, and basic details.

    Args:
        purchase_order: PurchaseOrder instance

    Returns:
        POEmailContext: Core PO context with communications and basic details
    """
    # Get all communications related to this PO through email threads
    related_communications = get_po_related_communications(purchase_order)

    # Get latest communication
    latest_email = related_communications[0] if related_communications else None

    # Get last 5 communications for context
    recent_communications = related_communications[:5]

    # Get PO items
    po_items = list(purchase_order.items.all())

    # Build typed items list
    items: List[POItem] = [
        POItem(
            item_name=item.item.description if item.item else "Unknown",
            quantity=item.quantity,
            price=item.price,
            requested_date=item.requested_date,
        )
        for item in po_items
    ]

    # Build latest email info
    latest_email_info: EmailInfo | None = None
    if latest_email:
        latest_email_info = EmailInfo(
            subject=latest_email.email_subject,
            body=latest_email.email_content,
            comm_time=latest_email.comm_time,
            email_from=latest_email.email_from,
            direction=latest_email.direction,
        )

    # Build email history
    email_history: List[EmailInfo] = [
        EmailInfo(
            subject=comm.email_subject,
            body=comm.email_content,
            comm_time=comm.comm_time,
            email_from=comm.email_from,
            direction=comm.direction,
        )
        for comm in recent_communications
    ]

    context = POEmailContext(
        po_number=purchase_order.po_number,
        po_status=purchase_order.order_status,
        placement_time=purchase_order.placement_time,
        expected_delivery_date=getattr(purchase_order, "expected_delivery_date", None),
        supplier_name=purchase_order.supplier.name if purchase_order.supplier else None,
        supplier_email=purchase_order.supplier.default_email
        if purchase_order.supplier
        else None,
        total_cost=purchase_order.total_cost,
        items=items,
        latest_email=latest_email_info,
        email_history=email_history,
    )

    return context


def generate_follow_up_task_params(
    purchase_order: PurchaseOrder,
    follow_up_type: str,
    attempts_made: int = 0,
    max_attempts: int = 3,
) -> TaskParams:
    """
    Generate task parameters dynamically based on follow-up type and email context.
    Uses team and supplier-specific follow-up configurations.

    Args:
        purchase_order: PurchaseOrder instance
        follow_up_type: Type of follow-up ("Order acknowledgement", "Shipping", etc.)
        attempts_made: Number of attempts already made
        max_attempts: Maximum attempts allowed before escalation

    Returns:
        dict: Task parameters including email subject, body, and actions
    """
    # Get follow-up configuration for this team/supplier combination
    followup_config = get_followup_config_for_supplier(
        team=purchase_order.team, supplier=purchase_order.supplier
    )

    # Get full context for generation
    email_context = get_po_email_context(purchase_order)

    supplier = purchase_order.supplier
    if not supplier:
        raise ValueError("Purchase order must have a supplier")

    base_params = {
        "supplier_name": supplier.name,
        "po_number": purchase_order.po_number,
        "follow_up_type": follow_up_type,
        "followup_config": followup_config,
        "attempts_made": attempts_made,
        "max_attempts": max_attempts,
        "is_final_attempt": attempts_made >= max_attempts - 1,
    }

    if follow_up_type == "Order acknowledgement":
        # Generate email content using LLM
        email_content = generate_oa_email_with_llm(
            purchase_order, email_context, base_params
        )

        return TaskParams(
            supplier_name=base_params["supplier_name"],
            po_number=base_params["po_number"],
            follow_up_type=base_params["follow_up_type"],
            followup_config=base_params["followup_config"],
            attempts_made=base_params["attempts_made"],
            max_attempts=base_params["max_attempts"],
            is_final_attempt=base_params["is_final_attempt"],
            email_subject=email_content.email_subject,
            email_body=email_content.email_body,
            email_type=email_content.email_type,
            context=email_context,
        )
    elif follow_up_type == "Ship dates":
        # Get OA context and generate ship date specific email
        oa_context = get_oa_context(purchase_order)
        email_content = generate_ship_date_email_with_llm(
            purchase_order, email_context, oa_context, base_params
        )

        return TaskParams(
            supplier_name=base_params["supplier_name"],
            po_number=base_params["po_number"],
            follow_up_type=base_params["follow_up_type"],
            followup_config=base_params["followup_config"],
            attempts_made=base_params["attempts_made"],
            max_attempts=base_params["max_attempts"],
            is_final_attempt=base_params["is_final_attempt"],
            email_subject=email_content.email_subject,
            email_body=email_content.email_body,
            email_type=email_content.email_type,
            context=email_context,
        )
    else:
        return TaskParams(
            supplier_name=base_params["supplier_name"],
            po_number=base_params["po_number"],
            follow_up_type=base_params["follow_up_type"],
            followup_config=base_params["followup_config"],
            attempts_made=base_params["attempts_made"],
            max_attempts=base_params["max_attempts"],
            is_final_attempt=base_params["is_final_attempt"],
            email_subject=f"Follow-up: {follow_up_type} status for Purchase Order {purchase_order.po_number}",
            email_body=(
                f"Dear {supplier.name},\n\n"
                f"We are following up on the {follow_up_type} of Purchase Order {purchase_order.po_number}.\n\n"
                f"Please provide an update on the {follow_up_type} status at your earliest convenience.\n\n"
                f"Best regards"
            ),
            email_type=f"a {follow_up_type} follow up",
            context=email_context,
        )


def generate_oa_email_with_llm(
    purchase_order: PurchaseOrder, email_context: POEmailContext, base_params: dict
) -> EmailContent:
    """
    Generate Order Acknowledgement email content using LLM based on context.

    Args:
        purchase_order: PurchaseOrder instance
        email_context: Email context with history and existing OAs
        base_params: Base parameters including retry info

    Returns:
        EmailContent: Email subject, body, and type
    """
    # TODO: Implement LLM call to generate contextual email content
    # The LLM should consider:
    # - base_params['attempts_made'] and base_params['max_attempts'] for urgency
    # - base_params['is_final_attempt'] for escalation language
    # - email_context['latest_email'] for conversation continuity
    # - email_context['existing_oas'] for partial acknowledgements
    # - email_context['items'] for order details
    # - base_params['followup_config'] for team/supplier preferences

    # Placeholder implementation - replace with actual LLM call
    supplier_name = base_params["supplier_name"]
    po_number = base_params["po_number"]
    attempts_made = base_params["attempts_made"]
    is_final_attempt = base_params["is_final_attempt"]

    # Build context prompt for LLM
    context_prompt = f"""
    Generate a professional follow-up email for an Order Acknowledgement request.
    
    Context:
    - Purchase Order: {po_number}
    - Supplier: {supplier_name}
    - Attempt: {attempts_made + 1} of {base_params["max_attempts"]}
    - Is Final Attempt: {is_final_attempt}
    - Order Date: {purchase_order.placement_time.strftime("%Y-%m-%d")}
    - Order Value: ${purchase_order.total_cost}
    - Items Count: {len(email_context.items)}
    
    Email History: {len(email_context.email_history)} previous communications
    Latest Email: {email_context.latest_email.subject if email_context.latest_email else 'None'}
    
    Note: No Order Acknowledgement has been received yet for this PO
    
    Please generate:
    1. Professional email subject
    2. Contextual email body that references previous communications if any
    3. Appropriate urgency level based on attempt number
    4. Request for specific deliverables (order acceptance, ship dates, confirmations)
    """

    # Placeholder return - replace with actual LLM response
    urgency_prefix = "URGENT: " if is_final_attempt else ""

    return EmailContent(
        email_subject=f"{urgency_prefix}Order Acknowledgement Request - Purchase Order {po_number}",
        email_body=f"[LLM GENERATED CONTENT PLACEHOLDER]\n\nContext: {context_prompt}",
        email_type="an order acknowledgement request",
    )


def get_oa_context(purchase_order: PurchaseOrder) -> OAContext:
    """
    Get Order Acknowledgement context including ship date completeness.

    Args:
        purchase_order: PurchaseOrder instance

    Returns:
        OAContext: OA-specific context with ship date analysis
    """
    # Get existing OAs
    existing_oas = list(
        OrderAcknowledgement.objects.filter(
            purchase_order=purchase_order
        ).prefetch_related("items__order_item__item")
    )

    incomplete_items = []
    has_all_ship_dates = True

    # Check if all items in all OAs have ship dates
    for oa in existing_oas:
        for oa_item in oa.items.all():
            if not oa_item.promised_ship_date:
                has_all_ship_dates = False
                item_name = (
                    oa_item.order_item.item.description
                    if oa_item.order_item.item
                    else "Unknown"
                )
                incomplete_items.append(item_name)

    return OAContext(
        existing_oas=[
            {
                "oa_status": oa.oa_status,
                "created_at": oa.created_at,
                "items_with_ship_dates": [
                    {
                        "item_name": oa_item.order_item.item.description
                        if oa_item.order_item.item
                        else "Unknown",
                        "promised_ship_date": oa_item.promised_ship_date,
                        "promised_delivery_date": oa_item.promised_delivery_date,
                        "has_ship_date": bool(oa_item.promised_ship_date),
                    }
                    for oa_item in oa.items.all()
                ],
            }
            for oa in existing_oas
        ],
        incomplete_items=incomplete_items,
        has_all_ship_dates=has_all_ship_dates,
    )


def check_oa_ship_dates_complete(purchase_order_id: str) -> bool:
    """
    Check if all items in existing OAs have ship dates.

    Args:
        purchase_order_id: UUID of the purchase order

    Returns:
        bool: True if all items have ship dates, False otherwise
    """
    try:
        purchase_order = PurchaseOrder.objects.get(pk=purchase_order_id)
        oa_context = get_oa_context(purchase_order)
        return oa_context.has_all_ship_dates

    except Exception as e:
        logger.error(
            "error checking OA ship dates completeness",
            purchase_order_id=purchase_order_id,
            error=str(e),
        )
        return False


def generate_ship_date_email_with_llm(
    purchase_order: PurchaseOrder,
    po_context: POEmailContext,
    oa_context: OAContext,
    base_params: dict,
) -> EmailContent:
    """
    Generate ship date follow-up email content using LLM.

    Args:
        purchase_order: PurchaseOrder instance
        po_context: PO email context
        oa_context: OA context with incomplete items
        base_params: Base parameters including retry info

    Returns:
        EmailContent: Email subject, body, and type
    """
    supplier_name = base_params["supplier_name"]
    po_number = base_params["po_number"]
    attempts_made = base_params["attempts_made"]
    is_final_attempt = base_params["is_final_attempt"]

    # Build context prompt for LLM
    context_prompt = f"""
    Generate a professional follow-up email requesting ship dates for Order Acknowledgement items.
    
    Context:
    - Purchase Order: {po_number}
    - Supplier: {supplier_name}
    - Attempt: {attempts_made + 1} of {base_params['max_attempts']}
    - Is Final Attempt: {is_final_attempt}
    - Order Date: {purchase_order.placement_time.strftime('%Y-%m-%d')}
    - Order Value: ${purchase_order.total_cost}
    
    Email History: {len(po_context.email_history)} previous communications
    Latest Email: {po_context.latest_email.subject if po_context.latest_email else 'None'}
    
    OA Status: Order acknowledgement received but missing ship dates
    Items Missing Ship Dates: {', '.join(oa_context.incomplete_items)}
    
    Please generate:
    1. Professional email subject
    2. Contextual email body that references the received OA
    3. Appropriate urgency level based on attempt number
    4. Request for specific ship dates for the incomplete items
    """

    urgency_prefix = "URGENT: " if is_final_attempt else ""

    return EmailContent(
        email_subject=f"{urgency_prefix}Ship Date Request - Purchase Order {po_number}",
        email_body=f"[LLM GENERATED SHIP DATE CONTENT PLACEHOLDER]\n\nContext: {context_prompt}",
        email_type="a ship date request",
    )


# Generic follow-up functions
def create_generic_follow_up_task_params(
    purchase_order: PurchaseOrder,
    follow_up_type: str,
    attempts_made: int = 0,
    max_attempts: int = 3,
) -> TaskParams:
    """
    Generic task parameter generation that delegates to specific generators.

    Args:
        purchase_order: PurchaseOrder instance
        follow_up_type: Type of follow-up
        attempts_made: Number of attempts already made
        max_attempts: Maximum attempts allowed before escalation

    Returns:
        TaskParams: Complete task parameters
    """
    # Get followup config and base PO context
    followup_config = get_followup_config_for_supplier(
        team=purchase_order.team, supplier=purchase_order.supplier
    )

    po_context = get_po_email_context(purchase_order)

    supplier = purchase_order.supplier
    if not supplier:
        raise ValueError("Purchase order must have a supplier")

    base_params = {
        "supplier_name": supplier.name,
        "po_number": purchase_order.po_number,
        "follow_up_type": follow_up_type,
        "followup_config": followup_config,
        "attempts_made": attempts_made,
        "max_attempts": max_attempts,
        "is_final_attempt": attempts_made >= max_attempts - 1,
    }

    # Delegate to specific email generators based on type
    if follow_up_type == "Order acknowledgement":
        email_content = generate_oa_email_with_llm(
            purchase_order, po_context, base_params
        )
    elif follow_up_type == "Ship dates":
        oa_context = get_oa_context(purchase_order)
        email_content = generate_ship_date_email_with_llm(
            purchase_order, po_context, oa_context, base_params
        )
    else:
        # Default generic email
        email_content = EmailContent(
            email_subject=f"Follow-up: {follow_up_type} status for Purchase Order {purchase_order.po_number}",
            email_body=(
                f"Dear {supplier.name},\n\n"
                f"We are following up on the {follow_up_type} of Purchase Order {purchase_order.po_number}.\n\n"
                f"Please provide an update on the {follow_up_type} status at your earliest convenience.\n\n"
                f"Best regards"
            ),
            email_type=f"a {follow_up_type} follow up",
        )

    return TaskParams(
        supplier_name=base_params["supplier_name"],
        po_number=base_params["po_number"],
        follow_up_type=base_params["follow_up_type"],
        followup_config=base_params["followup_config"],
        attempts_made=base_params["attempts_made"],
        max_attempts=base_params["max_attempts"],
        is_final_attempt=base_params["is_final_attempt"],
        email_subject=email_content.email_subject,
        email_body=email_content.email_body,
        email_type=email_content.email_type,
        context=po_context,
    )


# =============================================================================
# SHIPMENT FOLLOW-UP UTILITIES
# =============================================================================

# Percentage-based follow-up schedules
SHIPMENT_FOLLOWUP_PERCENTAGE_SCHEDULES = {
    "aggressive": [0.8, 0.6, 0.4, 0.2, 0.05, 0.01],  # 6 follow-ups including day before
    "balanced": [0.7, 0.4, 0.15, 0.05, 0.01],  # 5 follow-ups including day before
    "conservative": [0.6, 0.15, 0.01],  # 3 follow-ups including day before
}


def get_unshipped_oa_items_with_ship_dates(
    purchase_order: PurchaseOrder,
) -> List[Tuple[OrderAcknowledgementItem, date, float]]:
    """
    Get OA items that have promised ship dates but haven't been fully shipped.

    Note: OrderAcknowledgementItem.item relates to catalog Item, while
    ShipmentLineItem.order_item relates to OrderItem. We match through:
    oa_item.item == order_item.item (same catalog item)

    Args:
        purchase_order: PurchaseOrder instance

    Returns:
        List of tuples: (oa_item, promised_ship_date, unshipped_quantity)
    """
    # Get shipped quantities for each order item - this returns Dict[OrderItem, float]
    shipped_items = Shipment.objects.get_shipped_items(purchase_order)

    # Get all OA items with promised ship dates
    oa_items_with_ship_dates = []

    for oa in purchase_order.order_acknowledgements.all():
        for oa_item in oa.items.all():
            if oa_item.promised_ship_date:
                # Find matching OrderItem by Item (catalog item)
                # oa_item.item == order_item.item (same catalog item)
                matching_order_item = None
                for order_item in purchase_order.items.all():
                    if order_item.item == oa_item.item:
                        matching_order_item = order_item
                        break

                if matching_order_item:
                    shipped_qty = shipped_items.get(matching_order_item, 0.0)
                    # Compare OA quantity vs shipped quantity for this item
                    unshipped_qty = oa_item.quantity - shipped_qty

                    if unshipped_qty > 0:
                        oa_items_with_ship_dates.append(
                            (oa_item, oa_item.promised_ship_date, unshipped_qty)
                        )

    return oa_items_with_ship_dates


def calculate_shipment_followup_dates(
    ship_date: date,
    strategy: str,
    oa_received_date: datetime,
    initial_wait_hours: int = 24,
) -> List[date]:
    """
    Calculate follow-up dates for shipment using percentage-based strategy.

    Args:
        ship_date: Promised ship date from OA
        strategy: "aggressive", "balanced", or "conservative"
        oa_received_date: When the OA was received
        initial_wait_hours: Hours to wait after OA before first follow-up

    Returns:
        List of follow-up dates sorted chronologically
    """
    if strategy not in SHIPMENT_FOLLOWUP_PERCENTAGE_SCHEDULES:
        logger.warning(f"Unknown strategy {strategy}, using balanced")
        strategy = "balanced"

    followup_dates = []
    today = date.today()

    # 1. Initial follow-up after OA receipt
    initial_followup = oa_received_date + timedelta(hours=initial_wait_hours)
    if initial_followup.date() < ship_date and initial_followup.date() >= today:
        followup_dates.append(initial_followup.date())

    # 2. Calculate lead time from today to ship date
    lead_time_days = (ship_date - today).days

    if lead_time_days > 0:
        # 3. Percentage-based follow-ups
        for percentage in SHIPMENT_FOLLOWUP_PERCENTAGE_SCHEDULES[strategy]:
            days_before = max(1, int(lead_time_days * percentage))
            followup_date = ship_date - timedelta(days=days_before)

            # Don't duplicate dates and ensure it's in the future
            if (
                followup_date not in followup_dates
                and followup_date >= today
                and followup_date < ship_date
            ):
                followup_dates.append(followup_date)

    return sorted(followup_dates)


def group_items_by_ship_date(
    unshipped_items: List[Tuple[OrderAcknowledgementItem, date, float]],
    tolerance_days: int = 3,
) -> List[List[Tuple[OrderAcknowledgementItem, date, float]]]:
    """
    Group unshipped OA items by similar ship dates.

    Args:
        unshipped_items: List of (oa_item, ship_date, unshipped_qty) tuples
        tolerance_days: Days tolerance for grouping (±3 calendar days)

    Returns:
        List of groups, each group is a list of items with similar ship dates
    """
    if not unshipped_items:
        return []

    # Sort by ship date
    sorted_items = sorted(unshipped_items, key=lambda x: x[1])
    groups = []
    current_group = [sorted_items[0]]

    for item in sorted_items[1:]:
        current_ship_date = item[1]
        group_ship_date = current_group[0][1]

        # Check if within tolerance (using calendar days for simplicity)
        if abs((current_ship_date - group_ship_date).days) <= tolerance_days:
            current_group.append(item)
        else:
            # Start new group
            groups.append(current_group)
            current_group = [item]

    # Add the last group
    groups.append(current_group)

    return groups


def check_shipment_follow_up_needed(
    purchase_order: PurchaseOrder,
    strategy: str,
    initial_wait_hours: int = 24,
) -> Tuple[bool, str, List[Tuple[OrderAcknowledgementItem, date, float]]]:
    """
    Check if shipment follow-up is needed today for the given PO.

    This function implements the core logic for determining when to send
    proactive "are you on track to ship?" follow-ups before ship dates.

    Args:
        purchase_order: PurchaseOrder instance
        strategy: Follow-up strategy ("aggressive", "balanced", "conservative")
        initial_wait_hours: Hours to wait after OA before first follow-up

    Returns:
        Tuple of (needs_followup, reason_message, items_to_followup)
    """
    today = date.today()

    # Get unshipped items with ship dates
    unshipped_items = get_unshipped_oa_items_with_ship_dates(purchase_order)

    if not unshipped_items:
        return False, "No unshipped items with ship dates", []

    # Group items by ship date (within ±3 days)
    item_groups = group_items_by_ship_date(unshipped_items)

    # Check the earliest group (first in list after sorting)
    earliest_group = item_groups[0]
    earliest_ship_date = earliest_group[0][1]  # ship_date from first item in group

    # Check if ship date has passed (overdue)
    if earliest_ship_date < today:
        return (
            True,
            f"Ship date {earliest_ship_date} has passed - urgent follow-up needed",
            earliest_group,
        )

    # Get OA received date (use the latest OA for this calculation)
    latest_oa = purchase_order.order_acknowledgements.order_by("-created_at").first()
    if not latest_oa:
        return False, "No OA found for shipment follow-up", []

    # Calculate follow-up dates for the earliest group
    followup_dates = calculate_shipment_followup_dates(
        ship_date=earliest_ship_date,
        strategy=strategy,
        oa_received_date=latest_oa.created_at,
        initial_wait_hours=initial_wait_hours,
    )

    # Check if today is a follow-up date
    if today in followup_dates:
        days_until_ship = (earliest_ship_date - today).days
        return (
            True,
            f"Scheduled follow-up - {days_until_ship} days until ship date",
            earliest_group,
        )

    return False, "No follow-up needed today", []
