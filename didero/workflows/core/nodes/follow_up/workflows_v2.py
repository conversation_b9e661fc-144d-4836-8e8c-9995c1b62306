import asyncio
from datetime import timed<PERSON><PERSON>
from typing import Any, Optional

from temporalio import workflow

with workflow.unsafe.imports_passed_through():
    import structlog
    from asgiref.sync import sync_to_async

    from didero.users.utils.team_setting_utils import get_followup_config_for_supplier
    from didero.workflows.core.nodes.follow_up.follow_up_handlers import (
        execute_follow_up_handler,
    )
    from didero.workflows.core.nodes.follow_up.schemas import (
        FinalShippingStatus,
        FollowUpPhase,
        FollowUpResult,
        FollowUpStatus,
        WorkflowPhaseResult,
    )
    from didero.workflows.core.nodes.follow_up.utils import (
        get_purchase_order_with_relations,
    )


logger = structlog.get_logger(__name__)


# Phase sequence - order in which we attempt follow-ups
PHASE_SEQUENCE = [
    FollowUpPhase.ORDER_ACKNOWLEDGEMENT,
    FollowUpPhase.SHIP_DATES,
    FollowUpPhase.SHIPPING,
]


@workflow.defn
class FollowupWorkflowV2:
    @workflow.run
    async def run(
        self, purchase_order_id: str, email_object_id: str, starting_phase: str = "oa"
    ) -> FollowUpResult:
        """
        Sequential follow-up workflow that handles multiple phases of follow-ups.

        The workflow progresses through phases:
        1. Order Acknowledgement follow-up
        2. Ship dates follow-up (once OA exists)
        3. Shipping follow-up (once ship dates exist)

        Args:
            purchase_order_id: ID of the purchase order to follow up on
            email_object_id: Email object ID for logging purposes
            starting_phase: Phase to start from ("oa", "ship_dates", "shipping")

        Returns:
            FollowUpResult containing final workflow outcome
        """
        logger.info(
            "starting sequential follow_up workflow v2",
            purchase_order_id=purchase_order_id,
            email_object_id=email_object_id,
            starting_phase=starting_phase,
        )

        # Convert string phase to enum
        try:
            current_phase = FollowUpPhase(starting_phase)
        except ValueError:
            error_msg = (
                f"Invalid starting_phase '{starting_phase}'. "
                f"Valid values are: {', '.join([p.value for p in FollowUpPhase])}"
            )
            logger.error(
                "invalid starting phase provided",
                purchase_order_id=purchase_order_id,
                starting_phase=starting_phase,
                valid_phases=[p.value for p in FollowUpPhase],
            )
            raise ValueError(error_msg)

        # Get PO and followup config
        purchase_order = await workflow.execute_activity(
            sync_to_async(get_purchase_order_with_relations, thread_sensitive=True),
            schedule_to_close_timeout=timedelta(minutes=5),
            args=[purchase_order_id],
            retry_policy=None,
        )

        followup_config = await workflow.execute_activity(
            sync_to_async(get_followup_config_for_supplier, thread_sensitive=True),
            schedule_to_close_timeout=timedelta(minutes=5),
            args=[purchase_order.team, purchase_order.supplier],
            retry_policy=None,
        )

        # Execute phases sequentially
        final_phase_attempted = current_phase
        total_retries = 0

        while current_phase:
            logger.info(
                "executing follow-up phase",
                purchase_order_id=purchase_order_id,
                phase=current_phase.value,
            )

            phase_result = await self.execute_follow_up_phase(
                purchase_order_id=purchase_order_id,
                phase=current_phase,
                followup_config=followup_config,
            )

            final_phase_attempted = current_phase
            total_retries += getattr(
                phase_result.activity_result, "retries_attempted", 0
            )

            # If the condition was already met (success=True), we still continue to next phase
            # If it wasn't met, we created a task but still continue to check other phases
            if phase_result.should_continue_to_next_phase:
                # Move to next phase
                current_phase = phase_result.next_phase
                logger.info(
                    "moving to next phase",
                    purchase_order_id=purchase_order_id,
                    completed_phase=final_phase_attempted.value,
                    condition_was_met=phase_result.phase_completed,
                    next_phase=current_phase.value if current_phase else "none",
                )
            else:
                # No more phases
                logger.info(
                    "no more phases to check",
                    purchase_order_id=purchase_order_id,
                    final_phase=final_phase_attempted.value,
                )
                break

        # Determine final status
        if final_phase_attempted == FollowUpPhase.SHIPPING:
            final_shipping_status = FinalShippingStatus.FULLY_SHIPPED
        elif final_phase_attempted == FollowUpPhase.SHIP_DATES:
            final_shipping_status = FinalShippingStatus.PARTIALLY_SHIPPED
        else:
            final_shipping_status = FinalShippingStatus.NOT_SHIPPED

        follow_up_type_name = final_phase_attempted.value.replace("_", " ").title()

        return FollowUpResult(
            success=True,
            status=FollowUpStatus.COMPLETED,
            message=f"Follow-up workflow completed through {follow_up_type_name} phase",
            purchase_order_id=purchase_order_id,
            follow_up_type=follow_up_type_name,
            retries_attempted=total_retries,
            final_shipping_status=final_shipping_status,
        )

    async def execute_follow_up_phase(
        self,
        purchase_order_id: str,
        phase: FollowUpPhase,
        followup_config: Any,
    ) -> WorkflowPhaseResult:
        """
        Execute a single follow-up phase with retry logic.

        Each phase runs independently - we don't check dependencies,
        but we DO retry follow-ups based on config.

        Args:
            purchase_order_id: ID of the purchase order
            phase: The follow-up phase to execute
            followup_config: Team/supplier follow-up configuration

        Returns:
            WorkflowPhaseResult indicating phase outcome and next steps
        """
        # Get phase-specific configuration
        config = self._get_phase_config(phase, followup_config)

        # Check if this phase is enabled
        if not config.enabled:
            logger.info(
                "follow-up phase disabled",
                purchase_order_id=purchase_order_id,
                phase=phase.value,
            )
            return self._create_skip_to_next_phase_result(phase)

        # Execute the retry loop for this phase
        return await self._execute_phase_retry_loop(
            purchase_order_id=purchase_order_id,
            phase=phase,
            config=config,
        )

    def _get_phase_config(self, phase: FollowUpPhase, followup_config: Any):
        """Get configuration for a specific phase."""
        if phase == FollowUpPhase.ORDER_ACKNOWLEDGEMENT:
            return followup_config.oa_followup
        elif phase == FollowUpPhase.SHIP_DATES:
            return followup_config.oa_followup  # Ship dates use OA config for now
        elif phase == FollowUpPhase.SHIPPING:
            return followup_config.shipment_followup
        else:
            # Default fallback
            return followup_config.oa_followup

    def _create_skip_to_next_phase_result(
        self, phase: FollowUpPhase
    ) -> WorkflowPhaseResult:
        """Create result when skipping to next phase."""
        next_phase = self._get_next_phase(phase)
        return WorkflowPhaseResult(
            phase_completed=True,
            should_continue_to_next_phase=next_phase is not None,
            next_phase=next_phase,
            activity_result=None,
        )

    async def _execute_phase_retry_loop(
        self, purchase_order_id: str, phase: FollowUpPhase, config: Any
    ) -> WorkflowPhaseResult:
        """
        Execute the retry loop for a phase.

        For independent phases: We retry the follow-up multiple times,
        but always move to the next phase after exhausting retries.
        """
        # Initial wait before first attempt
        await self._initial_wait(purchase_order_id, phase, config)

        # Try multiple times based on config
        activity_result = None
        for attempt in range(config.max_attempts_before_task):
            # Execute single attempt using our handler
            activity_result = await self._execute_single_attempt(
                phase=phase,
                purchase_order_id=purchase_order_id,
                attempts_made=attempt,
                max_attempts=config.max_attempts_before_task,
            )

            # Check if we succeeded (condition already met)
            if activity_result.success:
                logger.info(
                    "phase condition met, no follow-up needed",
                    purchase_order_id=purchase_order_id,
                    phase=phase.value,
                    attempt=attempt + 1,
                )
                # Condition met, move to next phase
                return self._create_phase_result(
                    phase, activity_result, phase_completed=True
                )

            # Wait before next retry (if not last attempt)
            if attempt < config.max_attempts_before_task - 1:
                await self._wait_between_retries(
                    purchase_order_id, phase, config, attempt
                )

        # Max retries reached, task was created, move to next phase anyway
        logger.info(
            "phase max retries reached, task created, moving to next phase",
            purchase_order_id=purchase_order_id,
            phase=phase.value,
            max_attempts=config.max_attempts_before_task,
        )
        return self._create_phase_result(phase, activity_result, phase_completed=False)

    async def _initial_wait(
        self, purchase_order_id: str, phase: FollowUpPhase, config: Any
    ):
        """Handle initial wait before first attempt."""
        initial_wait_time = config.initial_wait_hours * 60 * 60
        logger.info(
            "initial wait before phase execution",
            purchase_order_id=purchase_order_id,
            phase=phase.value,
            wait_time_hours=config.initial_wait_hours,
        )
        await asyncio.sleep(initial_wait_time)

    async def _wait_between_retries(
        self,
        purchase_order_id: str,
        phase: FollowUpPhase,
        config: Any,
        attempt: int,
    ):
        """Handle waiting between retry attempts."""
        wait_time = config.repeat_every_hours * 60 * 60
        logger.info(
            "waiting before next retry",
            purchase_order_id=purchase_order_id,
            phase=phase.value,
            wait_time_hours=config.repeat_every_hours,
            attempt=attempt + 1,
            remaining_attempts=config.max_attempts_before_task - attempt - 1,
        )
        await asyncio.sleep(wait_time)

    async def _execute_single_attempt(
        self,
        phase: FollowUpPhase,
        purchase_order_id: str,
        attempts_made: int,
        max_attempts: int,
    ):
        """Execute a single attempt using our handler-based activity."""
        return await workflow.execute_activity(
            execute_follow_up_handler,
            schedule_to_close_timeout=timedelta(days=14),
            args=[phase.value, purchase_order_id, attempts_made, max_attempts],
            retry_policy=None,
        )

    def _create_phase_result(
        self, phase: FollowUpPhase, activity_result: Any, phase_completed: bool
    ) -> WorkflowPhaseResult:
        """Create result for a phase."""
        next_phase = self._get_next_phase(phase)
        return WorkflowPhaseResult(
            phase_completed=phase_completed,
            should_continue_to_next_phase=next_phase is not None,
            next_phase=next_phase,
            activity_result=activity_result,
        )

    def _get_next_phase(self, current_phase: FollowUpPhase) -> Optional[FollowUpPhase]:
        """Get the next phase in the sequence."""
        try:
            current_index = PHASE_SEQUENCE.index(current_phase)
            if current_index + 1 < len(PHASE_SEQUENCE):
                return PHASE_SEQUENCE[current_index + 1]
        except ValueError:
            pass

        return None
