from typing import List

import structlog
from asgiref.sync import sync_to_async
from django.contrib.contenttypes.models import ContentType

from didero.orders.models import PurchaseOrder
from didero.tasks.schemas import (
    TaskActionButtonType,
    TaskActionType,
    TaskContextPanelType,
    TaskType,
)
from didero.tasks.utils import CreateTaskActionParams, create_task_v2
from didero.workflows.core.nodes.follow_up.schemas import (
    TaskCreationResult,
    TaskParams,
)
from didero.workflows.core.nodes.follow_up.utils import (
    generate_follow_up_task_params,
    get_purchase_order_with_relations,
    get_task_assignment_user,
)

logger = structlog.get_logger(__name__)


def _create_task_actions(
    task_params: TaskParams,
    purchase_order: PurchaseOrder,
    attempts_made: int,
    max_attempts: int,
) -> List[CreateTaskActionParams]:
    """
    Create task actions based on attempt number.

    Early attempts: Just email action
    Final attempt: Email + Could add call supplier action

    Args:
        task_params: Generated task parameters
        purchase_order: PO object
        attempts_made: Number of attempts already made (0-indexed)
        max_attempts: Maximum attempts allowed

    Returns:
        List of task actions
    """
    actions: List[CreateTaskActionParams] = []

    # Always include email action
    actions.append(
        {
            "action_type": TaskActionType.SEND_EMAIL,
            "action_params": {
                "email_type": task_params.email_type,
            },
            "action_execution_params": {
                "email_to": purchase_order.supplier.default_email
                if purchase_order.supplier
                else "<supplier_email>",
                "email_subject": task_params.email_subject,
                "email_body": task_params.email_body,
            },
            "button_type": TaskActionButtonType.STANDARD,
        }
    )

    # On final attempt, add escalation action
    is_final_attempt = attempts_made >= max_attempts - 1
    if is_final_attempt:
        pass
        # TODO: Add CALL_SUPPLIER action when available
        # actions.append({
        #     "action_type": TaskActionType.CALL_SUPPLIER,
        #     "action_params": {},
        #     "action_execution_params": {
        #         "phone_number": purchase_order.supplier.phone_number or "",
        #     },
        #     "button_type": TaskActionButtonType.WARNING,
        # })

    return actions


async def create_follow_up_task(
    purchase_order_id: str,
    follow_up_type: str,
    attempts_made: int = 0,
    max_attempts: int = 3,
) -> TaskCreationResult:
    """
    Create a follow-up task for any follow-up type.

    This single function replaces create_oa_follow_up_task and create_ship_date_follow_up_task
    which were 99% identical.

    Args:
        purchase_order_id: UUID of the purchase order
        follow_up_type: Type of follow-up ("Order acknowledgement", "Ship dates", etc.)
        attempts_made: Number of attempts already made
        max_attempts: Maximum attempts allowed before escalation

    Returns:
        TaskCreationResult: Result of task creation with success status
    """
    logger.info(
        "creating follow-up task",
        purchase_order_id=purchase_order_id,
        follow_up_type=follow_up_type,
        attempts_made=attempts_made,
        max_attempts=max_attempts,
    )

    # Get PO with relations
    purchase_order = await sync_to_async(
        get_purchase_order_with_relations, thread_sensitive=True
    )(purchase_order_id)

    # Get user for task assignment
    user = await sync_to_async(get_task_assignment_user, thread_sensitive=True)(
        purchase_order
    )

    # Ensure supplier exists
    if purchase_order.supplier is None:
        raise ValueError(
            f"Purchase order must have a supplier for {follow_up_type} follow-up"
        )

    # Generate task parameters with context and retry info
    task_params = await sync_to_async(
        generate_follow_up_task_params, thread_sensitive=True
    )(purchase_order, follow_up_type, attempts_made, max_attempts)

    # Get content type for the model
    content_type = await sync_to_async(
        ContentType.objects.get_for_model, thread_sensitive=True
    )(purchase_order)

    # Create follow-up task
    task = await sync_to_async(create_task_v2, thread_sensitive=True)(
        task_type=TaskType.FOLLOW_UP,
        user=user,
        model_type=content_type,
        model_id=str(purchase_order.pk),
        task_type_params={
            "supplier_name": task_params.supplier_name,
            "po_number": task_params.po_number,
            "follow_up_type": task_params.follow_up_type,
        },
        context_panels=[
            {
                "panel_type": TaskContextPanelType.PO_DETAILS,
                "param_values": {"purchaseOrderId": purchase_order_id},
            }
        ],
        actions=_create_task_actions(
            task_params, purchase_order, attempts_made, max_attempts
        ),
    )

    logger.info(
        "follow-up task created successfully",
        purchase_order_id=purchase_order_id,
        follow_up_type=follow_up_type,
        task_id=task.pk,
    )
    return TaskCreationResult(
        success=True,
        task_id=str(task.pk),
        message=f"{follow_up_type} follow-up task created successfully",
    )


# All individual/generic activities removed - now using handler system
