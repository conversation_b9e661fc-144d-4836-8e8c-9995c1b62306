from typing import Optional

import structlog
from temporalio import activity, workflow

from didero.workflows.core.nodes.purchase_orders.order_acknowledgement.errors import (
    OrderAcknowledgementWorkflowError,
    OrderAcknowledgementWorkflowErrorType,
)
from didero.workflows.errors import ResourceNotFoundError, ValidationError

with workflow.unsafe.imports_passed_through():
    from django.contrib.contenttypes.models import ContentType
    from django.db import OperationalError

    from didero.ai.order_acknowledgment.order_ack import (
        ai_extract_order_acknowledgement_details,
    )
    from didero.emails.models import EmailThreadToPurchaseOrderLink
    from didero.orders.models import (
        OrderAcknowledgement as DjangoOrderAcknowledgement,
    )
    from didero.orders.models import (
        PurchaseOrder,
        PurchaseOrderComment,
        PurchaseOrderStatus,
    )
    from didero.orders.schemas import OrderAcknowledgement as OrderAcknowledgementSchema
    from didero.suppliers.models import Communication
    from didero.tasks.schemas import (
        TaskActionButtonType,
        TaskActionType,
        TaskContextPanelType,
        TaskType,
    )
    from didero.tasks.utils import ContextPanelDefinition, create_task_v2
    from didero.utils.utils import get_didero_ai_user
    from didero.workflows.core.nodes.base_nodes import (
        NodeResult,
        WorkflowAction,
    )
    from didero.workflows.core.nodes.follow_up.schemas import FollowUpType
    from didero.workflows.core.nodes.purchase_orders.order_acknowledgement.schemas import (
        ExtractOrderAcknowledgementDetailsParams,
        OrderAcknowledgementPostValidationActionsParams,
        ValidateOrderAcknowledgementInfoParams,
    )
    from didero.workflows.core.nodes.purchase_orders.shipments import (
        get_purchase_order_from_po_number,
    )
    from didero.workflows.notification_utils import get_notification_recipient
    from didero.workflows.schemas import WorkflowRunState

logger = structlog.get_logger(__name__)


def create_order_acknowledgement_human_validation_task(
    purchase_order: PurchaseOrder,
    order_acknowledgement: OrderAcknowledgementSchema,
    email_id: str,
):
    """
    Create a task for human validation of the order acknowledgement.

    Args:
        purchase_order: The PurchaseOrder instance
        order_acknowledgement: The OrderAcknowledgement instance
        email_id: ID of the email that contains the order acknowledgement
    """
    user = get_didero_ai_user(team=purchase_order.team)
    if not user:
        logger.warning(
            "No AI user found for team",
            team_id=purchase_order.team.id if purchase_order.team else None,
            po_id=purchase_order.id,
            po_number=purchase_order.po_number,
        )
        return

    # Prepare context panels
    context_panels: list[ContextPanelDefinition] = [
        {
            "panel_type": TaskContextPanelType.PO_DETAILS,
            "param_values": {"purchaseOrderId": str(purchase_order.id)},
        }
    ]

    # Add communication panel if email_id is available
    if email_id:
        context_panels.append(
            {
                "panel_type": TaskContextPanelType.COMMUNICATION_DETAILS,
                "param_values": {"communicationId": email_id},
            }
        )

    # Format total amount properly for display
    total_amount = order_acknowledgement.total_amount
    if isinstance(total_amount, str) and not total_amount.startswith("$"):
        total_amount = f"${total_amount}"

    # Prepare task parameters
    task_params = {
        "po_number": purchase_order.po_number,
        "supplier_name": purchase_order.supplier.name
        if purchase_order.supplier
        else "Unknown",
        "total_cost": total_amount,
    }

    return create_task_v2(
        task_type=TaskType.ORDER_ACKNOWLEDGEMENT_WORKFLOW_COMPLETED,
        user=user,
        model_type=ContentType.objects.get_for_model(PurchaseOrder),
        model_id=str(purchase_order.id),
        task_type_params=task_params,
        context_panels=context_panels,
        actions=[
            {
                "action_type": TaskActionType.CONFIRM_ORDER_ACKNOWLEDGEMENT,
                "action_params": {},
                "action_execution_params": {
                    "purchase_order_id": str(purchase_order.id),
                },
                "button_type": TaskActionButtonType.GREEN,
            },
            {
                "action_type": TaskActionType.REJECT_ORDER_ACKNOWLEDGEMENT,
                "action_params": {},
                "action_execution_params": {
                    "purchase_order_id": str(purchase_order.id),
                },
                "button_type": TaskActionButtonType.RED,
            },
        ],
    )


def get_purchase_order_acknowledgement_workflow_parameters(
    email: Communication, is_core_workflow: bool = False
):
    if is_core_workflow:
        # Core workflows expect flat parameters (workflow_id and team_id are added by run_workflow)
        return {
            "email_id": str(email.pk),
        }
    else:
        # DAG workflows expect nested parameters under "default"
        return {
            "default": {
                "email_id": str(email.pk),
            }
        }


@activity.defn
def extract_order_acknowledgement_details(
    params: ExtractOrderAcknowledgementDetailsParams,
) -> ValidateOrderAcknowledgementInfoParams:
    """
    Extract order acknowledgement details from email.

    Raises:
        ResourceNotFoundError: If email or PO not found (non-retryable)
        ValidationError: If extraction fails (non-retryable)
        OperationalError: Database issues (retryable)
    """
    email_id = params["email_id"]
    logger.info(
        "Order Acknowledgement Workflow: Extracting order acknowledgement details",
        email_id=email_id,
    )
    try:
        email = Communication.objects.select_related(
            "team", "supplier", "email_thread"
        ).get(id=email_id)
        logger.info(
            "Order Acknowledgement Workflow: Found communication",
            email_id=email_id,
        )
    except Communication.DoesNotExist:
        logger.error(
            "Order Acknowledgement Workflow: Communication not found",
            email_id=email_id,
        )
        raise ResourceNotFoundError(
            resource_type="Communication",
            resource_id=email_id,
        )
    except OperationalError:
        # Database connection errors should be retried by Temporal
        logger.error(f"Database connection error fetching email {email_id}")
        raise

    # Extract order acknowledgement details
    logger.info(
        "Order Acknowledgement Workflow: Matching order acknowledgement to purchase order",
        email_id=email_id,
    )

    try:
        order_acknowledgement = ai_extract_order_acknowledgement_details(email)
    except Exception as e:
        # AI extraction errors might be transient (API issues)
        # Let them propagate for retry
        logger.error(
            f"AI extraction failed for email {email_id}: {str(e)}",
            exc_info=True,
        )
        raise

    if not order_acknowledgement:
        logger.error(
            "Order Acknowledgement Workflow: Failed to extract order acknowledgement details",
            email_id=email_id,
        )
        raise ValidationError(
            "Failed to extract order acknowledgement details from email",
            email_id=email_id,
        )

    logger.info(
        "Order Acknowledgement Workflow: Successfully extracted order acknowledgement details",
        email_id=email_id,
        order_acknowledgement_purchase_order_number=order_acknowledgement.po_number,
    )

    # Simple PO lookup - DAG workflows don't support auto-creation
    purchase_order = get_purchase_order_from_po_number(
        order_acknowledgement.po_number, email.team.id
    )

    if not purchase_order:
        logger.error(
            "Order Acknowledgement Workflow: Purchase order not found",
            email_id=email_id,
            po_number=order_acknowledgement.po_number,
        )
        raise ResourceNotFoundError(
            resource_type="PurchaseOrder",
            resource_id=order_acknowledgement.po_number,
            team_id=email.team.id,
        )

    logger.info(
        "Order Acknowledgement Workflow: Purchase order found",
        email_id=email_id,
        purchase_order_id=purchase_order.id,
    )

    # NOTE: OA creation moved to post-validation phase for data integrity
    # This ensures only validated OAs are persisted to the database
    logger.info(
        "Order Acknowledgement Workflow: Extracted OA data, deferring creation until after validation",
        email_id=email_id,
        purchase_order_id=purchase_order.id,
        extracted_po_number=order_acknowledgement.po_number,
    )

    # Link the communication to the purchase order (still needed)
    logger.info(
        "Order Acknowledgement Workflow: Linking communication to purchase order",
        email_id=email_id,
        purchase_order_id=purchase_order.id,
    )

    try:
        link, created = EmailThreadToPurchaseOrderLink.objects.get_or_create(
            email_thread=email.email_thread,
            purchase_order=purchase_order,
        )
        if created:
            logger.info(
                "Order Acknowledgement Workflow: Linked email thread to purchase order",
                email_id=email_id,
                purchase_order_id=purchase_order.id,
            )
    except OperationalError:
        # Database errors should be retried
        logger.error("Database error linking email to PO")
        raise

    return {
        "order_acknowledgement": order_acknowledgement,
        "purchase_order_id": str(purchase_order.id),
        "email_id": email_id,
    }


def create_order_acknowledgement_success_notification(
    purchase_order: PurchaseOrder,
    order_acknowledgement: OrderAcknowledgementSchema,
    email_id: str,
):
    """
    Create a success notification when human validation is bypassed.

    Args:
        purchase_order: The PurchaseOrder instance
        order_acknowledgement: The OrderAcknowledgement instance
        email_id: ID of the email that contains the order acknowledgement
    """
    # Use the shared helper function to determine notification recipient
    user = get_notification_recipient(purchase_order)
    if not user:
        logger.warning(
            "No notification recipient found for team",
            team_id=purchase_order.team.id if purchase_order.team else None,
            po_id=purchase_order.id,
            po_number=purchase_order.po_number,
        )
        return

    # Prepare context panels
    context_panels: list[ContextPanelDefinition] = [
        {
            "panel_type": TaskContextPanelType.PO_DETAILS,
            "param_values": {"purchaseOrderId": str(purchase_order.id)},
        }
    ]

    # Add communication panel if email_id is available
    if email_id:
        context_panels.append(
            {
                "panel_type": TaskContextPanelType.COMMUNICATION_DETAILS,
                "param_values": {"communicationId": email_id},
            }
        )

    # Format total amount properly for display
    total_amount = order_acknowledgement.total_amount
    if isinstance(total_amount, str) and not total_amount.startswith("$"):
        total_amount = f"${total_amount}"

    # Create a detailed description for the notification
    details_description = (
        f"Order Acknowledgement #{order_acknowledgement.order_number} for PO #{purchase_order.po_number} "
        f"from {purchase_order.supplier.name if purchase_order.supplier else 'Unknown'} "
        f"has been automatically accepted.\n\n"
        f"Total: {total_amount}\n"
        f"Order is now in Awaiting Shipment status. No action is required. This is an automated notification."
    )

    # Import serialization utils
    from didero.workflows.serialization_utils import (
        serialize_order_acknowledgement,
        serialize_purchase_order,
    )

    order_acknowledgement_data = serialize_order_acknowledgement(order_acknowledgement)
    po_data = serialize_purchase_order(purchase_order)

    # Prepare task parameters
    task_params = {
        "po_number": purchase_order.po_number,
        "supplier_name": purchase_order.supplier.name
        if purchase_order.supplier
        else "Unknown",
        "total_cost": total_amount,
        "details": details_description,
        "order_acknowledgement_data": order_acknowledgement_data,
        "po_data": po_data,
    }

    return create_task_v2(
        task_type=TaskType.ORDER_ACKNOWLEDGEMENT_WORKFLOW_SUCCESS_NOTIFICATION,
        user=user,
        model_type=ContentType.objects.get_for_model(PurchaseOrder),
        model_id=str(purchase_order.id),
        task_type_params=task_params,
        context_panels=context_panels,
        # No actions needed for notification - just a simple dismiss button will be shown
    )


@activity.defn
def order_acknowledgement_post_validation_actions(
    params: OrderAcknowledgementPostValidationActionsParams,
) -> bool:
    """
    Perform post-validation actions for order acknowledgement.

    Returns:
        bool: True if human validation was bypassed (auto-accepted)

    Raises:
        ResourceNotFoundError: If PO or email not found (non-retryable)
        ValidationError: If OA creation fails (non-retryable)
        OperationalError: Database issues (retryable)
    """
    # Fetch the purchase order
    try:
        purchase_order = PurchaseOrder.objects.get(id=params["purchase_order_id"])
    except PurchaseOrder.DoesNotExist:
        logger.error(
            "Order Acknowledgement Workflow - Post Validation: Purchase order not found",
            purchase_order_id=params["purchase_order_id"],
        )
        raise ResourceNotFoundError(
            resource_type="PurchaseOrder",
            resource_id=params["purchase_order_id"],
        )
    except OperationalError:
        # Database connection errors should be retried by Temporal
        logger.error(
            f"Database connection error fetching purchase order {params['purchase_order_id']}"
        )
        raise

    # Get the extracted order acknowledgement data
    order_acknowledgement_data = params["order_acknowledgement"]
    email_id = params["email_id"]

    # Get the email for OA creation
    try:
        email = Communication.objects.get(id=params["email_id"])
    except Communication.DoesNotExist:
        logger.error(
            "Order Acknowledgement Workflow - Post Validation: Email not found",
            email_id=params["email_id"],
        )
        raise ResourceNotFoundError(
            resource_type="Communication",
            resource_id=params["email_id"],
        )
    except OperationalError:
        # Database connection errors should be retried by Temporal
        logger.error(f"Database connection error fetching email {params['email_id']}")
        raise

    # Create the Django OrderAcknowledgement model after validation
    logger.info(
        "Order Acknowledgement Workflow - Post Validation: Creating OrderAcknowledgement Django model",
        email_id=email_id,
        purchase_order_id=purchase_order.id,
    )

    # Check if OA already exists for this PO and order number
    existing_oa = None
    if order_acknowledgement_data.order_number:
        try:
            existing_oa = DjangoOrderAcknowledgement.objects.get(
                purchase_order=purchase_order,
                order_number=order_acknowledgement_data.order_number,
            )
            logger.warning(
                "Order Acknowledgement already exists for this PO and order number",
                email_id=email_id,
                purchase_order_id=purchase_order.id,
                order_number=order_acknowledgement_data.order_number,
                existing_oa_id=existing_oa.id,
            )
        except DjangoOrderAcknowledgement.DoesNotExist:
            pass  # No existing OA, proceed with creation
        except OperationalError:
            # Database connection errors should be retried by Temporal
            logger.error(
                "Database connection error checking existing OrderAcknowledgement"
            )
            raise

    # OA creation is now handled in the main workflow via save_oa_activity
    # This post-validation step only handles notification and task creation
    logger.info(
        "Order Acknowledgement Workflow - Post Validation: OA creation handled by main workflow",
        email_id=email_id,
        purchase_order_id=purchase_order.id,
    )

    # Get email date for comments
    email_date = email.created_at.strftime("%Y-%m-%d")

    # Import team setting utility
    from didero.users.models.user_team_setting_models import TeamSettingEnums
    from didero.users.utils.team_setting_utils import get_team_setting_boolean_value

    # Check if human validation is enabled for this team
    human_validation_enabled = get_team_setting_boolean_value(
        TeamSettingEnums.TEAM_ORDER_ACK_HUMAN_VALIDATION_ENABLED.value,
        purchase_order.team,
    )

    logger.info(
        "Order Acknowledgement Workflow - Post Validation: Checking human validation setting",
        purchase_order_id=str(purchase_order.id),
        team_id=str(purchase_order.team.id),
        human_validation_enabled=human_validation_enabled,
    )

    if human_validation_enabled:
        # Create a task for human validation (original behavior)
        create_order_acknowledgement_human_validation_task(
            purchase_order=purchase_order,
            order_acknowledgement=order_acknowledgement_data,
            email_id=email_id,
        )

        # Create a comment on the purchase order for review
        comment, created = PurchaseOrderComment.objects.get_or_create(
            purchase_order=purchase_order,
            comment=f"Order Acknowledgement received: {purchase_order.supplier.name} sent order confirmation #{order_acknowledgement_data.order_number} on {email_date} for PO #{purchase_order.po_number}. Please review.",
            created_by=get_didero_ai_user(team=purchase_order.team),
        )
        if created:
            logger.info(
                "Created review comment for order acknowledgement",
                purchase_order_id=str(purchase_order.id),
                order_number=order_acknowledgement_data.order_number,
            )
    else:
        # Automatically approve the order acknowledgement
        from didero.activity_log.models import PurchaseOrderStatusUpdate
        from didero.activity_log.schemas import SystemActor

        # Update the order status to Awaiting_shipment
        old_status = purchase_order.order_status
        purchase_order.order_status = PurchaseOrderStatus.AWAITING_SHIPMENT.value
        purchase_order.save()

        # Create a status update log - use SYSTEM_LOGIC for automated actions
        PurchaseOrderStatusUpdate.objects.create(
            purchase_order=purchase_order,
            user_actor=None,
            system_actor=SystemActor.SYSTEM_LOGIC.value,
            triggering_communication=email,
            status_changed="order_status",
            old_status=old_status,
            new_status=purchase_order.order_status,
        )

        # Create a success notification
        create_order_acknowledgement_success_notification(
            purchase_order=purchase_order,
            order_acknowledgement=order_acknowledgement_data,
            email_id=email_id,
        )

        # Create a comment on the purchase order for auto-acceptance
        comment, created = PurchaseOrderComment.objects.get_or_create(
            purchase_order=purchase_order,
            comment=f"{purchase_order.supplier.name} sent order confirmation #{order_acknowledgement_data.order_number} on {email_date} for PO #{purchase_order.po_number}. PO has been moved to Awaiting Shipment status.",
            created_by=get_didero_ai_user(team=purchase_order.team),
        )
        if created:
            logger.info(
                "Created auto-acceptance comment for order acknowledgement",
                purchase_order_id=str(purchase_order.id),
                order_number=order_acknowledgement_data.order_number,
            )

        logger.info(
            "Order Acknowledgement Workflow - Post Validation: Order acknowledgement automatically accepted",
            purchase_order_id=str(purchase_order.id),
            team_id=str(purchase_order.team.id),
            new_status=purchase_order.order_status,
        )

    # Trigger the follow-up workflow for this purchase order
    logger.info(
        "Order Acknowledgement Workflow - Post Validation: Triggering follow-up workflow",
        purchase_order_id=str(purchase_order.id),
        email_id=params["email_id"],
    )

    # Return True if human validation was bypassed
    return not human_validation_enabled


class OrderAcknowledgementPostValidationActions(WorkflowAction):
    activity = order_acknowledgement_post_validation_actions

    async def execute(self):
        try:
            error, res = await self.execute_activity()
            if error:
                await self.handle_error(error)
                await self.set_dag_state(self.id, WorkflowRunState.FAILED)
                return NodeResult(
                    next_nodes=[],
                    continue_workflow=False,
                )
        except Exception:
            await self.set_dag_state(self.id, WorkflowRunState.FAILED)
            logger.error(
                "Order Acknowledgement Workflow: Received unexpected exception {e} when executing OrderAcknowledgementPostValidationActions activity. Failing the workflow.",
            )
            return NodeResult(
                next_nodes=[],
                continue_workflow=False,
            )

        await self.set_dag_state(self.id, WorkflowRunState.SUCCESS)
        self.completed = True

        next = [succ for succ in self.successors if not succ.completed]
        for succ in next:
            succ.params = res

        return NodeResult(
            next_nodes=next,
        )

    async def handle_error(self, error: OrderAcknowledgementWorkflowError):
        await self.set_dag_state(self.id, WorkflowRunState.FAILED)
        logger.error(
            "Order Acknowledgement Workflow: Received unexpected exception {error} when executing OrderAcknowledgementPostValidationActions activity. Failing the workflow.",
            error=error,
        )
        return NodeResult(
            next_nodes=[],
            continue_workflow=False,
        )


class ExtractOrderAcknowledgementDetails(WorkflowAction):
    activity = extract_order_acknowledgement_details

    async def execute(self):
        try:
            error, res = await self.execute_activity()
            if error:
                await self.handle_error(error)
                await self.set_dag_state(self.id, WorkflowRunState.FAILED)
                return NodeResult(
                    next_nodes=[],
                    continue_workflow=False,
                )
        except Exception as e:
            await self.set_dag_state(self.id, WorkflowRunState.FAILED)
            logger.error(
                f"Order Acknowledgement Workflow: Received unexpected exception {e} when executing ExtractOrderAcknowledgementDetails activity. Failing the workflow.",
            )
            return NodeResult(
                next_nodes=[],
                continue_workflow=False,
            )

        await self.set_dag_state(self.id, WorkflowRunState.SUCCESS)
        self.completed = True

        next = [succ for succ in self.successors if not succ.completed]
        for succ in next:
            succ.params = res

        return NodeResult(
            next_nodes=next,
        )

    async def handle_error(self, error: OrderAcknowledgementWorkflowErrorType):
        await self.set_dag_state(self.id, WorkflowRunState.FAILED)
        logger.error(
            "Order Acknowledgement Workflow: Received unexpected exception {error} when executing ExtractOrderAcknowledgementDetails activity. Failing the workflow.",
            error=error,
        )
        return NodeResult(
            next_nodes=[],
            continue_workflow=False,
        )
