from enum import StrEnum
from typing import Any, List, Optional, TypedDict

from django.contrib.contenttypes.models import ContentType
from django.db import IntegrityError
from pydantic import BaseModel
from temporalio import activity, workflow

from didero.orders.schemas import (
    CarrierType,
    PurchaseOrderStatus,
    ShipmentStatus,
)
from didero.tasks.schemas import (
    TaskActionButtonType,
    TaskContextPanelType,
)
from didero.tasks.schemas import (
    TaskActionType as TaskActionTypeName,
)
from didero.tasks.schemas import TaskType as TaskTypeName
from didero.tasks.utils import ContextPanelDefinition, create_task_v2
from didero.utils.utils import get_didero_ai_user
from didero.workflows.core.nodes.tasks import CreateTaskParams, CreateTaskV2Params
from didero.workflows.notification_utils import get_notification_recipient

with workflow.unsafe.imports_passed_through():
    import structlog

    from didero.ai.email_processing.shipment_info import (
        GetPickupInfoResponse,
        OrderItemFields,
        get_pickup_info_from_email,
        get_shipment_info_for_email,
        get_shipment_order_items_for_email,
    )
    from didero.emails.models import EmailThreadToPurchaseOrderLink
    from didero.integrations.utils.fedex.fedex import (
        get_tracking_info_for_tracking_number,
    )
    from didero.orders.models import (
        OrderItem,
        PurchaseOrder,
        PurchaseOrderComment,
        Shipment,
        ShipmentLineItem,
    )
    from didero.suppliers.models import Communication
    from didero.users.models import User
    from didero.workflows.core.nodes.base_nodes import (
        ConditionActivityResult,
        NodeResult,
        WorkflowAction,
        WorkflowCondition,
    )
    from didero.workflows.schemas import WorkflowRunState

logger = structlog.get_logger(__name__)


class ShipmentWorkflowErrorType(StrEnum):
    COMMUNICATION_NOT_FOUND = "communication_not_found"
    CARRIER_NAME_NOT_FOUND = "carrier_name_not_found"
    ORDER_ITEM_LIST_NOT_FOUND = "order_item_list_not_found"
    PURCHASE_ORDER_NOT_FOUND = "purchase_order_not_found"
    OPENAI_ERROR = "openai_error"
    UNKNOWN_ERROR = "unknown_error"
    CARRIER_NAME_INVALID = "carrier_name_invalid"
    SHIPMENT_DATE_INVALID = "shipment_date_invalid"
    UNMATCHED_ORDER_ITEMS = "unmatched_order_items"
    INVALID_SHIPMENT_EMAIL = "invalid_shipment_email"
    TRACKING_NUMBER_REQUIRED = "tracking_number_required"


class ShipmentWorkflowError(BaseModel):
    error_type: ShipmentWorkflowErrorType
    error_details: dict[str, Any]


# These error types map to Tasks that are created to notify the ops team
TASK_CREATION_ERROR_TYPES: list[ShipmentWorkflowErrorType] = [
    ShipmentWorkflowErrorType.CARRIER_NAME_INVALID,
    ShipmentWorkflowErrorType.SHIPMENT_DATE_INVALID,
    ShipmentWorkflowErrorType.UNMATCHED_ORDER_ITEMS,
    ShipmentWorkflowErrorType.TRACKING_NUMBER_REQUIRED,
]


class ShipmentDetails(BaseModel):
    carrier: str
    carrier_type: CarrierType
    tracking_number: Optional[str]
    purchase_order_number: str
    shipment_date: Optional[str]
    estimated_delivery_date: Optional[str]
    order_items: Optional[List[OrderItemFields]]
    email_pk: int
    team_id: int


class ParseShipmentParams(TypedDict):
    email_id: str


def get_shipping_workflow_parameters(
    email: Communication,
    notification_type: Optional[str] = None,
    is_core_workflow: bool = False,
) -> dict:
    """
    Get workflow parameters for shipping/pickup workflows.

    Args:
        email: The Communication object
        notification_type: Type of notification - 'shipped' or 'pickup' (optional)
        is_core_workflow: Whether this is for a core workflow implementation

    Returns:
        dict: Workflow parameters formatted for the workflow type
    """
    if is_core_workflow:
        # Core workflows expect flat parameters
        params = {
            "email_id": str(email.pk),
            "team_id": str(email.team.id),
            "workflow_id": "",  # Will be set by trigger_workflow_if_exists
        }
        if notification_type:
            params["notification_type"] = notification_type
        return params
    else:
        # DAG workflows expect nested structure
        params = {
            "default": {
                "email_id": str(email.pk),
            }
        }
        if notification_type:
            params["default"]["notification_type"] = notification_type
        return params


# This works around the fact that some purchase orders have a PO- prefix and some don't.
# Didero POs have a PO- prefix by default, but it's not a requirement.
# So there's a chance the matching PO does not have a PO- prefix.
def get_purchase_order_from_po_number(
    po_number: str, team_id: int
) -> Optional[PurchaseOrder]:
    # Sanitize the PO number by removing any whitespace
    po_number = po_number.strip()

    try:
        purchase_order = PurchaseOrder.objects.get(
            po_number=f"PO-{po_number}", team_id=team_id, archived_at__isnull=True
        )
    except PurchaseOrder.DoesNotExist:
        try:
            purchase_order = PurchaseOrder.objects.get(
                po_number=po_number, team_id=team_id, archived_at__isnull=True
            )
        except PurchaseOrder.DoesNotExist:
            return None

    return purchase_order


def get_purchase_order_comment_for_shipment(
    purchase_order: PurchaseOrder, shipment_details: ShipmentDetails
) -> str:
    if shipment_details.shipment_date and shipment_details.estimated_delivery_date:
        return f"{purchase_order.po_number} was shipped on {shipment_details.shipment_date} via {shipment_details.carrier} with Tracking Number {shipment_details.tracking_number} and has an estimated delivery date of {shipment_details.estimated_delivery_date}."
    elif shipment_details.shipment_date:
        return f"{purchase_order.po_number} was shipped on {shipment_details.shipment_date} via {shipment_details.carrier} with Tracking Number {shipment_details.tracking_number}. The estimated delivery date is not known at this point."
    elif shipment_details.estimated_delivery_date:
        return f"A shipment update was received for {purchase_order.po_number}. A shipping label was created with {shipment_details.carrier} with Tracking Number {shipment_details.tracking_number}. It has not shipped yet however the carrier has given an estimated delivery date of {shipment_details.estimated_delivery_date}."
    else:
        return f"A shipment update was received for {purchase_order.po_number}. A shipping label was created with {shipment_details.carrier} with Tracking Number {shipment_details.tracking_number}. It has not shipped yet and the estimated delivery date is not known at this point. An update will be made when more information is available."


def create_shipment_success_notification(
    order: "PurchaseOrder", shipment: "Shipment", email_id: Optional[str] = None
):
    """
    Create a success notification when human validation is bypassed for shipments.

    Args:
        order: The PurchaseOrder instance
        shipment: The Shipment instance
        email_id: ID of the email that triggered the workflow
    """
    # Use the shared helper function to determine notification recipient
    user = get_notification_recipient(order)
    if not user:
        logger.warning(
            "No notification recipient found for team",
            team_id=order.team.id if order.team else None,
            order_id=order.id,
            shipment_id=shipment.id,
        )
        return

    # Use string representations directly to avoid formatting issues
    shipment_date_str = (
        str(shipment.shipment_date) if shipment.shipment_date else "Unknown"
    )

    # Prepare context parameters
    po_details_context_params: ContextPanelDefinition = {
        "panel_type": TaskContextPanelType.PO_DETAILS,
        "param_values": {"purchaseOrderId": str(order.pk)},
    }
    communication_context_params: Optional[ContextPanelDefinition] = None

    # Add communication ID if available
    if email_id:
        communication_context_params = {
            "panel_type": TaskContextPanelType.COMMUNICATION_DETAILS,
            "param_values": {"communicationId": email_id},
        }

    context_panels = [po_details_context_params]
    if communication_context_params:
        context_panels.append(communication_context_params)

    # Import serialization utils
    from didero.workflows.serialization_utils import (
        serialize_purchase_order,
        serialize_shipment,
    )

    po_data = serialize_purchase_order(order)
    shipment_data = serialize_shipment(shipment)

    # Create a detailed description for the notification
    details_description = (
        f"Shipment automatically confirmed for PO #{order.po_number}.\n\n"
        f"Carrier: {shipment.carrier or 'Unknown'}\n"
        f"Tracking Number: {shipment.tracking_number or 'N/A'}\n"
        f"Shipment Date: {shipment_date_str}\n"
        f"Estimated Delivery: {shipment.estimated_delivery_date or 'Not available'}\n\n"
        f"No action is required. This is an automated notification."
    )

    # Prepare task parameters
    task_params = {
        "po_number": order.po_number,
        "tracking_number": shipment.tracking_number or "N/A",
        "carrier": shipment.carrier or "Unknown",
        "shipment_date": shipment_date_str,
        "details": details_description,
        "po_data": po_data,
        "shipment_data": shipment_data,
    }

    return create_task_v2(
        task_type=TaskTypeName.SHIPMENT_WORKFLOW_SUCCESS_NOTIFICATION,
        user=user,
        model_type=ContentType.objects.get_for_model(PurchaseOrder),
        model_id=str(order.pk),
        task_type_params=task_params,
        context_panels=context_panels,
        # No actions needed for notification - just a simple dismiss button will be shown
    )


def create_shipment_workflow_completed_task(
    order: "PurchaseOrder", shipment: "Shipment", email_id: Optional[str] = None
):
    """
    Create a task for the ops team to confirm the shipment before updating Netsuite.

    Args:
        order: The PurchaseOrder instance
        shipment: The Shipment instance
        email_id: ID of the email that triggered the workflow
    """
    user = get_didero_ai_user(team=order.team)
    if not user:
        logger.warning(
            "No Didero AI user found for team, not making shipment workflow completed task",
            team_id=order.team.id,
            order_id=order.id,
            shipment_id=shipment.id,
        )
        return

    # Use string representations directly to avoid formatting issues
    shipment_date_str = (
        str(shipment.shipment_date) if shipment.shipment_date else "Unknown"
    )

    # Prepare context parameters
    po_details_context_params: ContextPanelDefinition = {
        "panel_type": TaskContextPanelType.PO_DETAILS,
        "param_values": {"purchaseOrderId": str(order.pk)},
    }
    communication_context_params = None

    # Add communication ID if available
    if email_id:
        communication_context_params: ContextPanelDefinition = {
            "panel_type": TaskContextPanelType.COMMUNICATION_DETAILS,
            "param_values": {"communicationId": email_id},
        }

    context_panels = [po_details_context_params]
    if communication_context_params:
        context_panels.append(communication_context_params)

    # Import serialization utils
    from didero.workflows.serialization_utils import (
        serialize_purchase_order,
        serialize_shipment,
    )

    shipment_data = serialize_shipment(shipment)
    po_data = serialize_purchase_order(order)

    # Prepare task parameters
    task_params = {
        "po_number": order.po_number,
        "tracking_number": shipment.tracking_number or "N/A",
        "carrier": shipment.carrier or "Unknown",
        "shipment_date": shipment_date_str,
        "estimated_delivery_date": str(shipment.estimated_delivery_date)
        if shipment.estimated_delivery_date
        else "N/A",
        "shipment_data": shipment_data,
        "po_data": po_data,
    }

    return create_task_v2(
        task_type=TaskTypeName.OPS_TASK_SHIPMENT_WORKFLOW_COMPLETED,
        user=user,
        model_type=ContentType.objects.get_for_model(PurchaseOrder),
        model_id=str(order.pk),
        task_type_params=task_params,
        context_panels=context_panels,
        actions=[
            {
                "action_type": TaskActionTypeName.CONFIRM_SHIPMENT,
                "action_params": {},
                "action_execution_params": {
                    "purchase_order_id": str(order.pk),
                    "shipment_id": str(shipment.pk),
                },
                "button_type": TaskActionButtonType.GREEN,
            },
            {
                "action_type": TaskActionTypeName.CANCEL_SHIPMENT,
                "action_params": {},
                "action_execution_params": {
                    "purchase_order_id": str(order.pk),
                    "shipment_id": str(shipment.pk),
                },
                "button_type": TaskActionButtonType.RED,
            },
        ],
    )


@activity.defn
def parse_shipment_details_from_email(
    params: ParseShipmentParams,
) -> ShipmentDetails:
    """
    Parse shipment details from email using AI.

    Raises:
        ResourceNotFoundError: If email/communication/PO not found (non-retryable)
        ValidationError: If data validation fails (non-retryable)
        Exception: For transient errors like OpenAI failures (retryable)
    """
    from django.db import OperationalError

    from didero.workflows.errors import ResourceNotFoundError, ValidationError

    logger.info(
        "Starting parse_shipment_details_from_email",
        email_id=params["email_id"],
    )
    email_id = params["email_id"]

    try:
        email = Communication.objects.select_related(
            "team", "supplier", "email_thread"
        ).get(id=email_id)
    except Communication.DoesNotExist:
        logger.error(
            "Parse Shipment Details: Email not found, failing the Shipment Workflow",
            email_id=email_id,
        )
        raise ResourceNotFoundError(
            resource_type="Communication",
            resource_id=email_id,
            message="Email not found, failing the Shipment Workflow",
        )
    except OperationalError:
        # Database connection errors should be retried by Temporal
        logger.error("Database connection error fetching email")
        raise

    shipment_details_extraction = get_shipment_info_for_email(email)
    user = get_didero_ai_user(email.team)
    if not user or user.id is None:
        logger.error(
            "Parse Shipment Details: Didero AI user not found, failing the Shipment Workflow",
            email_id=email_id,
        )
        raise ResourceNotFoundError(
            resource_type="User",
            resource_id="didero_ai_user",
            team_id=email.team.id,
            message="Didero AI user not found, failing the Shipment Workflow",
        )

    if not shipment_details_extraction:
        logger.error(
            "OpenAI returned None for Shipment Fields Extraction",
            email_id=email_id,
            team_id=email.team.pk,
        )
        # This is a transient error that should be retried
        raise Exception("OpenAI returned None for Shipment Fields Extraction")

    # Simple PO lookup - DAG workflows don't support auto-creation
    purchase_order = get_purchase_order_from_po_number(
        shipment_details_extraction.purchase_order_number, email.team.id
    )

    if not purchase_order:
        logger.error(
            "Shipment Workflow: Purchase order not found",
            po_number=shipment_details_extraction.purchase_order_number,
            email_id=email_id,
            team_id=email.team.pk,
        )
        raise ResourceNotFoundError(
            resource_type="PurchaseOrder",
            resource_id=shipment_details_extraction.purchase_order_number,
            team_id=email.team.id,
            message=f"Purchase order {shipment_details_extraction.purchase_order_number} not found",
        )

    # Validate carrier name
    if (
        not shipment_details_extraction.carrier_name
        or shipment_details_extraction.carrier_name.lower()
        in [
            "unknown",
            "null",
            "",
            "none",
        ]  # carrier name is optional, so LLM returns None if it's not present. This is an additional check to ensure the carrier name is None, since the LLM sometimes may return a string.
    ):
        logger.error(
            "Parse Shipment Details: Invalid carrier name ",
            email_id=email_id,
            carrier_name=shipment_details_extraction.carrier_name,
            po_number=shipment_details_extraction.purchase_order_number,
        )
        raise ValidationError(
            field="carrier_name",
            value=shipment_details_extraction.carrier_name,
            message="Carrier name is invalid or missing in the shipment email. Please review the shipment email and validate the carrier name.",
            context={
                "email_id": email_id,
                "po_number": shipment_details_extraction.purchase_order_number,
                "purchase_order_id": purchase_order.pk,
            },
        )

    try:
        # Get order items from the purchase order
        order_items = list(purchase_order.items.all())

        # Get shipment items from the extracted details
        shipment_items = shipment_details_extraction.order_items

        # Match the shipment items with order items
        logger.info(
            "Matching shipment items with order items",
            email_id=email_id,
            order_items=order_items,
            shipment_items=shipment_items,
        )
        order_items = get_shipment_order_items_for_email(
            email, order_items, shipment_items
        )
    except Exception as e:
        logger.error(
            "Parse Shipment Details: Error getting shipment order items",
            error=e,
        )
        # This is a transient error that should be retried
        raise

    # Validate order items
    if not order_items.matched_items:
        logger.warning(
            "Parse Shipment Details: Unmatched order items found in shipment details",
            email_id=email_id,
            po_number=purchase_order.po_number,
            matched_items=order_items.matched_items,
        )
        raise ValidationError(
            field="order_items",
            value=order_items.matched_items,
            message=f"Order items from the shipment update could not be matched to the items in PO {purchase_order.po_number}. Please verify the shipment email and validate that the items in the shipment match the PO items.",
            context={
                "purchase_order_id": purchase_order.pk,
                "email_id": email_id,
                "po_number": purchase_order.po_number,
            },
        )

    # We only support live tracking for Fedex at the moment.
    # If the carrier type is not Fedex, we should not attempt to fetch tracking info.
    if (
        shipment_details_extraction.carrier_type == CarrierType.FEDEX
        and shipment_details_extraction.tracking_number
    ):
        tracking_info = get_tracking_info_for_tracking_number(
            shipment_details_extraction.tracking_number
        )
    else:
        tracking_info = None

    # If we get an error when fetching tracking info from Fedex, we should create a task to notify the ops team.
    if not tracking_info:
        logger.warning(
            "Parse Shipment Details: Could not fetch tracking information for tracking number, using shipment date from email to create shipment",
            email_id=email_id,
            team_id=email.team.pk,
            tracking_number=shipment_details_extraction.tracking_number,
            carrier_name=shipment_details_extraction.carrier_name,
            purchase_order_number=shipment_details_extraction.purchase_order_number,
        )

    # Use the shipment date from tracking info as source of truth (it's the most accurate)
    # As a backup, we use the shipment date from the email.
    # If both are not present, we return None.
    if tracking_info and tracking_info.shipment_date:
        final_shipment_date = tracking_info.shipment_date
    else:
        final_shipment_date = shipment_details_extraction.shipment_date

    # Validate shipment date
    if not final_shipment_date:
        logger.error(
            "Parse Shipment Details: No valid shipment date found",
            email_id=email_id,
            po_number=purchase_order.po_number,
        )
        raise ValidationError(
            field="shipment_date",
            value=final_shipment_date,
            message=f"No valid shipment date was found in the shipment update email for PO {purchase_order.po_number}. Please review the email and validate the shipment date.",
            context={
                "purchase_order_id": purchase_order.pk,
                "email_id": email_id,
                "po_number": purchase_order.po_number,
            },
        )

    # Additional validation for THS. THS needs to have a tracking number to create a shipment.
    # These are team specific reuqirements that need to be seperated from the core logic later
    if not shipment_details_extraction.tracking_number:
        raise ValidationError(
            field="tracking_number",
            value=shipment_details_extraction.tracking_number,
            message=f"No tracking number was found in the shipment update email for PO {purchase_order.po_number}. Please review the email and validate the tracking number.",
            context={
                "purchase_order_id": purchase_order.pk,
                "email_id": email_id,
                "po_number": purchase_order.po_number,
            },
        )

    # Use the estimated delivery date from tracking info as source of truth (it's the most accurate)
    # As a backup, we use the estimated delivery date from the email.
    # If both are not present, we return None.
    if tracking_info and tracking_info.eta:
        final_estimated_delivery_date = tracking_info.eta
    else:
        final_estimated_delivery_date = (
            shipment_details_extraction.estimated_delivery_date
        )

    return ShipmentDetails(
        # If we have a carrier from tracking info, use it as it's likely more accurate.
        # Otherwise, use the carrier from the shipment details extraction.
        carrier=tracking_info.carrier
        if tracking_info
        else shipment_details_extraction.carrier_name,
        carrier_type=shipment_details_extraction.carrier_type,
        tracking_number=shipment_details_extraction.tracking_number,
        purchase_order_number=shipment_details_extraction.purchase_order_number,
        shipment_date=final_shipment_date,
        estimated_delivery_date=final_estimated_delivery_date,
        order_items=order_items.matched_items,
        email_pk=email.pk,
        team_id=email.team.id,
    )


class ReadShipmentDetails(WorkflowAction):
    activity = parse_shipment_details_from_email

    async def execute(self):
        from didero.workflows.errors import ValidationError

        try:
            res = await self.execute_activity()
        except ValidationError as e:
            # Handle validation errors by creating tasks
            await self.handle_validation_error(e)
            await self.set_dag_state(self.id, WorkflowRunState.FAILED)
            return NodeResult(
                next_nodes=[],
                continue_workflow=False,
            )
        except Exception as e:
            await self.set_dag_state(self.id, WorkflowRunState.FAILED)
            logger.error(
                f"Shipment Workflow: Received unexpected exception {e} when executing ReadShipmentDetails activity. Failing the workflow.",
                exception=e,
            )
            return NodeResult(
                next_nodes=[],
                continue_workflow=False,
            )

        await self.set_dag_state(self.id, WorkflowRunState.SUCCESS)
        self.completed = True

        next = [succ for succ in self.successors if not succ.completed]
        for succ in next:
            succ.params = res

        return NodeResult(
            next_nodes=next,
        )

    async def handle_validation_error(self, error):
        """Handle validation errors by creating tasks for ops team"""
        logger.error(
            "Shipment Workflow: Received validation error when executing ReadShipmentDetails activity. Failing the workflow.",
            error=str(error),
            field=getattr(error, "field", None),
            message=getattr(error, "message", str(error)),
        )

        # Extract context from the ValidationError
        context = getattr(error, "context", {})
        purchase_order_id = context.get("purchase_order_id")
        po_number = context.get("po_number")

        # Get the appropriate user for task assignment
        if purchase_order_id:
            try:
                from didero.orders.models import PurchaseOrder

                order = PurchaseOrder.objects.get(pk=purchase_order_id)
                user = get_didero_ai_user(team=order.team)

                if user and order.team:
                    logger.info(
                        "Creating task to notify the ops team about a validation error",
                        validation_error=str(error.message),
                        user_id=user.id,
                        model_id=purchase_order_id,
                        team_id=order.team.id,
                        po_number=po_number,
                    )

                    # Create TaskV1 (original)
                    await self.create_task(
                        CreateTaskParams(
                            task_type=TaskTypeName.SHIPMENT_WORKFLOW_VALIDATION_ERROR,
                            task_params={
                                "validation_error": str(error.message),
                                "po_number": po_number,
                            },
                            send_to_ids=[int(user.id)],
                            model_id=int(purchase_order_id),
                            model_type="PurchaseOrder",
                            team_id=int(order.team.id),
                        ),
                    )

                    # Create TaskV2
                    await self.create_task_v2(
                        CreateTaskV2Params(
                            task_type=TaskTypeName.SHIPMENT_WORKFLOW_VALIDATION_ERROR,
                            task_type_params={
                                "validation_error": str(error.message),
                                "po_number": po_number,
                            },
                            send_to_ids=[int(user.id)],
                            model_id=int(purchase_order_id),
                            model_type="PurchaseOrder",
                            team_id=int(order.team.id),
                            context_panels=[
                                {
                                    "panel_type": TaskContextPanelType.PO_DETAILS,
                                    "param_values": {
                                        "purchaseOrderId": str(purchase_order_id)
                                    },
                                }
                            ],
                        ),
                    )
            except Exception as e:
                logger.error(
                    "Failed to create validation error task",
                    error=str(e),
                    purchase_order_id=purchase_order_id,
                )


def _get_shipments_from_details(
    shipment_details: ShipmentDetails, purchase_order: PurchaseOrder
) -> list[Shipment]:
    try:
        logger.info(
            "Getting shipment from details",
            shipment_details=shipment_details,
        )
        if shipment_details.tracking_number:
            shipment = Shipment.objects.get(
                purchase_order=purchase_order,
                carrier=shipment_details.carrier,
                shipment_date=shipment_details.shipment_date,
                tracking_number=shipment_details.tracking_number,
            )
            shipments = [shipment]
        else:
            shipments = list(
                Shipment.objects.filter(
                    purchase_order=purchase_order,
                    carrier=shipment_details.carrier,
                    shipment_date=shipment_details.shipment_date,
                )
            )

        logger.info(
            "Found shipments",
            num_shipments=len(shipments),
            shipment_ids=[s.pk for s in shipments],
        )
        return shipments

    except Shipment.DoesNotExist:
        logger.info(
            "Shipment not found",
            shipment_details=shipment_details,
        )
        return []

    except Exception as e:
        logger.error(
            "Error getting shipment from details",
            error=e,
        )
        raise Exception("Failed to get shipment from details") from e


# TODO: Match this email to email that created the retrived shipment.
# Right now, there is not way to identify a seperate shipment if tracking number is not present and the rest of the details are the same.
# E.g. item 1 has quantity 2 on PO. It is shipped on same day with quantity 1 (no tracking number). This triggers 2 emails, but only 1 shipment is created as all the rest of the details are the same.
# Shipment object doesn't have the email that created it, so we can't match the email to the shipment.
#
# This should become obsolete when we have a way to match emails to shipments. There are too many edge cases to handle here.
# When it comes from a different email, we can assume the order items are different and proceed with creating a shipment.
# This function then becomes a check to verify if the email is different or not for the list of shipments retrieved.


def _get_unmatched_order_items(
    shipments: list[Shipment],
    shipment_details: ShipmentDetails,
    purchase_order: PurchaseOrder,
) -> dict[OrderItem, int]:
    # Get all shipment line items from all shipments
    shipment_line_items = ShipmentLineItem.objects.filter(shipment__in=shipments)

    email_order_items = shipment_details.order_items
    unmatched_order_items = {}

    # Create a dictionary of shipment line items for easier lookup
    shipment_line_items_dict = {
        str(item.order_item.item_id): item for item in shipment_line_items
    }

    logger.info(
        "Checking email order items against existing shipment line items",
        shipment_ids=[s.pk for s in shipments],
        num_existing_line_items=len(shipment_line_items_dict),
        num_email_order_items=len(email_order_items) if email_order_items else 0,
    )

    # Check each email order item against shipment line items
    for email_order_item in email_order_items:
        if email_order_item.order_item_id not in shipment_line_items_dict:
            order_item = OrderItem.objects.get(
                purchase_order=purchase_order, item_id=email_order_item.order_item_id
            )
            unmatched_order_items[order_item] = email_order_item.quantity_shipped
            logger.info(
                "Found unmatched order item",
                shipment_ids=[s.pk for s in shipments],
                order_item_id=email_order_item.order_item_id,
                quantity_shipped=email_order_item.quantity_shipped,
            )
            continue

        # Check if the quantity matches any shipment line item's shipped quantity
        shipment_line_item = shipment_line_items_dict[email_order_item.order_item_id]
        if shipment_line_item.shipped_quantity != email_order_item.quantity_shipped:
            # Check other shipments for this order item
            other_shipment_line_items = ShipmentLineItem.objects.filter(
                shipment__in=shipments,
                order_item__item_id=email_order_item.order_item_id,
            ).exclude(pk=shipment_line_item.pk)

            quantity_matched = False
            for other_line_item in other_shipment_line_items:
                if (
                    other_line_item.shipped_quantity
                    == email_order_item.quantity_shipped
                ):
                    quantity_matched = True
                    break

            if not quantity_matched:
                order_item = OrderItem.objects.get(
                    purchase_order=purchase_order,
                    item_id=email_order_item.order_item_id,
                )
                unmatched_order_items[order_item] = email_order_item.quantity_shipped
                logger.info(
                    "Found quantity mismatch in all shipment line items",
                    shipment_ids=[s.pk for s in shipments],
                    order_item_id=email_order_item.order_item_id,
                    existing_quantities=[
                        item.shipped_quantity for item in other_shipment_line_items
                    ]
                    + [shipment_line_item.shipped_quantity],
                    new_quantity=email_order_item.quantity_shipped,
                )
                continue

    return unmatched_order_items


@activity.defn
def create_shipment_from_details(params: ShipmentDetails):
    """
    Create a shipment from extracted details.

    Raises:
        ResourceNotFoundError: If PO not found (non-retryable)
        DuplicateResourceError: If exact duplicate shipment exists (non-retryable)
        OperationalError: Database issues (retryable)
    """
    from django.db import OperationalError

    from didero.workflows.errors import DuplicateResourceError, ResourceNotFoundError

    shipment_details = params
    logger.info(
        "Starting shipment creation process",
        po_number=shipment_details.purchase_order_number,
        team_id=shipment_details.team_id,
        carrier=shipment_details.carrier,
        tracking_number=shipment_details.tracking_number,
        shipment_date=shipment_details.shipment_date,
    )

    # Simple PO lookup - DAG workflows don't support auto-creation
    try:
        order = get_purchase_order_from_po_number(
            shipment_details.purchase_order_number, shipment_details.team_id
        )

        if not order:
            logger.error(
                "Shipment Creation: Purchase order not found",
                po_number=shipment_details.purchase_order_number,
                team_id=shipment_details.team_id,
            )
            raise ResourceNotFoundError(
                resource_type="PurchaseOrder",
                resource_id=shipment_details.purchase_order_number,
                team_id=shipment_details.team_id,
            )

    except OperationalError:
        # Database connection errors should be retried by Temporal
        logger.error("Database connection error fetching purchase order")
        raise

    logger.info(
        "Found purchase order",
        po_number=order.po_number,
        order_id=order.pk,
    )

    # Get existing shipments
    try:
        shipments = _get_shipments_from_details(shipment_details, order)
    except OperationalError:
        # Database connection errors should be retried by Temporal
        logger.error("Database connection error fetching existing shipments")
        raise

    if shipments:
        try:
            unmatched_order_items = _get_unmatched_order_items(
                shipments, shipment_details, order
            )
        except OperationalError:
            # Database connection errors should be retried by Temporal
            logger.error("Database connection error checking unmatched items")
            raise

        # If we have unmatched items, we need to create a new shipment
        if unmatched_order_items:
            logger.info(
                "Creating new shipment for unmatched items",
                shipment_ids=[s.pk for s in shipments],
                num_unmatched_items=len(unmatched_order_items),
            )
            try:
                shipment = Shipment.objects.create_shipment(
                    purchase_order=order,
                    carrier=shipment_details.carrier,
                    tracking_number=shipment_details.tracking_number,
                    shipment_date=shipment_details.shipment_date,
                    estimated_delivery_date=shipment_details.estimated_delivery_date,
                    status=ShipmentStatus.SHIPPED.value,
                    line_items=unmatched_order_items,
                )
                logger.info(
                    "Created new shipment for unmatched items",
                    new_shipment_id=shipment.pk,
                    po_number=order.po_number,
                )
            except IntegrityError as e:
                # Handle duplicate shipment creation
                error_msg = str(e).lower()
                if "unique" in error_msg or "duplicate" in error_msg:
                    logger.warning(
                        "Shipment creation failed due to duplicate",
                        po_number=order.po_number,
                        carrier=shipment_details.carrier,
                        tracking_number=shipment_details.tracking_number,
                        error=str(e),
                    )
                    raise DuplicateResourceError(
                        resource_type="Shipment",
                        identifier=f"PO: {order.po_number}, Tracking: {shipment_details.tracking_number}, Date: {shipment_details.shipment_date}",
                        purchase_order_id=str(order.pk),
                    )
                else:
                    # Other integrity errors should be raised
                    raise
            except OperationalError:
                # Database connection errors should be retried by Temporal
                logger.error("Database connection error creating shipment")
                raise
        else:
            logger.info(
                "No unmatched items found, treating as duplicate shipment",
                shipment_ids=[s.pk for s in shipments],
                po_number=order.po_number,
            )
            # Use the first existing shipment for the workflow to continue
            shipment = shipments[0]
            logger.info(
                "Using existing shipment",
                shipment_id=shipment.pk,
                po_number=order.po_number,
            )

    else:
        logger.info(
            "No existing shipments found, creating new shipment",
            po_number=order.po_number,
            carrier=shipment_details.carrier,
            shipment_date=shipment_details.shipment_date,
        )
        # Create new shipment if it doesn't exist
        line_items = {}
        try:
            for email_order_item in shipment_details.order_items:
                order_item = OrderItem.objects.get(
                    purchase_order=order, item_id=email_order_item.order_item_id
                )
                line_items[order_item] = email_order_item.quantity_shipped
                logger.info(
                    "Adding line item to new shipment",
                    order_item_id=order_item.id,
                    quantity_shipped=email_order_item.quantity_shipped,
                )
        except OrderItem.DoesNotExist:
            logger.error(
                "Order item not found",
                po_number=order.po_number,
                item_id=email_order_item.order_item_id,
            )
            raise ResourceNotFoundError(
                resource_type="OrderItem",
                resource_id=email_order_item.order_item_id,
                purchase_order_id=str(order.pk),
            )
        except OperationalError:
            # Database connection errors should be retried by Temporal
            logger.error("Database connection error fetching order items")
            raise

        try:
            shipment = Shipment.objects.create_shipment(
                purchase_order=order,
                carrier=shipment_details.carrier,
                tracking_number=shipment_details.tracking_number,
                shipment_date=shipment_details.shipment_date,
                estimated_delivery_date=shipment_details.estimated_delivery_date,
                status=ShipmentStatus.SHIPPED.value,
                line_items=line_items,
            )
            logger.info(
                "Created new shipment",
                shipment_id=shipment.pk,
                po_number=order.po_number,
                num_line_items=len(line_items),
            )
        except IntegrityError as e:
            # Handle duplicate shipment creation
            error_msg = str(e).lower()
            if "unique" in error_msg or "duplicate" in error_msg:
                logger.warning(
                    "Shipment creation failed due to duplicate",
                    po_number=order.po_number,
                    carrier=shipment_details.carrier,
                    tracking_number=shipment_details.tracking_number,
                    error=str(e),
                )
                raise DuplicateResourceError(
                    resource_type="Shipment",
                    identifier=f"PO: {order.po_number}, Tracking: {shipment_details.tracking_number}, Date: {shipment_details.shipment_date}",
                    purchase_order_id=str(order.pk),
                )
            else:
                # Other integrity errors should be raised
                raise
        except OperationalError:
            # Database connection errors should be retried by Temporal
            logger.error("Database connection error creating shipment")
            raise

    logger.info(
        "Shipment details",
        carrier=shipment_details.carrier,
        tracking_number=shipment_details.tracking_number,
        shipment_date=shipment_details.shipment_date,
        estimated_delivery_date=shipment_details.estimated_delivery_date,
        shipment_id=shipment.pk,
        po_number=order.po_number,
    )

    if not shipment_details.order_items:
        logger.warning(
            "Create Shipment: No order items found in shipment details, skipping shipment line items",
            shipment_details=shipment_details,
        )

    # Leave a comment on the PO with the shipment details
    try:
        order_comment = get_purchase_order_comment_for_shipment(order, shipment_details)
        comment, created = PurchaseOrderComment.objects.get_or_create(
            purchase_order=order,
            comment=order_comment,
            created_by=get_didero_ai_user(team=order.team),
        )
        if created:
            logger.info(
                "Added shipment comment to purchase order",
                po_number=order.po_number,
                shipment_id=shipment.pk,
            )
        else:
            logger.info(
                "Comment already exists for shipment",
                po_number=order.po_number,
                shipment_id=shipment.pk,
                comment_id=comment.pk,
            )
    except OperationalError:
        # Database connection errors should be retried by Temporal
        logger.error("Database connection error creating comment")
        raise

    # Fetch the email from the database
    try:
        email = Communication.objects.get(id=shipment_details.email_pk)
        logger.info(
            "Found original email",
            email_id=email.pk,
            shipment_id=shipment.pk,
        )
    except Communication.DoesNotExist:
        logger.error(
            "Communication not found",
            email_id=shipment_details.email_pk,
        )
        raise ResourceNotFoundError(
            resource_type="Communication",
            resource_id=str(shipment_details.email_pk),
        )
    except OperationalError:
        # Database connection errors should be retried by Temporal
        logger.error("Database connection error fetching email")
        raise

    # Link the original email to the PO
    try:
        link, created = EmailThreadToPurchaseOrderLink.objects.get_or_create(
            email_thread=email.email_thread,
            purchase_order=order,
        )
        if created:
            logger.info(
                "Linked email thread to purchase order",
                email_id=email.pk,
                po_number=order.po_number,
                shipment_id=shipment.pk,
            )
        else:
            logger.info(
                "Email thread already linked to purchase order",
                email_id=email.pk,
                po_number=order.po_number,
                shipment_id=shipment.pk,
                link_id=link.pk,
            )
    except IntegrityError:
        logger.warning(
            "Create Shipment: Email thread to purchase order link already exists, skipping",
            email_id=email.pk,
            email_thread_id=email.email_thread.pk if email.email_thread else None,
            purchase_order_id=order.pk,
        )
    except OperationalError:
        # Database connection errors should be retried by Temporal
        logger.error("Database connection error linking email to PO")
        raise

    # Check if human validation is enabled for this team
    from didero.users.models.user_team_setting_models import TeamSettingEnums
    from didero.users.utils.team_setting_utils import get_team_setting_boolean_value

    # Check if human validation is enabled for this team
    human_validation_enabled = get_team_setting_boolean_value(
        TeamSettingEnums.TEAM_SHIPMENT_HUMAN_VALIDATION_ENABLED.value,
        order.team,
    )

    if human_validation_enabled:
        # Create a task v2 for the ops team to update Netsuite with shipment details
        create_shipment_workflow_completed_task(
            order, shipment, email_id=str(shipment_details.email_pk)
        )
        logger.info(
            "Created human validation task for shipment",
            po_number=order.po_number,
            shipment_id=shipment.pk,
        )
    else:
        # Create a success notification instead of a human validation task
        create_shipment_success_notification(
            order, shipment, email_id=str(shipment_details.email_pk)
        )
        logger.info(
            "Created shipment success notification - bypassing human validation",
            po_number=order.po_number,
            shipment_id=shipment.pk,
        )

        # Check if automatic ERP sync is enabled
        from asgiref.sync import async_to_sync

        from didero.users.models import UserWorkflow
        from didero.workflows.core.activities.erp_sync import sync_erp_purchase_order
        from didero.workflows.core_workflows.shipments.schemas import (
            ShipmentBehaviorConfig,
        )

        try:
            # Get workflow behavior config
            workflow = UserWorkflow.objects.select_related("behavior_config").get(
                team_id=shipment_details.team_id, workflow_type="shipments"
            )

            if workflow.behavior_config:
                config = ShipmentBehaviorConfig.model_validate(
                    workflow.behavior_config.config
                )

                # Check if automatic ERP sync is enabled
                if config.enable_erp_sync and config.erp_sync_mode == "automatic":
                    logger.info(
                        "Triggering automatic ERP sync",
                        shipment_id=shipment.id,
                        po_number=order.po_number,
                    )

                    # Call ERP sync activity
                    sync_result = async_to_sync(sync_erp_purchase_order)(
                        {
                            "shipment_id": shipment.id,
                            "team_id": shipment_details.team_id,
                            "sync_mode": "automatic",
                        }
                    )

                    # Log result but don't fail the workflow
                    if sync_result["success"]:
                        logger.info(
                            "Automatic ERP sync completed",
                            shipment_id=shipment.id,
                            synced_fields=sync_result["synced_fields"],
                        )
                    else:
                        logger.error(
                            "Automatic ERP sync failed",
                            shipment_id=shipment.id,
                            error=sync_result.get("error_message"),
                        )
                        # Error task was already created by the sync activity

        except Exception as e:
            # Log but don't fail the workflow
            logger.exception(
                "Failed to check/execute ERP sync",
                po_number=order.po_number,
                error=str(e),
            )

    logger.info(
        "Completed shipment creation process",
        po_number=order.po_number,
        shipment_id=shipment.pk,
    )


class CreateShipmentFromDetails(WorkflowAction):
    activity = create_shipment_from_details

    async def execute(self):
        try:
            res = await self.execute_activity()
        except Exception as e:
            logger.error(
                "Failed to create shipment from details",
                exception=e,
            )
            await self.set_dag_state(self.id, WorkflowRunState.FAILED)
            return NodeResult(
                next_nodes=[],
                continue_workflow=False,
            )

        await self.set_dag_state(self.id, WorkflowRunState.SUCCESS)
        self.completed = True

        next = [succ for succ in self.successors if not succ.completed]
        for succ in next:
            succ.params = res

        return NodeResult(next_nodes=next)


@activity.defn
def validate_shipment_email(
    params: ParseShipmentParams,
) -> ParseShipmentParams:
    """
    Validate that the email exists before processing shipment.

    Raises:
        ResourceNotFoundError: If email/communication not found (non-retryable)
        OperationalError: Database issues (retryable)
    """
    from django.db import OperationalError

    from didero.workflows.errors import ResourceNotFoundError

    logger.info(
        "Starting validate_shipment_email",
        email_id=params["email_id"],
    )
    email_id = params["email_id"]

    try:
        Communication.objects.get(id=email_id)
    except Communication.DoesNotExist:
        logger.error(
            "Parse Shipment Details: Email not found, failing the Shipment Workflow",
            email_id=email_id,
        )
        raise ResourceNotFoundError(
            resource_type="Communication",
            resource_id=email_id,
            message="Email not found, failing the Shipment Workflow",
        )
    except OperationalError:
        # Database connection errors should be retried by Temporal
        logger.error("Database connection error validating email")
        raise

    logger.info(
        "Shipment email validated",
        email_id=email_id,
    )
    return params


class ValidateShipmentEmail(WorkflowAction):
    activity = validate_shipment_email

    async def execute(self):
        try:
            res = await self.execute_activity()
        except Exception as e:
            logger.error(
                "Failed to validate shipment email",
                exception=e,
            )
            await self.set_dag_state(self.id, WorkflowRunState.FAILED)
            return NodeResult(
                next_nodes=[],
                continue_workflow=False,
            )

        await self.set_dag_state(self.id, WorkflowRunState.SUCCESS)
        self.completed = True

        next = [succ for succ in self.successors if not succ.completed]
        for succ in next:
            succ.params = res

        return NodeResult(
            next_nodes=next,
        )


# TODO: implement this once we have a working Netsuite integration.
@activity.defn
def submit_shipment_to_netsuite(params):
    # shipment_details = params["shipment_details"]
    pass


class UploadShipmentToNetsuite(WorkflowAction):
    activity = submit_shipment_to_netsuite


# Conditional node to check notification type
@activity.defn
def check_notification_type(params: dict):
    """Determines if this is a pickup or shipment notification

    Args:
        params: Dictionary containing notification_type and email_id

    Returns:
        ConditionActivityResult with condition_met=True for pickup, False for shipment
    """
    notification_type: str = params.get("notification_type", "shipped")
    is_pickup: bool = notification_type == "pickup"

    logger.info(
        "Checking notification type",
        notification_type=notification_type,
        is_pickup=is_pickup,
        email_id=params.get("email_id"),
    )

    return ConditionActivityResult(
        condition_met=is_pickup,
        message=f"Notification type: {notification_type}",
        continue_workflow=True,
    )


class NotificationTypeRouter(WorkflowCondition):
    """Routes to pickup or shipment path based on notification type"""

    activity = check_notification_type


# Base class for shared notification logic
class BaseNotificationHandler:
    """Base class for shipment and pickup notification handlers"""

    @staticmethod
    def link_email_to_po(email: Communication, order: PurchaseOrder):
        """Shared logic for linking email threads to POs"""
        try:
            link, created = EmailThreadToPurchaseOrderLink.objects.get_or_create(
                email_thread=email.email_thread,
                purchase_order=order,
            )
            if created:
                logger.info(
                    "Linked email thread to purchase order",
                    email_id=email.pk,
                    po_number=order.po_number,
                )
            else:
                logger.info(
                    "Email thread already linked to purchase order",
                    email_id=email.pk,
                    po_number=order.po_number,
                    link_id=link.pk,
                )
        except IntegrityError:
            logger.warning(
                "Email thread to purchase order link already exists, skipping",
                email_id=email.pk,
                email_thread_id=email.email_thread.pk if email.email_thread else None,
                purchase_order_id=order.pk,
            )

    @staticmethod
    def add_po_comment(order: PurchaseOrder, message: str, user: Optional[User]):
        """Shared logic for adding comments to POs"""
        comment, created = PurchaseOrderComment.objects.get_or_create(
            purchase_order=order,
            comment=message,
            created_by=user,
        )
        if created:
            logger.info(
                "Added comment to purchase order",
                po_number=order.po_number,
            )
        else:
            logger.info(
                "Comment already exists on purchase order",
                po_number=order.po_number,
                comment_id=comment.pk,
            )


def create_pickup_success_notification(
    order: PurchaseOrder,
    pickup_info: GetPickupInfoResponse,
    email_id: Optional[str] = None,
):
    """
    Create a notification task for successful pickup ready status.

    Args:
        order: The PurchaseOrder instance
        pickup_info: The pickup information extracted from email
        email_id: ID of the email that triggered the workflow
    """
    user = get_didero_ai_user(team=order.team)
    if not user:
        logger.warning(
            "No Didero AI user found for team, not making pickup notification",
            team_id=order.team.id,
            order_id=order.id,
        )
        return

    # Prepare context parameters
    po_details_context_params: ContextPanelDefinition = {
        "panel_type": TaskContextPanelType.PO_DETAILS,
        "param_values": {"purchaseOrderId": str(order.pk)},
    }
    communication_context_params = None

    # Add communication ID if available
    if email_id:
        communication_context_params: ContextPanelDefinition = {
            "panel_type": TaskContextPanelType.COMMUNICATION_DETAILS,
            "param_values": {"communicationId": email_id},
        }

    context_panels = [po_details_context_params]
    if communication_context_params:
        context_panels.append(communication_context_params)

    # Import serialization utils
    from didero.workflows.serialization_utils import serialize_purchase_order

    po_data = serialize_purchase_order(order)

    # Prepare task parameters - Include location but not detailed instructions
    task_params = {
        "po_number": order.po_number,
        "supplier_name": order.supplier.name if order.supplier else "Unknown",
        "pickup_location": pickup_info.pickup_location or "",  # Include location
        "pickup_instructions": "",  # Don't include detailed instructions in task
        "po_data": po_data,
    }

    return create_task_v2(
        task_type=TaskTypeName.PICKUP_WORKFLOW_SUCCESS_NOTIFICATION,
        user=user,
        model_type=ContentType.objects.get_for_model(PurchaseOrder),
        model_id=str(order.pk),
        task_type_params=task_params,
        context_panels=context_panels,
        # No actions needed for notification - just a simple dismiss button will be shown
    )


# Pickup notification handler
@activity.defn
def process_pickup_notification(params: dict):
    """Process ready for pickup notification

    Args:
        params: Dictionary containing email_id

    Raises:
        ResourceNotFoundError: If email/PO not found (non-retryable)
        ValidationError: If pickup info extraction fails (non-retryable)
        OperationalError: Database issues (retryable)
    """
    from django.db import OperationalError

    from didero.workflows.errors import ResourceNotFoundError, ValidationError

    email_id = params["email_id"]

    try:
        email = Communication.objects.select_related(
            "team", "supplier", "email_thread"
        ).get(id=email_id)
    except Communication.DoesNotExist:
        logger.error(
            "Process Pickup: Email not found, failing the Pickup Workflow",
            email_id=email_id,
        )
        raise ResourceNotFoundError(
            resource_type="Communication",
            resource_id=email_id,
            message="Email not found, failing the Pickup Workflow",
        )
    except OperationalError:
        # Database connection errors should be retried by Temporal
        logger.error("Database connection error fetching email")
        raise

    # Extract pickup information from email
    pickup_info = get_pickup_info_from_email(email)

    if not pickup_info:
        logger.error(
            "Failed to extract pickup information from email",
            email_id=email_id,
        )
        raise ValidationError(
            field="pickup_info",
            value=None,
            message=f"Could not extract pickup information from email {email_id}. Email may not contain valid pickup details or PO number.",
            context={"email_id": email_id},
        )

    # Simple PO lookup - DAG workflows don't support auto-creation
    try:
        order = get_purchase_order_from_po_number(
            pickup_info.purchase_order_number, email.team.id
        )

        if not order:
            logger.error(
                "Pickup Notification: Purchase order not found",
                po_number=pickup_info.purchase_order_number,
                email_id=email_id,
                team_id=email.team.id,
            )
            raise ResourceNotFoundError(
                resource_type="PurchaseOrder",
                resource_id=pickup_info.purchase_order_number,
                team_id=email.team.id,
                message=f"Purchase order {pickup_info.purchase_order_number} not found",
            )

    except OperationalError:
        # Database connection errors should be retried by Temporal
        logger.error("Database connection error fetching purchase order")
        raise

    # Update PO status to READY_FOR_PICKUP
    order.order_status = PurchaseOrderStatus.READY_FOR_PICKUP.value
    order.save()

    logger.info(
        "Updated purchase order status to READY_FOR_PICKUP",
        po_number=order.po_number,
        order_id=order.id,
    )

    # Link email thread to PO
    BaseNotificationHandler.link_email_to_po(email, order)

    # Add pickup comment
    user = get_didero_ai_user(team=order.team)
    comment = "Order is ready for pickup."
    if pickup_info.pickup_location:
        comment += f" Location: {pickup_info.pickup_location}."

    BaseNotificationHandler.add_po_comment(order, comment, user)

    # Create notification task
    create_pickup_success_notification(order, pickup_info, str(email.pk))

    logger.info(
        "Successfully processed pickup notification",
        po_number=order.po_number,
        email_id=email_id,
    )


class ProcessPickupNotification(WorkflowAction):
    activity = process_pickup_notification
