"""Tests for ERP-related PO creation activities."""

import json
from unittest.mock import AsyncMock, MagicMock, patch
from django.test import TestCase
from decimal import Decimal

from didero.ai.purchase_order.po_extraction import PurchaseOrderDetails
from didero.ai.po_extraction.po_extraction import POExtractionAddressInformation
from didero.workflows.core.nodes.purchase_orders.po_creation.activities import (
    extract_po_from_erp,
)
from didero.workflows.errors import ValidationError, ResourceNotFoundError


class TestExtractPOFromERPActivity(TestCase):
    """Test the extract_po_from_erp activity."""

    def setUp(self):
        """Set up test data."""
        self.sample_params = {
            "email_id": "123",
            "team_id": 4,  # IonQ team ID
            "po_number": "PO431",
            "po_details": {
                "po_number": "PO431",
                "supplier_name": "Some Supplier",
                "requested_action": "PROCESS",
            },
        }

        self.sample_netsuite_data = {
            "metadata": {
                "extraction_timestamp": "2025-01-22T10:00:00Z",
                "extractor_version": "v4",
            },
            "header": {
                "tranId": "PO431",
                "memo": "Test memo",
                "status": "Pending Receipt",
                "total": "1220.57",
                "currencyName": "USD",
                "tranDate": "2022-10-03T21:00:00.000-07:00",
            },
            "vendor": {
                "internalId": "550",
                "name": "V10072 ThorLabs",
            },
            "addresses": {
                "billing": {
                    "country": "_unitedStates",
                    "addressee": "ThorLabs Inc",
                    "addr1": "56 Sparta Ave",
                    "city": "Newton",
                    "state": "NJ",
                    "zip": "07860",
                },
                "shipping": {
                    "country": "_unitedStates",
                    "addressee": "IonQ Inc",
                    "addrText": "IonQ Inc\r\n4505 Campus Dr\r\nCollege Park MD 20740\r\nUnited States",
                },
            },
            "line_items": [
                {
                    "item_reference": {
                        "internalId": "2761",
                        "name": "502-00097",
                    },
                    "fields": {
                        "description": "Compact Power and Energy Meter Console",
                        "vendorName": "PM100D",
                        "quantity": "1.0",
                        "rate": "1220.57",
                        "amount": "1220.57",
                    },
                    "custom_fields": {},
                }
            ],
            "custom_fields": {
                "header": {},
                "line_items": [],
            },
        }

        self.sample_mapped_po_details = PurchaseOrderDetails(
            po_number="PO431",
            supplier_name="V10072 ThorLabs",
            currency="USD",
            notes="Test memo",
            supplier_address=POExtractionAddressInformation(
                line1="56 Sparta Ave",
                line2="",
                city="Newton",
                state="NJ",
                zip="07860",
                country="US",
            ),
            shipping_address=POExtractionAddressInformation(
                line1="4505 Campus Dr",
                line2="",
                city="College Park",
                state="MD",
                zip="20740",
                country="US",
            ),
            items=[
                {
                    "item_number": "502-00097",
                    "item_description": "Compact Power and Energy Meter Console",
                    "quantity": 1.0,
                    "unit_price": "1220.57",
                    "total_price": "1220.57",
                    "unit_of_measure": "Each",
                    "requested_date": None,
                }
            ],
        )

    @patch(
        "didero.workflows.core.nodes.purchase_orders.po_creation.activities.sync_to_async"
    )
    @patch(
        "didero.integrations.erp.mappers.customers.ionq.po_mapper.IonQNetSuitePOMapper"
    )
    @patch("didero.integrations.erp.NetSuiteClient")
    @patch("didero.integrations.erp.credentials.SimpleEnvCredentialProvider")
    async def test_extract_po_from_erp_success(
        self, mock_provider, mock_client, mock_mapper, mock_sync_to_async
    ):
        """Test successful ERP extraction."""
        # Setup mocks
        mock_erp_config = MagicMock()
        mock_erp_config.erp_type = "netsuite"
        mock_team = MagicMock()

        # Create async mocks for the database calls
        async def mock_team_get(*args, **kwargs):
            return mock_team

        async def mock_erp_config_get(*args, **kwargs):
            return mock_erp_config

        async def mock_netsuite_fetch(*args, **kwargs):
            return self.sample_netsuite_data

        # Mock the sync_to_async wrapper to return async functions
        mock_sync_to_async.side_effect = [
            mock_team_get,  # Team.objects.get
            mock_erp_config_get,  # ERPIntegrationConfig.objects.get
            mock_netsuite_fetch,  # netsuite_client.get_complete_purchase_order
        ]

        # Mock credentials and NetSuite client
        mock_creds = MagicMock()
        mock_provider.return_value.get_credentials.return_value = mock_creds
        mock_netsuite_client = MagicMock()
        mock_client.return_value = mock_netsuite_client

        # Mock mapper
        mock_mapper_instance = MagicMock()
        mock_mapper.return_value = mock_mapper_instance
        mock_mapper_instance.map_to_purchase_order_details.return_value = (
            self.sample_mapped_po_details
        )

        # Mock validation functions
        with patch(
            "didero.ai.purchase_order.po_extraction.is_valid_purchase_order",
            return_value=True,
        ), patch(
            "didero.ai.purchase_order.po_extraction.is_po_data_sufficient",
            return_value=True,
        ):
            # Execute the activity
            result = await extract_po_from_erp(self.sample_params)

            # Assertions
            self.assertIsNotNone(result)
            self.assertEqual(result["po_number"], "PO431")
            self.assertEqual(result["team_id"], 4)
            self.assertEqual(result["email_id"], "123")
            self.assertTrue(result["is_data_sufficient"])
            self.assertIsInstance(result["po_details"], PurchaseOrderDetails)
            self.assertEqual(result["po_details"].supplier_name, "V10072 ThorLabs")

            # Verify the NetSuite client was called correctly
            mock_provider.return_value.get_credentials.assert_called_once_with(
                "4", "netsuite"
            )
            mock_client.assert_called_once_with(credentials=mock_creds)

            # Verify mapper was called correctly
            mock_mapper.assert_called_once()
            mock_mapper_instance.map_to_purchase_order_details.assert_called_once_with(
                self.sample_netsuite_data
            )

    @patch(
        "didero.workflows.core.nodes.purchase_orders.po_creation.activities.sync_to_async"
    )
    async def test_extract_po_from_erp_no_config(self, mock_sync_to_async):
        """Test ERP extraction when no ERP config exists."""
        from didero.integrations.models import ERPIntegrationConfig

        # Create async mocks
        async def mock_team_get(*args, **kwargs):
            return MagicMock()

        async def mock_erp_config_get(*args, **kwargs):
            raise ERPIntegrationConfig.DoesNotExist()

        mock_sync_to_async.side_effect = [
            mock_team_get,  # Team.objects.get succeeds
            mock_erp_config_get,  # ERPIntegrationConfig.objects.get raises
        ]

        with self.assertRaises(ValidationError) as cm:
            await extract_po_from_erp(self.sample_params)

        self.assertIn("No ERP configuration found for team", str(cm.exception))
        self.assertIn("fallback_to_ai", cm.exception.details)

    @patch(
        "didero.workflows.core.nodes.purchase_orders.po_creation.activities.sync_to_async"
    )
    async def test_extract_po_from_erp_team_not_found(self, mock_sync_to_async):
        """Test ERP extraction when team does not exist."""
        from didero.users.models import Team

        # Create async mock that raises exception
        async def mock_team_get(*args, **kwargs):
            raise Team.DoesNotExist()

        mock_sync_to_async.side_effect = [
            mock_team_get,  # Team.objects.get raises
        ]

        with self.assertRaises(ResourceNotFoundError) as cm:
            await extract_po_from_erp(self.sample_params)

        self.assertIn("Team", cm.exception.resource_type)

    @patch(
        "didero.workflows.core.nodes.purchase_orders.po_creation.activities.sync_to_async"
    )
    async def test_extract_po_from_erp_unsupported_erp_type(self, mock_sync_to_async):
        """Test ERP extraction with unsupported ERP type."""
        # Setup mocks
        mock_erp_config = MagicMock()
        mock_erp_config.erp_type = "sap"  # Unsupported type
        mock_team = MagicMock()

        # Create async mocks
        async def mock_team_get(*args, **kwargs):
            return mock_team

        async def mock_erp_config_get(*args, **kwargs):
            return mock_erp_config

        mock_sync_to_async.side_effect = [
            mock_team_get,  # Team.objects.get
            mock_erp_config_get,  # ERPIntegrationConfig.objects.get
        ]

        with self.assertRaises(ValidationError) as cm:
            await extract_po_from_erp(self.sample_params)

        self.assertIn("Unsupported ERP type: sap", str(cm.exception))
        self.assertIn("fallback_to_ai", cm.exception.details)

    @patch(
        "didero.workflows.core.nodes.purchase_orders.po_creation.activities.sync_to_async"
    )
    @patch("didero.integrations.erp.credentials.SimpleEnvCredentialProvider")
    async def test_extract_po_from_erp_credential_error(
        self, mock_provider, mock_sync_to_async
    ):
        """Test ERP extraction when credentials fail."""
        # Setup mocks
        mock_erp_config = MagicMock()
        mock_erp_config.erp_type = "netsuite"
        mock_team = MagicMock()

        # Create async mocks
        async def mock_team_get(*args, **kwargs):
            return mock_team

        async def mock_erp_config_get(*args, **kwargs):
            return mock_erp_config

        mock_sync_to_async.side_effect = [
            mock_team_get,  # Team.objects.get
            mock_erp_config_get,  # ERPIntegrationConfig.objects.get
        ]

        # Mock credential provider to raise exception
        mock_provider.return_value.get_credentials.side_effect = Exception(
            "Credential error"
        )

        with self.assertRaises(ValidationError) as cm:
            await extract_po_from_erp(self.sample_params)

        self.assertIn("Failed to initialize NetSuite client", str(cm.exception))
        self.assertIn("fallback_to_ai", cm.exception.details)

    @patch(
        "didero.workflows.core.nodes.purchase_orders.po_creation.activities.sync_to_async"
    )
    @patch("didero.integrations.erp.NetSuiteClient")
    @patch("didero.integrations.erp.credentials.SimpleEnvCredentialProvider")
    async def test_extract_po_from_erp_netsuite_fetch_error(
        self, mock_provider, mock_client, mock_sync_to_async
    ):
        """Test ERP extraction when NetSuite fetch fails."""
        # Setup mocks
        mock_erp_config = MagicMock()
        mock_erp_config.erp_type = "netsuite"
        mock_team = MagicMock()

        # Create async mocks
        async def mock_team_get(*args, **kwargs):
            return mock_team

        async def mock_erp_config_get(*args, **kwargs):
            return mock_erp_config

        async def mock_netsuite_fetch(*args, **kwargs):
            raise Exception("NetSuite fetch error")

        mock_sync_to_async.side_effect = [
            mock_team_get,  # Team.objects.get
            mock_erp_config_get,  # ERPIntegrationConfig.objects.get
            mock_netsuite_fetch,  # netsuite_client.get_complete_purchase_order raises
        ]

        # Mock credentials and NetSuite client
        mock_creds = MagicMock()
        mock_provider.return_value.get_credentials.return_value = mock_creds
        mock_netsuite_client = MagicMock()
        mock_client.return_value = mock_netsuite_client

        with self.assertRaises(ValidationError) as cm:
            await extract_po_from_erp(self.sample_params)

        self.assertIn("Failed to fetch PO from NetSuite", str(cm.exception))
        self.assertIn("fallback_to_ai", cm.exception.details)

    @patch(
        "didero.workflows.core.nodes.purchase_orders.po_creation.activities.sync_to_async"
    )
    @patch(
        "didero.integrations.erp.mappers.customers.ionq.po_mapper.IonQNetSuitePOMapper"
    )
    @patch("didero.integrations.erp.NetSuiteClient")
    @patch("didero.integrations.erp.credentials.SimpleEnvCredentialProvider")
    async def test_extract_po_from_erp_mapping_error(
        self, mock_provider, mock_client, mock_mapper, mock_sync_to_async
    ):
        """Test ERP extraction when mapping fails."""
        # Setup mocks
        mock_erp_config = MagicMock()
        mock_erp_config.erp_type = "netsuite"
        mock_team = MagicMock()

        # Create async mocks
        async def mock_team_get(*args, **kwargs):
            return mock_team

        async def mock_erp_config_get(*args, **kwargs):
            return mock_erp_config

        async def mock_netsuite_fetch(*args, **kwargs):
            return self.sample_netsuite_data

        mock_sync_to_async.side_effect = [
            mock_team_get,  # Team.objects.get
            mock_erp_config_get,  # ERPIntegrationConfig.objects.get
            mock_netsuite_fetch,  # netsuite_client.get_complete_purchase_order
        ]

        # Mock credentials and NetSuite client
        mock_creds = MagicMock()
        mock_provider.return_value.get_credentials.return_value = mock_creds
        mock_netsuite_client = MagicMock()
        mock_client.return_value = mock_netsuite_client

        # Mock mapper to raise exception
        mock_mapper_instance = MagicMock()
        mock_mapper.return_value = mock_mapper_instance
        mock_mapper_instance.map_to_purchase_order_details.side_effect = Exception(
            "Mapping error"
        )

        with self.assertRaises(ValidationError) as cm:
            await extract_po_from_erp(self.sample_params)

        self.assertIn("Failed to map NetSuite data", str(cm.exception))
        self.assertIn("fallback_to_ai", cm.exception.details)

    @patch(
        "didero.workflows.core.nodes.purchase_orders.po_creation.activities.sync_to_async"
    )
    @patch("didero.integrations.erp.NetSuiteClient")
    @patch("didero.integrations.erp.credentials.SimpleEnvCredentialProvider")
    async def test_extract_po_from_erp_unsupported_team(
        self, mock_provider, mock_client, mock_sync_to_async
    ):
        """Test ERP extraction for team without mapper."""
        # Setup mocks for non-IonQ team
        params_non_ionq = self.sample_params.copy()
        params_non_ionq["team_id"] = 999  # Non-IonQ team ID

        mock_erp_config = MagicMock()
        mock_erp_config.erp_type = "netsuite"
        mock_team = MagicMock()

        # Create async mocks
        async def mock_team_get(*args, **kwargs):
            return mock_team

        async def mock_erp_config_get(*args, **kwargs):
            return mock_erp_config

        mock_sync_to_async.side_effect = [
            mock_team_get,  # Team.objects.get
            mock_erp_config_get,  # ERPIntegrationConfig.objects.get
        ]

        # Mock credentials and NetSuite client to reach the team check
        mock_creds = MagicMock()
        mock_provider.return_value.get_credentials.return_value = mock_creds
        mock_netsuite_client = MagicMock()
        mock_client.return_value = mock_netsuite_client

        with self.assertRaises(ValidationError) as cm:
            await extract_po_from_erp(params_non_ionq)

        self.assertIn("No ERP mapper configured for team 999", str(cm.exception))
        self.assertIn("fallback_to_ai", cm.exception.details)

    @patch(
        "didero.workflows.core.nodes.purchase_orders.po_creation.activities.sync_to_async"
    )
    @patch(
        "didero.integrations.erp.mappers.customers.ionq.po_mapper.IonQNetSuitePOMapper"
    )
    @patch("didero.integrations.erp.NetSuiteClient")
    @patch("didero.integrations.erp.credentials.SimpleEnvCredentialProvider")
    async def test_extract_po_from_erp_validation_failure(
        self, mock_provider, mock_client, mock_mapper, mock_sync_to_async
    ):
        """Test ERP extraction when PO validation fails."""
        # Setup mocks
        mock_erp_config = MagicMock()
        mock_erp_config.erp_type = "netsuite"
        mock_team = MagicMock()

        # Create async mocks
        async def mock_team_get(*args, **kwargs):
            return mock_team

        async def mock_erp_config_get(*args, **kwargs):
            return mock_erp_config

        async def mock_netsuite_fetch(*args, **kwargs):
            return self.sample_netsuite_data

        mock_sync_to_async.side_effect = [
            mock_team_get,  # Team.objects.get
            mock_erp_config_get,  # ERPIntegrationConfig.objects.get
            mock_netsuite_fetch,  # netsuite_client.get_complete_purchase_order
        ]

        # Mock credentials and NetSuite client
        mock_creds = MagicMock()
        mock_provider.return_value.get_credentials.return_value = mock_creds
        mock_netsuite_client = MagicMock()
        mock_client.return_value = mock_netsuite_client

        # Mock mapper
        mock_mapper_instance = MagicMock()
        mock_mapper.return_value = mock_mapper_instance
        mock_mapper_instance.map_to_purchase_order_details.return_value = (
            self.sample_mapped_po_details
        )

        # Mock validation to fail
        with patch(
            "didero.ai.purchase_order.po_extraction.is_valid_purchase_order",
            return_value=False,  # Validation fails
        ), patch(
            "didero.ai.purchase_order.po_extraction.is_po_data_sufficient",
            return_value=True,
        ):
            with self.assertRaises(ValidationError) as cm:
                await extract_po_from_erp(self.sample_params)

            self.assertIn("ERP-extracted PO failed validation", str(cm.exception))
            self.assertIn("fallback_to_ai", cm.exception.details)
