import asyncio
import uuid
from argparse import Argument<PERSON>arser
from datetime import datetime, timedelta

import structlog
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction

from didero.ai.invoice.extraction import ai_extract_invoice_details
from didero.suppliers.models import Communication
from didero.users.models.team_models import Team
from didero.workflows.core_workflows.invoice_processing.workflow import (
    InvoiceProcessingParams,
    InvoiceProcessingWorkflow,
)
from didero.workflows.models import UserWorkflow
from didero.workflows.queue_config import get_queue_for_workflow_type
from didero.workflows.schemas import WorkflowTrigger, WorkflowType
from didero.workflows.utils import get_temporal_client

logger = structlog.get_logger(__name__)


class Command(BaseCommand):
    help = "Trigger invoice processing workflow for a specific PO number"

    def add_arguments(self, parser: ArgumentParser) -> None:
        parser.add_argument(
            "--po-number",
            type=str,
            required=True,
            help="PO number to match invoices against",
        )
        parser.add_argument(
            "--team-id",
            type=int,
            required=True,
            help="Team ID to process invoice for",
        )
        parser.add_argument(
            "--email-id",
            type=str,
            help="Optional email ID to process (creates fake email if not provided)",
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be processed without actually running the workflow",
        )
        parser.add_argument(
            "--validate-extraction",
            action="store_true",
            help="Validate that invoice extraction returns complete data before processing",
        )
        parser.add_argument(
            "--skip-validation",
            action="store_true",
            help="Skip validation and process even if extraction is incomplete (for testing)",
        )

    def handle(self, **options: dict[str, str | int | bool | None]) -> None:
        po_number_raw = options["po_number"]
        team_id_raw = options["team_id"]

        if not isinstance(po_number_raw, str):
            raise CommandError("po_number must be a string")
        if not isinstance(team_id_raw, int):
            raise CommandError("team_id must be an integer")

        po_number = po_number_raw
        team_id = team_id_raw
        email_id = options.get("email_id")
        if email_id is not None:
            email_id = str(email_id)
        dry_run = bool(options.get("dry_run", False))

        try:
            # Validate team exists
            team = Team.objects.get(id=team_id)
            self.stdout.write(f"Found team: {team.name} (ID: {team_id})")

            # Handle email
            if email_id:
                try:
                    email = Communication.objects.get(pk=email_id, team_id=team_id)
                    self.stdout.write(f"Using email: {email.pk}")
                except Communication.DoesNotExist:
                    raise CommandError(
                        f"Email with ID '{email_id}' not found for team {team_id}"
                    )
            else:
                # Create a mock email for testing
                email = self._create_mock_email(team_id, po_number)
                self.stdout.write(f"Created mock email: {email.pk}")

            # Find invoice processing workflow for the team
            try:
                workflow = UserWorkflow.objects.get(
                    workflow_type=WorkflowType.INVOICE_PROCESSING,
                    trigger=WorkflowTrigger.ON_INVOICE_EMAIL_RECEIVED,
                    team=team,
                )
                self.stdout.write(f"Found invoice processing workflow: {workflow.pk}")
            except UserWorkflow.DoesNotExist:
                raise CommandError(
                    f"No invoice processing workflow found for team {team_id}. "
                    "Please set up the workflow first."
                )

            if dry_run:
                self.stdout.write(
                    self.style.WARNING(
                        "DRY RUN - Would trigger invoice processing with:"
                    )
                )
                self.stdout.write(f"  - PO Number: {po_number}")
                self.stdout.write(f"  - Team ID: {team_id}")
                self.stdout.write(f"  - Email ID: {email.pk}")
                self.stdout.write(f"  - Workflow ID: {workflow.pk}")
                return

            # Validate extraction if requested
            validate_extraction = bool(options.get("validate_extraction", False))
            skip_validation = bool(options.get("skip_validation", False))

            if validate_extraction and not skip_validation:
                self.stdout.write("Validating invoice extraction...")
                validation_result = self._validate_invoice_extraction(email)

                if not validation_result["is_valid"]:
                    self.stdout.write(
                        self.style.ERROR(
                            f"Invoice extraction validation failed: {validation_result['message']}"
                        )
                    )
                    self.stdout.write("\nExtracted data:")
                    for field, value in validation_result["extracted_data"].items():
                        self.stdout.write(f"  - {field}: {value}")

                    self.stdout.write(
                        self.style.WARNING(
                            "\nUse --skip-validation to process anyway (for testing)"
                        )
                    )
                    return
                else:
                    self.stdout.write(
                        self.style.SUCCESS("Invoice extraction validation passed!")
                    )
                    self.stdout.write("\nExtracted data:")
                    for field, value in validation_result["extracted_data"].items():
                        self.stdout.write(f"  - {field}: {value}")

            # Run the workflow
            asyncio.run(self._run_invoice_workflow(workflow, email, team))

        except Exception as e:
            raise CommandError(f"Failed to trigger invoice processing: {str(e)}")

    def _validate_invoice_extraction(self, email: Communication) -> dict:
        """
        Validate that the invoice extraction returns complete data.

        Returns:
            dict with 'is_valid', 'message', and 'extracted_data'
        """
        try:
            # Extract invoice details using AI
            invoice = ai_extract_invoice_details(email)

            if not invoice:
                return {
                    "is_valid": False,
                    "message": "AI extraction returned no invoice data",
                    "extracted_data": {},
                }

            # Check required fields
            missing_fields = []
            extracted_data = {}

            # Core identifiers
            if not invoice.po_number:
                missing_fields.append("po_number")
            extracted_data["po_number"] = invoice.po_number or "MISSING"

            if not invoice.invoice_number:
                missing_fields.append("invoice_number")
            extracted_data["invoice_number"] = invoice.invoice_number or "MISSING"

            # Line items validation
            if not invoice.order_items or len(invoice.order_items) == 0:
                missing_fields.append("order_items (no line items found)")
            else:
                extracted_data["line_items_count"] = len(invoice.order_items)
                # Check if line items have required data
                for idx, item in enumerate(invoice.order_items):
                    if not item.item_description:
                        missing_fields.append(f"line_item_{idx}_description")
                    if not item.quantity or item.quantity <= 0:
                        missing_fields.append(f"line_item_{idx}_quantity")
                    if not item.unit_price:
                        missing_fields.append(f"line_item_{idx}_unit_price")

            # Financial totals
            if not invoice.total_amount:
                missing_fields.append("total_amount")
            extracted_data["total_amount"] = invoice.total_amount or "MISSING"

            if not invoice.subtotal:
                missing_fields.append("subtotal")
            extracted_data["subtotal"] = invoice.subtotal or "MISSING"

            # Dates
            if not invoice.invoice_date:
                missing_fields.append("invoice_date")
            extracted_data["invoice_date"] = invoice.invoice_date or "MISSING"

            if not invoice.due_date:
                missing_fields.append("due_date")
            extracted_data["due_date"] = invoice.due_date or "MISSING"

            # Billing address
            if not invoice.billing_address:
                missing_fields.append("billing_address")
            else:
                if not invoice.billing_address.line_1:
                    missing_fields.append("billing_address.line_1")
                if not invoice.billing_address.city:
                    missing_fields.append("billing_address.city")
                if not invoice.billing_address.country:
                    missing_fields.append("billing_address.country")

            # Additional extracted data for info
            extracted_data["tax_amount"] = invoice.tax_amount or "None"
            extracted_data["shipping_charges"] = (
                invoice.shipping_or_freight_charges or "None"
            )
            extracted_data["payment_terms"] = invoice.payment_terms or "None"

            if missing_fields:
                return {
                    "is_valid": False,
                    "message": f"Missing required fields: {', '.join(missing_fields)}",
                    "extracted_data": extracted_data,
                }

            return {
                "is_valid": True,
                "message": "All required fields present",
                "extracted_data": extracted_data,
            }

        except Exception as e:
            return {
                "is_valid": False,
                "message": f"Exception during validation: {str(e)}",
                "extracted_data": {},
            }

    def _create_mock_email(self, team_id: int, po_number: str) -> Communication:
        """Create a mock email for testing purposes"""
        team = Team.objects.get(id=team_id)

        # Create a more comprehensive mock email with proper invoice content
        mock_subject = f"Invoice for PO {po_number}"
        mock_body = f"""
        Dear Team,
        
        Please find attached the invoice for Purchase Order {po_number}.
        
        INVOICE
        
        Invoice Number: INV-{po_number}-001
        Invoice Date: {datetime.now().strftime('%Y-%m-%d')}
        Due Date: {(datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')}
        
        Bill To:
        Your Company Name
        123 Main Street
        Suite 100
        New York, NY 10001
        United States
        
        Purchase Order Number: {po_number}
        
        LINE ITEMS:
        
        Item 1:
        Description: Widget A - Premium Quality
        Quantity: 10
        Unit Price: $50.00
        Total: $500.00
        
        Item 2:
        Description: Widget B - Standard Quality
        Quantity: 20
        Unit Price: $25.00
        Total: $500.00
        
        Subtotal: $1,000.00
        Tax (8.5%): $85.00
        Shipping: $15.00
        
        TOTAL AMOUNT DUE: $1,100.00
        
        Payment Terms: Net 30
        
        Thank you for your business!
        
        Best regards,
        Supplier
        """

        # We need a supplier to create a Communication
        # Try to find any existing supplier or create a mock one
        from didero.suppliers.models import Supplier

        supplier, _ = Supplier.objects.get_or_create(
            name="Mock Supplier for Invoice Testing",
            team=team,
            defaults={
                "website_url": "https://example.com",
            },
        )

        with transaction.atomic():
            email = Communication.objects.create(
                team=team,
                supplier=supplier,
                email_subject=mock_subject,
                email_content=mock_body,
                email_from="<EMAIL>",
                email_message_id=f"<test-invoice-{uuid.uuid4()}@example.com>",
                comm_type=Communication.TYPE_EMAIL,
                direction=Communication.DIRECTION_INCOMING,
            )

        logger.info(f"Created mock email {email.pk} for PO {po_number}")
        return email

    async def _run_invoice_workflow(
        self, workflow: UserWorkflow, email: Communication, team: Team
    ) -> None:
        """Run the invoice processing workflow"""
        workflow_run = None
        try:
            # Generate temporal ID first
            workflow_uuid = uuid.uuid4()
            temporal_id = f"invoice_processing_{workflow_uuid}"

            # Create workflow run record - note: WorkflowRun model needs snapshot, not workflow
            # For core workflows, we don't have snapshots, so we'll skip the run record for now
            # and just start the workflow directly

            # Prepare workflow parameters
            params = InvoiceProcessingParams(
                email_id=str(email.pk),
                team_id=str(team.pk),
                workflow_id=str(workflow.pk),
            )

            self.stdout.write("Starting invoice processing workflow...")
            self.stdout.write(f"Temporal ID: {temporal_id}")
            self.stdout.write(f"Workflow UUID: {workflow_uuid}")

            # Start the workflow
            client = await get_temporal_client()

            # Use the proper queue configuration
            task_queue = get_queue_for_workflow_type(
                WorkflowType.INVOICE_PROCESSING.value
            )
            self.stdout.write(f"Using task queue: {task_queue}")

            await client.start_workflow(
                InvoiceProcessingWorkflow.run,
                args=[str(workflow.pk), params],
                id=temporal_id,
                task_queue=task_queue,
            )

            self.stdout.write(
                self.style.SUCCESS(
                    "Successfully started invoice processing workflow for PO matching!"
                )
            )
            self.stdout.write(f"Monitor progress with temporal ID: {temporal_id}")

        except Exception as e:
            logger.error(f"Failed to start workflow: {str(e)}", exc_info=True)
            if workflow_run is not None:
                workflow_run.state = "FAILED"
                workflow_run.save()
            raise CommandError(f"Failed to start invoice processing workflow: {str(e)}")
