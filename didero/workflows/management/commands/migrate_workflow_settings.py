"""
Management command to migrate team settings to workflow behavior configurations.
This is part of the workflow refactoring to move from scattered team settings
to centralized, type-safe workflow-specific configurations.

Core workflows don't need WorkflowSnapshot - they use code instead of JSON graphs.
"""

from django.core.management.base import BaseCommand
from django.db import transaction

from didero.users.models import Team
from didero.users.models.user_team_setting_models import TeamSettingEnums
from didero.users.utils.team_setting_utils import get_team_setting_boolean_value
from didero.workflows.models import UserWorkflow, WorkflowBehaviorConfig
from didero.workflows.schemas import WorkflowTrigger, WorkflowType


class Command(BaseCommand):
    help = "Migrate team settings to workflow behavior configurations"

    def add_arguments(self, parser):
        parser.add_argument(
            "--team-id",
            type=int,
            help="Specific team ID to migrate (if not provided, migrates all teams)",
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be migrated without making changes",
        )

    def handle(self, *args, **options):
        team_id = options.get("team_id")
        dry_run = options.get("dry_run", False)

        if dry_run:
            self.stdout.write(
                self.style.WARNING("DRY RUN MODE - No changes will be made")
            )

        if team_id:
            teams = Team.objects.filter(id=team_id)
            if not teams.exists():
                self.stdout.write(self.style.ERROR(f"Team with ID {team_id} not found"))
                return
        else:
            teams = Team.objects.all()

        total_teams = teams.count()
        self.stdout.write(f"Processing {total_teams} teams...")

        migrated_count = 0
        error_count = 0

        for team in teams:
            try:
                if dry_run:
                    self._dry_run_migrate_team(team)
                else:
                    with transaction.atomic():
                        self._migrate_team_to_workflow_configs(team)
                migrated_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f"✓ Migrated team {team.id}: {team.name}")
                )
            except Exception as e:
                error_count += 1
                self.stdout.write(
                    self.style.ERROR(f"✗ Error migrating team {team.id}: {str(e)}")
                )

        # Summary
        self.stdout.write("")
        self.stdout.write(
            self.style.SUCCESS(f"Successfully migrated: {migrated_count}")
        )
        if error_count > 0:
            self.stdout.write(self.style.ERROR(f"Errors encountered: {error_count}"))

    def _dry_run_migrate_team(self, team: Team):
        """Show what would be migrated for a team without making changes"""
        self.stdout.write(f"\nTeam {team.id}: {team.name}")
        # Check PO creation human validation setting
        po_human_validation = get_team_setting_boolean_value(
            TeamSettingEnums.TEAM_PO_CREATION_HUMAN_VALIDATION_ENABLED.value, team
        )
        self.stdout.write(f"  - PO Human Validation Enabled: {po_human_validation}")
        # Check if workflow exists
        workflow_exists = UserWorkflow.objects.filter(
            team=team,
            workflow_type=WorkflowType.PURCHASE_ORDER_CREATION,
        ).exists()
        if workflow_exists:
            self.stdout.write("  - PO Creation workflow exists")
            workflow = UserWorkflow.objects.get(
                team=team,
                workflow_type=WorkflowType.PURCHASE_ORDER_CREATION,
            )
            has_config = hasattr(workflow, "behavior_config")
            self.stdout.write(f"  - Has behavior config: {has_config}")
            self.stdout.write(f"  - Uses core workflow: {workflow.uses_core_workflow}")
        else:
            self.stdout.write(
                "  - PO Creation workflow will be created (core, no snapshot)"
            )

    def _migrate_team_to_workflow_configs(self, team: Team):
        """Migrate team settings to workflow behavior configs"""

        # Get existing team settings
        po_human_validation = get_team_setting_boolean_value(
            TeamSettingEnums.TEAM_PO_CREATION_HUMAN_VALIDATION_ENABLED.value, team
        )

        # Find or create PO Creation workflow
        # Note: NO snapshot needed for core workflows!
        workflow, created = UserWorkflow.objects.get_or_create(
            team=team,
            workflow_type=WorkflowType.PURCHASE_ORDER_CREATION,
            trigger=WorkflowTrigger.ON_PURCHASE_ORDER_EMAIL_RECEIVED,
            defaults={
                # current_snapshot remains null for core workflows
                "current_snapshot": None,
            },
        )

        if created:
            self.stdout.write(
                f"  Created PO Creation workflow for team {team.id} (core implementation)"
            )

        # Create or update behavior config
        config_data = {
            "enabled": True,
            "require_human_validation": po_human_validation,
            "validate_duplicate_po": True,
            "auto_approve_threshold": None,
            "auto_approve_currency": "USD",
            "post_extraction_activities": [],
            "pre_creation_activities": [],
            "post_creation_activities": [],
            "enable_notifications": True,
            "notification_channels": ["email"],
            "notify_on_validation_required": True,
            "max_retry_attempts": 3,
            "retry_backoff_seconds": 60,
        }

        behavior_config, config_created = (
            WorkflowBehaviorConfig.objects.update_or_create(
                workflow=workflow,
                defaults={
                    "config": config_data,
                },
            )
        )

        if config_created:
            self.stdout.write(
                f"  Created behavior config with human_validation={po_human_validation}"
            )
        else:
            self.stdout.write(
                f"  Updated behavior config with human_validation={po_human_validation}"
            )
