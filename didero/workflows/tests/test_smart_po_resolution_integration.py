"""
Integration tests for Smart PO Resolution in workflows.

These tests verify that the order acknowledgement and shipment workflows
properly integrate with the smart PO resolution functionality.
"""

import asyncio
from unittest.mock import patch, AsyncMock, MagicMock
from django.test import TestCase

from didero.workflows.core.nodes.purchase_orders.order_acknowledgement.order_ack import (
    extract_order_acknowledgement_details,
)
from didero.workflows.core.nodes.purchase_orders.shipments import (
    parse_shipment_details_from_email,
    create_shipment_from_details,
    process_pickup_notification,
)


class TestOrderAcknowledgementSmartResolution(TestCase):
    """Test order acknowledgement workflow with smart PO resolution."""

    @patch(
        "didero.workflows.core.nodes.purchase_orders.order_acknowledgement.order_ack.resolve_purchase_order_with_auto_creation"
    )
    @patch(
        "didero.workflows.core.nodes.purchase_orders.order_acknowledgement.order_ack.get_purchase_order_from_po_number"
    )
    @patch(
        "didero.workflows.core.nodes.purchase_orders.order_acknowledgement.order_ack.ai_extract_order_acknowledgement_details"
    )
    @patch(
        "didero.workflows.core.nodes.purchase_orders.order_acknowledgement.order_ack.Communication"
    )
    def test_order_ack_with_successful_auto_creation(
        self, mock_communication, mock_ai_extract, mock_get_po, mock_smart_resolve
    ):
        """Test order acknowledgement workflow with successful PO auto-creation."""
        # Mock email/communication
        mock_email = MagicMock()
        mock_email.team.id = 4  # IonQ team
        mock_email.email_thread = MagicMock()
        mock_communication.objects.get.return_value = mock_email

        # Mock AI extraction
        mock_order_ack = MagicMock()
        mock_order_ack.po_number = "PO431"
        mock_ai_extract.return_value = mock_order_ack

        # Mock smart resolution with auto-creation
        mock_smart_resolve.return_value = {
            "success": True,
            "auto_created": True,
            "purchase_order_id": "123",
            "creation_details": {"workflow_id": "auto-workflow-123"},
        }

        # Mock final PO retrieval
        mock_po = MagicMock()
        mock_po.po_number = "PO431"
        mock_po.id = "123"
        mock_get_po.return_value = mock_po

        # Execute the activity (async)
        async def run_test():
            params = {"email_id": "email123"}
            await extract_order_acknowledgement_details(params)

            # Verify smart resolution was called
            mock_smart_resolve.assert_called_once_with(
                po_number="PO431",
                team_id=4,
                source_email_id="email123",
                source_context="order_acknowledgement",
            )

            # Verify final PO lookup was called
            mock_get_po.assert_called_once_with("PO431", 4)

        # Run the async test
        asyncio.run(run_test())

    @patch(
        "didero.workflows.core.nodes.purchase_orders.order_acknowledgement.order_ack.resolve_purchase_order_with_auto_creation"
    )
    @patch(
        "didero.workflows.core.nodes.purchase_orders.order_acknowledgement.order_ack.ai_extract_order_acknowledgement_details"
    )
    @patch(
        "didero.workflows.core.nodes.purchase_orders.order_acknowledgement.order_ack.Communication"
    )
    def test_order_ack_with_failed_auto_creation(
        self, mock_communication, mock_ai_extract, mock_smart_resolve
    ):
        """Test order acknowledgement workflow with failed PO auto-creation."""
        from didero.workflows.errors import ResourceNotFoundError

        # Mock email/communication
        mock_email = MagicMock()
        mock_email.team.id = 4  # IonQ team
        mock_communication.objects.get.return_value = mock_email

        # Mock AI extraction
        mock_order_ack = MagicMock()
        mock_order_ack.po_number = "PO999"
        mock_ai_extract.return_value = mock_order_ack

        # Mock smart resolution failure
        mock_smart_resolve.return_value = {
            "success": False,
            "auto_creation_attempted": True,
            "auto_creation_error": "NetSuite API error",
        }

        # Execute the activity and expect ResourceNotFoundError
        async def run_test():
            params = {"email_id": "email123"}
            from didero.workflows.errors import ResourceNotFoundError

            with self.assertRaises(ResourceNotFoundError) as exc_info:
                await extract_order_acknowledgement_details(params)

            # Verify error details include auto-creation context
            self.assertTrue(exc_info.exception.details["auto_creation_attempted"])
            self.assertEqual(
                exc_info.exception.details["auto_creation_error"], "NetSuite API error"
            )

        asyncio.run(run_test())


class TestShipmentSmartResolution(TestCase):
    """Test shipment workflow with smart PO resolution."""

    @patch(
        "didero.workflows.core.nodes.purchase_orders.shipments.resolve_purchase_order_with_auto_creation"
    )
    @patch(
        "didero.workflows.core.nodes.purchase_orders.shipments.get_purchase_order_from_po_number"
    )
    @patch(
        "didero.workflows.core.nodes.purchase_orders.shipments.get_shipment_info_for_email"
    )
    @patch("didero.workflows.core.nodes.purchase_orders.shipments.Communication")
    def test_shipment_parsing_with_auto_creation(
        self,
        mock_communication,
        mock_get_shipment_info,
        mock_get_po,
        mock_smart_resolve,
    ):
        """Test shipment parsing workflow with PO auto-creation."""
        # Mock email/communication
        mock_email = MagicMock()
        mock_email.team.id = 4  # IonQ team
        mock_email.team.pk = 4
        mock_communication.objects.get.return_value = mock_email

        # Mock shipment extraction
        mock_shipment_details = MagicMock()
        mock_shipment_details.purchase_order_number = "PO431"
        mock_shipment_details.carrier_name = "FedEx"
        mock_shipment_details.tracking_number = "123456789"
        mock_shipment_details.shipment_date = "2023-10-01"
        mock_get_shipment_info.return_value = mock_shipment_details

        # Mock smart resolution with auto-creation
        mock_smart_resolve.return_value = {
            "success": True,
            "auto_created": True,
            "purchase_order_id": "456",
            "creation_details": {"workflow_id": "auto-workflow-456"},
        }

        # Mock final PO retrieval
        mock_po = MagicMock()
        mock_po.po_number = "PO431"
        mock_get_po.return_value = mock_po

        # Execute the activity
        async def run_test():
            params = {"email_id": "shipment_email_123"}
            await parse_shipment_details_from_email(params)

            # Verify smart resolution was called
            mock_smart_resolve.assert_called_once_with(
                po_number="PO431",
                team_id=4,
                source_email_id="shipment_email_123",
                source_context="shipment",
            )

        import asyncio

        asyncio.run(run_test())

    @patch(
        "didero.workflows.core.nodes.purchase_orders.shipments.resolve_purchase_order_with_auto_creation"
    )
    @patch(
        "didero.workflows.core.nodes.purchase_orders.shipments.get_purchase_order_from_po_number"
    )
    def test_shipment_creation_with_auto_creation(
        self, mock_get_po, mock_smart_resolve
    ):
        """Test shipment creation with PO auto-creation."""
        # Mock shipment details
        mock_shipment_details = MagicMock()
        mock_shipment_details.purchase_order_number = "PO431"
        mock_shipment_details.team_id = 4
        mock_shipment_details.carrier = "UPS"
        mock_shipment_details.tracking_number = "987654321"
        mock_shipment_details.shipment_date = "2023-10-01"

        # Mock smart resolution
        mock_smart_resolve.return_value = {
            "success": True,
            "auto_created": True,
            "purchase_order_id": "789",
        }

        # Mock PO retrieval
        mock_po = MagicMock()
        mock_po.po_number = "PO431"
        mock_po.pk = "789"
        mock_get_po.return_value = mock_po

        # Execute the activity
        async def run_test():
            await create_shipment_from_details(mock_shipment_details)

            # Verify smart resolution was called with correct context
            mock_smart_resolve.assert_called_once_with(
                po_number="PO431",
                team_id=4,
                source_email_id=None,  # No email context in this flow
                source_context="shipment_creation",
            )

        import asyncio

        asyncio.run(run_test())

    @patch(
        "didero.workflows.core.nodes.purchase_orders.shipments.resolve_purchase_order_with_auto_creation"
    )
    @patch(
        "didero.workflows.core.nodes.purchase_orders.shipments.get_purchase_order_from_po_number"
    )
    @patch(
        "didero.workflows.core.nodes.purchase_orders.shipments.get_pickup_info_from_email"
    )
    @patch("didero.workflows.core.nodes.purchase_orders.shipments.Communication")
    def test_pickup_notification_with_auto_creation(
        self, mock_communication, mock_get_pickup_info, mock_get_po, mock_smart_resolve
    ):
        """Test pickup notification with PO auto-creation."""
        # Mock email/communication
        mock_email = MagicMock()
        mock_email.team.id = 4  # IonQ team
        mock_communication.objects.get.return_value = mock_email

        # Mock pickup info extraction
        mock_pickup_info = MagicMock()
        mock_pickup_info.purchase_order_number = "PO431"
        mock_get_pickup_info.return_value = mock_pickup_info

        # Mock smart resolution
        mock_smart_resolve.return_value = {
            "success": True,
            "auto_created": True,
            "purchase_order_id": "101112",
        }

        # Mock PO retrieval and status update
        mock_po = MagicMock()
        mock_po.po_number = "PO431"
        mock_get_po.return_value = mock_po

        # Execute the activity
        async def run_test():
            params = {"email_id": "pickup_email_456"}
            await process_pickup_notification(params)

            # Verify smart resolution was called
            mock_smart_resolve.assert_called_once_with(
                po_number="PO431",
                team_id=4,
                source_email_id="pickup_email_456",
                source_context="pickup_notification",
            )

            # Verify PO status was updated
            mock_po.save.assert_called_once()

        import asyncio

        asyncio.run(run_test())


class TestSmartResolutionErrorHandling(TestCase):
    """Test error handling scenarios in smart resolution."""

    @patch(
        "didero.workflows.core.nodes.purchase_orders.shipments.resolve_purchase_order_with_auto_creation"
    )
    @patch(
        "didero.workflows.core.nodes.purchase_orders.shipments.get_shipment_info_for_email"
    )
    @patch("didero.workflows.core.nodes.purchase_orders.shipments.Communication")
    def test_shipment_with_non_ionq_team_no_auto_creation(
        self, mock_communication, mock_get_shipment_info, mock_smart_resolve
    ):
        """Test shipment workflow with non-IonQ team - no auto-creation attempted."""
        from didero.workflows.errors import ResourceNotFoundError

        # Mock email/communication for non-IonQ team
        mock_email = MagicMock()
        mock_email.team.id = 999  # Non-IonQ team
        mock_email.team.pk = 999
        mock_communication.objects.get.return_value = mock_email

        # Mock shipment extraction
        mock_shipment_details = MagicMock()
        mock_shipment_details.purchase_order_number = "PO999"
        mock_shipment_details.carrier_name = "FedEx"
        mock_shipment_details.tracking_number = "123456789"
        mock_shipment_details.shipment_date = "2023-10-01"
        mock_get_shipment_info.return_value = mock_shipment_details

        # Mock smart resolution - no auto-creation for non-IonQ team
        mock_smart_resolve.return_value = {
            "success": False,
            "auto_creation_attempted": False,
            "auto_creation_error": "Auto-creation not enabled for team",
        }

        # Execute the activity and expect ResourceNotFoundError
        async def run_test():
            params = {"email_id": "shipment_email_999"}
            with pytest.raises(ResourceNotFoundError) as exc_info:
                await parse_shipment_details_from_email(params)

            # Verify no auto-creation was attempted
            assert exc_info.value.details["auto_creation_attempted"] is False

        import asyncio

        asyncio.run(run_test())

    def test_configuration_integration(self):
        """Test that configuration changes are properly reflected."""
        from didero.integrations.models import ERPIntegrationConfig
        from didero.workflows.shared_activities.purchase_order_operations import (
            should_auto_create_po_for_team,
        )
        from django.test import override_settings

        with patch(
            "didero.workflows.shared_activities.purchase_order_operations.ERPIntegrationConfig"
        ) as mock_config:
            # Test enabled configuration
            mock_config.objects.filter.return_value.exists.return_value = True
            assert should_auto_create_po_for_team(4) is True

            # Verify correct filter was applied
            mock_config.objects.filter.assert_called_with(
                team_id=4,
                enabled=True,
                auto_create_missing_pos=True,
                erp_type="netsuite",
            )

            # Test disabled configuration
            mock_config.objects.filter.return_value.exists.return_value = False
            assert should_auto_create_po_for_team(999) is False
