from decimal import Decimal
from enum import StrEnum

from pydantic import BaseModel, Field, field_validator


class ToleranceType(StrEnum):
    """Type of tolerance measurement"""

    PERCENTAGE = "percentage"
    FIXED_AMOUNT = "fixed_amount"


class ToleranceConfig(BaseModel):
    """Configuration for tolerance settings"""

    type: ToleranceType = Field(
        default=ToleranceType.PERCENTAGE,
        description="Type of tolerance: percentage or fixed amount",
    )
    value: float = Field(
        default=5.0,
        ge=0.0,
        description="Tolerance value (percentage 0-100 or dollar amount). Uses float for JSON serialization compatibility with Temporal activities.",
    )
    currency: str = Field(
        default="USD",
        description="Currency for fixed amount tolerance (ignored for percentage)",
    )

    @field_validator("value")
    @classmethod
    def validate_tolerance_value(cls, v: float) -> float:
        """Validate tolerance value is non-negative"""
        if v < 0.0:
            raise ValueError("Tolerance value must be >= 0.0")
        return v

    @field_validator("currency")
    @classmethod
    def validate_tolerance_currency(cls, v: str) -> str:
        """Validate currency for fixed amount tolerance"""
        valid_currencies = ["USD", "EUR", "GBP", "JPY", "CAD"]
        if v not in valid_currencies:
            raise ValueError(f"Currency must be one of {valid_currencies}")
        return v

    def is_within_tolerance(
        self, amount1: Decimal, amount2: Decimal, currency: str = "USD"
    ) -> bool:
        """
        Check if two amounts are within the configured tolerance.

        Args:
            amount1: First amount to compare
            amount2: Second amount to compare (reference amount)
            currency: Currency of the amounts

        Returns:
            bool: True if amounts are within tolerance
        """
        if amount2 == 0:
            return amount1 == 0

        if self.type == ToleranceType.PERCENTAGE:
            # Keep all calculations in Decimal to maintain precision
            percentage_diff = (abs(amount1 - amount2) / amount2) * Decimal("100")
            return percentage_diff <= Decimal(
                str(self.value)
            )  # Convert float to Decimal for comparison

        elif self.type == ToleranceType.FIXED_AMOUNT:
            # For fixed amount, we need to consider currency
            # For now, assume same currency (currency conversion would be handled elsewhere)
            if currency != self.currency:
                # TODO: Handle currency conversion when needed
                pass

            absolute_diff = abs(amount1 - amount2)
            return absolute_diff <= Decimal(
                str(self.value)
            )  # Convert float to Decimal for comparison

        return False


class WorkflowType(StrEnum):
    PURCHASE_ORDER_APPROVAL_FLOW = "purchase_order_approval_flow"
    PURCHASE_ORDER_SHIPPED = "purchase_order_shipped"
    PURCHASE_ORDER_SUPPLIER_ACKNOWLEDGEMENT = "purchase_order_supplier_acknowledgement"
    PURCHASE_ORDER_ACKNOWLEDGEMENT = "purchase_order_acknowledgement"
    PURCHASE_ORDER_CREATION = "purchase_order_creation"
    PURCHASE_ORDER_FOLLOW_UP = "purchase_order_follow_up"
    INVOICE_PROCESSING = "invoice_processing"


class WorkflowTrigger(StrEnum):
    NULL_TRIGGER = ""  # for workflows that don't have triggers
    ON_PURCHASE_ORDER_CREATED = "on_purchase_order_created"
    ON_ORDER_SHIPPED_EMAIL_RECEIVED = "on_order_shipped_email_received"
    ON_SEND_PURCHASE_ORDER_TO_SUPPLIER = "on_send_purchase_order_to_supplier"
    ON_PURCHASE_ORDER_ACKNOWLEDGEMENT_EMAIL_RECEIVED = (
        "on_purchase_order_acknowledgement_email_received"
    )
    ON_PURCHASE_ORDER_EMAIL_RECEIVED = "on_purchase_order_email_received"
    ON_PURCHASE_ORDER_FOLLOW_UP = "on_purchase_order_follow_up"
    ON_INVOICE_EMAIL_RECEIVED = "on_invoice_email_received"


class WorkflowRunState(StrEnum):
    QUEUED = "queued"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"


class WorkflowBehaviorConfigBase(BaseModel):
    """Base configuration for all workflows"""

    enabled: bool = True
    enable_notifications: bool = True
    enable_audit_logging: bool = True
    max_retry_attempts: int = Field(default=3, ge=1, le=10)
    retry_backoff_seconds: int = Field(default=60, ge=1, le=600)

    # Smart PO Auto-Creation (Chapter 2)
    # When enabled, workflows that process documents referencing POs (Order Acknowledgements,
    # Shipments, etc.) will automatically create missing POs from configured ERP systems.
    # This eliminates workflow failures when suppliers reference POs that exist in ERP but
    # not yet in Didero. Requires ERP integration to be configured for the team.
    enable_po_auto_creation: bool = Field(
        default=False,
        description="Automatically create missing POs from ERP when referenced in supplier documents",
    )
