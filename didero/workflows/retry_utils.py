"""
Shared retry policy utilities for Temporal workflows.
"""

from datetime import timedelta
from typing import Any

from temporalio.common import RetryPolicy


def get_retry_policy(config: Any = None, use_defaults: bool = False) -> RetryPolicy:
    """
    Get retry policy from configuration or defaults.

    Args:
        config: Configuration object with max_retry_attempts and retry_backoff_seconds
        use_defaults: If True, use default values instead of config

    Returns:
        RetryPolicy based on config or defaults
    """
    if use_defaults or config is None:
        return RetryPolicy(
            maximum_attempts=3,
            initial_interval=timedelta(seconds=2),
            maximum_interval=timedelta(seconds=10),
            backoff_coefficient=2.0,
        )

    return RetryPolicy(
        maximum_attempts=config.max_retry_attempts,
        initial_interval=timedelta(seconds=config.retry_backoff_seconds),
        maximum_interval=timedelta(seconds=config.retry_backoff_seconds * 10),
        backoff_coefficient=2.0,
    )
