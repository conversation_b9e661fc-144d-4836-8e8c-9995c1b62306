# Generated by Django 4.2.7 on 2025-06-11 18:37

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("workflows", "0013_workflowbehaviorconfig"),
    ]

    operations = [
        migrations.AlterField(
            model_name="userworkflow",
            name="trigger",
            field=models.CharField(
                choices=[
                    ("", "NULL_TRIGGER"),
                    ("on_purchase_order_created", "ON_PURCHASE_ORDER_CREATED"),
                    (
                        "on_order_shipped_email_received",
                        "ON_ORDER_SHIPPED_EMAIL_RECEIVED",
                    ),
                    (
                        "on_send_purchase_order_to_supplier",
                        "ON_SEND_PURCHASE_ORDER_TO_SUPPLIER",
                    ),
                    (
                        "on_purchase_order_acknowledgement_email_received",
                        "ON_PURCHASE_ORDER_ACKNOWLEDGEMENT_EMAIL_RECEIVED",
                    ),
                    (
                        "on_purchase_order_email_received",
                        "ON_PURCHASE_ORDER_EMAIL_RECEIVED",
                    ),
                    ("on_purchase_order_follow_up", "ON_PURCHASE_ORDER_FOLLOW_UP"),
                    ("on_followup_workflow_trigger", "ON_FOLLOWUP_WORKFLOW_TRIGGER"),
                ],
                help_text="DEPRECATED: This field is redundant as each workflow_type has a 1:1 mapping with a trigger. Will be removed in future versions.",
                max_length=64,
            ),
        ),
    ]
