{% extends "admin/base_site.html" %}
{% load i18n l10n admin_urls static %}

{% block content %}
<div class="card mb-4">
    <div class="card-header">
        <h3>{{ workflow_display_name }} Workflow Configuration</h3>
        <p class="mb-0">Team: <strong>{{ workflow.team.name }}</strong></p>
    </div>
    <div class="card-body">
        <p>{{ description }}</p>
        
        <div class="workflow-info mb-4">
            <h4>Workflow Information</h4>
            <table class="table table-bordered">
                <tr>
                    <th>Workflow ID</th>
                    <td>{{ workflow.id }}</td>
                </tr>
                <tr>
                    <th>Workflow Type</th>
                    <td>{{ workflow.workflow_type }}</td>
                </tr>
                <tr>
                    <th>Trigger</th>
                    <td>{{ workflow.trigger }}</td>
                </tr>
                <tr>
                    <th>Team</th>
                    <td>{{ workflow.team.name }}</td>
                </tr>
                <tr>
                    <th>Implementation</th>
                    <td>
                        {% if workflow.uses_core_workflow %}
                        <span class="badge badge-primary">Core Workflow</span>
                        {% else %}
                        <span class="badge badge-secondary">DAG Workflow</span>
                        {% endif %}
                    </td>
                </tr>
            </table>
        </div>

        {% if workflow.behavior_config %}
        <div class="behavior-config mb-4">
            <h4>Behavior Configuration</h4>
            <div class="alert alert-info">
                <strong>Note:</strong> This workflow uses a core implementation with behavior configuration. 
                To modify the configuration, please edit the JSON below or use the team workflow setup page.
            </div>
            
            <div class="config-display">
                <h5>Current Configuration:</h5>
                <pre class="bg-light p-3" style="border-radius: 4px; max-height: 400px; overflow-y: auto;">{{ workflow.behavior_config.config|pprint }}</pre>
            </div>
            
            <div class="config-actions mt-3">
                <a href="{% url 'admin:workflows_workflowbehaviorconfig_change' workflow.behavior_config.id %}" 
                   class="btn btn-primary" target="_blank">
                    Edit Configuration
                </a>
                <a href="{% url 'admin:users_team_setup_workflows' workflow.team.id %}" 
                   class="btn btn-secondary">
                    Team Workflow Setup
                </a>
            </div>
        </div>
        
        {% if workflow.workflow_type == "invoice_processing" %}
        <div class="invoice-specific-info">
            <h5>Invoice Processing Settings</h5>
            <table class="table table-sm">
                <tr>
                    <th>Assigned Users</th>
                    <td>
                        {% if workflow.behavior_config.config.assigned_user_ids %}
                            {{ workflow.behavior_config.config.assigned_user_ids|length }} user(s) configured
                        {% else %}
                            No specific users assigned
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <th>Assigned User Groups</th>
                    <td>
                        {% if workflow.behavior_config.config.assigned_user_group_names %}
                            {{ workflow.behavior_config.config.assigned_user_group_names|join:", " }}
                        {% else %}
                            No user groups assigned
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <th>Manual Validation Required</th>
                    <td>
                        {% if workflow.behavior_config.config.require_manual_validation %}
                            <span class="badge badge-warning">Yes</span>
                        {% else %}
                            <span class="badge badge-success">No</span>
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <th>Create Task on Mismatch</th>
                    <td>
                        {% if workflow.behavior_config.config.create_task_on_mismatch %}
                            <span class="badge badge-success">Yes</span>
                        {% else %}
                            <span class="badge badge-secondary">No</span>
                        {% endif %}
                    </td>
                </tr>
            </table>
        </div>
        {% endif %}
        
        {% else %}
        <div class="alert alert-warning">
            <strong>No Configuration Found:</strong> This core workflow doesn't have a behavior configuration yet.
            <div class="mt-2">
                <a href="{% url 'admin:users_team_setup_workflows' workflow.team.id %}" 
                   class="btn btn-primary">
                    Configure via Team Setup
                </a>
            </div>
        </div>
        {% endif %}
        
        <div class="quick-actions mt-4">
            <h4>Quick Actions</h4>
            <div class="btn-group" role="group">
                <a href="{% url 'admin:workflows_userworkflow_changelist' %}" class="btn btn-secondary">
                    Back to Workflows
                </a>
                <a href="{% url 'admin:users_team_change' workflow.team.id %}" class="btn btn-info">
                    Team Settings
                </a>
                {% if workflow.behavior_config %}
                <a href="{% url 'admin:workflows_workflowbehaviorconfig_changelist' %}?workflow__id__exact={{ workflow.id }}" 
                   class="btn btn-outline-primary">
                    View All Configs
                </a>
                {% endif %}
                {% if has_delete_permission %}
                <a href="{% url 'admin:workflows_userworkflow_delete' workflow.id %}" class="btn btn-danger">
                    Delete Workflow
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
/* Dark theme styles matching Django admin */
body {
    background-color: #0d1117;
    color: #e6edf3;
}

.card {
    border: 1px solid #30363d;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    background-color: #161b22;
}

.card-header {
    background-color: #21262d;
    border-bottom: 1px solid #30363d;
    padding: 1.25rem;
    border-radius: 8px 8px 0 0;
}

.card-header h3 {
    margin: 0 0 0.5rem 0;
    color: #f0f6fc;
    font-size: 1.5rem;
    font-weight: 600;
}

.card-header p {
    margin: 0;
    color: #8b949e;
    font-size: 0.95rem;
}

.card-body {
    padding: 1.5rem;
    background-color: #161b22;
    color: #e6edf3;
}

.card-body > p {
    color: #c9d1d9;
    font-size: 1rem;
    margin-bottom: 1.5rem;
}

.card-body h4 {
    color: #f0f6fc;
    border-bottom: 2px solid #30363d;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.card-body h5 {
    color: #f0f6fc;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Badge styles - dark theme */
.badge {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
    border-radius: 4px;
    font-weight: 500;
}
.badge-primary { background-color: #1f6feb; color: #ffffff; }
.badge-secondary { background-color: #6e7681; color: #ffffff; }
.badge-success { background-color: #238636; color: #ffffff; }
.badge-warning { background-color: #9e6a03; color: #000000; }

/* Button styles - dark theme */
.btn {
    padding: 0.5rem 1rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    text-decoration: none;
    border-radius: 6px;
    display: inline-block;
    font-weight: 500;
    border: 1px solid transparent;
    transition: all 0.2s ease;
}
.btn:hover {
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}
.btn-primary { 
    background-color: #238636; 
    color: #ffffff; 
    border-color: #238636; 
}
.btn-primary:hover { 
    background-color: #2ea043; 
    border-color: #2ea043; 
    color: #ffffff;
}
.btn-secondary { 
    background-color: #6e7681; 
    color: #ffffff; 
    border-color: #6e7681; 
}
.btn-secondary:hover { 
    background-color: #8b949e; 
    border-color: #8b949e; 
    color: #ffffff;
}
.btn-info { 
    background-color: #1f6feb; 
    color: #ffffff; 
    border-color: #1f6feb; 
}
.btn-info:hover { 
    background-color: #388bfd; 
    border-color: #388bfd; 
    color: #ffffff;
}
.btn-outline-primary { 
    background-color: transparent; 
    color: #238636; 
    border-color: #238636; 
}
.btn-outline-primary:hover { 
    background-color: #238636; 
    color: #ffffff; 
}
.btn-danger { 
    background-color: #da3633; 
    color: #ffffff; 
    border-color: #da3633; 
}
.btn-danger:hover { 
    background-color: #f85149; 
    border-color: #f85149; 
    color: #ffffff;
}

/* Table styles - dark theme */
.table {
    width: 100%;
    margin-bottom: 1rem;
    border-collapse: collapse;
    background-color: #161b22;
}
.table th, .table td {
    padding: 0.75rem;
    border: 1px solid #30363d;
    color: #e6edf3;
}
.table th {
    background-color: #21262d;
    font-weight: 600;
    color: #f0f6fc;
}
.table-sm th, .table-sm td {
    padding: 0.5rem;
}

/* Alert styles - dark theme */
.alert {
    padding: 1rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 6px;
}
.alert-info {
    background-color: #0d419d;
    border-color: #1f6feb;
    color: #cae8ff;
}
.alert-warning {
    background-color: #7d4e00;
    border-color: #9e6a03;
    color: #fff8c5;
}

/* Configuration display - dark theme */
.config-display pre {
    background-color: #0d1117 !important;
    border: 1px solid #30363d;
    color: #e6edf3 !important;
    padding: 1rem !important;
    border-radius: 6px;
    font-size: 0.9rem;
    line-height: 1.4;
    overflow-x: auto;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

/* Workflow info sections - dark theme */
.workflow-info, .behavior-config, .invoice-specific-info, .quick-actions {
    background-color: #0d1117;
    border: 1px solid #30363d;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

/* Button group */
.btn-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* Dark theme text colors */
* {
    color: inherit;
}

h1, h2, h3, h4, h5, h6 {
    color: #f0f6fc;
}

p, span, div {
    color: #e6edf3;
}

/* Links in dark theme */
a {
    color: #58a6ff;
}
a:hover {
    color: #79c0ff;
}

/* Override any light theme remnants */
.bg-light {
    background-color: #21262d !important;
}

/* Scrollbar styling for dark theme */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}
::-webkit-scrollbar-track {
    background: #161b22;
}
::-webkit-scrollbar-thumb {
    background: #30363d;
    border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
    background: #484f58;
}
</style>
{% endblock %}