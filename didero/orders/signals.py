"""
Django signals for the orders app.

This module contains signal handlers for order-related events,
such as triggering workflows when purchase order status changes.
"""

import structlog
from django.core.exceptions import ValidationError
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver

from didero.orders.models import PurchaseOrder
from didero.orders.schemas import PurchaseOrderStatus
from didero.workflows.schemas import WorkflowTrigger, WorkflowType
from didero.workflows.utils import trigger_workflow_if_exists

logger = structlog.get_logger(__name__)


@receiver(pre_save, sender=PurchaseOrder)
def track_status_change(sender, instance, **kwargs):
    """
    Track if PO status is changing to ISSUED and validate status transitions.

    We use pre_save to detect the change and validate that the status
    transition is valid before allowing the save.
    """
    if instance.pk:  # Existing PO
        try:
            old_instance = PurchaseOrder.objects.get(pk=instance.pk)
            old_status = old_instance.order_status
            new_status = instance.order_status

            # Validate status transitions to ISSUED
            if new_status == PurchaseOrderStatus.ISSUED and old_status != new_status:
                # Define invalid transitions to ISSUED
                invalid_from_statuses = [
                    PurchaseOrderStatus.AWAITING_SHIPMENT,
                    PurchaseOrderStatus.SHIPPED,
                    PurchaseOrderStatus.RECEIVED,
                    PurchaseOrderStatus.PARTIALLY_RECEIVED,
                ]

                if old_status in invalid_from_statuses:
                    raise ValidationError(
                        f"Invalid status transition: Cannot change PO status from "
                        f"'{old_status}' to 'ISSUED'. Purchase orders cannot be "
                        f"moved backwards in the fulfillment process."
                    )

                logger.info(
                    "Valid PO status change to ISSUED detected",
                    purchase_order_id=instance.pk,
                    old_status=old_status,
                    new_status=new_status,
                )

                # Mark that we should trigger follow-up after save
                instance._trigger_follow_up = True
            else:
                instance._trigger_follow_up = False

        except PurchaseOrder.DoesNotExist:
            # This shouldn't happen but handle gracefully
            logger.warning(
                "PO not found during pre_save signal",
                purchase_order_id=instance.pk,
            )
            instance._trigger_follow_up = False
    else:  # New PO
        # For new POs, check if being created in ISSUED status
        instance._trigger_follow_up = (
            instance.order_status == PurchaseOrderStatus.ISSUED
        )


@receiver(post_save, sender=PurchaseOrder)
def trigger_follow_up_on_issued(sender, instance, created, **kwargs):
    """
    Trigger follow-up workflow when PO status changes to ISSUED.

    We use post_save to ensure the PO is fully saved before triggering
    the workflow, avoiding any transaction issues.
    """
    # Check if we should trigger follow-up (set by pre_save)
    should_trigger = getattr(instance, "_trigger_follow_up", False)

    # For new POs created directly in ISSUED status
    if created and instance.order_status == PurchaseOrderStatus.ISSUED:
        should_trigger = True

    if should_trigger:
        logger.info(
            "Triggering follow-up workflow for ISSUED PO",
            purchase_order_id=instance.pk,
            po_number=instance.po_number,
            supplier_name=instance.supplier.name if instance.supplier else None,
        )

        try:
            # Trigger the follow-up workflow
            workflow_triggered = trigger_workflow_if_exists(
                workflow_type=WorkflowType.PURCHASE_ORDER_FOLLOW_UP,
                trigger=WorkflowTrigger.ON_PURCHASE_ORDER_FOLLOW_UP,
                team=instance.team,
                params={
                    "purchase_order_id": str(instance.pk),
                    "email_object_id": "",  # No email context for status change trigger
                },
            )

            # Note: Core workflows return None, so we can't rely on the return value
            # The actual triggering is logged by the workflow utils
            logger.info(
                "Follow-up workflow trigger completed",
                purchase_order_id=instance.pk,
                po_number=instance.po_number,
            )

        except Exception as e:
            # Don't let workflow errors break the PO save
            logger.error(
                "Failed to trigger follow-up workflow",
                purchase_order_id=instance.pk,
                error=str(e),
                exc_info=True,
            )

        # Clean up the temporary attribute
        if hasattr(instance, "_trigger_follow_up"):
            delattr(instance, "_trigger_follow_up")
