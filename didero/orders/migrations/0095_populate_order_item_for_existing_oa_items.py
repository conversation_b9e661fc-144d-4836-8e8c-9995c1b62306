# Generated by Django 4.2.7 on 2025-07-08 15:54

from django.db import migrations


def populate_order_item_for_existing_oa_items(apps, schema_editor):
    """
    Populate order_item field for existing OrderAcknowledgementItems.
    Match by item_number between OA items and OrderItems within the same PO.
    """
    OrderAcknowledgementItem = apps.get_model("orders", "OrderAcknowledgementItem")
    OrderItem = apps.get_model("orders", "OrderItem")

    updated_count = 0
    unmatched_count = 0

    # Get all OA items without order_item populated
    oa_items = OrderAcknowledgementItem.objects.filter(order_item__isnull=True)

    for oa_item in oa_items:
        if not oa_item.item_number or not oa_item.order_acknowledgement_id:
            unmatched_count += 1
            continue

        # Find matching OrderItem by item number within the same PO
        order_item = OrderItem.objects.filter(
            purchase_order=oa_item.order_acknowledgement.purchase_order,
            item__item_number__iexact=oa_item.item_number,
        ).first()

        if order_item:
            oa_item.order_item = order_item
            oa_item.save(update_fields=["order_item"])
            updated_count += 1
        else:
            unmatched_count += 1

    print(
        f"Data migration completed: {updated_count} items matched, {unmatched_count} items unmatched"
    )


def reverse_populate_order_item(apps, schema_editor):
    """
    Reverse migration: clear order_item field for all OrderAcknowledgementItems.
    """
    OrderAcknowledgementItem = apps.get_model("orders", "OrderAcknowledgementItem")
    OrderAcknowledgementItem.objects.update(order_item=None)


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0094_add_order_item_to_oa_item"),
    ]

    operations = [
        migrations.RunPython(
            populate_order_item_for_existing_oa_items, reverse_populate_order_item
        ),
    ]
