# Generated by Django 4.2.7 on 2025-07-18 20:41

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0100_merge_20250717_2006"),
    ]

    operations = [
        migrations.AddField(
            model_name="orderacknowledgement",
            name="global_promised_delivery_date",
            field=models.CharField(
                blank=True,
                help_text="Global delivery date promised by supplier for ALL items when no item-specific dates are provided (YYYY-MM-DD)",
                max_length=50,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="orderacknowledgement",
            name="global_promised_ship_date",
            field=models.Char<PERSON>ield(
                blank=True,
                help_text="Global ship date promised by supplier for ALL items when no item-specific dates are provided (YYYY-MM-DD)",
                max_length=50,
                null=True,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="orderacknowledgement",
            name="oa_status",
            field=models.Char<PERSON><PERSON>(
                choices=[
                    ("EXTRACTED", "Extracted - data extracted from supplier response"),
                    ("COMPLETE", "Complete - has all needed info"),
                    ("INCOMPLETE", "Incomplete - missing item ship dates"),
                    (
                        "PENDING_CLARIFICATION",
                        "Pending clarification - validation issues detected",
                    ),
                    ("REJECTED", "Rejected - supplier cannot fulfill order"),
                ],
                default="EXTRACTED",
                help_text="Status of this order acknowledgment",
                max_length=30,
            ),
        ),
    ]
