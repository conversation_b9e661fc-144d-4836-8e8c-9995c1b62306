from django.contrib.contenttypes.models import ContentType
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods

from didero.orders.models import PurchaseOrder
from didero.tasks.schemas import TaskActionType as TaskActionTypeName
from didero.tasks.schemas import TaskContextPanelType
from didero.tasks.schemas import TaskType as TaskTypeName
from didero.tasks.utils import create_task_v2
from didero.users.models import User


@csrf_exempt
@require_http_methods(["GET", "POST", "PUT", "PATCH", "DELETE"])
def po_update_demo(request):
    """Demo endpoint that creates a hardcoded v2 task."""
    # Get email from request parameters
    email = request.GET.get("email") or request.POST.get("email")
    if not email:
        return JsonResponse({"error": "Email parameter is required"}, status=400)

    # Get purchase order ID from request parameters
    purchase_order_id = request.GET.get("purchase_order_id") or request.POST.get(
        "purchase_order_id"
    )
    if not purchase_order_id:
        return JsonResponse(
            {"error": "Purchase order ID parameter is required"}, status=400
        )

    # Find user by email
    try:
        user = User.objects.get(email=email)
    except User.DoesNotExist:
        return JsonResponse(
            {"error": f"User with email '{email}' not found"}, status=404
        )

    # Find purchase order by ID
    try:
        purchase_order = PurchaseOrder.objects.get(id=purchase_order_id)
    except PurchaseOrder.DoesNotExist:
        return JsonResponse(
            {"error": f"Purchase order with ID '{purchase_order_id}' not found"},
            status=404,
        )

    print(user, purchase_order)

    # Get ContentType for User model (hardcoded)
    user_content_type = ContentType.objects.get_for_model(User)

    # Create a hardcoded v2 task
    task = create_task_v2(
        task_type=TaskTypeName.ORDER_ACKNOWLEDGEMENT_VALIDATION_ERROR,
        user=user,
        model_type=user_content_type,
        model_id=str(user.id),
        task_type_params={
            "validation_error": f"Price mismatch for Demo Supplier PO #{purchase_order.po_number} detected. New price per unit $4.73 per unit (increase from $3.50, equals increase of 35%)",
            "po_number": purchase_order.po_number,
            "error_type_label": "Price Mismatch",
        },
        actions=[
            {
                "action_type": TaskActionTypeName.OPS_ADD_COMMENT,
                "action_params": {
                    "button_text": "Accept Mismatch and Update ERP",
                    "button_sub_text": "Accept the price change and update NetSuite",
                },
                "action_execution_params": {
                    "parent_object_type": "purchase_order",
                    "parent_object_id": purchase_order_id,
                    "comment": f"Price mismatch accepted for Demo Supplier PO #{purchase_order.po_number}. New price per unit $4.73 (increase from $3.50, equals increase of 35%). Updated ERP accordingly.",
                },
            },
            {
                "action_type": TaskActionTypeName.SEND_EMAIL,
                "action_params": {},
                "action_execution_params": {
                    "email_to": "<EMAIL>",
                    "email_subject": f"Price Discrepancy - PO #{purchase_order.po_number}",
                    "email_body": f"Dear Demo Supplier,\n\nWe've noticed a price discrepancy for item A311296 on PO #{purchase_order.po_number}. The new price per unit is $4.73 (increase from $3.50, equals increase of 35%).\n\nPlease confirm if this price change is correct.\n\nBest regards,\nProcurement Team",
                },
            },
        ],
        context_panels=[
            {
                "panel_type": TaskContextPanelType.PO_DETAILS,
                "param_values": {
                    "purchaseOrderId": purchase_order_id,
                },
            }
        ],
    )

    return JsonResponse(
        {
            "message": "Task created successfully",
            "task_id": str(task.id),
            "task_type": task.task_type_v2.name,
            "user": user.email,
        }
    )
