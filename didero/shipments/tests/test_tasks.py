from copy import deepcopy
from datetime import timedelta
from unittest.mock import patch

from django.utils import timezone

from didero.orders.models import PurchaseOrderComment, Shipment
from didero.orders.schemas import CarrierType, ShipmentStatus
from didero.shipments.tasks import update_pending_shipments
from didero.suppliers.models import Supplier
from didero.testing.cases import TestCase
from didero.users.models import User


class TestUpdatePendingShipments(TestCase):
    def setUp(self):
        super().setUp()

        # Create a user and team
        self.user = self.create_user(
            email="<EMAIL>", first_name="<PERSON>", last_name="<PERSON>"
        )
        self.auth_as_user(self.user)
        self.team = self.user.teams.first()

        # Create a supplier
        self.supplier = self.create_supplier(
            team=self.team, pk=1, name="Global Supplies"
        )

        # Create a purchase order
        self.purchase_order = self.create_purchase_order(
            supplier=self.supplier,
            team=self.team,
            placed_by=self.user,
            po_number="PO-5678",
        )

    @patch("didero.orders.tasks.get_tracking_info_for_tracking_number")
    def test_update_shipment_status_to_received_creates_delivery_confirmed_comment(
        self, mock_get_tracking
    ):
        # Create FedEx shipment
        self.fedex_shipment = Shipment.objects.create(
            purchase_order=self.purchase_order,
            carrier="Fedex",
            carrier_type=CarrierType.FEDEX.value,
            tracking_number="FEDEX123456",
            shipment_date=timezone.now().date(),
            status=ShipmentStatus.SHIPPED.value,
        )

        # Mock tracking info to simulate shipment received
        mock_get_tracking.return_value = type(
            "TrackingInfo",
            (),
            {
                "carrier": "Fedex",
                "carrier_type": CarrierType.FEDEX,
                "tracking_number": "FEDEX123456",
                "shipment_status": ShipmentStatus.RECEIVED.value,
                "eta": None,
                "shipment_date": self.fedex_shipment.shipment_date.isoformat(),
            },
        )

        # Run the task
        update_pending_shipments()

        # Refresh shipment from DB
        self.fedex_shipment.refresh_from_db()

        # Assert shipment status updated
        self.assertEqual(self.fedex_shipment.status, ShipmentStatus.RECEIVED.value)

        # Assert comment created
        comment = PurchaseOrderComment.objects.filter(
            purchase_order=self.purchase_order, comment__contains="Delivery Confirmed"
        ).first()
        self.assertIsNotNone(comment)
        self.assertIn("Delivery Confirmed", comment.comment)

    @patch("didero.orders.tasks.get_tracking_info_for_tracking_number")
    def test_update_shipment_estimated_delivery_date_creates_estimated_delivery_comment(
        self, mock_get_tracking
    ):
        # Create FedEx shipment
        self.fedex_shipment = Shipment.objects.create(
            purchase_order=self.purchase_order,
            carrier="Fedex",
            carrier_type=CarrierType.FEDEX.value,
            tracking_number="FEDEX123456",
            shipment_date=timezone.now().date(),
            status=ShipmentStatus.SHIPPED.value,
        )

        # Mock tracking info to add estimated delivery date
        mock_get_tracking.return_value = type(
            "TrackingInfo",
            (),
            {
                "carrier": "Fedex",
                "carrier_type": CarrierType.FEDEX,
                "tracking_number": "FEDEX123456",
                "shipment_status": ShipmentStatus.SHIPPED.value,
                "eta": (timezone.now().date() + timezone.timedelta(days=5)).isoformat(),
                "shipment_date": self.fedex_shipment.shipment_date.isoformat(),
            },
        )

        # Run the task
        update_pending_shipments()

        # Refresh shipment from DB
        self.fedex_shipment.refresh_from_db()

        # Assert estimated_delivery_date updated
        expected_eta = timezone.now().date() + timezone.timedelta(days=5)
        self.assertEqual(self.fedex_shipment.estimated_delivery_date, expected_eta)

        # Assert comment created
        comment = PurchaseOrderComment.objects.filter(
            purchase_order=self.purchase_order, comment__contains="Shipment Confirmed"
        ).first()
        self.assertIsNotNone(comment)
        self.assertIn("Shipment Confirmed", comment.comment)

    @patch("didero.orders.tasks.get_tracking_info_for_tracking_number")
    def test_no_update_for_non_fedex_shipments(self, mock_get_tracking):
        # Create non-FedEx shipment
        self.non_fedex_shipment = Shipment.objects.create(
            purchase_order=self.purchase_order,
            carrier="USPS",
            carrier_type="USPS",
            tracking_number="USPS654321",
            shipment_date=timezone.now().date(),
            status=ShipmentStatus.SHIPPED.value,
        )

        # Run the task
        update_pending_shipments()

        # Refresh non-FedEx shipment from DB
        self.non_fedex_shipment.refresh_from_db()

        # Assert shipment status remains unchanged
        self.assertEqual(self.non_fedex_shipment.status, ShipmentStatus.SHIPPED.value)

        # Assert no comments created
        comments = PurchaseOrderComment.objects.filter(
            purchase_order=self.purchase_order, comment__contains="Shipment"
        )
        self.assertFalse(comments.exists())

    @patch("didero.orders.tasks.get_tracking_info_for_tracking_number")
    def test_no_update_for_shipments_without_tracking_number(self, mock_get_tracking):
        # Create shipment without tracking number
        self.no_tracking_shipment = Shipment.objects.create(
            purchase_order=self.purchase_order,
            carrier="Fedex",
            carrier_type=CarrierType.FEDEX.value,
            tracking_number=None,
            shipment_date=timezone.now().date(),
            status=ShipmentStatus.SHIPPED.value,
        )

        # Run the task
        update_pending_shipments()

        # Refresh shipment from DB
        self.no_tracking_shipment.refresh_from_db()

        # Assert shipment status remains unchanged
        self.assertEqual(self.no_tracking_shipment.status, ShipmentStatus.SHIPPED.value)

        # Assert get_tracking_info_for_tracking_number was not called
        mock_get_tracking.assert_not_called()

        # Assert no comments created
        comments = PurchaseOrderComment.objects.filter(
            purchase_order=self.purchase_order, comment__contains="Shipment"
        )
        self.assertFalse(comments.exists())

    @patch("didero.orders.tasks.get_tracking_info_for_tracking_number")
    def test_handle_tracking_info_not_found(self, mock_get_tracking):
        # Create FedEx shipment
        self.fedex_shipment = Shipment.objects.create(
            purchase_order=self.purchase_order,
            carrier="Fedex",
            carrier_type=CarrierType.FEDEX.value,
            tracking_number="FEDEX123456",
            shipment_date=timezone.now().date(),
            status=ShipmentStatus.SHIPPED.value,
        )

        # Mock tracking info to return None
        mock_get_tracking.return_value = None

        # Run the task
        update_pending_shipments()

        # Refresh shipment from DB
        self.fedex_shipment.refresh_from_db()

        # Assert shipment status remains unchanged
        self.assertEqual(self.fedex_shipment.status, ShipmentStatus.SHIPPED.value)

        # Assert no comment was created for the shipment.
        comment = PurchaseOrderComment.objects.filter(
            purchase_order=self.purchase_order,
        )
        self.assertFalse(comment.exists())

    @patch("didero.orders.tasks.get_tracking_info_for_tracking_number")
    def test_handle_shipment_date_not_found(self, mock_get_tracking):
        # Create FedEx shipment
        self.fedex_shipment = Shipment.objects.create(
            purchase_order=self.purchase_order,
            carrier="Fedex",
            carrier_type=CarrierType.FEDEX.value,
            tracking_number="FEDEX123456",
            shipment_date=timezone.now().date(),
            estimated_delivery_date=timezone.now().date() + timedelta(days=5),
            status=ShipmentStatus.SHIPPED.value,
        )

        # Mock tracking info to return None
        mock_get_tracking.return_value = type(
            "TrackingInfo",
            (),
            {
                "carrier": "Fedex",
                "carrier_type": CarrierType.FEDEX,
                "tracking_number": "FEDEX123456",
                "shipment_status": ShipmentStatus.RECEIVED.value,
                "eta": None,
                "shipment_date": None,
            },
        )

        # Run the task
        update_pending_shipments()

        copy_fedex_shipment = deepcopy(self.fedex_shipment)
        # Refresh shipment from DB
        self.fedex_shipment.refresh_from_db()

        # Assert shipment status is set to received
        self.assertEqual(self.fedex_shipment.status, ShipmentStatus.RECEIVED.value)

        # Assert estimated_delivery_date is set to the previous estimated_delivery_date
        self.assertEqual(
            self.fedex_shipment.estimated_delivery_date,
            copy_fedex_shipment.estimated_delivery_date,
        )

        # Assert shipment date is set to the previous shipment date
        self.assertEqual(
            self.fedex_shipment.shipment_date,
            copy_fedex_shipment.shipment_date,
        )
