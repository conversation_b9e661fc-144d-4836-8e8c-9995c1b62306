import time
from copy import deepcopy
from datetime import date
from typing import List

import structlog
from celery import group, shared_task
from celery_singleton import Singleton
from django.db.models import Q

from didero.integrations.shipping.stagehand_carriers import get_stagehand_tracking_info
from didero.integrations.utils.fedex.fedex import (
    get_tracking_info_for_tracking_number,
)
from didero.orders.models import PurchaseOrderComment, Shipment
from didero.orders.schemas import CarrierType, ShipmentStatus

logger = structlog.get_logger(__name__)

# Carrier update frequency buckets
FAST_UPDATE_CARRIERS = [CarrierType.FEDEX]  # Update every 15 minutes
SLOW_UPDATE_CARRIERS = [
    CarrierType.ZIM,
    CarrierType.ONE,
    CarrierType.MSC,
    CarrierType.COSCO,
]  # Update twice daily

# All supported carriers
SUPPORTED_CARRIERS = FAST_UPDATE_CARRIERS + SLOW_UPDATE_CARRIERS

API_DELAY = 30


def _get_order_comment_for_shipment_update(
    prev_shipment: Shipment, new_shipment: Shipment
) -> str:
    if not new_shipment.purchase_order:
        return ""

    po_number = new_shipment.purchase_order.po_number
    carrier = new_shipment.carrier
    tracking_number = new_shipment.tracking_number
    if (
        prev_shipment.status != new_shipment.status
        and new_shipment.status == ShipmentStatus.RECEIVED
    ):
        delivery_datetime = new_shipment.shipment_date
        if delivery_datetime:
            formatted_delivery_date = delivery_datetime.strftime("%m/%d/%Y")
        else:
            formatted_delivery_date = "Unknown Date"
        return (
            f"Delivery Confirmed: {carrier} reported PO #{po_number} was delivered on "
            f"{formatted_delivery_date}."
        )

    if (
        prev_shipment.estimated_delivery_date != new_shipment.estimated_delivery_date
        and new_shipment.estimated_delivery_date is not None
    ):
        estimated_delivery_date = new_shipment.estimated_delivery_date
        if isinstance(estimated_delivery_date, date):
            formatted_date = estimated_delivery_date.strftime("%m/%d/%Y")
        else:
            formatted_date = "Unknown Date"
        return (
            f"ETA Updated: {carrier} expects PO #{po_number} with tracking #{tracking_number} to arrive on "
            f"{formatted_date}."
        )

    return ""


def _process_single_shipment(shipment: Shipment) -> bool:
    """Process tracking updates for a single shipment."""
    try:
        tracking_info = None

        if shipment.carrier_type == CarrierType.FEDEX:
            if not shipment.tracking_number:
                logger.warning(
                    f"Process Shipment Tracking - FedEx shipment {shipment.pk} has no tracking number"
                )
                return False
            fedex_info = get_tracking_info_for_tracking_number(shipment.tracking_number)
            if fedex_info:
                tracking_info = {
                    "tracking_number": fedex_info.tracking_number,
                    "eta": fedex_info.eta,
                    "shipment_date": fedex_info.shipment_date,
                    "shipment_status": fedex_info.shipment_status,
                }
        elif shipment.carrier_type in SLOW_UPDATE_CARRIERS:
            # Handle Stagehand-based carriers
            if not shipment.bol_number:
                logger.warning(
                    f"Process Shipment Tracking - {shipment.carrier_type} shipment {shipment.pk} has no BOL number"
                )
                return False

            # Get tracking info for Stagehand-based carrier
            carrier_info = get_stagehand_tracking_info(
                CarrierType(shipment.carrier_type), shipment.bol_number
            )

            if carrier_info:
                tracking_info = {
                    "tracking_number": carrier_info.tracking_number,
                    "eta": carrier_info.eta,
                    "shipment_date": carrier_info.shipment_date,
                    "actual_arrival_date": carrier_info.actual_arrival_date,
                }
                if carrier_info.port_of_departure:
                    if shipment.metadata is None:
                        shipment.metadata = {}
                    shipment.metadata["port_of_departure"] = (
                        carrier_info.port_of_departure
                    )
                if carrier_info.port_of_arrival:
                    if shipment.metadata is None:
                        shipment.metadata = {}
                    shipment.metadata["port_of_arrival"] = carrier_info.port_of_arrival

        if not tracking_info:
            logger.warning(
                f"Process Shipment Tracking - No tracking info found for shipment {shipment.pk}"
            )
            return False

        prev_shipment = deepcopy(shipment)

        if tracking_info.get("eta"):
            shipment.estimated_delivery_date = tracking_info["eta"]
        if tracking_info.get("shipment_date"):
            shipment.shipment_date = tracking_info["shipment_date"]
        if tracking_info.get("actual_arrival_date"):
            shipment.actual_delivery_date = tracking_info["actual_arrival_date"]
            shipment.status = ShipmentStatus.RECEIVED
        if tracking_info.get("shipment_status"):
            shipment.status = tracking_info["shipment_status"]

        order_comment = _get_order_comment_for_shipment_update(prev_shipment, shipment)

        shipment.save()

        if order_comment and shipment.purchase_order:
            try:
                PurchaseOrderComment.objects.create(
                    purchase_order=shipment.purchase_order, comment=order_comment
                )
                logger.info(
                    f"Process Shipment Tracking - Created order comment for shipment {shipment.pk}"
                )
            except Exception as e:
                logger.error(
                    f"Process Shipment Tracking - Error creating order comment for shipment {shipment.pk}: {e}"
                )

        logger.info(
            f"Process Shipment Tracking - Successfully updated tracking for shipment {shipment.pk}"
        )
        return True

    except Exception as e:
        logger.error(
            f"Process Shipment Tracking - Error processing shipment {shipment.pk}: {e}"
        )
        return False


def _process_shipments_for_carriers(carriers: List[CarrierType], task_name: str):
    """Common logic for processing shipments for a list of carriers."""

    # TODO: this is a really basic setup to get things working end to end. As a fast follow, we should
    # move the stagehand script into the stagehand container, and perhaps make this function it's own task so we can parallelize
    try:
        # Build dynamic Q filter based on carrier requirements
        q_filter = Q()
        for carrier in carriers:
            if carrier == CarrierType.FEDEX:
                q_filter |= Q(carrier_type=carrier, tracking_number__isnull=False)
            elif carrier in SLOW_UPDATE_CARRIERS:
                q_filter |= Q(carrier_type=carrier, bol_number__isnull=False)

        shipments = (
            Shipment.objects.filter(
                carrier_type__in=carriers,
                status__in=[ShipmentStatus.SHIPPED],
            )
            .filter(q_filter)
            .select_related("purchase_order")
        )

        shipment_count = shipments.count()
        logger.info(
            f"{task_name} - Found {shipment_count} pending shipments to update for carriers: {[c.value for c in carriers]}"
        )

        successful_count = 0
        failed_count = 0
        slow_carrier_count = 0

        for index, shipment in enumerate(shipments):
            logger.info(
                f"{task_name} - Processing shipment {index + 1}/{shipment_count} - "
                f"ID: {shipment.pk}, Carrier: {shipment.carrier_type}, "
                f"Tracking: {shipment.tracking_number or shipment.bol_number}"
            )

            success = _process_single_shipment(shipment)
            if success:
                successful_count += 1
            else:
                failed_count += 1

            # Add delay for slow carriers to avoid rate limiting
            if shipment.carrier_type in SLOW_UPDATE_CARRIERS:
                slow_carrier_count += 1
                # Only add delay if not the last shipment
                if index < shipment_count - 1:
                    logger.info(
                        f"{task_name} - Adding {API_DELAY} second delay after {shipment.carrier_type} shipment "
                        f"to avoid rate limiting..."
                    )
                    time.sleep(API_DELAY)

        logger.info(
            f"{task_name} - Completed processing {shipment_count} shipments. "
            f"Successful: {successful_count}, Failed: {failed_count}, "
            f"Slow carrier shipments: {slow_carrier_count}"
        )

    except Exception as e:
        logger.error(f"{task_name} - Error processing shipment updates: {e}")


@shared_task(queue="periodic_tasks")
def process_carrier_shipments(carrier_type: str, shipment_ids: List[int]):
    """
    Process shipments for a single carrier type.
    This task is designed to be run in parallel for different carriers.

    Args:
        carrier_type: The carrier type as a string (e.g., "ZIM", "ONE")
        shipment_ids: List of shipment IDs to process for this carrier
    """
    carrier = CarrierType(carrier_type)
    task_name = f"Process {carrier_type} Shipments"

    try:
        shipments = Shipment.objects.filter(pk__in=shipment_ids).select_related(
            "purchase_order"
        )

        shipment_count = len(shipment_ids)
        logger.info(
            f"{task_name} - Starting to process {shipment_count} shipments for {carrier_type}"
        )

        successful_count = 0
        failed_count = 0

        for index, shipment in enumerate(shipments):
            logger.info(
                f"{task_name} - Processing shipment {index + 1}/{shipment_count} - "
                f"ID: {shipment.pk}, Tracking: {shipment.tracking_number or shipment.bol_number}"
            )

            success = _process_single_shipment(shipment)
            if success:
                successful_count += 1
            else:
                failed_count += 1

            # Add delay for slow carriers to avoid rate limiting
            # Only add delay if not the last shipment
            if carrier in SLOW_UPDATE_CARRIERS and index < shipment_count - 1:
                logger.info(
                    f"{task_name} - Adding {API_DELAY} second delay to avoid rate limiting..."
                )
                time.sleep(API_DELAY)

        logger.info(
            f"{task_name} - Completed processing. "
            f"Successful: {successful_count}, Failed: {failed_count}"
        )

        return {
            "carrier": carrier_type,
            "total": shipment_count,
            "successful": successful_count,
            "failed": failed_count,
        }

    except Exception as e:
        logger.error(f"{task_name} - Error processing shipments: {e}")
        return {
            "carrier": carrier_type,
            "total": len(shipment_ids),
            "successful": 0,
            "failed": len(shipment_ids),
            "error": str(e),
        }


@shared_task(base=Singleton, lock_expiry=60 * 30, queue="periodic_tasks")
def update_fast_carriers():
    """Update shipments for carriers that need frequent updates (every 15 minutes)."""
    _process_shipments_for_carriers(FAST_UPDATE_CARRIERS, "Update Fast Carriers Task")


@shared_task(base=Singleton, lock_expiry=60 * 30 * 4, queue="periodic_tasks")
def update_slow_carriers():
    """Update shipments for carriers that need less frequent updates (twice daily)."""
    task_name = "Update Slow Carriers Task"

    try:
        # Build dynamic Q filter for slow carriers
        q_filter = Q()
        for carrier in SLOW_UPDATE_CARRIERS:
            q_filter |= Q(carrier_type=carrier, bol_number__isnull=False)

        # Get all pending shipments for slow carriers
        shipments = (
            Shipment.objects.filter(
                carrier_type__in=SLOW_UPDATE_CARRIERS,
                status__in=[ShipmentStatus.SHIPPED],
            )
            .filter(q_filter)
            .values("id", "carrier_type")
        )

        # Group shipments by carrier
        carrier_shipments = {}
        for shipment in shipments:
            carrier_type = shipment["carrier_type"]
            if carrier_type not in carrier_shipments:
                carrier_shipments[carrier_type] = []
            carrier_shipments[carrier_type].append(shipment["id"])

        if not carrier_shipments:
            logger.info(f"{task_name} - No pending shipments found for slow carriers")
            return

        # Create parallel tasks for each carrier
        job = group(
            process_carrier_shipments.s(carrier_type, shipment_ids)
            for carrier_type, shipment_ids in carrier_shipments.items()
        )

        # Execute all carrier tasks in parallel
        result = job.apply_async()

        logger.info(
            f"{task_name} - Spawned {len(carrier_shipments)} parallel tasks for carriers: "
            f"{list(carrier_shipments.keys())}"
        )

        return {
            "carriers_processed": list(carrier_shipments.keys()),
            "total_shipments": sum(len(ids) for ids in carrier_shipments.values()),
            "task_group_id": result.id if hasattr(result, "id") else None,
        }

    except Exception as e:
        logger.error(f"{task_name} - Error spawning carrier tasks: {e}")
        raise


@shared_task(base=Singleton, lock_expiry=60 * 30 * 4, queue="periodic_tasks")
def update_pending_shipments():
    """
    Update all pending shipments regardless of carrier.
    This is useful for manual triggers via admin or when you want to update all carriers at once.
    """
    _process_shipments_for_carriers(
        SUPPORTED_CARRIERS, "Update All Pending Shipments Task"
    )
