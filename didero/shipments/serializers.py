from rest_framework import serializers

from didero.orders.models import Shipment
from didero.shipments.models import import_shipments_from_csv


class ShipmentExportSerializer(serializers.ModelSerializer):
    """Serializer for exporting shipments to CSV."""

    port_of_departure = serializers.SerializerMethodField()
    port_of_arrival = serializers.SerializerMethodField()

    class Meta:
        model = Shipment
        fields = [
            "tracking_number",
            "bol_number",
            "container_number",
            "carrier_type",
            "shipment_date",
            "estimated_delivery_date",
            "actual_delivery_date",
            "port_of_departure",
            "port_of_arrival",
        ]

    def get_port_of_departure(self, obj):
        return obj.metadata.get("port_of_departure", "") if obj.metadata else ""

    def get_port_of_arrival(self, obj):
        return obj.metadata.get("port_of_arrival", "") if obj.metadata else ""


class BulkShipmentImportSerializer(serializers.Serializer):
    """Serializer for bulk importing shipments from CSV file."""

    file = serializers.FileField(required=True)

    def validate_file(self, value):
        if not value.name.endswith(".csv"):
            raise serializers.ValidationError("File must be a CSV file.")

        # Check file size (limit to 10MB)
        if value.size > 10 * 1024 * 1024:
            raise serializers.ValidationError("File size must not exceed 10MB.")

        return value

    def save(self, team):
        """Process the CSV file and create/update shipments."""
        file = self.validated_data["file"]

        # Read CSV content
        content = file.read()
        if isinstance(content, bytes):
            content = content.decode("utf-8-sig")  # Handle BOM if present

        # Delegate to the model function
        return import_shipments_from_csv(content, team)
