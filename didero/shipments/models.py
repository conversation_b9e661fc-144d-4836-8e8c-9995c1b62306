from datetime import datetime

from didero.orders.models import Shipment, ShipmentStatus
from didero.orders.schemas import CarrierType


def import_shipments_from_csv(csv_content: str, team) -> dict:
    """
    Import shipments from CSV content.

    Args:
        csv_content: The CSV file content as a string
        team: The team to scope shipments to

    Returns:
        Dictionary with 'updated' count, 'created' count and 'errors' list
    """
    import csv
    import io

    from django.db import transaction

    csv_file = io.StringIO(csv_content)
    reader = csv.DictReader(csv_file)

    results = {"updated": 0, "created": 0, "errors": []}

    with transaction.atomic():
        for row_num, row in enumerate(reader, start=2):
            try:
                cleaned_row = {
                    k.strip(): v.strip() if v else "" for k, v in row.items()
                }

                tracking_number = cleaned_row.get("tracking_number", "").strip()
                bol_number = cleaned_row.get("bol_number", "").strip()
                container_number = cleaned_row.get("container_number", "").strip()
                carrier_type_raw = cleaned_row.get("carrier_type", "").strip()

                if not tracking_number and not bol_number and not container_number:
                    results["errors"].append(
                        {
                            "row": row_num,
                            "errors": {
                                "identifiers": "At least one of tracking_number, bol_number, or container_number is required"
                            },
                        }
                    )
                    continue

                if not carrier_type_raw:
                    results["errors"].append(
                        {
                            "row": row_num,
                            "errors": {"carrier_type": "Carrier type is required"},
                        }
                    )
                    continue

                carrier_type_lower = carrier_type_raw.lower()
                try:
                    CarrierType(carrier_type_lower)
                except ValueError:
                    results["errors"].append(
                        {
                            "row": row_num,
                            "errors": {
                                "carrier_type": f"Invalid carrier type. Must be one of: {', '.join([c.value for c in CarrierType])}"
                            },
                        }
                    )
                    continue

                shipment = None

                if tracking_number:
                    shipment = Shipment.objects.filter(
                        tracking_number=tracking_number,
                        carrier_type=carrier_type_lower,
                        team=team,
                    ).first()

                if not shipment and bol_number:
                    shipment = Shipment.objects.filter(
                        bol_number=bol_number,
                        carrier_type=carrier_type_lower,
                        team=team,
                    ).first()

                if not shipment and container_number:
                    shipment = Shipment.objects.filter(
                        container_number=container_number,
                        carrier_type=carrier_type_lower,
                        team=team,
                    ).first()

                created_new = False
                if not shipment:
                    shipment = Shipment.objects.create(
                        tracking_number=tracking_number or None,
                        bol_number=bol_number or None,
                        container_number=container_number or None,
                        carrier_type=carrier_type_lower,
                        team=team,
                        purchase_order=None,
                        status=ShipmentStatus.SHIPPED,
                    )
                    results["created"] += 1
                    created_new = True

                updated = False

                if tracking_number and shipment.tracking_number != tracking_number:
                    shipment.tracking_number = tracking_number
                    updated = True

                if bol_number and shipment.bol_number != bol_number:
                    shipment.bol_number = bol_number
                    updated = True

                if container_number and shipment.container_number != container_number:
                    shipment.container_number = container_number
                    updated = True

                date_formats = ["%Y-%m-%d", "%m/%d/%Y", "%d/%m/%Y"]
                date_fields = [
                    ("shipment_date", "shipment_date"),
                    ("estimated_delivery_date", "estimated_delivery_date"),
                    ("actual_delivery_date", "actual_delivery_date"),
                ]

                for csv_field, model_field in date_fields:
                    date_str = cleaned_row.get(csv_field)
                    if date_str:
                        parsed_date = None
                        for date_format in date_formats:
                            try:
                                parsed_date = datetime.strptime(
                                    date_str, date_format
                                ).date()
                                break
                            except ValueError:
                                continue

                        if parsed_date:
                            setattr(shipment, model_field, parsed_date)
                            updated = True
                        else:
                            results["errors"].append(
                                {
                                    "row": row_num,
                                    "errors": {
                                        csv_field: "Invalid date format. Use YYYY-MM-DD, MM/DD/YYYY, or DD/MM/YYYY"
                                    },
                                }
                            )
                            continue

                if cleaned_row.get("port_of_departure"):
                    if "metadata" not in shipment.__dict__ or shipment.metadata is None:
                        shipment.metadata = {}
                    shipment.metadata["port_of_departure"] = cleaned_row[
                        "port_of_departure"
                    ]
                    updated = True

                if cleaned_row.get("port_of_arrival"):
                    if "metadata" not in shipment.__dict__ or shipment.metadata is None:
                        shipment.metadata = {}
                    shipment.metadata["port_of_arrival"] = cleaned_row[
                        "port_of_arrival"
                    ]
                    updated = True

                if updated:
                    shipment.save()
                    if not created_new:
                        results["updated"] += 1

            except Exception as e:
                results["errors"].append(
                    {"row": row_num, "errors": {"general": str(e)}}
                )

    return results
