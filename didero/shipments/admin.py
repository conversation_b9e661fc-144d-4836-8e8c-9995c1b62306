import csv
import io
from datetime import datetime

from django import forms
from django.contrib import admin, messages
from django.http import HttpResponse
from django.shortcuts import redirect, render
from django.urls import path, reverse

from didero.orders.models import Shipment
from didero.shipments.models import import_shipments_from_csv
from didero.shipments.serializers import ShipmentExportSerializer
from didero.shipments.tasks import update_pending_shipments
from didero.users.models import Team


class CsvImportForm(forms.Form):
    csv_file = forms.FileField(label="Select a CSV file")
    team = forms.ChoiceField(label="Team", choices=[])

    def __init__(self, *args, **kwargs):
        teams = kwargs.pop("teams", [])
        super().__init__(*args, **kwargs)
        self.fields["team"].choices = [(t.id, t.name) for t in teams]


@admin.register(Shipment)
class ShipmentAdmin(admin.ModelAdmin):
    list_display = ("id", "team", "carrier_type", "get_identifier", "status")
    change_list_template = "admin/shipments/shipment_changelist.html"

    def get_identifier(self, obj):
        return obj.tracking_number or obj.bol_number or obj.container_number or "-"

    get_identifier.short_description = "Identifier"

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        # Filter by team if specified in query params
        team_id = request.GET.get("team")
        if team_id:
            return qs.filter(team_id=team_id).select_related("team")
        return qs.select_related("team")

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path(
                "import-csv/",
                self.admin_site.admin_view(self.import_csv),
                name="orders_shipment_import_csv",
            ),
            path(
                "export-csv/",
                self.admin_site.admin_view(self.export_csv),
                name="orders_shipment_export_csv",
            ),
            path(
                "download-template/",
                self.admin_site.admin_view(self.download_template),
                name="orders_shipment_download_template",
            ),
            path(
                "trigger-update/",
                self.admin_site.admin_view(self.trigger_update),
                name="orders_shipment_trigger_update",
            ),
        ]
        return custom_urls + urls

    def import_csv(self, request):
        """Handle CSV import for a specific team."""
        teams = Team.objects.all()

        if request.method == "POST":
            form = CsvImportForm(request.POST, request.FILES, teams=teams)
            if form.is_valid():
                csv_file = request.FILES["csv_file"]
                team_id = form.cleaned_data["team"]
                team = Team.objects.filter(id=team_id).first()

                if not team:
                    messages.error(request, "Invalid team selected")
                    return redirect("..")

                try:
                    decoded_file = csv_file.read().decode("utf-8")
                    result = import_shipments_from_csv(decoded_file, team)

                    messages.success(
                        request,
                        f"Import complete - Created: {result['created']}, Updated: {result['updated']}, Errors: {result['errors']}",
                    )

                except Exception as e:
                    messages.error(request, f"Error: {str(e)}")

                return redirect("..")
        else:
            form = CsvImportForm(teams=teams)

        return render(
            request,
            "admin/shipments/csv_import.html",
            {
                "form": form,
                "opts": self.model._meta,
            },
        )

    def export_csv(self, request):
        """Export shipments for a specific team."""
        team_id = request.GET.get("team")
        if not team_id:
            messages.error(request, "Please select a team")
            return redirect(reverse("admin:orders_shipment_changelist"))

        team = Team.objects.filter(id=team_id).first()
        if not team:
            messages.error(request, "Invalid team")
            return redirect(reverse("admin:orders_shipment_changelist"))

        shipments = Shipment.objects.filter(team=team)

        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = (
            f'attachment; filename="shipments_{team.name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv"'
        )

        writer = csv.writer(response)
        writer.writerow(
            [
                "tracking_number",
                "bol_number",
                "container_number",
                "carrier_type",
                "shipment_date",
                "estimated_delivery_date",
                "actual_delivery_date",
                "port_of_departure",
                "port_of_arrival",
            ]
        )

        for shipment in shipments:
            serializer = ShipmentExportSerializer(shipment)
            data = serializer.data
            writer.writerow(
                [
                    data["tracking_number"],
                    data["bol_number"],
                    data["container_number"],
                    data["carrier_type"],
                    data["shipment_date"],
                    data["estimated_delivery_date"],
                    data["actual_delivery_date"],
                    data["port_of_departure"],
                    data["port_of_arrival"],
                ]
            )

        return response

    def download_template(self, request):
        """Download CSV template."""
        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = (
            'attachment; filename="shipments_template.csv"'
        )

        writer = csv.writer(response)
        # Header row
        writer.writerow(
            [
                "tracking_number",
                "bol_number",
                "container_number",
                "carrier_type",
                "shipment_date",
                "estimated_delivery_date",
                "actual_delivery_date",
                "port_of_departure",
                "port_of_arrival",
            ]
        )
        # Example row
        writer.writerow(
            [
                "1234567890",
                "BOL123456",
                "CONT123456",
                "FEDEX",
                "2024-01-15",
                "2024-01-20",
                "2024-01-19",
                "Shanghai, China",
                "Los Angeles, USA",
            ]
        )

        return response

    def trigger_update(self, request):
        """Trigger tracking update for a specific team."""
        team_id = request.GET.get("team")
        if not team_id:
            messages.error(request, "Please select a team")
            return redirect(reverse("admin:orders_shipment_changelist"))

        team = Team.objects.filter(id=team_id).first()
        if not team:
            messages.error(request, "Invalid team")
            return redirect(reverse("admin:orders_shipment_changelist"))

        result = update_pending_shipments.delay()
        messages.success(
            request, f"Tracking update triggered for {team.name} - Task ID: {result.id}"
        )

        return redirect(reverse("admin:orders_shipment_changelist"))

    def changelist_view(self, request, extra_context=None):
        extra_context = extra_context or {}
        extra_context["teams"] = Team.objects.all()
        extra_context["selected_team"] = request.GET.get("team", "")
        return super().changelist_view(request, extra_context=extra_context)
