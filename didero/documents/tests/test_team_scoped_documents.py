"""
Test team-scoped document deduplication to ensure teams can upload the same document
without conflicts when setting it as a primary PO document.
"""

from django.contrib.contenttypes.models import ContentType
from django.core.files.base import ContentFile
from django.test import TestCase

from didero.documents.models import Document, DocumentLink
from didero.orders.models import PurchaseOrder
from didero.orders.schemas import PaymentStatus, PurchaseOrderStatus, SourceChoices
from didero.suppliers.models import Communication, Supplier
from didero.users.models import Team, User


class TeamScopedDocumentTestCase(TestCase):
    def setUp(self):
        """Set up test data for team-scoped document tests."""
        # Create two teams
        self.team_a = Team.objects.create(name="Team A")
        self.team_b = Team.objects.create(name="Team B")

        # Create users for each team
        self.user_a = User.objects.create(
            email="<EMAIL>",
            first_name="User",
            last_name="A",
        )
        self.user_a.teams.add(self.team_a)

        self.user_b = User.objects.create(
            email="<EMAIL>",
            first_name="User",
            last_name="B",
        )
        self.user_b.teams.add(self.team_b)

        # Create suppliers for each team
        self.supplier_a = Supplier.objects.create(
            name="Supplier A",
            team=self.team_a,
        )

        self.supplier_b = Supplier.objects.create(
            name="Supplier B",
            team=self.team_b,
        )

        # Create identical PDF content that both teams will upload
        self.pdf_content = b"PDF content that is the same for both teams"
        self.pdf_filename = "purchase_order.pdf"

    def test_teams_can_have_same_document_content(self):
        """Test that two teams can upload documents with the same content."""
        # Team A uploads the document
        file_a = ContentFile(self.pdf_content, name=self.pdf_filename)
        doc_a = Document.get_or_create_from_file(
            file_a,
            team=self.team_a,
            name=self.pdf_filename,
            content_type="application/pdf",
        )

        # Team B uploads the same document
        file_b = ContentFile(self.pdf_content, name=self.pdf_filename)
        doc_b = Document.get_or_create_from_file(
            file_b,
            team=self.team_b,
            name=self.pdf_filename,
            content_type="application/pdf",
        )

        # Verify they are different document objects
        self.assertNotEqual(doc_a.pk, doc_b.pk)

        # Verify they have the same content hash
        self.assertEqual(doc_a.content_hash, doc_b.content_hash)

        # Verify they belong to different teams
        self.assertEqual(doc_a.team, self.team_a)
        self.assertEqual(doc_b.team, self.team_b)

    def test_purchase_orders_can_use_team_scoped_documents(self):
        """Test that POs from different teams can use documents with same content."""
        # Create documents for each team
        file_a = ContentFile(self.pdf_content, name=self.pdf_filename)
        doc_a = Document.get_or_create_from_file(
            file_a,
            team=self.team_a,
            name=self.pdf_filename,
            content_type="application/pdf",
        )

        file_b = ContentFile(self.pdf_content, name=self.pdf_filename)
        doc_b = Document.get_or_create_from_file(
            file_b,
            team=self.team_b,
            name=self.pdf_filename,
            content_type="application/pdf",
        )

        # Create POs for each team
        po_a = PurchaseOrder.objects.create(
            po_number="PO-001-A",
            team=self.team_a,
            supplier=self.supplier_a,
            order_status=PurchaseOrderStatus.DRAFT.value,
            payment_status=PaymentStatus.UNPAID.value,
            source=SourceChoices.EMAIL.value,
            document=doc_a,  # Assign team A's document
        )

        po_b = PurchaseOrder.objects.create(
            po_number="PO-001-B",
            team=self.team_b,
            supplier=self.supplier_b,
            order_status=PurchaseOrderStatus.DRAFT.value,
            payment_status=PaymentStatus.UNPAID.value,
            source=SourceChoices.EMAIL.value,
            document=doc_b,  # Assign team B's document
        )

        # Verify both POs have their respective documents
        self.assertEqual(po_a.document, doc_a)
        self.assertEqual(po_b.document, doc_b)

        # Verify no constraint violations occurred
        self.assertTrue(PurchaseOrder.objects.filter(pk=po_a.pk).exists())
        self.assertTrue(PurchaseOrder.objects.filter(pk=po_b.pk).exists())

    def test_document_already_assigned_protection(self):
        """Test that a document cannot be assigned to multiple POs."""
        # Create a document and assign it to a PO
        file_obj = ContentFile(self.pdf_content, name=self.pdf_filename)
        doc = Document.get_or_create_from_file(
            file_obj,
            team=self.team_a,
            name=self.pdf_filename,
            content_type="application/pdf",
        )

        po1 = PurchaseOrder.objects.create(
            po_number="PO-001",
            team=self.team_a,
            supplier=self.supplier_a,
            order_status=PurchaseOrderStatus.DRAFT.value,
            payment_status=PaymentStatus.UNPAID.value,
            source=SourceChoices.EMAIL.value,
            document=doc,
        )

        # Try to create another PO with the same document
        # This should fail with integrity error due to OneToOne constraint
        with self.assertRaises(Exception):  # Will be IntegrityError
            po2 = PurchaseOrder.objects.create(
                po_number="PO-002",
                team=self.team_a,
                supplier=self.supplier_a,
                order_status=PurchaseOrderStatus.DRAFT.value,
                payment_status=PaymentStatus.UNPAID.value,
                source=SourceChoices.EMAIL.value,
                document=doc,  # Same document - should fail
            )

    def test_document_queryset_team_filtering(self):
        """Test that Document.objects queries are properly filtered by team."""
        # Create documents for both teams
        file_a = ContentFile(self.pdf_content, name=self.pdf_filename)
        doc_a = Document.get_or_create_from_file(
            file_a,
            team=self.team_a,
            name=self.pdf_filename,
            content_type="application/pdf",
        )

        file_b = ContentFile(self.pdf_content, name=self.pdf_filename)
        doc_b = Document.get_or_create_from_file(
            file_b,
            team=self.team_b,
            name=self.pdf_filename,
            content_type="application/pdf",
        )

        # Team A should only see their document
        team_a_docs = Document.objects.filter(team=self.team_a)
        self.assertIn(doc_a, team_a_docs)
        self.assertNotIn(doc_b, team_a_docs)

        # Team B should only see their document
        team_b_docs = Document.objects.filter(team=self.team_b)
        self.assertIn(doc_b, team_b_docs)
        self.assertNotIn(doc_a, team_b_docs)

        # Without team filter, both documents are visible (dangerous!)
        all_docs = Document.objects.all()
        self.assertIn(doc_a, all_docs)
        self.assertIn(doc_b, all_docs)

        # Test content hash lookup with team filter (secure)
        team_a_doc_by_hash = Document.objects.filter(
            content_hash=doc_a.content_hash, team=self.team_a
        ).first()
        self.assertEqual(team_a_doc_by_hash, doc_a)

        # Test that team B cannot access team A's document by hash
        team_b_cannot_access = Document.objects.filter(
            content_hash=doc_a.content_hash, team=self.team_b
        ).first()
        self.assertNotEqual(team_b_cannot_access, doc_a)  # Different document
        self.assertEqual(team_b_cannot_access, doc_b)  # It's team B's copy
