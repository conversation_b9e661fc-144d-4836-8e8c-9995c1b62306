# Generated by Django 4.2.7 on 2025-07-18 19:14

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("documents", "0020_add_archived_at_field"),
    ]

    operations = [
        migrations.CreateModel(
            name="DocumentItemMatch",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "first_doc_type",
                    models.CharField(
                        choices=[
                            ("comply", "comply"),
                            ("contract", "contract"),
                            ("delivery_conf", "delivery_conf"),
                            ("external_po_import", "external_po_import"),
                            ("goodsrct", "goodsrct"),
                            ("invoice", "invoice"),
                            ("nda", "nda"),
                            ("order_ack", "order_ack"),
                            ("pickup_sheet", "pickup_sheet"),
                            ("shipping_order", "shipping_order"),
                            ("delivery_note", "delivery_note"),
                            ("despatch_list", "despatch_list"),
                            ("other", "other"),
                            ("payment_conf", "payment_conf"),
                            ("po", "po"),
                            ("price_list", "price_list"),
                            ("processing", "processing"),
                            ("quote_import", "quote_import"),
                            ("unknown", "unknown"),
                        ],
                        db_index=True,
                        max_length=50,
                    ),
                ),
                ("first_item_number", models.CharField(db_index=True, max_length=255)),
                ("first_item_description", models.TextField()),
                (
                    "second_doc_type",
                    models.CharField(
                        choices=[
                            ("comply", "comply"),
                            ("contract", "contract"),
                            ("delivery_conf", "delivery_conf"),
                            ("external_po_import", "external_po_import"),
                            ("goodsrct", "goodsrct"),
                            ("invoice", "invoice"),
                            ("nda", "nda"),
                            ("order_ack", "order_ack"),
                            ("pickup_sheet", "pickup_sheet"),
                            ("shipping_order", "shipping_order"),
                            ("delivery_note", "delivery_note"),
                            ("despatch_list", "despatch_list"),
                            ("other", "other"),
                            ("payment_conf", "payment_conf"),
                            ("po", "po"),
                            ("price_list", "price_list"),
                            ("processing", "processing"),
                            ("quote_import", "quote_import"),
                            ("unknown", "unknown"),
                        ],
                        db_index=True,
                        max_length=50,
                    ),
                ),
                ("second_item_number", models.CharField(db_index=True, max_length=255)),
                ("second_item_description", models.TextField()),
                ("is_match", models.BooleanField()),
                ("team_id", models.IntegerField(db_index=True)),
                (
                    "supplier_id",
                    models.IntegerField(blank=True, db_index=True, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["first_doc_type", "first_item_number", "team_id"],
                        name="documents_d_first_d_f79209_idx",
                    ),
                    models.Index(
                        fields=["second_doc_type", "second_item_number", "team_id"],
                        name="documents_d_second__43e373_idx",
                    ),
                    models.Index(
                        fields=["team_id", "is_match"],
                        name="documents_d_team_id_4b8bd1_idx",
                    ),
                ],
                "unique_together": {
                    (
                        "first_doc_type",
                        "first_item_number",
                        "second_doc_type",
                        "second_item_number",
                        "team_id",
                    )
                },
            },
        ),
    ]
