from typing import Any, Dict, Optional

import structlog
from django.db.models.signals import post_save
from django.dispatch import receiver

from didero.tasks.models import Task, TaskStatus

logger = structlog.get_logger(__name__)


@receiver(post_save, sender=Task)
def handle_task_completion(sender, instance, **kwargs):
    """Handle task completion - Phase 1: Basic document upload linking"""

    try:
        logger.debug(
            "Task post_save signal triggered",
            task_id=str(instance.id),
            status=instance.status,
            task_type_v2=instance.task_type_v2.name if instance.task_type_v2 else None,
        )

        if instance.status != TaskStatus.COMPLETED.value:
            return

        # Only handle TaskTypeV2-based tasks
        if not instance.task_type_v2:
            return

        # Get team_id from task_params
        task_params = instance.task_config.get("task_params", {})
        team_id = task_params.get("team_id")

        logger.info(
            "Processing completed task",
            task_id=str(instance.id),
            task_type=instance.task_type_v2.name,
        )

        # Check if this is a document upload task
        if instance.task_type_v2.name == "DOCUMENT_UPLOAD_REQUEST":
            logger.info(
                "Processing document upload task completion",
                task_id=str(instance.id),
            )
            # Handle document upload task completion
            _handle_document_upload_completion(instance)
        else:
            # Future: Handle other task types and workflow callbacks
            # This is where Phase 3 workflow integration will go
            pass

    except Exception as e:
        logger.error(
            "Error in task completion signal handler",
            task_id=str(instance.id),
            error=str(e),
            exc_info=True,
        )


def _handle_document_upload_completion(task_instance):
    """Handle completion of document upload tasks - Signal-based implementation"""

    from asgiref.sync import async_to_sync

    from didero.workflows.signals import (
        send_document_upload_failure_signal,
        send_document_upload_success_signal,
    )

    # Get task params once at the beginning
    task_params = task_instance.task_config.get("task_params", {})
    workflow_id = task_params.get("workflow_id")

    try:
        # Get completion data from task_config.action_data (where frontend will send it)
        action_data = task_instance.task_config.get("action_data", {})
        # Try both UPLOAD_DOCUMENT (original) and upload_document (camelCase converted)
        completion_data = action_data.get("UPLOAD_DOCUMENT", {}) or action_data.get(
            "upload_document", {}
        )

        uploaded_document_id = completion_data.get("uploaded_document_id")
        document_type = completion_data.get("document_type")

        # Ensure uploaded_document_id is a string (frontend might send integer)
        if uploaded_document_id is not None:
            uploaded_document_id = str(uploaded_document_id)

        logger.debug(
            "Processing document upload completion",
            task_id=str(task_instance.id),
            uploaded_document_id=uploaded_document_id,
            document_type=document_type,
        )

        if not uploaded_document_id:
            logger.warning(
                "Document upload task completed but no uploaded_document_id found",
                task_id=str(task_instance.id),
                action_data_keys=list(action_data.keys()),
            )
            return

        if not workflow_id:
            logger.warning(
                "No workflow_id found in task params - cannot send signal",
                task_id=str(task_instance.id),
            )
            # Fall back to polling-based approach for backwards compatibility
            return

        logger.info(
            "Document upload completed, sending signal to workflow",
            task_id=str(task_instance.id),
            uploaded_document_id=uploaded_document_id,
            document_type=document_type,
            workflow_id=workflow_id,
        )

        # Process document based on type
        success = async_to_sync(_process_document_by_type)(
            document_type=document_type,
            uploaded_document_id=uploaded_document_id,
            task_params=task_params,
            workflow_id=workflow_id,
            task_id=str(task_instance.id),
        )

        if not success:
            # Document processing failed - error already logged in the specific handler
            async_to_sync(send_document_upload_failure_signal)(
                workflow_id=workflow_id,
                error_message=f"Failed to process {document_type} document upload",
            )

    except Exception as e:
        logger.error(
            "Error handling document upload completion",
            task_id=str(task_instance.id),
            error=str(e),
            exc_info=True,
        )

        # Send failure signal if we have a workflow_id
        if workflow_id:
            async_to_sync(send_document_upload_failure_signal)(
                workflow_id=workflow_id,
                error_message=f"Failed to process document upload: {str(e)}",
            )


async def _trigger_po_creation_and_get_result(
    uploaded_document_id: str,
    task_params: dict,
) -> Optional[Dict[str, Any]]:
    """
    Trigger PO creation workflow and wait for result.

    Returns:
        Dict with po_id and po_number if successful, None otherwise
    """
    from didero.workflows.utils import (
        get_temporal_client,
        trigger_po_creation_workflow_for_document,
    )

    try:
        team_id = task_params.get("team_id")
        if not team_id:
            logger.error("No team_id in task params")
            return None

        # Start the workflow using existing function and get the workflow ID
        temporal_id = await trigger_po_creation_workflow_for_document(
            document_id=uploaded_document_id,
            team_id=str(team_id),  # Convert to string as expected by the workflow
        )

        # Wait for workflow completion and get result
        client = await get_temporal_client()
        workflow_handle = client.get_workflow_handle(temporal_id)

        # Wait for workflow to complete (with timeout)
        import asyncio

        result = await asyncio.wait_for(
            workflow_handle.result(), timeout=300
        )  # 5 minute timeout

        if result and result.get("success"):
            # Extract PO details from workflow result
            po_number = result.get("po_number")
            po_id = result.get("po_id")

            if po_number and po_id:
                # Return basic PO info - the receiving workflow will fetch full details
                po_basic_info = {
                    "po_number": po_number,
                    "po_id": po_id,
                    "created_in_draft": result.get("created_in_draft", False),
                    "task_id": result.get("task_id"),
                }

                logger.info(
                    "PO creation workflow completed successfully",
                    document_id=uploaded_document_id,
                    team_id=team_id,
                    po_number=po_number,
                    po_id=po_id,
                )

                return po_basic_info

        logger.warning(
            "PO creation workflow completed but no PO created",
            document_id=uploaded_document_id,
            team_id=team_id,
            result=result,
        )
        return None

    except Exception as e:
        logger.error(
            "Error triggering PO creation workflow",
            document_id=uploaded_document_id,
            error=str(e),
            exc_info=True,
        )
        return None


async def _process_document_by_type(
    document_type: str,
    uploaded_document_id: str,
    task_params: dict,
    workflow_id: str,
    task_id: str,
) -> bool:
    """
    Process document upload based on document type.

    Args:
        document_type: Type of document (purchase_order, invoice, etc.)
        uploaded_document_id: ID of the uploaded document
        task_params: Task parameters containing team_id, etc.
        workflow_id: ID of the workflow to signal
        task_id: ID of the task for logging

    Returns:
        True if processing succeeded, False otherwise
    """
    from asgiref.sync import async_to_sync

    from didero.workflows.signals import send_document_upload_success_signal

    try:
        # Route to appropriate document processor
        if document_type == "purchase_order":
            return await _process_purchase_order_document(
                uploaded_document_id=uploaded_document_id,
                task_params=task_params,
                workflow_id=workflow_id,
                task_id=task_id,
            )
        elif document_type == "invoice":
            # TODO: Implement invoice document processing
            logger.info(
                "Invoice document processing not yet implemented", task_id=task_id
            )
            return False
        elif document_type == "shipment":
            # TODO: Implement shipment document processing
            logger.info(
                "Shipment document processing not yet implemented", task_id=task_id
            )
            return False
        else:
            logger.warning(
                "Unknown document type for processing",
                task_id=task_id,
                document_type=document_type,
            )
            return False

    except Exception as e:
        logger.error(
            "Error in document type processing",
            task_id=task_id,
            document_type=document_type,
            error=str(e),
            exc_info=True,
        )
        return False


async def _process_purchase_order_document(
    uploaded_document_id: str,
    task_params: dict,
    workflow_id: str,
    task_id: str,
) -> bool:
    """
    Process purchase order document upload.

    Args:
        uploaded_document_id: ID of the uploaded document
        task_params: Task parameters containing team_id, etc.
        workflow_id: ID of the workflow to signal
        task_id: ID of the task for logging

    Returns:
        True if processing succeeded, False otherwise
    """
    from didero.workflows.signals import send_document_upload_success_signal

    try:
        po_basic_info = await _trigger_po_creation_and_get_result(
            uploaded_document_id=uploaded_document_id,
            task_params=task_params,
        )

        if po_basic_info:
            # Send basic PO info via signal - receiving workflow will fetch full details
            await send_document_upload_success_signal(
                workflow_id=workflow_id,
                document_data=po_basic_info,
            )
            logger.info(
                "Successfully created PO via workflow and sent signal",
                task_id=task_id,
                workflow_id=workflow_id,
                po_number=po_basic_info.get("po_number"),
                po_id=po_basic_info.get("po_id"),
            )
            return True
        else:
            logger.error(
                "PO creation workflow completed but no PO created",
                task_id=task_id,
            )
            return False

    except Exception as creation_error:
        logger.error(
            "Error during PO creation workflow",
            task_id=task_id,
            error=str(creation_error),
            exc_info=True,
        )
        return False
