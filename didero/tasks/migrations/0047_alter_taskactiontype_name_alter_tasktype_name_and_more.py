# Generated by Django 4.2.7 on 2025-07-07 19:40

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("tasks", "0046_alter_taskactiontype_name_alter_tasktypev2_category"),
    ]

    operations = [
        migrations.AlterField(
            model_name="taskactiontype",
            name="name",
            field=models.CharField(
                choices=[
                    ("SEND_EMAIL", "SEND_EMAIL"),
                    ("OPS_ADD_COMMENT", "OPS_ADD_COMMENT"),
                    ("ADD_COMMENT", "ADD_COMMENT"),
                    ("AUTO_COMMENT", "AUTO_COMMENT"),
                    ("UPDATE_NETSUITE", "UPDATE_NETSUITE"),
                    ("APPROVE_PURCHASE_ORDER", "APPROVE_PURCHASE_ORDER"),
                    ("DENY_PURCHASE_ORDER", "DENY_PURCHASE_ORDER"),
                    ("CONFIRM_PURCHASE_ORDER", "CONFIRM_PURCHASE_ORDER"),
                    ("CANCEL_PURCHASE_ORDER", "CANCEL_PURCHASE_ORDER"),
                    ("CONFIRM_ORDER_ACKNOWLEDGEMENT", "CONFIRM_ORDER_ACKNOWLEDGEMENT"),
                    ("REJECT_ORDER_ACKNOWLEDGEMENT", "REJECT_ORDER_ACKNOWLEDGEMENT"),
                    ("APPROVE_OA_MISMATCH", "APPROVE_OA_MISMATCH"),
                    ("APPROVE_FREIGHT_CHARGE", "APPROVE_FREIGHT_CHARGE"),
                    ("APPROVE_PRICE_CHANGE", "APPROVE_PRICE_CHANGE"),
                    ("APPROVE_ADDRESS_CHANGE", "APPROVE_ADDRESS_CHANGE"),
                    ("CONFIRM_SHIPMENT", "CONFIRM_SHIPMENT"),
                    ("CANCEL_SHIPMENT", "CANCEL_SHIPMENT"),
                    ("APPROVE_DOCUMENT_MATCH", "APPROVE_DOCUMENT_MATCH"),
                    (
                        "REQUEST_DOCUMENT_CLARIFICATION",
                        "REQUEST_DOCUMENT_CLARIFICATION",
                    ),
                    ("UPLOAD_DOCUMENT", "UPLOAD_DOCUMENT"),
                ],
                max_length=255,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="tasktype",
            name="name",
            field=models.CharField(
                choices=[
                    ("PO_APPROVAL_REQUEST", "PO_APPROVAL_REQUEST"),
                    ("PO_RECEIVED_CHECK", "PO_RECEIVED_CHECK"),
                    ("PO_CREATED_FROM_QUOTATION", "PO_CREATED_FROM_QUOTATION"),
                    ("PO_CREATION_CONFIRMATION", "PO_CREATION_CONFIRMATION"),
                    ("USER_MENTIONED_IN_COMMENT", "USER_MENTIONED_IN_COMMENT"),
                    (
                        "PO_STATUS_UPDATE_APPROVAL_DENIED",
                        "PO_STATUS_UPDATE_APPROVAL_DENIED",
                    ),
                    ("PO_STATUS_UPDATE_APPROVED", "PO_STATUS_UPDATE_APPROVED"),
                    (
                        "PO_STATUS_UPDATE_SUPPLIER_REJECTED",
                        "PO_STATUS_UPDATE_SUPPLIER_REJECTED",
                    ),
                    ("PO_STATUS_UPDATE_SHIPPED", "PO_STATUS_UPDATE_SHIPPED"),
                    ("PO_STATUS_UPDATE_RECEIVED", "PO_STATUS_UPDATE_RECEIVED"),
                    ("PO_STATUS_UPDATE_CANCELED", "PO_STATUS_UPDATE_CANCELED"),
                    ("PO_SUPPLIER_UNRESPONSIVE", "PO_SUPPLIER_UNRESPONSIVE"),
                    ("SHIP_DATE_CHANGED", "SHIP_DATE_CHANGED"),
                    (
                        "SUPPLIER_UNRESPONSIVE_TO_DIDERO_CHECKIN",
                        "SUPPLIER_UNRESPONSIVE_TO_DIDERO_CHECKIN",
                    ),
                    ("IMPORT_ITEMS_FROM_PRICE_LIST", "IMPORT_ITEMS_FROM_PRICE_LIST"),
                    (
                        "SUPPLIER_ONBOARDING_DOCUMENT_EXPIRING",
                        "SUPPLIER_ONBOARDING_DOCUMENT_EXPIRING",
                    ),
                    (
                        "SUPPLIER_ONBOARDING_DOCUMENT_EXPIRED",
                        "SUPPLIER_ONBOARDING_DOCUMENT_EXPIRED",
                    ),
                    ("DRAFT_EMAIL_SIMPLE_DISMISS", "DRAFT_EMAIL_SIMPLE_DISMISS"),
                    ("DRAFT_EMAIL_OR_COMMENT", "DRAFT_EMAIL_OR_COMMENT"),
                    ("PO_SHOULD_HAVE_SHIPPED", "PO_SHOULD_HAVE_SHIPPED"),
                    (
                        "MANUAL_NOTIFICATION_SIMPLE_DISMISS",
                        "MANUAL_NOTIFICATION_SIMPLE_DISMISS",
                    ),
                    (
                        "SHIPMENT_WORKFLOW_TRACKING_INFO_NOT_FOUND",
                        "SHIPMENT_WORKFLOW_TRACKING_INFO_NOT_FOUND",
                    ),
                    (
                        "SHIPMENT_WORKFLOW_NO_TRACKING_NUMBER_IN_SHIPMENT_UPDATE",
                        "SHIPMENT_WORKFLOW_NO_TRACKING_NUMBER_IN_SHIPMENT_UPDATE",
                    ),
                    (
                        "OPS_TASK_SHIPMENT_WORKFLOW_COMPLETED",
                        "OPS_TASK_SHIPMENT_WORKFLOW_COMPLETED",
                    ),
                    (
                        "ORDER_ACKNOWLEDGEMENT_WORKFLOW_COMPLETED",
                        "ORDER_ACKNOWLEDGEMENT_WORKFLOW_COMPLETED",
                    ),
                    (
                        "ORDER_ACKNOWLEDGEMENT_WORKFLOW_SUCCESS_NOTIFICATION",
                        "ORDER_ACKNOWLEDGEMENT_WORKFLOW_SUCCESS_NOTIFICATION",
                    ),
                    (
                        "PO_CREATION_WORKFLOW_SUCCESS_NOTIFICATION",
                        "PO_CREATION_WORKFLOW_SUCCESS_NOTIFICATION",
                    ),
                    (
                        "SHIPMENT_WORKFLOW_SUCCESS_NOTIFICATION",
                        "SHIPMENT_WORKFLOW_SUCCESS_NOTIFICATION",
                    ),
                    (
                        "PICKUP_WORKFLOW_SUCCESS_NOTIFICATION",
                        "PICKUP_WORKFLOW_SUCCESS_NOTIFICATION",
                    ),
                    ("ADDRESS_MISMATCH_CONFIRMATION", "ADDRESS_MISMATCH_CONFIRMATION"),
                    ("FREIGHT_CHARGE_CONFIRMATION", "FREIGHT_CHARGE_CONFIRMATION"),
                    ("PRICE_CHANGE_CONFIRMATION", "PRICE_CHANGE_CONFIRMATION"),
                    ("PO_CONFIRMATION_FOLLOWUP", "PO_CONFIRMATION_FOLLOWUP"),
                    ("SHIPPING_DETAILS_FOLLOWUP", "SHIPPING_DETAILS_FOLLOWUP"),
                    ("DELIVERY_DATE_CONFIRMATION", "DELIVERY_DATE_CONFIRMATION"),
                    ("PART_NUMBER_ETA_FOLLOWUP", "PART_NUMBER_ETA_FOLLOWUP"),
                    (
                        "ORDER_ACKNOWLEDGEMENT_VALIDATION_ERROR",
                        "ORDER_ACKNOWLEDGEMENT_VALIDATION_ERROR",
                    ),
                    (
                        "SHIPMENT_WORKFLOW_VALIDATION_ERROR",
                        "SHIPMENT_WORKFLOW_VALIDATION_ERROR",
                    ),
                    ("FOLLOW_UP", "FOLLOW_UP"),
                    ("ERP_SYNC_ERROR", "ERP_SYNC_ERROR"),
                    ("ERP_SYNC_SUCCESS", "ERP_SYNC_SUCCESS"),
                    ("DOCUMENT_MATCH_REVIEW", "DOCUMENT_MATCH_REVIEW"),
                    ("DOCUMENT_UPLOAD_REQUEST", "DOCUMENT_UPLOAD_REQUEST"),
                ],
                max_length=255,
            ),
        ),
        migrations.AlterField(
            model_name="tasktypev2",
            name="name",
            field=models.CharField(
                choices=[
                    ("PO_APPROVAL_REQUEST", "PO_APPROVAL_REQUEST"),
                    ("PO_RECEIVED_CHECK", "PO_RECEIVED_CHECK"),
                    ("PO_CREATED_FROM_QUOTATION", "PO_CREATED_FROM_QUOTATION"),
                    ("PO_CREATION_CONFIRMATION", "PO_CREATION_CONFIRMATION"),
                    ("USER_MENTIONED_IN_COMMENT", "USER_MENTIONED_IN_COMMENT"),
                    (
                        "PO_STATUS_UPDATE_APPROVAL_DENIED",
                        "PO_STATUS_UPDATE_APPROVAL_DENIED",
                    ),
                    ("PO_STATUS_UPDATE_APPROVED", "PO_STATUS_UPDATE_APPROVED"),
                    (
                        "PO_STATUS_UPDATE_SUPPLIER_REJECTED",
                        "PO_STATUS_UPDATE_SUPPLIER_REJECTED",
                    ),
                    ("PO_STATUS_UPDATE_SHIPPED", "PO_STATUS_UPDATE_SHIPPED"),
                    ("PO_STATUS_UPDATE_RECEIVED", "PO_STATUS_UPDATE_RECEIVED"),
                    ("PO_STATUS_UPDATE_CANCELED", "PO_STATUS_UPDATE_CANCELED"),
                    ("PO_SUPPLIER_UNRESPONSIVE", "PO_SUPPLIER_UNRESPONSIVE"),
                    ("SHIP_DATE_CHANGED", "SHIP_DATE_CHANGED"),
                    (
                        "SUPPLIER_UNRESPONSIVE_TO_DIDERO_CHECKIN",
                        "SUPPLIER_UNRESPONSIVE_TO_DIDERO_CHECKIN",
                    ),
                    ("IMPORT_ITEMS_FROM_PRICE_LIST", "IMPORT_ITEMS_FROM_PRICE_LIST"),
                    (
                        "SUPPLIER_ONBOARDING_DOCUMENT_EXPIRING",
                        "SUPPLIER_ONBOARDING_DOCUMENT_EXPIRING",
                    ),
                    (
                        "SUPPLIER_ONBOARDING_DOCUMENT_EXPIRED",
                        "SUPPLIER_ONBOARDING_DOCUMENT_EXPIRED",
                    ),
                    ("DRAFT_EMAIL_SIMPLE_DISMISS", "DRAFT_EMAIL_SIMPLE_DISMISS"),
                    ("DRAFT_EMAIL_OR_COMMENT", "DRAFT_EMAIL_OR_COMMENT"),
                    ("PO_SHOULD_HAVE_SHIPPED", "PO_SHOULD_HAVE_SHIPPED"),
                    (
                        "MANUAL_NOTIFICATION_SIMPLE_DISMISS",
                        "MANUAL_NOTIFICATION_SIMPLE_DISMISS",
                    ),
                    (
                        "SHIPMENT_WORKFLOW_TRACKING_INFO_NOT_FOUND",
                        "SHIPMENT_WORKFLOW_TRACKING_INFO_NOT_FOUND",
                    ),
                    (
                        "SHIPMENT_WORKFLOW_NO_TRACKING_NUMBER_IN_SHIPMENT_UPDATE",
                        "SHIPMENT_WORKFLOW_NO_TRACKING_NUMBER_IN_SHIPMENT_UPDATE",
                    ),
                    (
                        "OPS_TASK_SHIPMENT_WORKFLOW_COMPLETED",
                        "OPS_TASK_SHIPMENT_WORKFLOW_COMPLETED",
                    ),
                    (
                        "ORDER_ACKNOWLEDGEMENT_WORKFLOW_COMPLETED",
                        "ORDER_ACKNOWLEDGEMENT_WORKFLOW_COMPLETED",
                    ),
                    (
                        "ORDER_ACKNOWLEDGEMENT_WORKFLOW_SUCCESS_NOTIFICATION",
                        "ORDER_ACKNOWLEDGEMENT_WORKFLOW_SUCCESS_NOTIFICATION",
                    ),
                    (
                        "PO_CREATION_WORKFLOW_SUCCESS_NOTIFICATION",
                        "PO_CREATION_WORKFLOW_SUCCESS_NOTIFICATION",
                    ),
                    (
                        "SHIPMENT_WORKFLOW_SUCCESS_NOTIFICATION",
                        "SHIPMENT_WORKFLOW_SUCCESS_NOTIFICATION",
                    ),
                    (
                        "PICKUP_WORKFLOW_SUCCESS_NOTIFICATION",
                        "PICKUP_WORKFLOW_SUCCESS_NOTIFICATION",
                    ),
                    ("ADDRESS_MISMATCH_CONFIRMATION", "ADDRESS_MISMATCH_CONFIRMATION"),
                    ("FREIGHT_CHARGE_CONFIRMATION", "FREIGHT_CHARGE_CONFIRMATION"),
                    ("PRICE_CHANGE_CONFIRMATION", "PRICE_CHANGE_CONFIRMATION"),
                    ("PO_CONFIRMATION_FOLLOWUP", "PO_CONFIRMATION_FOLLOWUP"),
                    ("SHIPPING_DETAILS_FOLLOWUP", "SHIPPING_DETAILS_FOLLOWUP"),
                    ("DELIVERY_DATE_CONFIRMATION", "DELIVERY_DATE_CONFIRMATION"),
                    ("PART_NUMBER_ETA_FOLLOWUP", "PART_NUMBER_ETA_FOLLOWUP"),
                    (
                        "ORDER_ACKNOWLEDGEMENT_VALIDATION_ERROR",
                        "ORDER_ACKNOWLEDGEMENT_VALIDATION_ERROR",
                    ),
                    (
                        "SHIPMENT_WORKFLOW_VALIDATION_ERROR",
                        "SHIPMENT_WORKFLOW_VALIDATION_ERROR",
                    ),
                    ("FOLLOW_UP", "FOLLOW_UP"),
                    ("ERP_SYNC_ERROR", "ERP_SYNC_ERROR"),
                    ("ERP_SYNC_SUCCESS", "ERP_SYNC_SUCCESS"),
                    ("DOCUMENT_MATCH_REVIEW", "DOCUMENT_MATCH_REVIEW"),
                    ("DOCUMENT_UPLOAD_REQUEST", "DOCUMENT_UPLOAD_REQUEST"),
                ],
                max_length=255,
            ),
        ),
    ]
