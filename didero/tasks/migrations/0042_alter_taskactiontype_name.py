# Generated by Django 4.2.7 on 2025-06-12 21:13

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("tasks", "0041_alter_tasktype_name_alter_tasktypev2_name"),
    ]

    operations = [
        migrations.AlterField(
            model_name="taskactiontype",
            name="name",
            field=models.CharField(
                choices=[
                    ("SEND_EMAIL", "SEND_EMAIL"),
                    ("OPS_ADD_COMMENT", "OPS_ADD_COMMENT"),
                    ("ADD_COMMENT", "ADD_COMMENT"),
                    ("AUTO_COMMENT", "AUTO_COMMENT"),
                    ("UPDATE_NETSUITE", "UPDATE_NETSUITE"),
                    ("APPROVE_PURCHASE_ORDER", "APPROVE_PURCHASE_ORDER"),
                    ("DENY_PURCHASE_ORDER", "DENY_PURCHASE_ORDER"),
                    ("CONFIRM_PURCHASE_ORDER", "CONFIRM_PURCHASE_ORDER"),
                    ("CANCEL_PURCHASE_ORDER", "CANCEL_PURCHASE_ORDER"),
                    ("CONFIRM_ORDER_ACKNOWLEDGEMENT", "CONFIRM_ORDER_ACKNOWLEDGEMENT"),
                    ("REJECT_ORDER_ACKNOWLEDGEMENT", "REJECT_ORDER_ACKNOWLEDGEMENT"),
                    ("APPROVE_OA_MISMATCH", "APPROVE_OA_MISMATCH"),
                    ("APPROVE_FREIGHT_CHARGE", "APPROVE_FREIGHT_CHARGE"),
                    ("APPROVE_PRICE_CHANGE", "APPROVE_PRICE_CHANGE"),
                    ("APPROVE_ADDRESS_CHANGE", "APPROVE_ADDRESS_CHANGE"),
                    ("CONFIRM_SHIPMENT", "CONFIRM_SHIPMENT"),
                    ("CANCEL_SHIPMENT", "CANCEL_SHIPMENT"),
                ],
                max_length=255,
                unique=True,
            ),
        ),
    ]
