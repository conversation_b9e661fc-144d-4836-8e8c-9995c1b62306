import random
from datetime import date, timedelta
from decimal import Decimal

from django.conf import settings
from django.core.files.uploadedfile import SimpleUploadedFile
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.utils import timezone
from djmoney.money import Money

from didero.addresses.models import Address
from didero.documents.models import Document, DocumentLink
from didero.documents.schemas import DocumentType
from didero.invoices.models import Invoice, InvoiceItem, InvoiceLineItem
from didero.items.models import Item
from didero.orders.models import OrderItem, PurchaseOrder
from didero.orders.schemas import LineItemCategoryChoices, PurchaseOrderStatus
from didero.suppliers.models import Supplier, SupplierContact
from didero.users.models import User


class Command(BaseCommand):
    help = (
        "Create sample invoices with related purchase orders, suppliers, and line items"
    )

    def add_arguments(self, parser):
        parser.add_argument(
            "--count",
            type=int,
            default=5,
            help="Number of invoices to create (default: 5)",
        )
        parser.add_argument(
            "--clear",
            action="store_true",
            help="Clear existing invoices before seeding",
        )

    def handle(self, *args, **options):
        if settings.DIDERO_ENVIRONMENT != "dev":
            raise CommandError(
                f"This command is only available in development environments. "
                f"Current environment: {settings.DIDERO_ENVIRONMENT}"
            )

        count = options["count"]

        if options["clear"]:
            self.stdout.write("Clearing existing invoices...")
            Invoice.objects.all().delete()

        self.stdout.write(f"Creating {count} invoices with related data...")

        with transaction.atomic():
            # Get or create a user and team
            user = User.objects.first()
            if not user:
                self.stdout.write(
                    self.style.WARNING(
                        "No users found. Please create a user first or run demo data generation."
                    )
                )
                return

            team = user.teams.first()
            if not team:
                self.stdout.write(self.style.WARNING("User has no team."))
                return

            # Create suppliers
            suppliers = []
            supplier_names = [
                "TechParts Global Ltd.",
                "Industrial Supply Co.",
                "Component Masters Inc.",
                "Electronic Wholesale Partners",
                "Manufacturing Solutions LLC",
            ]

            for i, name in enumerate(supplier_names[: min(3, count)]):
                website_url = (
                    f"https://www.{name.lower().replace(' ', '').replace('.', '')}.com"
                )

                # Check if supplier already exists
                supplier = Supplier.objects.filter(
                    team=team, website_url=website_url
                ).first()

                if not supplier:
                    supplier = Supplier.objects.create(
                        name=name,
                        team=team,
                        website_url=website_url,
                    )

                    # Create a contact for the new supplier
                    contact = SupplierContact.objects.create(
                        supplier=supplier,
                        name=f"Sales Team - {name}",
                        email=f"contact@{name.lower().replace(' ', '').replace('.', '')}.com",
                        phone=f"******-{random.randint(100, 999)}-{random.randint(1000, 9999)}",
                    )

                    # Set as default contact
                    supplier.default_contact = contact
                    supplier.save()

                    self.stdout.write(f"Created supplier: {supplier.name}")
                else:
                    self.stdout.write(f"Using existing supplier: {supplier.name}")

                suppliers.append(supplier)

            # Create addresses for suppliers that don't have one
            for supplier in suppliers:
                if not supplier.addresses.exists():
                    Address.objects.create(
                        supplier=supplier,
                        line_1=f"{random.randint(100, 9999)} Business Park Drive",
                        line_2=f"Suite {random.randint(100, 999)}",
                        city=random.choice(
                            ["San Francisco", "New York", "Chicago", "Austin"]
                        ),
                        state_or_province=random.choice(["CA", "NY", "IL", "TX"]),
                        postal_code=f"{random.randint(10000, 99999)}",
                        country="US",
                    )

            # Create billing address for the team
            billing_address = Address.objects.create(
                team=team,
                line_1="123 Main Street",
                line_2="Finance Department",
                city="San Francisco",
                state_or_province="CA",
                postal_code="94105",
                country="US",
            )

            # Create items for the catalog
            items = []
            item_data = [
                ("CHIP-001", "Microprocessor Chip - 3.2GHz", "EA", 125.50),
                ("PCB-002", "Printed Circuit Board - 6 Layer", "EA", 45.75),
                ("CAP-003", "Capacitor 100uF 25V", "EA", 0.85),
                ("RES-004", "Resistor Kit - 1000 pieces", "KIT", 25.00),
                ("CONN-005", "USB-C Connector - Gold Plated", "EA", 3.25),
                ("LCD-006", "LCD Display Module 7 inch", "EA", 89.99),
                ("BAT-007", "Lithium Battery 3000mAh", "EA", 12.50),
                ("SENS-008", "Temperature Sensor Module", "EA", 15.75),
                ("MOT-009", "Stepper Motor NEMA 17", "EA", 28.50),
                ("LED-010", "LED Strip RGB 5m", "ROLL", 35.00),
            ]

            for item_number, description, uom, price in item_data:
                # Check if item already exists
                item = Item.objects.filter(item_number=item_number, team=team).first()

                if not item:
                    item = Item.objects.create(
                        item_number=item_number,
                        description=description,
                        unit_of_measure=uom,
                        price=Decimal(str(price)),
                        team=team,
                    )
                items.append(item)

            # Create invoices with related data
            for i in range(count):
                # Select a random supplier
                supplier = random.choice(suppliers)

                # Create a purchase order
                po_number = f"PO-{date.today().year}-{random.randint(10000, 99999)}"
                po = PurchaseOrder.objects.create(
                    po_number=po_number,
                    supplier=supplier,
                    team=team,
                    placed_by=user,
                    order_status=random.choice(
                        [
                            PurchaseOrderStatus.APPROVED,
                            PurchaseOrderStatus.PARTIALLY_RECEIVED,
                            PurchaseOrderStatus.RECEIVED,
                        ]
                    ).value,
                    shipping_address=billing_address,
                    sender_address=supplier.addresses.first(),
                    placement_time=timezone.now()
                    - timedelta(days=random.randint(7, 60)),
                    requested_date=timezone.now().date()
                    + timedelta(days=random.randint(7, 30)),
                )

                # Add items to the purchase order
                po_total = Decimal("0.00")
                selected_items = random.sample(items, k=random.randint(3, 6))

                for item in selected_items:
                    quantity = random.randint(1, 100)
                    unit_price = item.price * Decimal(
                        str(random.uniform(0.9, 1.1))
                    )  # Small price variation
                    total_price = unit_price * quantity

                    OrderItem.objects.create(
                        purchase_order=po,
                        item=item,
                        quantity=quantity,
                        unit_of_measure=item.unit_of_measure,
                        price=unit_price,
                    )
                    po_total += total_price

                po.total_cost = po_total
                po.save()

                # Create invoice for this PO
                invoice_number = (
                    f"INV-{date.today().year}-{random.randint(10000, 99999)}"
                )
                invoice_date = po.placement_time.date() + timedelta(
                    days=random.randint(1, 30)
                )

                payment_terms_choices = [
                    "Net 15",
                    "Net 30",
                    "Net 45",
                    "Net 60",
                    "2/10 Net 30",
                ]
                payment_terms = random.choice(payment_terms_choices)

                # Calculate due date based on payment terms
                if "15" in payment_terms:
                    due_date = invoice_date + timedelta(days=15)
                elif "45" in payment_terms:
                    due_date = invoice_date + timedelta(days=45)
                elif "60" in payment_terms:
                    due_date = invoice_date + timedelta(days=60)
                else:  # Default to 30 days
                    due_date = invoice_date + timedelta(days=30)

                invoice = Invoice.objects.create(
                    team=team,
                    purchase_order=po,
                    supplier=supplier,  # Add supplier to invoice
                    invoice_number=invoice_number,
                    invoice_date=invoice_date,
                    due_date=due_date,
                    payment_terms=payment_terms,
                    billing_address=billing_address,
                    notes=f"Reference PO: {po_number}",
                    special_instructions="Please remit payment to the address above.",
                )

                # Create invoice items matching the PO items
                invoice_subtotal = Decimal("0.00")
                for order_item in po.items.all():
                    total_price = Money(
                        order_item.price.amount * Decimal(str(order_item.quantity)),
                        "USD",
                    )
                    invoice_item = InvoiceItem.objects.create(
                        invoice=invoice,
                        item=order_item.item,
                        item_number=order_item.item.item_number,
                        item_description=order_item.item.description,
                        quantity=order_item.quantity,
                        unit_of_measure=order_item.unit_of_measure,
                        unit_price=order_item.price,
                        total_price=total_price,
                    )
                    invoice_subtotal += invoice_item.total_price

                # Add line items (taxes, shipping, etc.)
                # Add tax
                tax_rate = Decimal(str(random.uniform(0.05, 0.10)))  # 5-10% tax
                tax_amount = Money(
                    (invoice_subtotal.amount * tax_rate).quantize(Decimal("0.01")),
                    "USD",
                )
                InvoiceLineItem.objects.create(
                    invoice=invoice,
                    category=LineItemCategoryChoices.TAX,
                    description=f"Sales Tax ({(tax_rate * 100):.1f}%)",
                    amount=tax_amount,
                )

                # Add shipping
                shipping_amount = Money(
                    Decimal(str(random.uniform(25, 150))).quantize(Decimal("0.01")),
                    "USD",
                )
                InvoiceLineItem.objects.create(
                    invoice=invoice,
                    category=LineItemCategoryChoices.SHIPPING,
                    description="Standard Shipping",
                    amount=shipping_amount,
                )

                # Occasionally add a discount
                if random.random() < 0.3:  # 30% chance of discount
                    discount_rate = Decimal(
                        str(random.uniform(0.02, 0.05))
                    )  # 2-5% discount
                    discount_amount = Money(
                        -(invoice_subtotal.amount * discount_rate).quantize(
                            Decimal("0.01")
                        ),
                        "USD",
                    )
                    InvoiceLineItem.objects.create(
                        invoice=invoice,
                        category=LineItemCategoryChoices.DISCOUNT,
                        description=f"Early Payment Discount ({(discount_rate * 100):.0f}%)",
                        amount=discount_amount,
                    )

                # Create invoice document
                invoice_pdf_content = f"""
INVOICE
Invoice Number: {invoice.invoice_number}
Date: {invoice.invoice_date}
Due Date: {invoice.due_date}
Payment Terms: {invoice.payment_terms}

Supplier: {supplier.name}
PO Number: {po.po_number}

Items:
""".encode("utf-8")

                # Add invoice items to the document
                for invoice_item in invoice.items.all():
                    item_line = f"{invoice_item.item_number} - {invoice_item.item_description} | Qty: {invoice_item.quantity} | Unit Price: ${invoice_item.unit_price.amount} | Total: ${invoice_item.total_price.amount}\n"
                    invoice_pdf_content += item_line.encode("utf-8")

                # Add line items
                invoice_pdf_content += b"\nAdditional Charges:\n"
                for line_item in invoice.line_items.all():
                    line_item_text = (
                        f"{line_item.description}: ${line_item.amount.amount}\n"
                    )
                    invoice_pdf_content += line_item_text.encode("utf-8")

                # Add total
                invoice_total = invoice_subtotal
                for line_item in invoice.line_items.all():
                    invoice_total += line_item.amount
                invoice_pdf_content += (
                    f"\nTotal Amount: ${invoice_total.amount}\n".encode("utf-8")
                )
                invoice_pdf_content += (
                    b"\n[This is a simulated invoice document for testing]"
                )

                invoice_doc = Document.objects.create(
                    name=f"Invoice-{invoice.invoice_number}.pdf",
                    document=SimpleUploadedFile(
                        f"invoice_{invoice.invoice_number}.pdf",
                        invoice_pdf_content,
                        content_type="application/pdf",
                    ),
                    doc_type=DocumentType.INVOICE,
                    team=team,
                    user=user,
                    filesize_bytes=len(invoice_pdf_content),
                )

                # Link document to purchase order
                DocumentLink.objects.create(document=invoice_doc, parent_object=po)

                self.stdout.write(
                    f"Created invoice {invoice.invoice_number} for {supplier.name} "
                    f"with {invoice.items.count()} items, {invoice.line_items.count()} line items, "
                    f"and invoice document"
                )

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully created {count} invoices with all related data"
            )
        )
