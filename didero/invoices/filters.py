from django.db.models import Q
from django.db.models.query import QuerySet
from django_filters import rest_framework as filters

from didero.filters import BaseFilter, NumberInFilter
from didero.invoices.models import Invoice


class InvoiceFilter(BaseFilter):
    """
    Filter class for Invoice queryset.

    Supports filtering invoices by:
    - Invoice number (contains)
    - Invoice date range
    - Due date range
    - Purchase order IDs
    - Supplier IDs
    """

    invoice_number = filters.CharFilter(
        field_name="invoice_number", lookup_expr="icontains"
    )
    invoice_date_after = filters.DateFilter(
        field_name="invoice_date", lookup_expr="gte"
    )
    invoice_date_before = filters.DateFilter(
        field_name="invoice_date", lookup_expr="lte"
    )
    due_date_after = filters.DateFilter(field_name="due_date", lookup_expr="gte")
    due_date_before = filters.DateFilter(field_name="due_date", lookup_expr="lte")
    purchase_order_ids = NumberInFilter(field_name="purchase_order__id")
    supplier_ids = NumberInFilter(field_name="supplier__id")

    class Meta:
        model = Invoice
        fields = []

    def multi_field_search(
        self, queryset: QuerySet[Invoice], name: str, value: str
    ) -> QuerySet[Invoice]:
        """
        Search across multiple fields for the given value.
        """
        return queryset.filter(
            Q(invoice_number__icontains=value)
            | Q(purchase_order__po_number__icontains=value)
            | Q(notes__icontains=value)
            | Q(payment_terms__icontains=value)
        ).distinct()
