from decimal import Decimal
from typing import TYPE_CHECKING

from auditlog.registry import auditlog
from django.contrib.contenttypes.models import ContentType
from django.db import models
from djmoney.models.fields import MoneyField
from djmoney.money import Currency

from didero.addresses.models import Address
from didero.documents.models import Document, DocumentLink
from didero.documents.schemas import DocumentType
from didero.items.models import Item
from didero.models import BaseModelWithWebsocketPublishing
from didero.orders.schemas import LineItemCategoryChoices
from didero.users.models.team_models import Team

if TYPE_CHECKING:
    from didero.orders.models import PurchaseOrder
    from didero.suppliers.models import Supplier


class Invoice(BaseModelWithWebsocketPublishing):
    """
    Represents an invoice.
    Each invoice is always related to a team.
    Each invoice can be related to a purchase order or not (e.g. for extra charges)
    """

    # Core identifiers
    purchase_order = models.ForeignKey(
        "orders.PurchaseOrder",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="invoices",
        help_text="The purchase order this invoice is for",
    )
    supplier = models.ForeignKey(
        "suppliers.Supplier",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="invoices",
        help_text="The supplier this invoice is from",
    )
    team = models.ForeignKey(
        Team,
        on_delete=models.CASCADE,
        related_name="invoices",
        help_text="The team this invoice is for",
    )
    invoice_number = models.CharField(max_length=255)

    # Invoice details
    invoice_date = models.DateField()
    due_date = models.DateField(null=True, blank=True)
    billing_address = models.ForeignKey(
        Address,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="invoices",
    )

    # Payment information
    payment_terms = models.CharField(max_length=100, null=True, blank=True)

    # Additional information
    notes = models.TextField(null=True, blank=True)
    special_instructions = models.TextField(null=True, blank=True)

    subtotal = MoneyField(
        max_digits=14,
        decimal_places=8,
        default_currency=Currency("USD"),
        default=Decimal("0.00"),
        null=True,
        blank=True,
    )
    tax_rate = models.DecimalField(
        max_digits=14, decimal_places=8, default=Decimal("0.00"), null=True, blank=True
    )
    tax_amount = MoneyField(
        max_digits=14,
        decimal_places=8,
        default_currency=Currency("USD"),
        default=Decimal("0.00"),
        null=True,
        blank=True,
    )

    shipping_or_freight_charges = MoneyField(
        max_digits=14,
        decimal_places=8,
        default_currency=Currency("USD"),
        default=Decimal("0.00"),
        null=True,
        blank=True,
    )
    other_special_charges = MoneyField(
        max_digits=14,
        decimal_places=8,
        default_currency=Currency("USD"),
        default=Decimal("0.00"),
        null=True,
        blank=True,
    )
    payment_status = models.CharField(max_length=100, null=True, blank=True)
    status = models.CharField(max_length=100, null=True, blank=True)

    def get_document(self) -> dict[str, int | str] | None:
        """
        Get invoice document using multiple fallback paths.

        Priority order:
        1. Direct DocumentLink to Invoice (most efficient)
        2. Communication-linked document (current workflow)
        3. PO-linked document (legacy fallback)
        """
        # Path 1: Direct DocumentLink to Invoice (future-proof)
        invoice_content_type = ContentType.objects.get_for_model(Invoice)
        direct_link = DocumentLink.objects.filter(
            parent_object_type=invoice_content_type,
            parent_object_id=self.id,
            document__doc_type=DocumentType.INVOICE,
        ).first()

        if direct_link and direct_link.document:
            return {
                "id": direct_link.document.id,
                "name": direct_link.document.name,
                "doc_type": direct_link.document.doc_type,
            }

        # Path 2: EmailThread-linked document (using linking pattern like POs)
        from didero.emails.models import EmailThreadToInvoiceLink
        from didero.suppliers.models import Communication

        # Get email threads linked to this invoice
        email_thread_ids = EmailThreadToInvoiceLink.objects.filter(
            invoice=self
        ).values_list("email_thread_id", flat=True)

        if email_thread_ids:
            # Get document link efficiently - convert IDs to strings for CharField lookup
            comm_content_type = ContentType.objects.get_for_model(Communication)
            communication_ids = Communication.objects.filter(
                email_thread__id__in=email_thread_ids
            ).values_list("id", flat=True)

            comm_link = DocumentLink.objects.filter(
                parent_object_type=comm_content_type,
                parent_object_id__in=[str(comm_id) for comm_id in communication_ids],
                document__doc_type=DocumentType.INVOICE,
            ).first()

            if comm_link and comm_link.document:
                return {
                    "id": comm_link.document.id,
                    "name": comm_link.document.name,
                    "doc_type": comm_link.document.doc_type,
                }

        # Path 3: PO-linked document (legacy fallback)
        if self.purchase_order:
            from didero.orders.models import PurchaseOrder

            po_content_type = ContentType.objects.get_for_model(PurchaseOrder)
            po_link = DocumentLink.objects.filter(
                parent_object_type=po_content_type,
                parent_object_id=str(self.purchase_order.id),  # type: ignore[attr-defined]
                document__doc_type=DocumentType.INVOICE,
            ).first()

            if po_link and po_link.document:
                return {
                    "id": po_link.document.id,
                    "name": po_link.document.name,
                    "doc_type": po_link.document.doc_type,
                }

        return None

    def __str__(self):
        po_info = (
            f"PO: {self.purchase_order.po_number}" if self.purchase_order else "No PO"
        )
        return f"Invoice - {self.invoice_number} for {po_info}"


auditlog.register(Invoice)


class InvoiceLineItem(models.Model):
    """
    Represents extra charges as line items in the invoice.
    """

    invoice = models.ForeignKey(
        Invoice, on_delete=models.CASCADE, related_name="line_items"
    )

    category = models.CharField(
        max_length=20,
        choices=[(choice.value, choice.name) for choice in LineItemCategoryChoices],
        default=LineItemCategoryChoices.CUSTOM.value,
    )
    description = models.CharField(max_length=255, blank=True, null=True)
    amount = MoneyField(
        max_digits=14,
        decimal_places=8,
        default_currency=Currency("USD"),
        default=Decimal("0.00"),
    )

    def __str__(self):
        if self.category == LineItemCategoryChoices.CUSTOM:
            return f"Custom: {self.description}"
        return self.category


auditlog.register(InvoiceLineItem)


class InvoiceItem(models.Model):
    """
    Represents the items in the invoice.
    """

    # Core relationships
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name="items")
    item = models.ForeignKey(
        Item,
        on_delete=models.CASCADE,
        related_name="invoice_items",
        null=True,
        blank=True,
    )

    # Snapshot of item details on invoice. (some items may not exist in items table(catalog))
    item_number = models.CharField(max_length=100)
    item_description = models.TextField()
    quantity = models.FloatField()
    unit_of_measure = models.CharField(max_length=50)

    # Pricing information
    unit_price = MoneyField(
        max_digits=14,
        decimal_places=8,
        default_currency=Currency("USD"),
        default=Decimal("0.00"),
    )
    total_price = MoneyField(
        max_digits=14,
        decimal_places=8,
        default_currency=Currency("USD"),
        default=Decimal("0.00"),
    )

    def __str__(self):
        return f"{self.item_number} - {self.item_description} x {self.quantity}"


auditlog.register(InvoiceItem)
