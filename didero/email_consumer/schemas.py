"""
Type definitions and schemas for the email consumer.
"""

from typing import Dict, List, Optional, TypedDict

from pydantic import BaseModel, Field

from didero.suppliers.models import SupplierContact, SupplierDomain


class EmailAddress(BaseModel):
    """Type definition for email address participant."""

    email: str
    name: Optional[str] = None


class EmailAttachment(BaseModel):
    """Type definition for email attachment metadata."""

    id: str
    content_type: str
    filename: str
    size: int


class NylasEmailMessage(BaseModel):
    """Pydantic model for Nylas email messages"""

    id: str
    grant_id: str
    email_from: List[EmailAddress] = Field(alias="from")
    email_to: List[EmailAddress] = Field(alias="to")
    email_cc: List[EmailAddress] = Field(alias="cc")
    email_bcc: List[EmailAddress] = Field(alias="bcc")
    email_reply_to: List[EmailAddress] = Field(alias="reply_to")
    subject: str
    body: str
    attachments: List[EmailAttachment]
    thread_id: str
    date: int
    folders: List[str]

    model_config = {
        "populate_by_name": True  # Allow both alias and field name
    }


class EmailParticipants(BaseModel):
    """Email participants grouped by their role in the email message."""

    sender: str  # single sender email address
    recipients_to: List[str]  # TO recipients
    recipients_cc: List[str]  # CC recipients
    recipients_bcc: List[str]  # BCC recipients
    reply_to_addresses: List[str]  # Reply-to addresses


class SupplierLookupData(TypedDict):
    """Type definition for batch-fetched supplier data."""

    domain_to_supplier_domain: Dict[str, SupplierDomain]
    email_to_contact: Dict[str, SupplierContact]
