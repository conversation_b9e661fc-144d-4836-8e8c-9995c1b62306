{% extends "admin/change_list.html" %}
{% load i18n admin_urls static %}

{% block content_title %}
<h1>Shipment Management</h1>
{% endblock %}

{% block search %}
<div style="margin-bottom: 20px; padding: 15px; background: #f8f8f8; border: 1px solid #ddd; border-radius: 4px;">
    <form method="get" style="display: inline-block; margin-right: 20px;">
        <label for="team-select" style="margin-right: 10px;">Select Team:</label>
        <select id="team-select" name="team" onchange="this.form.submit()" style="padding: 5px;">
            <option value="">-- Select a team --</option>
            {% for team in teams %}
            <option value="{{ team.id }}" {% if selected_team == team.id|stringformat:"s" %}selected{% endif %}>
                {{ team.name }}
            </option>
            {% endfor %}
        </select>
    </form>
    
    {% if selected_team %}
    <div style="display: inline-block;">
        <a href="{% url 'admin:orders_shipment_import_csv' %}" class="button" style="margin-right: 10px;">
            Import CSV
        </a>
        <a href="{% url 'admin:orders_shipment_export_csv' %}?team={{ selected_team }}" class="button" style="margin-right: 10px;">
            Export CSV
        </a>
        <a href="{% url 'admin:orders_shipment_download_template' %}" class="button" style="margin-right: 10px;">
            Download Template
        </a>
        <a href="{% url 'admin:orders_shipment_trigger_update' %}?team={{ selected_team }}" class="button" style="background-color: #ba2121;">
            Trigger Tracking Update
        </a>
    </div>
    {% else %}
    <p style="display: inline-block; margin-left: 20px; color: #666;">
        Please select a team to enable operations
    </p>
    {% endif %}
</div>
{% endblock %}

{% block result_list %}
{% if selected_team %}
{{ block.super }}
{% else %}
<p style="padding: 20px; text-align: center; color: #666;">
    Select a team above to view shipments
</p>
{% endif %}
{% endblock %}