{% extends "admin/base_site.html" %}
{% load i18n admin_urls static %}

{% block title %}Import Shipments CSV{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
    &rsaquo; <a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a>
    &rsaquo; <a href="{% url 'admin:orders_shipment_changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
    &rsaquo; Import CSV
</div>
{% endblock %}

{% block content %}
<h1>Import Shipments from CSV</h1>

<form method="post" enctype="multipart/form-data">
    {% csrf_token %}
    {{ form.as_p }}
    
    <div class="submit-row">
        <input type="submit" value="Import CSV" class="default" />
        <a href="{% url 'admin:orders_shipment_changelist' %}" class="button">Cancel</a>
    </div>
</form>

<div style="margin-top: 2em;">
    <h3>CSV Format Requirements:</h3>
    <p>The CSV must have these columns: tracking_number, bol_number, container_number, carrier_type, shipment_date, estimated_delivery_date, actual_delivery_date, port_of_departure, port_of_arrival</p>
    <p><strong>Note:</strong> At least one identifier (tracking/BOL/container) and carrier_type are required.</p>
</div>
{% endblock %}