"""Field mapping utilities for ERP integrations."""

from datetime import date
from typing import Any, Dict, List, Optional

import structlog

from didero.integrations.erp.customers.ionq.constants import IONQ_LINE_LEVEL_MAPPINGS
from didero.integrations.erp.schemas import ERPUpdateRequest, LineItemUpdate
from didero.orders.models import (
    OrderAcknowledgementItem,
    PurchaseOrder,
    Shipment,
)

logger = structlog.get_logger(__name__)


class NetSuiteFieldMapper:
    """Field mapper for NetSuite integration."""

    @staticmethod
    def prepare_shipment_data(
        shipment: Shipment, field_mappings: Dict[str, str]
    ) -> ERPUpdateRequest:
        """
        Prepare shipment data with field mappings applied.

        Only include line_items when line-level fields are being mapped.
        This prevents NetSuite "amount" errors when updating only header fields.

        Args:
            shipment: Shipment instance with related data
            field_mappings: Field mapping configuration

        Returns:
            ERPUpdateRequest with mapped field names and values, conditionally including line items
        """
        # Collect basic field data
        tracking_number = None
        estimated_delivery_date = None
        promised_ship_date = None
        line_items = None

        # Map tracking number if available (header field)
        if shipment.tracking_number and field_mappings.get("tracking_number"):
            tracking_number = shipment.tracking_number

        # Check if we need line-level fields
        needs_line_data = any(
            field_mappings.get(field) for field in IONQ_LINE_LEVEL_MAPPINGS
        )

        # Only include line items if we have line-level field mappings
        if needs_line_data:
            # Map estimated delivery date if available
            if shipment.estimated_delivery_date and field_mappings.get(
                "estimated_delivery_date"
            ):
                estimated_delivery_date = shipment.estimated_delivery_date

            # Get line-item-specific data
            line_items_data = _get_line_items_with_promised_dates(shipment)
            if line_items_data:
                # Convert to LineItemUpdate objects
                line_items = [
                    LineItemUpdate(
                        line=item["line"],
                        item_number=item["item_number"],
                        item_id=str(item["item_id"]) if item.get("item_id") else None,
                        quantity=item["quantity"],
                        promised_ship_date=item.get("promised_ship_date"),
                        estimated_delivery_date=item.get("estimated_delivery_date"),
                    )
                    for item in line_items_data
                ]
                logger.info(
                    "Using line-specific data for ERP update",
                    shipment_id=shipment.id,
                    total_line_items=len(line_items),
                    line_numbers=[item.line for item in line_items],
                )
            else:
                # Fallback to old behavior if no line items
                if shipment.purchase_order:
                    promised_ship_date = _get_promised_ship_date_from_oa(
                        shipment.purchase_order
                    )
                    if promised_ship_date and field_mappings.get("promised_ship_date"):
                        logger.info(
                            "Using fallback flat promise date for ERP update",
                            shipment_id=shipment.id,
                            promised_ship_date=promised_ship_date,
                        )

        # Create ERPUpdateRequest
        request = ERPUpdateRequest(
            tracking_number=tracking_number,
            estimated_delivery_date=estimated_delivery_date,
            promised_ship_date=promised_ship_date,
            line_items=line_items,
        )

        logger.info(
            "Prepared shipment data",
            shipment_id=shipment.id,
            has_tracking=bool(request.tracking_number),
            has_line_items=bool(request.line_items),
            needs_line_data=needs_line_data,
        )

        return request


def _get_line_items_with_promised_dates(shipment: Shipment) -> List[Dict[str, Any]]:
    """
    Get line-item-specific data including promised dates.

    This function:
    1. Iterates through ShipmentLineItems
    2. Matches each to its OrderItem
    3. Finds the corresponding OA item with promised date
    4. Returns structured data preserving line-item relationships

    Args:
        shipment: Shipment with line items

    Returns:
        List of dicts with line-specific data
    """
    line_items_data = []

    try:
        # Check if purchase order exists
        if not shipment.purchase_order:
            logger.warning(
                "Shipment has no purchase order - cannot map line items for ERP",
                shipment_id=shipment.id,
            )
            return []

        # Get PO items to determine line numbers
        po_items = list(shipment.purchase_order.items.all().order_by("id"))

        if not po_items:
            logger.warning(
                "No PO items found for line mapping",
                shipment_id=shipment.id,
                po_id=shipment.purchase_order.id,
            )
            return []

        # Create a mapping of OrderItem to line number
        item_to_line = {item.id: idx + 1 for idx, item in enumerate(po_items)}

        logger.info(
            "Created line mapping for ERP sync",
            shipment_id=shipment.id,
            total_po_items=len(po_items),
            line_mapping=item_to_line,
        )

        # Process each shipment line item
        shipment_lines = list(shipment.line_items.select_related("order_item__item"))

        if not shipment_lines:
            logger.warning("No shipment line items found", shipment_id=shipment.id)
            return []

        logger.info(
            "Processing shipment lines for ERP mapping",
            shipment_id=shipment.id,
            total_shipment_lines=len(shipment_lines),
        )

        for shipment_line in shipment_lines:
            order_item = shipment_line.order_item

            # Get line number from position with validation
            line_number = item_to_line.get(order_item.id)

            if line_number is None:
                logger.error(
                    "OrderItem not found in PO line mapping",
                    shipment_id=shipment.id,
                    order_item_id=order_item.id,
                    item_number=order_item.item.item_number,
                    available_po_items=[item.id for item in po_items],
                )
                # Skip this line item as it can't be mapped
                continue

            # Find promised date for this specific item
            promised_date = None

            # NEW: Direct lookup by order_item
            oa_item = (
                OrderAcknowledgementItem.objects.filter(
                    order_acknowledgement__purchase_order=shipment.purchase_order,
                    order_item=order_item,
                )
                .order_by("-order_acknowledgement__created_at")
                .first()
            )

            if oa_item:
                promised_date = oa_item.promised_ship_date
            else:
                # FALLBACK: Look for OA items matching this order item's Item
                oa_items = OrderAcknowledgementItem.objects.filter(
                    order_acknowledgement__purchase_order=shipment.purchase_order,
                    item=order_item.item,
                ).order_by("-order_acknowledgement__created_at")

                if oa_items.exists():
                    oa_item = oa_items.first()
                    promised_date = oa_item.promised_ship_date

            line_data = {
                "line": line_number,
                "item_number": order_item.item.item_number,
                "item_id": order_item.item.id,  # Add item_id for better tracking
                "quantity": shipment_line.shipped_quantity,
                "promised_ship_date": promised_date,
                "estimated_delivery_date": shipment.estimated_delivery_date,
            }

            line_items_data.append(line_data)

            logger.info(
                "Mapped line item data",
                line=line_number,
                item_id=order_item.item.id,
                item_number=order_item.item.item_number,
                promised_date=promised_date,
                estimated_delivery_date=shipment.estimated_delivery_date,
            )

    except Exception as e:
        logger.warning(
            "Error building line items data, falling back to flat structure",
            shipment_id=shipment.id,
            error=str(e),
        )
        return []

    # Final validation and summary
    if line_items_data:
        logger.info(
            "Successfully mapped line items for ERP sync",
            shipment_id=shipment.id,
            total_mapped_lines=len(line_items_data),
            mapped_line_numbers=[item["line"] for item in line_items_data],
            mapped_items=[item["item_number"] for item in line_items_data],
        )
    else:
        logger.warning(
            "No line items mapped for ERP sync",
            shipment_id=shipment.id,
            total_po_items=len(po_items) if po_items else 0,
            total_shipment_lines=len(shipment_lines) if shipment_lines else 0,
        )

    return line_items_data


def _get_promised_ship_date_from_oa(purchase_order: PurchaseOrder) -> Optional[date]:
    """
    Extract promised ship date from order acknowledgment.

    Args:
        purchase_order: PurchaseOrder instance

    Returns:
        Promised ship date if found, None otherwise
    """
    try:
        # Get the latest OA
        oa = purchase_order.order_acknowledgements.order_by("-created_at").first()
        if not oa:
            return None

        # Get the first item with a promised ship date
        oa_item = oa.items.exclude(promised_ship_date__isnull=True).first()
        if oa_item:
            return oa_item.promised_ship_date

    except Exception as e:
        logger.warning(
            "Error extracting promised ship date from OA",
            po_number=purchase_order.po_number,
            error=str(e),
        )

    return None
