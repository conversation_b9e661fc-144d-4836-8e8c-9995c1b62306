import os
from typing import Dict, Optional

import structlog

from .schemas import ERPType

logger = structlog.get_logger(__name__)


class SimpleEnvCredentialProvider:
    """
    Simple credential provider that reads from environment variables.
    The .env file should be loaded by Django's manage.py already.

    Credential format: {TEAM_ID}_{ERP_TYPE}_{FIELD_NAME}
    Example: IONQ_NETSUITE_ACCOUNT_ID
    """

    def __init__(self):
        # Environment variables should already be loaded by Django's manage.py
        logger.info("Using environment variables for ERP credentials")

    def get_credentials(
        self, team_id: str, erp_type: ERPType
    ) -> Dict[str, Optional[str]]:
        """
        Get credentials for a specific team and ERP type.

        Args:
            team_id: Team identifier (will be uppercased)
            erp_type: ERP type as ERPType enum

        Returns:
            Dictionary of credential fields
        """
        # Special handling for IonQ teams (both test and production)
        if team_id in ["4", "173"]:  # 4 is test, 173 is IonQ's production team ID
            prefix = f"IONQ_{erp_type.upper()}_"
        else:
            prefix = f"{team_id.upper()}_{erp_type.upper()}_"

        # Known credential fields for different ERP types
        credential_fields = {
            "netsuite": [
                "ACCOUNT_ID",
                "CONSUMER_KEY",
                "CONSUMER_SECRET",
                "TOKEN_ID",
                "TOKEN_SECRET",
            ],
            "sap": ["CLIENT_ID", "CLIENT_SECRET", "TENANT_ID"],
            "oracle": ["USERNAME", "PASSWORD", "INSTANCE_URL"],
        }

        # Get the fields for this ERP type (StrEnum can be used as string)
        fields = credential_fields.get(erp_type.lower(), [])

        # Build credentials dictionary
        credentials = {}
        for field in fields:
            env_key = f"{prefix}{field}"
            value = os.getenv(env_key)
            if value:
                credentials[field.lower()] = value
            else:
                logger.warning(f"Credential field not found: {env_key}")

        return credentials

    def validate_credentials(
        self, credentials: Dict[str, str], erp_type: ERPType
    ) -> bool:
        """
        Validate that all required credentials are present.

        Args:
            credentials: Dictionary of credentials
            erp_type: Type of ERP system

        Returns:
            True if all required fields are present
        """
        required_fields = {
            "netsuite": [
                "account_id",
                "consumer_key",
                "consumer_secret",
                "token_id",
                "token_secret",
            ],
            "sap": ["client_id", "client_secret", "tenant_id"],
            "oracle": ["username", "password", "instance_url"],
        }

        required = required_fields.get(erp_type.lower(), [])
        missing = [
            field
            for field in required
            if field not in credentials or not credentials[field]
        ]

        if missing:
            logger.error(
                f"Missing required credential fields for {erp_type}: {missing}"
            )
            return False

        return True
