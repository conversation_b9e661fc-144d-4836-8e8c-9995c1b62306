"""Constants for ION-Q specific ERP integration, focusing on NetSuite fields."""

# Internal field names (used within the Didero application)
# These ensure consistency in our internal data structures (e.g., ERPUpdateRequest)
TRACKING_NUMBER = "tracking_number"
ESTIMATED_DELIVERY_DATE = "estimated_delivery_date"
PROMISED_SHIP_DATE = "promised_ship_date"
LINE_ITEMS = "line_items"


# NetSuite Field Script IDs (as defined in NetSuite)
# These are the actual identifiers for custom and standard fields in the API.

# -- Header Fields --
NS_HEADER_TRACKING_NUMBER = "custbody_ionq_tracking_number"

# -- Line Item (Column) Fields --
NS_LINE_EXPECTED_RECEIPT_DATE = (
    "expectedreceiptdate"  # This is a standard NetSuite field
)
NS_LINE_PROMISED_SHIP_DATE = "custcol_ionq_supplierpromisedatefield"


# Field Groupings
# These sets provide a single source of truth for categorizing fields.
# Using sets allows for efficient 'in' checks (O(1) average time complexity).

IONQ_HEADER_FIELDS = {
    NS_HEADER_TRACKING_NUMBER,
}

IONQ_LINE_FIELDS = {
    NS_LINE_EXPECTED_RECEIPT_DATE,
    NS_LINE_PROMISED_SHIP_DATE,
}

# Mapping from internal names to NetSuite script IDs
# This is crucial for the client to correctly map fields from our internal representation
# to the ERP-specific representation.
FIELD_MAP = {
    TRACKING_NUMBER: NS_HEADER_TRACKING_NUMBER,
    ESTIMATED_DELIVERY_DATE: NS_LINE_EXPECTED_RECEIPT_DATE,
    PROMISED_SHIP_DATE: NS_LINE_PROMISED_SHIP_DATE,
}

# Field mappings that indicate line-level data is needed (kept for backward compatibility)
IONQ_LINE_LEVEL_MAPPINGS = [
    ESTIMATED_DELIVERY_DATE,  # Maps to expectedreceiptdate
    PROMISED_SHIP_DATE,  # Maps to custcol_ionq_supplierpromisedatefield
]

# IONQ team IDs for credential detection
IONQ_TEAM_IDS = [
    4,  # IONQ test team
    173,  # IONQ production team
]
