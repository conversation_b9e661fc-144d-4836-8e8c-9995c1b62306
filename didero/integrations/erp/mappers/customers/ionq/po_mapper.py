"""
IonQ-specific mapper for converting NetSuite Purchase Order data to PurchaseOrderDetails.

This mapper handles the specific field mappings and data transformations required
for IonQ's NetSuite integration. It converts NetSuite's XML structure into Didero's
standard PurchaseOrderDetails format while handling IonQ-specific business rules
and field mappings.

Key Features:
- Maps NetSuite vendor information to Didero supplier format
- Converts NetSuite line items with proper pricing and descriptions
- Handles address mapping with country code normalization
- Processes custom fields specific to IonQ's NetSuite configuration
- Validates and normalizes monetary values and quantities
"""

import re
from datetime import datetime
from typing import Any, Dict, List, Optional

import structlog

from didero.ai.po_extraction.po_extraction import POExtractionAddressInformation
from didero.ai.purchase_order.schemas import PurchaseOrderDetails, PurchaseOrderItem

logger = structlog.get_logger(__name__)


class IonQNetSuitePOMapper:
    """
    IonQ-specific mapper for NetSuite PO data to PurchaseOrderDetails schema.

    This mapper converts the raw NetSuite V4 extraction data into the
    PurchaseOrderDetails Pydantic model that can be used with the existing
    create_po_from_extracted_data() activity.

    The mapper handles IonQ's specific NetSuite configuration including:
    - Custom field mappings for tracking numbers and delivery dates
    - Vendor name format handling (e.g., "V10072 ThorLabs" -> "V10072 ThorLabs")
    - Address format normalization for US/international addresses
    - Line item processing with proper unit conversions
    - Payment terms mapping from NetSuite codes to readable text
    """

    # NetSuite country codes to ISO 2-letter codes mapping
    # Used for normalizing address country fields from NetSuite format to standard ISO codes
    COUNTRY_CODE_MAP = {
        "_unitedStates": "US",
        "_unitedKingdom": "GB",
        "_canada": "CA",
        "_france": "FR",
        "_germany": "DE",
        "_australia": "AU",
        "_japan": "JP",
        "_china": "CN",
        "_india": "IN",
        "_singapore": "SG",
    }

    def map_to_purchase_order_details(
        self, netsuite_data: Dict[str, Any]
    ) -> PurchaseOrderDetails:
        """
        Convert NetSuite PO data to PurchaseOrderDetails format for IonQ.

        This is the main mapping method that transforms NetSuite's complex XML structure
        into Didero's standardized PurchaseOrderDetails schema. It handles all the
        IonQ-specific business logic and field mappings.

        Args:
            netsuite_data: Raw NetSuite PO data from the V4 extraction method

        Returns:
            PurchaseOrderDetails: Mapped PO data ready for Didero's PO creation system

        The mapping process includes:
        1. Basic PO information (number, dates, terms)
        2. Vendor/supplier information with name normalization
        3. Address mapping for both shipping and billing
        4. Line item processing with pricing and descriptions
        5. Custom field handling for IonQ-specific data
        """
        # Log the start of mapping process with key identifiers for troubleshooting
        logger.info(
            "Starting NetSuite to PurchaseOrderDetails mapping",
            po_number=netsuite_data.get("header", {}).get("tranId"),
            vendor_name=netsuite_data.get("vendor", {}).get("name"),
            line_item_count=len(netsuite_data.get("line_items", [])),
        )

        # Extract main data sections from NetSuite structure
        # NetSuite organizes data into logical sections: header, vendor, addresses, line_items
        header = netsuite_data.get("header", {})
        vendor = netsuite_data.get("vendor", {})
        addresses = netsuite_data.get("addresses", {})
        line_items = netsuite_data.get("line_items", [])

        # Required field validation - ensure we have minimum data to create a PO
        po_number = header.get("tranId")
        if not po_number:
            raise ValueError("Missing required field: po_number (header.tranId)")

        supplier_name = vendor.get("name")
        if not supplier_name:
            raise ValueError("Missing required field: supplier_name (vendor.name)")

        # Map basic fields
        mapped_data = {
            "po_number": po_number,
            "supplier_name": supplier_name,
            "order_date": self._parse_date(header.get("tranDate")),
            "currency": header.get("currencyName", "USD"),
            "payment_terms": self._extract_payment_terms(header),
            "notes": header.get("memo"),
            "total_amount": header.get("total"),
        }

        # Map addresses
        mapped_data["supplier_address"] = self._map_supplier_address(
            addresses.get("billing")
        )
        mapped_data["shipping_address"] = self._map_shipping_address(
            addresses.get("shipping")
        )

        # Map NetSuite line items to Didero's PurchaseOrderItem format
        # Each NetSuite line item contains item details, pricing, and quantities
        # that need to be normalized for Didero's PO creation process
        mapped_data["items"] = self._map_line_items(line_items)

        # Log successful mapping
        logger.info(
            "Successfully mapped NetSuite data to PurchaseOrderDetails",
            po_number=po_number,
            supplier_name=supplier_name,
            mapped_item_count=len(mapped_data.get("items", [])),
            has_supplier_address=mapped_data["supplier_address"] is not None,
            has_shipping_address=mapped_data["shipping_address"] is not None,
        )

        return PurchaseOrderDetails(**mapped_data)

    def _parse_date(self, date_str: Optional[str]) -> Optional[str]:
        """Parse NetSuite date string to ISO format.

        NetSuite dates are in format: "2022-10-03T21:00:00.000-07:00"
        """
        if not date_str:
            return None

        try:
            # NetSuite dates are already ISO formatted, just return as-is
            return date_str
        except Exception as e:
            logger.warning("Failed to parse date", date_str=date_str, error=str(e))
            return date_str  # Return original if parsing fails

    def _extract_payment_terms(self, header: Dict[str, Any]) -> Optional[str]:
        """Extract payment terms from header.

        NetSuite terms structure: {"internalId": "4", "name": "Net 30"}
        """
        terms = header.get("terms")
        if terms and isinstance(terms, dict):
            return terms.get("name")
        return None

    def _map_supplier_address(
        self, billing_address: Optional[Dict[str, str]]
    ) -> Optional[POExtractionAddressInformation]:
        """Map NetSuite billing address to supplier address.

        NetSuite billing address has complete structure that can be directly mapped.
        """
        if not billing_address:
            return None

        try:
            # Map NetSuite country code to ISO code
            country_code = billing_address.get("country")
            iso_country = self.COUNTRY_CODE_MAP.get(country_code, "US")

            return POExtractionAddressInformation(
                line1=billing_address.get("addr1", ""),
                line2=billing_address.get("addr2", ""),
                city=billing_address.get("city", ""),
                state=billing_address.get("state", ""),
                zip=billing_address.get("zip", ""),
                country=iso_country,
            )
        except Exception as e:
            logger.warning(
                "Failed to map supplier address",
                billing_address=billing_address,
                error=str(e),
            )
            return None

    def _map_shipping_address(
        self, shipping_address: Optional[Dict[str, str]]
    ) -> Optional[POExtractionAddressInformation]:
        """Map NetSuite shipping address to shipping address.

        NetSuite shipping address often has minimal data (just addrText).
        This may require AI parsing for complete address extraction.
        """
        if not shipping_address:
            return None

        # If we have structured address data, use it
        if all(shipping_address.get(field) for field in ["addr1", "city", "state"]):
            country_code = shipping_address.get("country")
            iso_country = self.COUNTRY_CODE_MAP.get(country_code, "US")

            return POExtractionAddressInformation(
                line1=shipping_address.get("addr1", ""),
                line2=shipping_address.get("addr2", ""),
                city=shipping_address.get("city", ""),
                state=shipping_address.get("state", ""),
                zip=shipping_address.get("zip", ""),
                country=iso_country,
            )

        # If we only have raw text, try basic parsing
        addr_text = shipping_address.get("addrText")
        if addr_text:
            return self._parse_address_text(addr_text)

        # If we have addressee, create minimal address
        addressee = shipping_address.get("addressee")
        if addressee:
            country_code = shipping_address.get("country")
            iso_country = self.COUNTRY_CODE_MAP.get(country_code, "US")

            return POExtractionAddressInformation(
                line1=addressee,
                line2="",
                city="",
                state="",
                zip="",
                country=iso_country,
            )

        return None

    def _parse_address_text(self, addr_text: str) -> POExtractionAddressInformation:
        """Basic parsing of raw address text.

        For minimal addresses like "CP Tooling (NI)\\r\\nUnited States",
        create a basic address structure. More sophisticated parsing could
        be added later or delegated to AI.
        """
        # Clean up the text
        clean_text = addr_text.replace("\\r\\n", "\n").replace("\\n", "\n")
        lines = [line.strip() for line in clean_text.split("\n") if line.strip()]

        # Basic parsing - first line is usually the company/address
        line1 = lines[0] if lines else ""

        # Look for country in the text
        country = "US"  # Default
        for line in lines:
            if "United States" in line:
                country = "US"
            elif "Canada" in line:
                country = "CA"
            elif "United Kingdom" in line:
                country = "GB"

        return POExtractionAddressInformation(
            line1=line1,
            line2="",
            city="",
            state="",
            zip="",
            country=country,
        )

    def _map_line_items(
        self, line_items: List[Dict[str, Any]]
    ) -> List[PurchaseOrderItem]:
        """
        Map NetSuite line items to Didero PurchaseOrderItem format.

        This method handles the conversion of NetSuite's line item structure to Didero's
        standardized format. It processes item numbers, descriptions, quantities, pricing,
        and units while handling IonQ-specific formatting requirements.

        Args:
            line_items: List of NetSuite line item dictionaries

        Returns:
            List of PurchaseOrderItem objects ready for PO creation
        """
        mapped_items = []

        for idx, item in enumerate(line_items):
            try:
                mapped_item = self._map_single_line_item(item, idx)
                if mapped_item:
                    mapped_items.append(mapped_item)
            except Exception as e:
                logger.warning(
                    "Failed to map line item",
                    item_index=idx,
                    item_data=item,
                    error=str(e),
                )
                continue  # Skip failed items

        logger.info(
            "Mapped line items",
            original_count=len(line_items),
            mapped_count=len(mapped_items),
        )

        return mapped_items

    def _map_single_line_item(
        self, item: Dict[str, Any], index: int
    ) -> Optional[PurchaseOrderItem]:
        """Map a single NetSuite line item to PurchaseOrderItem.

        NetSuite item structure:
        {
            "item_reference": {"internalId": "2761", "name": "502-00097"},
            "fields": {
                "description": "Compact Power and Energy Meter Console",
                "vendorName": "PM100D",
                "quantity": "1.0",
                "rate": "1220.57",
                "amount": "1220.57",
                "expectedReceiptDate": "2022-11-03T21:00:00.000-07:00"
            },
            "custom_fields": {...}
        }
        """
        item_reference = item.get("item_reference", {})
        fields = item.get("fields", {})

        # Required fields
        item_number = item_reference.get("name")
        if not item_number:
            logger.warning(f"Line item {index} missing item_number")
            return None

        item_description = fields.get("description")
        if not item_description:
            logger.warning(f"Line item {index} missing description")
            return None

        quantity_str = fields.get("quantity")
        if not quantity_str:
            logger.warning(f"Line item {index} missing quantity")
            return None

        unit_price = fields.get("rate")
        if not unit_price:
            logger.warning(f"Line item {index} missing unit_price (rate)")
            return None

        # Convert quantity to float
        try:
            quantity = float(quantity_str)
        except (ValueError, TypeError):
            logger.warning(
                f"Line item {index} invalid quantity",
                quantity_str=quantity_str,
            )
            return None

        # Calculate total price (required field but not used in PO creation)
        try:
            total_price = str(float(quantity_str) * float(unit_price))
        except (ValueError, TypeError):
            total_price = unit_price  # Fallback to unit price

        return PurchaseOrderItem(
            item_number=item_number,
            item_description=item_description,
            quantity=quantity,
            unit_of_measure="Each",  # Default - NetSuite doesn't provide this
            unit_price=unit_price,  # Keep as string
            total_price=total_price,
            requested_date=self._parse_date(fields.get("expectedReceiptDate")),
        )
