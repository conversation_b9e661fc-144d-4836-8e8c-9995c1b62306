"""Tests for IonQ NetSuite PO mapper."""

import unittest
from decimal import Decimal

from didero.ai.po_extraction.po_extraction import POExtractionAddressInformation
from didero.ai.purchase_order.schemas import PurchaseOrderDetails, PurchaseOrderItem

from .po_mapper import IonQNetSuitePOMapper


class IonQNetSuitePOMapperTests(unittest.TestCase):
    """Test cases for IonQ NetSuite PO mapper."""

    def setUp(self):
        """Set up test data."""
        self.mapper = IonQNetSuitePOMapper()

        # Sample V4 extraction data matching what NetSuite client returns
        self.sample_netsuite_data = {
            "metadata": {
                "extraction_timestamp": "2024-01-22T10:30:00Z",
                "extractor_version": "v4",
            },
            "header": {
                "tranId": "PO431",
                "memo": "WEB NUW1334695",
                "status": "PENDING_RECEIPT",
                "total": "1220.57",
                "currencyName": "USD",
                "tranDate": "2022-10-03T21:00:00.000-07:00",
                "terms": {"internalId": "4", "name": "Net 30"},
            },
            "vendor": {"internalId": "550", "name": "V10072 ThorLabs"},
            "addresses": {
                "billing": {
                    "country": "_unitedStates",
                    "addressee": "ThorLabs Inc.",
                    "addr1": "56 Sparta Ave",
                    "addr2": "Suite 200",
                    "city": "Newton",
                    "state": "NJ",
                    "zip": "07860",
                },
                "shipping": {
                    "country": "_unitedStates",
                    "addressee": "CP Tooling (NI)",
                    "addrText": "CP Tooling (NI)\\r\\nUnited States",
                },
            },
            "line_items": [
                {
                    "item_reference": {"internalId": "2761", "name": "502-00097"},
                    "fields": {
                        "description": "Compact Power and Energy Meter Console",
                        "vendorName": "PM100D",
                        "quantity": "1.0",
                        "rate": "1220.57",
                        "amount": "1220.57",
                        "expectedReceiptDate": "2022-11-03T21:00:00.000-07:00",
                    },
                    "custom_fields": {
                        "custcol_ionq_supplierpromisedatefield": "2022-11-03T21:00:00.000-07:00"
                    },
                }
            ],
            "custom_fields": {
                "header": {"custbody_ionq_tracking_number": "1Z07X6270360947105"},
                "line_items": [],
            },
        }

    def test_map_to_purchase_order_details_success(self):
        """Test successful mapping of complete NetSuite data."""
        result = self.mapper.map_to_purchase_order_details(self.sample_netsuite_data)

        # Verify it returns a PurchaseOrderDetails object
        self.assertIsInstance(result, PurchaseOrderDetails)

        # Verify basic fields
        self.assertEqual(result.po_number, "PO431")
        self.assertEqual(result.supplier_name, "V10072 ThorLabs")
        self.assertEqual(result.currency, "USD")
        self.assertEqual(result.payment_terms, "Net 30")
        self.assertEqual(result.notes, "WEB NUW1334695")
        self.assertEqual(result.total_amount, "1220.57")
        self.assertEqual(result.order_date, "2022-10-03T21:00:00.000-07:00")

        # Verify addresses
        self.assertIsNotNone(result.supplier_address)
        self.assertIsInstance(result.supplier_address, POExtractionAddressInformation)
        self.assertEqual(result.supplier_address.line1, "56 Sparta Ave")
        self.assertEqual(result.supplier_address.line2, "Suite 200")
        self.assertEqual(result.supplier_address.city, "Newton")
        self.assertEqual(result.supplier_address.state, "NJ")
        self.assertEqual(result.supplier_address.zip, "07860")
        self.assertEqual(result.supplier_address.country, "US")

        self.assertIsNotNone(result.shipping_address)
        self.assertEqual(result.shipping_address.line1, "CP Tooling (NI)")
        self.assertEqual(result.shipping_address.country, "US")

        # Verify line items
        self.assertIsNotNone(result.items)
        self.assertEqual(len(result.items), 1)

        item = result.items[0]
        self.assertIsInstance(item, PurchaseOrderItem)
        self.assertEqual(item.item_number, "502-00097")
        self.assertEqual(
            item.item_description, "Compact Power and Energy Meter Console"
        )
        self.assertEqual(item.quantity, 1.0)
        self.assertEqual(item.unit_of_measure, "Each")
        self.assertEqual(item.unit_price, "1220.57")
        self.assertEqual(item.total_price, "1220.57")
        self.assertEqual(item.requested_date, "2022-11-03T21:00:00.000-07:00")

    def test_missing_po_number_raises_error(self):
        """Test that missing PO number raises ValueError."""
        data = self.sample_netsuite_data.copy()
        del data["header"]["tranId"]

        with self.assertRaises(ValueError) as context:
            self.mapper.map_to_purchase_order_details(data)

        self.assertIn("po_number", str(context.exception))

    def test_missing_supplier_name_raises_error(self):
        """Test that missing supplier name raises ValueError."""
        data = self.sample_netsuite_data.copy()
        del data["vendor"]["name"]

        with self.assertRaises(ValueError) as context:
            self.mapper.map_to_purchase_order_details(data)

        self.assertIn("supplier_name", str(context.exception))

    def test_country_code_mapping(self):
        """Test NetSuite country code to ISO code mapping."""
        # Test various country codes
        test_cases = [
            ("_unitedStates", "US"),
            ("_unitedKingdom", "GB"),
            ("_canada", "CA"),
            ("_france", "FR"),
            ("_unknown", "US"),  # Default fallback
        ]

        for netsuite_code, expected_iso in test_cases:
            data = self.sample_netsuite_data.copy()
            data["addresses"]["billing"]["country"] = netsuite_code

            result = self.mapper.map_to_purchase_order_details(data)
            self.assertEqual(result.supplier_address.country, expected_iso)

    def test_minimal_shipping_address(self):
        """Test handling of minimal shipping address data."""
        data = self.sample_netsuite_data.copy()
        # Remove structured shipping address, keep only addressee
        data["addresses"]["shipping"] = {
            "country": "_unitedStates",
            "addressee": "Minimal Company",
        }

        result = self.mapper.map_to_purchase_order_details(data)

        self.assertIsNotNone(result.shipping_address)
        self.assertEqual(result.shipping_address.line1, "Minimal Company")
        self.assertEqual(result.shipping_address.country, "US")
        self.assertEqual(result.shipping_address.city, "")
        self.assertEqual(result.shipping_address.state, "")

    def test_parse_address_text(self):
        """Test parsing of raw address text."""
        test_cases = [
            {
                "input": "CP Tooling (NI)\\r\\nUnited States",
                "expected_line1": "CP Tooling (NI)",
                "expected_country": "US",
            },
            {
                "input": "Company Name\\r\\nCanada",
                "expected_line1": "Company Name",
                "expected_country": "CA",
            },
        ]

        for case in test_cases:
            result = self.mapper._parse_address_text(case["input"])
            self.assertEqual(result.line1, case["expected_line1"])
            self.assertEqual(result.country, case["expected_country"])

    def test_invalid_line_items_handling(self):
        """Test handling of invalid line items."""
        data = self.sample_netsuite_data.copy()

        # Add some invalid line items
        data["line_items"].extend(
            [
                # Missing item_number
                {
                    "item_reference": {},
                    "fields": {
                        "description": "Item without number",
                        "quantity": "1.0",
                        "rate": "100.00",
                    },
                },
                # Missing description
                {
                    "item_reference": {"name": "ITEM002"},
                    "fields": {"quantity": "1.0", "rate": "200.00"},
                },
                # Invalid quantity
                {
                    "item_reference": {"name": "ITEM003"},
                    "fields": {
                        "description": "Item with bad quantity",
                        "quantity": "not_a_number",
                        "rate": "300.00",
                    },
                },
            ]
        )

        result = self.mapper.map_to_purchase_order_details(data)

        # Should still have the original valid item
        self.assertEqual(len(result.items), 1)
        self.assertEqual(result.items[0].item_number, "502-00097")

    def test_no_addresses(self):
        """Test handling when no addresses are provided."""
        data = self.sample_netsuite_data.copy()
        data["addresses"] = {}

        result = self.mapper.map_to_purchase_order_details(data)

        self.assertIsNone(result.supplier_address)
        self.assertIsNone(result.shipping_address)

    def test_no_line_items(self):
        """Test handling when no line items are provided."""
        data = self.sample_netsuite_data.copy()
        data["line_items"] = []

        result = self.mapper.map_to_purchase_order_details(data)

        self.assertEqual(len(result.items), 0)

    def test_multiple_line_items(self):
        """Test mapping multiple line items."""
        data = self.sample_netsuite_data.copy()

        # Add a second line item
        data["line_items"].append(
            {
                "item_reference": {"internalId": "2762", "name": "502-00098"},
                "fields": {
                    "description": "S120C Standard Photodiode Power Sensor",
                    "vendorName": "S120C",
                    "quantity": "2.0",
                    "rate": "366.71",
                    "amount": "733.42",
                    "expectedReceiptDate": "2022-10-05T21:00:00.000-07:00",
                },
            }
        )

        result = self.mapper.map_to_purchase_order_details(data)

        self.assertEqual(len(result.items), 2)

        # Check first item
        self.assertEqual(result.items[0].item_number, "502-00097")
        self.assertEqual(result.items[0].quantity, 1.0)

        # Check second item
        self.assertEqual(result.items[1].item_number, "502-00098")
        self.assertEqual(
            result.items[1].item_description, "S120C Standard Photodiode Power Sensor"
        )
        self.assertEqual(result.items[1].quantity, 2.0)
        self.assertEqual(result.items[1].unit_price, "366.71")
        self.assertEqual(result.items[1].total_price, "733.42")


if __name__ == "__main__":
    unittest.main()
