"""NetSuite SOAP API client implementation.

This module provides a SOAP-based client for interacting with NetSuite's web services.
It handles OAuth 1.0a authentication, SOAP envelope construction, and provides methods
for searching and updating purchase orders.

Key features:
- OAuth 1.0a signature generation with HMAC-SHA256
- SOAP envelope construction with proper namespaces
- Purchase order search using TransactionSearchBasic
- Field updates for both header and line item fields
- Automatic date formatting for NetSuite's expected format
"""

import base64
import hashlib
import hmac
import random
import re
import string
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import requests
import structlog
from requests.exceptions import RequestException

from didero.integrations.erp.customers.ionq.constants import (
    ESTIMATED_DELIVERY_DATE,
    FIELD_MAP,
    IONQ_HEADER_FIELDS,
    IONQ_LINE_FIELDS,
    NS_LINE_EXPECTED_RECEIPT_DATE,
    NS_LINE_PROMISED_SHIP_DATE,
    PROMISED_SHIP_DATE,
    TRACKING_NUMBER,
)
from didero.integrations.erp.schemas import ERPUpdateRequest, ERPUpdateResponse

from .base import ERPClientBase

logger = structlog.get_logger(__name__)


class NetSuiteClient(ERPClientBase):
    """NetSuite SOAP API client using OAuth 1.0 authentication.

    This client implements NetSuite's SOAP web services using OAuth 1.0a for authentication.
    It provides methods for searching, retrieving, and updating purchase orders in NetSuite.

    The client handles:
    - OAuth signature generation following NetSuite's specific requirements
    - SOAP envelope construction with proper XML namespaces
    - Field mapping between internal names and NetSuite field IDs
    - Date formatting to NetSuite's expected ISO 8601 format with timezone
    """

    # Default SOAP API version if not specified in config
    # NetSuite releases new API versions periodically - update this as needed
    DEFAULT_API_VERSION = "2023_2"

    def __init__(
        self,
        credentials: Dict[str, str],
        config: Optional[Dict[str, Any]] = None,
        field_mappings: Optional[Dict[str, str]] = None,
    ):
        """
        Initialize NetSuite client with OAuth credentials and configuration.

        Args:
            credentials: Dictionary containing required OAuth credentials
                - account_id: NetSuite account ID (e.g., "1234567" or "1234567_SB1")
                - consumer_key: OAuth consumer key from NetSuite integration record
                - consumer_secret: OAuth consumer secret from NetSuite integration record
                - token_id: Access token ID generated for specific user/role/integration
                - token_secret: Access token secret paired with token_id

            config: Optional configuration dictionary
                - api_version: SOAP API version (default: "2023_2")
                  NetSuite releases new versions periodically. Format: "YYYY_R" where R is release number
                - endpoint: Custom SOAP endpoint URL (auto-generated from account_id if not provided)
                  Useful for sandbox vs production environments

            field_mappings: Optional dictionary mapping internal field names to NetSuite field IDs
                Example: {"tracking_number": "custbody_ionq_tracking_number"}

        Raises:
            ValueError: If any required credentials are missing

        Example:
            >>> credentials = {
            ...     "account_id": "1234567_SB1",
            ...     "consumer_key": "abc123...",
            ...     "consumer_secret": "def456...",
            ...     "token_id": "ghi789...",
            ...     "token_secret": "jkl012..."
            ... }
            >>> client = NetSuiteClient(credentials)
        """
        super().__init__(credentials, config, field_mappings)

        # Validate required credentials
        required_fields = [
            "account_id",
            "consumer_key",
            "consumer_secret",
            "token_id",
            "token_secret",
        ]
        missing_fields = [f for f in required_fields if not credentials.get(f)]
        if missing_fields:
            raise ValueError(f"Missing required credentials: {missing_fields}")

        # Set up configuration
        self.api_version = self.config.get("api_version", self.DEFAULT_API_VERSION)
        self.endpoint = self._get_endpoint()
        self.session = requests.Session()

    def _get_endpoint(self) -> str:
        """Generate the SOAP endpoint URL based on account ID and API version.

        NetSuite's SOAP endpoint URL follows a specific pattern:
        https://{account-id}.suitetalk.api.netsuite.com/services/NetSuitePort_{api_version}

        The account ID in the URL must have underscores replaced with hyphens and be lowercase.
        For example: "1234567_SB1" becomes "1234567-sb1"

        Returns:
            str: The complete SOAP endpoint URL

        Example:
            For account_id="1234567_SB1" and api_version="2023_2":
            Returns: "https://1234567-sb1.suitetalk.api.netsuite.com/services/NetSuitePort_2023_2"
        """
        # Allow override via config for custom endpoints (useful for testing)
        if self.config.get("endpoint"):
            return self.config["endpoint"]

        # Generate endpoint from account ID
        account_id = self.credentials["account_id"]
        # NetSuite requires underscores to be replaced with hyphens in the URL
        # and the account ID must be lowercase
        url_account_id = account_id.replace("_", "-").lower()

        return f"https://{url_account_id}.suitetalk.api.netsuite.com/services/NetSuitePort_{self.api_version}"

    def _generate_oauth_signature(self) -> Tuple[str, str, str]:
        """
        Generate OAuth 1.0a signature for NetSuite SOAP requests.

        NetSuite uses a specific OAuth 1.0a implementation for SOAP authentication:
        1. Generate a timestamp (seconds since epoch) and random nonce
        2. Create a base string by concatenating: account_id, consumer_key, token_id, nonce, timestamp
        3. Create signing key by concatenating: consumer_secret & token_secret
        4. Generate HMAC-SHA256 signature of the base string using the signing key
        5. Base64 encode the signature

        This differs from standard OAuth 1.0a which typically includes HTTP method, URL, and
        parameters in the base string. NetSuite's SOAP implementation uses this simplified approach.

        Returns:
            Tuple[str, str, str]: A tuple containing:
                - timestamp: Current Unix timestamp as string
                - nonce: Random 20-character alphanumeric string
                - signature: Base64-encoded HMAC-SHA256 signature

        Example:
            >>> timestamp, nonce, signature = client._generate_oauth_signature()
            >>> print(f"Timestamp: {timestamp}, Nonce: {nonce}, Signature: {signature[:10]}...")
            Timestamp: **********, Nonce: a1B2c3D4e5F6g7H8i9J0, Signature: dGhpcyBpcyBh...
        """
        # Generate timestamp as seconds since Unix epoch
        timestamp = str(int(time.time()))

        # Generate a random nonce (number used once) to prevent replay attacks
        # NetSuite expects a 20-character alphanumeric string
        nonce = "".join(random.choices(string.ascii_letters + string.digits, k=20))

        # Create base string for signature
        # NetSuite requires these 5 elements joined with "&" in this specific order
        base_string = "&".join(
            [
                self.credentials["account_id"],
                self.credentials["consumer_key"],
                self.credentials["token_id"],
                nonce,
                timestamp,
            ]
        )

        # Create signing key by concatenating consumer secret and token secret with "&"
        # This is standard OAuth 1.0a practice
        key = (
            f"{self.credentials['consumer_secret']}&{self.credentials['token_secret']}"
        )

        # Generate HMAC-SHA256 signature
        # 1. Create HMAC object with the signing key and SHA256 algorithm
        # 2. Update with the base string
        # 3. Get the digest (raw bytes)
        # 4. Base64 encode the digest
        signature = base64.b64encode(
            hmac.new(
                key.encode("utf-8"), base_string.encode("utf-8"), hashlib.sha256
            ).digest()
        ).decode("utf-8")

        return timestamp, nonce, signature

    def _create_soap_envelope(self, body_content: str) -> str:
        """
        Create a complete SOAP envelope with OAuth authentication header.

        NetSuite's SOAP API requires specific XML namespaces and structure:

        1. XML Namespaces:
           - soap: Standard SOAP envelope namespace
           - xsi: XML Schema instance for type definitions
           - platformCore: Core NetSuite platform types (includes auth elements)
           - platformMsgs: NetSuite message types (includes request/response elements)
           - tranPurch: Purchase transaction types (for PO operations)

        2. Authentication Header:
           - Uses tokenPassport element with OAuth credentials
           - Must include all 6 elements in specific order
           - Signature algorithm must be specified as "HMAC-SHA256"

        3. Version-specific namespaces:
           - All NetSuite namespaces include the API version (e.g., 2023_2)
           - This ensures compatibility with the specific API version

        Args:
            body_content: The SOAP body content (request-specific XML)

        Returns:
            str: Complete SOAP envelope with authentication header

        Example body_content:
            <platformMsgs:get>
                <platformMsgs:baseRef xsi:type="platformCore:RecordRef"
                                     type="purchaseOrder" internalId="123"/>
            </platformMsgs:get>
        """
        # Generate fresh OAuth signature for this request
        timestamp, nonce, signature = self._generate_oauth_signature()

        # Construct SOAP envelope with proper namespaces and authentication
        # Note: The formatting and order of elements is important for NetSuite
        return f"""<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" 
               xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:platformCore="urn:core_{self.api_version}.platform.webservices.netsuite.com"
               xmlns:platformMsgs="urn:messages_{self.api_version}.platform.webservices.netsuite.com"
               xmlns:tranPurch="urn:purchases_{self.api_version}.transactions.webservices.netsuite.com">
    <soap:Header>
        <platformMsgs:tokenPassport>
            <platformCore:account>{self.credentials['account_id']}</platformCore:account>
            <platformCore:consumerKey>{self.credentials['consumer_key']}</platformCore:consumerKey>
            <platformCore:token>{self.credentials['token_id']}</platformCore:token>
            <platformCore:nonce>{nonce}</platformCore:nonce>
            <platformCore:timestamp>{timestamp}</platformCore:timestamp>
            <platformCore:signature algorithm="HMAC-SHA256">{signature}</platformCore:signature>
        </platformMsgs:tokenPassport>
    </soap:Header>
    <soap:Body>
        {body_content}
    </soap:Body>
</soap:Envelope>"""

    def _make_soap_request(self, action: str, body: str) -> requests.Response:
        """
        Make a SOAP request to NetSuite with proper headers and error handling.

        This method:
        1. Wraps the body content in a complete SOAP envelope with auth
        2. Sets required HTTP headers including SOAPAction
        3. Makes the POST request to the NetSuite endpoint
        4. Handles errors and logging

        Args:
            action: SOAP action name (e.g., "search", "update", "get")
                   This becomes the SOAPAction HTTP header value
            body: SOAP body content (the actual request XML)
                  Should NOT include the envelope - just the body content

        Returns:
            requests.Response: The HTTP response object

        Raises:
            RequestException: If the HTTP request fails

        Example:
            body = '<platformMsgs:get>...</platformMsgs:get>'
            response = self._make_soap_request("get", body)
        """
        # NetSuite requires specific headers for SOAP requests
        headers = {"Content-Type": "text/xml; charset=utf-8", "SOAPAction": action}

        # Wrap body in complete SOAP envelope with OAuth authentication
        envelope = self._create_soap_envelope(body)

        try:
            # Make the POST request with a reasonable timeout
            response = self.session.post(
                self.endpoint, data=envelope, headers=headers, timeout=30
            )

            # Log non-200 responses for debugging
            # NetSuite may return SOAP faults with 500 status
            if response.status_code != 200:
                logger.error(
                    "NetSuite SOAP error response",
                    status_code=response.status_code,
                    response_text=response.text[
                        :1000
                    ],  # First 1000 chars to avoid huge logs
                )

            # Raise exception for HTTP errors
            response.raise_for_status()
            return response

        except RequestException as e:
            logger.error(
                "NetSuite SOAP request failed",
                action=action,
                endpoint=self.endpoint,
                error=str(e),
            )
            raise

    def _parse_soap_response(self, response: requests.Response) -> Dict[str, Any]:
        """
        Parse SOAP response and check for success or error status.

        NetSuite SOAP responses include a status element that indicates success/failure:
        <platformCore:status isSuccess="true"> or <platformCore:status isSuccess="false">

        For errors, the response includes:
        - platformCore:message: Human-readable error message
        - platformCore:code: Error code (optional)

        This method performs basic parsing to:
        1. Check if the operation succeeded
        2. Extract error details if it failed
        3. Return the raw response for further processing if needed

        Args:
            response: HTTP response from NetSuite SOAP request

        Returns:
            Dict[str, Any]: Dictionary containing:
                - success: True if operation succeeded
                - raw_response: Complete XML response text for further parsing

        Raises:
            Exception: If the SOAP response indicates an error, with message and code

        Example error response:
            <platformCore:status isSuccess="false">
                <platformCore:statusDetail>
                    <platformCore:code>INVALID_LOGIN_CREDENTIALS</platformCore:code>
                    <platformCore:message>Invalid login attempt.</platformCore:message>
                </platformCore:statusDetail>
            </platformCore:status>
        """
        xml_text = response.text

        # Check for success status in the response
        # NetSuite uses isSuccess attribute to indicate operation result
        if '<platformCore:status isSuccess="true"' in xml_text:
            return {"success": True, "raw_response": xml_text}

        # If not successful, extract error details
        # Use regex to parse error message from XML
        error_match = re.search(
            r"<platformCore:message>([^<]+)</platformCore:message>", xml_text
        )
        error_msg = error_match.group(1) if error_match else "Unknown error"

        # Also try to extract error code for more specific error handling
        code_match = re.search(
            r"<platformCore:code>([^<]+)</platformCore:code>", xml_text
        )
        error_code = code_match.group(1) if code_match else None

        # Log the error for debugging
        logger.error(
            "NetSuite SOAP error",
            error_message=error_msg,
            error_code=error_code,
        )

        # Raise exception with formatted error message
        raise Exception(
            f"NetSuite error: {error_msg}"
            + (f" (Code: {error_code})" if error_code else "")
        )

    def test_connection(self) -> bool:
        """
        Test connection to NetSuite by requesting the server time.

        This is a lightweight operation that verifies:
        - OAuth credentials are valid
        - Network connectivity to NetSuite
        - SOAP envelope construction is correct
        - API version compatibility

        The getServerTime operation requires no parameters and returns
        the current server timestamp, making it ideal for connection testing.

        Returns:
            bool: True if connection is successful, False otherwise

        Example:
            >>> if client.test_connection():
            ...     print("Connected to NetSuite successfully")
            ... else:
            ...     print("Failed to connect to NetSuite")
        """
        try:
            # Simple request body - getServerTime requires no parameters
            body = """<platformMsgs:getServerTime/>"""

            response = self._make_soap_request("getServerTime", body)
            result = self._parse_soap_response(response)

            logger.info("NetSuite connection test successful")
            return result["success"]

        except Exception as e:
            logger.error("NetSuite connection test failed", error=str(e))
            return False

    def get_purchase_order(self, po_number: str) -> Dict[str, Any]:
        """
        Search for a purchase order by PO number and return its internal ID.

        Why we use TransactionSearchBasic:
        NetSuite has multiple search types, but for searching across different transaction
        types (POs, SOs, Invoices, etc.) by transaction ID, TransactionSearchBasic is the
        appropriate choice. It provides a unified way to search transactions by their
        document numbers (tranId field).

        The search uses an exact match operator ("is") to find the specific PO.

        Important namespace handling:
        The search operation has a quirk where it doesn't expect the platformMsgs prefix
        on the search element itself, even though it's in the platformMsgs namespace.
        We handle this by creating the envelope normally then removing the prefix.

        Args:
            po_number: The purchase order number to search for (e.g., "PO12345")

        Returns:
            Dict[str, Any]: Dictionary containing:
                - internal_id: NetSuite's internal ID for the PO
                - po_number: The PO number that was searched

        Raises:
            Exception: If the PO is not found or if the search fails

        Example:
            >>> po_data = client.get_purchase_order("PO12345")
            >>> print(f"Found PO with internal ID: {po_data['internal_id']}")
        """
        # Construct search request using TransactionSearchBasic
        # This search type allows searching across all transaction types by tranId
        body = f"""<search xmlns="urn:messages_{self.api_version}.platform.webservices.netsuite.com">
            <searchRecord xmlns:q1="urn:common_{self.api_version}.platform.webservices.netsuite.com" 
                         xsi:type="q1:TransactionSearchBasic">
                <tranId operator="is" searchValue="{po_number}" 
                       xmlns="urn:common_{self.api_version}.platform.webservices.netsuite.com"/>
            </searchRecord>
        </search>"""

        # Create SOAP envelope with standard method
        envelope = self._create_soap_envelope(body)

        # NetSuite quirk: The search element should not have the platformMsgs prefix
        # even though it's technically in that namespace. Remove the prefix.
        envelope = envelope.replace("<platformMsgs:search", "<search")
        envelope = envelope.replace("</platformMsgs:search>", "</search>")

        headers = {"Content-Type": "text/xml; charset=utf-8", "SOAPAction": "search"}

        try:
            response = self.session.post(
                self.endpoint, data=envelope, headers=headers, timeout=30
            )

            if response.status_code != 200:
                logger.error(
                    "NetSuite search failed",
                    status_code=response.status_code,
                    response_text=response.text[:500],
                )
                response.raise_for_status()

        except Exception as e:
            logger.error("NetSuite search request failed", error=str(e))
            raise

        # Verify we got search results
        if "searchResult" not in response.text:
            raise Exception(f"No search results found for PO: {po_number}")

        # Extract internal ID from the search result
        # NetSuite returns records with their internalId as an attribute
        id_match = re.search(
            r'<platformCore:record[^>]*internalId="(\d+)"', response.text
        )
        if not id_match:
            raise Exception(f"Purchase order not found: {po_number}")

        # Double-check we found the right PO by verifying the tranId matches
        tranid_match = re.search(
            r"<tranPurch:tranId>([^<]+)</tranPurch:tranId>", response.text
        )
        if tranid_match and tranid_match.group(1) != po_number:
            logger.warning(
                "PO number mismatch",
                searched_for=po_number,
                found=tranid_match.group(1),
            )

        return {"internal_id": id_match.group(1), "po_number": po_number}

    def update_purchase_order(
        self,
        po_identifier: str,
        updates: ERPUpdateRequest,
    ) -> ERPUpdateResponse:
        """
        Update fields on a purchase order including both header and line item fields.

        Field Update Process:
        1. Maps field names using field_mappings configuration
        2. Separates fields into header fields (PO-level) and line item fields
        3. Constructs update request with appropriate field types
        4. Uses replaceAll="false" to preserve existing data

        Header vs Line Item Fields:
        - Header fields: Apply to the entire PO (e.g., tracking number)
        - Line item fields: Apply to individual lines (e.g., expected receipt date)

        Currently supported fields:
        - Header: custbody_ionq_tracking_number (tracking number)
        - Line items: expectedreceiptdate, custcol_ionq_supplierpromisedatefield

        Note: Currently updates only the first line item. Future enhancement
        could support updating specific lines or all lines.

        Args:
            po_identifier: Either the PO number (e.g., "PO12345") or internal ID
            updates: ERPUpdateRequest with fields to update

        Returns:
            ERPUpdateResponse with success status and internal ID

        Raises:
            Exception: If the update fails or PO is not found

        Example:
            >>> updates = ERPUpdateRequest(
            ...     tracking_number="1Z999AA10123456784",
            ...     estimated_delivery_date=datetime(2024, 1, 20),
            ...     promised_ship_date=datetime(2024, 1, 15)
            ... )
            >>> result = client.update_purchase_order("PO12345", updates)
        """
        erp_updates = {}

        # Map individual fields that have values
        if updates.tracking_number:
            mapped = self.map_fields({TRACKING_NUMBER: updates.tracking_number})
            erp_updates.update(mapped)

        if updates.estimated_delivery_date:
            mapped = self.map_fields(
                {ESTIMATED_DELIVERY_DATE: updates.estimated_delivery_date}
            )
            erp_updates.update(mapped)

        if updates.promised_ship_date:
            mapped = self.map_fields({PROMISED_SHIP_DATE: updates.promised_ship_date})
            erp_updates.update(mapped)

        # Handle line items as structured data
        if updates.line_items:
            erp_updates["line_items"] = updates.line_items

        # Log the field mapping for debugging
        logger.info("Updates before mapping", updates=updates.model_dump())
        logger.info("Updates after mapping", erp_updates=erp_updates)

        # Determine if we need to look up the internal ID
        # If po_identifier is purely numeric, assume it's already an internal ID
        if not po_identifier.isdigit():
            po_data = self.get_purchase_order(po_identifier)
            internal_id = po_data["internal_id"]
        else:
            internal_id = po_identifier

        # Categorize fields as header-level or line-level based on their NetSuite field IDs
        # Header fields apply to the entire purchase order
        # Line fields apply to individual line items
        header_fields = {}
        line_fields = {}

        for field, value in erp_updates.items():
            # Custom body fields (header-level fields)
            if field in IONQ_HEADER_FIELDS:
                header_fields[field] = value
            # Line item fields (both standard and custom column fields)
            elif field in IONQ_LINE_FIELDS:
                line_fields[field] = value

        # Build the SOAP update request body
        # Start with the update wrapper and record specification
        body_parts = [
            "<platformMsgs:update>",
            # Specify we're updating a PurchaseOrder using its internal ID
            f'    <platformMsgs:record xsi:type="tranPurch:PurchaseOrder" internalId="{internal_id}">',
        ]

        # Add header-level custom fields if present
        # These are wrapped in a customFieldList at the PO level
        if header_fields:
            body_parts.append("        <tranPurch:customFieldList>")
            for field, value in header_fields.items():
                # NetSuite custom fields require type specification
                # Using StringCustomFieldRef for text fields like tracking number
                body_parts.append(
                    f'            <platformCore:customField xsi:type="platformCore:StringCustomFieldRef" scriptId="{field}">'
                )
                body_parts.append(
                    f"                <platformCore:value>{value}</platformCore:value>"
                )
                body_parts.append("            </platformCore:customField>")
            body_parts.append("        </tranPurch:customFieldList>")

        # GUARD LOGIC: Only process line items if we actually have line-level field updates
        # This prevents NetSuite "amount" errors when updating only header fields
        has_line_field_updates = any(field in erp_updates for field in IONQ_LINE_FIELDS)

        # Check if we have structured line items data AND need line-level updates
        if (
            "line_items" in erp_updates
            and isinstance(erp_updates["line_items"], list)
            and has_line_field_updates
        ):
            # New behavior: structured line-item-specific data
            body_parts.append('        <tranPurch:itemList replaceAll="false">')

            for line_item in erp_updates["line_items"]:
                body_parts.append("            <tranPurch:item>")
                body_parts.append(
                    f"                <tranPurch:line>{line_item.line}</tranPurch:line>"
                )

                # Handle expected receipt date
                if line_item.estimated_delivery_date:
                    date_value = line_item.estimated_delivery_date
                    if hasattr(date_value, "strftime"):
                        date_value = date_value.strftime("%Y-%m-%dT%H:%M:%S.000-07:00")
                    body_parts.append(
                        f"                <tranPurch:expectedReceiptDate>{date_value}</tranPurch:expectedReceiptDate>"
                    )

                # Handle promised ship date - SPECIFIC to this line item
                if line_item.promised_ship_date:
                    date_value = line_item.promised_ship_date
                    if hasattr(date_value, "strftime"):
                        date_value = date_value.strftime("%Y-%m-%dT%H:%M:%S.000-07:00")

                    body_parts.append("                <tranPurch:customFieldList>")
                    body_parts.append(
                        f'                    <platformCore:customField xsi:type="platformCore:DateCustomFieldRef" scriptId="{NS_LINE_PROMISED_SHIP_DATE}">'
                    )
                    body_parts.append(
                        f"                        <platformCore:value>{date_value}</platformCore:value>"
                    )
                    body_parts.append("                    </platformCore:customField>")
                    body_parts.append("                </tranPurch:customFieldList>")

                body_parts.append("            </tranPurch:item>")

                logger.info(
                    "Added line item to update",
                    line=line_item.line,
                    item_number=line_item.item_number,
                    promised_date=line_item.promised_ship_date,
                )

            body_parts.append("        </tranPurch:itemList>")

        elif line_fields:
            # Old behavior: flat structure with single date for all items
            # Use replaceAll="false" to update specific lines without replacing all items
            # This preserves existing line data while updating only specified fields
            body_parts.append('        <tranPurch:itemList replaceAll="false">')

            # First, retrieve the current PO details to get line numbers
            # This ensures we're updating existing lines rather than creating new ones
            po_data = self._get_purchase_order_details(internal_id)
            line_numbers = self._extract_line_numbers(po_data["raw_response"])

            logger.info(
                "Processing line item updates",
                po_identifier=po_identifier,
                total_lines=len(line_numbers),
                line_numbers=line_numbers,
                line_fields=list(line_fields.keys()),
            )

            # Update ALL line items (fixed from previous [:1] limitation)
            # This ensures multi-line POs get complete updates across all lines
            for line_num in line_numbers:
                body_parts.append("            <tranPurch:item>")
                # Line number is required to identify which line to update
                body_parts.append(
                    f"                <tranPurch:line>{line_num}</tranPurch:line>"
                )

                # Handle standard line fields
                if NS_LINE_EXPECTED_RECEIPT_DATE in line_fields:
                    # NetSuite date format: ISO 8601 with timezone
                    # Example: 2024-01-15T00:00:00.000-07:00 (MST/PDT)
                    date_value = line_fields[NS_LINE_EXPECTED_RECEIPT_DATE]
                    if hasattr(date_value, "strftime"):
                        # Convert datetime object to NetSuite's expected format
                        # Using -07:00 for MST/PDT (adjust as needed for your timezone)
                        date_value = date_value.strftime("%Y-%m-%dT%H:%M:%S.000-07:00")
                    body_parts.append(
                        f"                <tranPurch:expectedReceiptDate>{date_value}</tranPurch:expectedReceiptDate>"
                    )

                # Handle custom column fields (line-level custom fields)
                if NS_LINE_PROMISED_SHIP_DATE in line_fields:
                    date_value = line_fields[NS_LINE_PROMISED_SHIP_DATE]
                    if hasattr(date_value, "strftime"):
                        # Same date format as above
                        date_value = date_value.strftime("%Y-%m-%dT%H:%M:%S.000-07:00")

                    # Custom line fields go in their own customFieldList
                    body_parts.append("                <tranPurch:customFieldList>")
                    # Note: Using DateCustomFieldRef for date fields (not StringCustomFieldRef)
                    body_parts.append(
                        f'                    <platformCore:customField xsi:type="platformCore:DateCustomFieldRef" scriptId="{NS_LINE_PROMISED_SHIP_DATE}">'
                    )
                    body_parts.append(
                        f"                        <platformCore:value>{date_value}</platformCore:value>"
                    )
                    body_parts.append("                    </platformCore:customField>")
                    body_parts.append("                </tranPurch:customFieldList>")

                body_parts.append("            </tranPurch:item>")

            body_parts.append("        </tranPurch:itemList>")

        body_parts.extend(["    </platformMsgs:record>", "</platformMsgs:update>"])

        body = "\n".join(body_parts)

        # Log the body for debugging
        logger.info("Update request body", body=body[:500])

        # Make the update request
        response = self._make_soap_request("update", body)
        self._parse_soap_response(response)

        logger.info(
            "Purchase order updated",
            po_identifier=po_identifier,
            internal_id=internal_id,
            fields_updated=list(erp_updates.keys()),
        )

        return ERPUpdateResponse(success=True, internal_id=internal_id)

    def get_complete_purchase_order(self, po_number: str) -> Dict[str, Any]:
        """
        IonQ ERP Integration: Fetch complete purchase order data from NetSuite using V4 extraction.

        This method implements the core NetSuite data extraction for IonQ's ERP integration.
        It provides a complete, structured representation of a purchase order from NetSuite,
        including all line items, addresses, vendor information, and custom fields.

        The V4 extraction pattern ensures consistent, reliable data extraction that handles:
        - Complex NetSuite XML structures and namespaces
        - Multiple address types (billing, shipping)
        - Line item arrays with pricing and descriptions
        - Custom fields specific to IonQ's NetSuite configuration
        - Proper error handling and validation

        This method replaces the need for RPA by providing direct API access to complete
        PO data, resulting in faster, more reliable extraction with rich metadata.

        Process:
        1. Searches for the PO by number to get internal ID
        2. Fetches full PO details using the internal ID
        3. Extracts structured data using the proven V4 pattern
        4. Returns clean, structured data ready for IonQ-specific mapping

        Args:
            po_number: The purchase order number to fetch (e.g., "PO431")

        Returns:
            Dict[str, Any]: Complete PO data in V4 extraction schema format with:
                - header: Basic PO information (number, dates, terms)
                - vendor: Supplier information and details
                - addresses: Billing and shipping address structures
                - line_items: Array of items with pricing and descriptions
                - custom_fields: IonQ-specific NetSuite customizations

        Raises:
            Exception: If PO is not found or extraction fails

        Example:
            >>> po_data = client.get_complete_purchase_order("PO431")
            >>> print(po_data["header"]["tranId"])  # "PO431"
            >>> print(po_data["vendor"]["name"])    # "V10072 ThorLabs"
        """
        logger.info(
            "Starting complete PO extraction from NetSuite",
            po_number=po_number,
            extraction_method="v4_clean",
        )

        try:
            # Step 1: Search for PO to get internal ID
            po_search_result = self.get_purchase_order(po_number)
            internal_id = po_search_result["internal_id"]

            logger.info(
                "Found PO in NetSuite", po_number=po_number, internal_id=internal_id
            )

            # Step 2: Get full PO details using internal ID
            po_details = self._get_purchase_order_details(internal_id)
            xml_response = po_details["raw_response"]

            # Step 3: Apply V4 extraction pattern
            extracted_data = self._extract_purchase_order_v4(xml_response)

            logger.info(
                "Successfully extracted complete PO data",
                po_number=po_number,
                vendor_name=extracted_data.get("vendor", {}).get("name", "Unknown"),
                line_item_count=len(extracted_data.get("line_items", [])),
                has_custom_fields=bool(
                    extracted_data.get("custom_fields", {}).get("header")
                ),
            )

            return extracted_data

        except Exception as e:
            logger.error(
                "Failed to extract complete PO from NetSuite",
                po_number=po_number,
                error=str(e),
                exc_info=True,
            )
            raise Exception(f"Failed to fetch complete PO {po_number}: {str(e)}")

    def _extract_purchase_order_v4(self, xml_text: str) -> Dict[str, Any]:
        """
        Extract all PO data from NetSuite XML using V4 clean extraction pattern.

        Returns raw but structured data - mapper handles all conversions.
        This method is adapted from experiment_netsuite_extraction_v4.py
        """
        result = {
            "metadata": {
                "extraction_timestamp": datetime.now().isoformat(),
                "extractor_version": "v4",
            },
            "header": self._extract_header_v4(xml_text),
            "vendor": self._extract_vendor_v4(xml_text),
            "addresses": {
                "billing": self._extract_address_v4(xml_text, "billingAddress"),
                "shipping": self._extract_address_v4(xml_text, "shippingAddress"),
            },
            "line_items": self._extract_line_items_v4(xml_text),
            "custom_fields": {
                "header": self._extract_custom_fields_v4(xml_text, "purchaseOrder"),
                "line_items": [],  # Populated during line item extraction
            },
        }

        return result

    def _extract_header_v4(self, xml_text: str) -> Dict[str, str]:
        """Extract all header-level fields using V4 pattern"""
        header = {}

        # Simple fields that are direct children of purchaseOrder
        simple_fields = [
            "tranId",
            "memo",
            "status",
            "total",
            "email",
            "currencyName",
            "tranDate",
            "createdDate",
            "lastModifiedDate",
            "nexus",
            "subsidiary",
            "location",
            "department",
        ]

        for field in simple_fields:
            pattern = f"<tranPurch:{field}>([^<]*)</tranPurch:{field}>"
            match = re.search(pattern, xml_text, re.DOTALL)
            if match and match.group(1).strip():
                header[field] = match.group(1).strip()

        # Fields with nested structure
        # Payment terms
        terms_match = re.search(
            r'<tranPurch:terms[^>]*internalId="([^"]*)"[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>',
            xml_text,
            re.DOTALL,
        )
        if terms_match:
            header["terms"] = {
                "internalId": terms_match.group(1),
                "name": terms_match.group(2).strip(),
            }

        # Shipping method
        ship_method_match = re.search(
            r'<tranPurch:shipMethod[^>]*internalId="([^"]*)"[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>',
            xml_text,
            re.DOTALL,
        )
        if ship_method_match:
            header["shipMethod"] = {
                "internalId": ship_method_match.group(1),
                "name": ship_method_match.group(2).strip(),
            }

        return header

    def _extract_vendor_v4(self, xml_text: str) -> Dict[str, str]:
        """Extract vendor/entity information using V4 pattern"""
        vendor = {}

        vendor_match = re.search(
            r'<tranPurch:entity[^>]*internalId="([^"]*)"[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>',
            xml_text,
            re.DOTALL,
        )
        if vendor_match:
            vendor = {
                "internalId": vendor_match.group(1),
                "name": vendor_match.group(2).strip(),
            }

        return vendor

    def _extract_address_v4(self, xml_text: str, address_type: str) -> Dict[str, str]:
        """Extract address fields without any conversion using V4 pattern"""
        address = {}

        pattern = f"<tranPurch:{address_type}[^>]*>(.*?)</tranPurch:{address_type}>"
        match = re.search(pattern, xml_text, re.DOTALL)
        if not match:
            return address

        addr_xml = match.group(1)

        # Extract all available address fields
        address_fields = [
            "country",
            "addressee",
            "addr1",
            "addr2",
            "addr3",
            "city",
            "state",
            "zip",
            "addrText",
            "override",
        ]

        for field in address_fields:
            field_pattern = f"<platformCommon:{field}>([^<]*)</platformCommon:{field}>"
            field_match = re.search(field_pattern, addr_xml, re.DOTALL)
            if field_match and field_match.group(1).strip():
                address[field] = field_match.group(1).strip()

        return address

    def _extract_line_items_v4(self, xml_text: str) -> List[Dict[str, Any]]:
        """Extract all line items with their fields using V4 pattern"""
        items = []

        itemlist_match = re.search(
            r"<tranPurch:itemList>(.*?)</tranPurch:itemList>", xml_text, re.DOTALL
        )
        if not itemlist_match:
            return items

        items_xml = itemlist_match.group(1)

        # The structure is actually flat - each item in the list has all fields as siblings
        # Split by the item reference tags to get each complete item
        item_blocks = re.split(r"(?=<tranPurch:item\s+internalId)", items_xml)

        for block in item_blocks:
            if not block.strip():
                continue
            # Each block contains the item reference and all its fields
            item = self._extract_single_item_v4(block)
            if item["item_reference"]:  # Only add if we found a valid item
                items.append(item)

        return items

    def _extract_single_item_v4(self, item_xml: str) -> Dict[str, Any]:
        """Extract all fields from a single line item using V4 pattern"""
        item = {"item_reference": {}, "fields": {}, "custom_fields": {}}

        # Extract item reference (the linked item) - it's a nested item tag
        ref_pattern = r'<tranPurch:item\s+internalId="([^"]*)"[^>]*>\s*<platformCore:name>([^<]*)</platformCore:name>'
        ref_match = re.search(ref_pattern, item_xml)
        if ref_match:
            item["item_reference"] = {
                "internalId": ref_match.group(1),
                "name": ref_match.group(2).strip(),
            }

        # Extract standard fields - they're siblings to the item reference
        standard_fields = [
            "line",
            "description",
            "vendorName",
            "quantity",
            "units",
            "rate",
            "amount",
            "expectedReceiptDate",
            "isClosed",
            "taxCode",
            "taxRate1",
            "taxRate2",
            "grossAmt",
        ]

        for field in standard_fields:
            pattern = f"<tranPurch:{field}>([^<]*)</tranPurch:{field}>"
            match = re.search(pattern, item_xml)
            if match and match.group(1).strip():
                item["fields"][field] = match.group(1).strip()

        # Extract custom fields for this line item
        custom_fields_match = re.search(
            r"<tranPurch:customFieldList[^>]*>(.*?)</tranPurch:customFieldList>",
            item_xml,
            re.DOTALL,
        )
        if custom_fields_match:
            custom_xml = custom_fields_match.group(1)
            custom_pattern = r'scriptId="([^"]+)"[^>]*>.*?<platformCore:value[^>]*>([^<]*)</platformCore:value>'
            for match in re.finditer(custom_pattern, custom_xml, re.DOTALL):
                if match.group(2).strip():
                    item["custom_fields"][match.group(1)] = match.group(2).strip()

        return item

    def _extract_custom_fields_v4(
        self, xml_text: str, level: str = "purchaseOrder"
    ) -> Dict[str, str]:
        """Extract custom fields at header level using V4 pattern"""
        custom_fields = {}

        # Look for customFieldList at the purchase order level
        if level == "purchaseOrder":
            pattern = (
                r"<tranPurch:customFieldList[^>]*>(.*?)</tranPurch:customFieldList>"
            )
            match = re.search(pattern, xml_text, re.DOTALL)
            if match:
                custom_xml = match.group(1)
                # Extract each custom field
                custom_pattern = r'scriptId="([^"]+)"[^>]*>.*?<platformCore:value[^>]*>([^<]*)</platformCore:value>'
                for field_match in re.finditer(custom_pattern, custom_xml, re.DOTALL):
                    if field_match.group(2).strip():
                        custom_fields[field_match.group(1)] = field_match.group(
                            2
                        ).strip()

        return custom_fields

    def _get_purchase_order_details(self, internal_id: str) -> Dict[str, Any]:
        """
        Retrieve full purchase order details using NetSuite's get operation.

        This method is used internally to fetch complete PO data, particularly
        to identify existing line items before performing updates.

        The get operation requires:
        - Record type: "purchaseOrder"
        - Internal ID: NetSuite's unique identifier for the record

        Args:
            internal_id: NetSuite internal ID of the purchase order

        Returns:
            Dict[str, Any]: Parsed response containing PO details
                Includes "raw_response" key with full XML for further parsing
        """
        body = f"""<platformMsgs:get>
            <platformMsgs:baseRef xsi:type="platformCore:RecordRef" type="purchaseOrder" internalId="{internal_id}">
            </platformMsgs:baseRef>
        </platformMsgs:get>"""

        response = self._make_soap_request("get", body)
        return self._parse_soap_response(response)

    def _extract_line_numbers(self, xml_response: str) -> List[str]:
        """
        Extract line numbers from a purchase order XML response.

        NetSuite PO line items include a <tranPurch:line> element that
        contains the line number. These numbers are used to identify
        specific lines when performing updates.

        Args:
            xml_response: Raw XML response from NetSuite containing PO data

        Returns:
            List[str]: List of line numbers found in the PO
                      Returns empty list if no lines found

        Example:
            Given XML containing:
            <tranPurch:item>
                <tranPurch:line>1</tranPurch:line>
                ...
            </tranPurch:item>
            <tranPurch:item>
                <tranPurch:line>2</tranPurch:line>
                ...
            </tranPurch:item>

            Returns: ["1", "2"]
        """
        return re.findall(r"<tranPurch:line>(\d+)</tranPurch:line>", xml_response)

    def update_shipment_fields(
        self,
        po_number: str,
        tracking_number: Optional[str] = None,
        estimated_delivery_date: Optional[datetime] = None,
        promised_ship_date: Optional[datetime] = None,
    ) -> ERPUpdateResponse:
        """
        Convenience method to update common shipment-related fields on a purchase order.

        This method handles the most common shipment updates:
        1. Tracking number (header field) - for package tracking
        2. Estimated delivery date (line field) - expected receipt date
        3. Promised ship date (line field) - supplier's promised ship date

        Date Formatting:
        - Automatically converts datetime objects to NetSuite's required format
        - Format: ISO 8601 with timezone (e.g., 2024-01-15T00:00:00.000-07:00)
        - If dates are already strings, assumes they're properly formatted

        Field Mapping:
        This method uses internal field names that get mapped to NetSuite field IDs:
        - "tracking_number" -> "custbody_ionq_tracking_number" (header)
        - "estimated_delivery_date" -> "expectedreceiptdate" (line item)
        - "promised_ship_date" -> "custcol_ionq_supplierpromisedatefield" (line item)

        Args:
            po_number: The purchase order number to update
            tracking_number: Optional tracking number for the shipment
            estimated_delivery_date: Optional estimated delivery date
            promised_ship_date: Optional supplier promised ship date

        Returns:
            ERPUpdateResponse: Response with update status and internal ID

        Example:
            >>> from datetime import datetime
            >>> result = client.update_shipment_fields(
            ...     po_number="PO12345",
            ...     tracking_number="1Z999AA10123456784",
            ...     estimated_delivery_date=datetime(2024, 1, 20),
            ...     promised_ship_date=datetime(2024, 1, 15)
            ... )
            >>> print(f"Updated PO with internal ID: {result['internal_id']}")
        """
        updates = {}

        # Add tracking number if provided
        if tracking_number:
            updates[TRACKING_NUMBER] = tracking_number

        # Handle estimated delivery date
        if estimated_delivery_date:
            # Convert datetime to NetSuite's expected format
            # NetSuite requires ISO 8601 format with timezone offset
            # Example: 2024-01-15T00:00:00.000-07:00 (MST/PDT)
            if hasattr(estimated_delivery_date, "strftime"):
                updates[ESTIMATED_DELIVERY_DATE] = estimated_delivery_date.strftime(
                    "%Y-%m-%dT%H:%M:%S.000-07:00"
                )
            else:
                # If it's already a string, assume it's properly formatted
                updates[ESTIMATED_DELIVERY_DATE] = estimated_delivery_date

        # Handle promised ship date
        if promised_ship_date:
            if hasattr(promised_ship_date, "strftime"):
                updates[PROMISED_SHIP_DATE] = promised_ship_date.strftime(
                    "%Y-%m-%dT%H:%M:%S.000-07:00"
                )
            else:
                updates[PROMISED_SHIP_DATE] = promised_ship_date

        # Check if there's anything to update
        if not updates:
            logger.warning("No fields to update")
            # Return a successful response with no internal_id since no update occurred
            return ERPUpdateResponse(success=True, internal_id="")

        # Create ERPUpdateRequest and delegate to the main update method
        request = ERPUpdateRequest(**updates)
        return self.update_purchase_order(po_number, request)
