"""Integration tests for NetSuite API calls using real credentials."""

import json
import os
from unittest import skipUnless

from django.test import TestCase

from didero.integrations.erp import NetSuiteClient
from didero.integrations.erp.credentials import SimpleEnvCredentialProvider
from didero.integrations.erp.mappers.customers.ionq.po_mapper import (
    IonQNetSuitePOMapper,
)


@skipUnless(
    all(
        [
            os.getenv("IONQ_NETSUITE_ACCOUNT_ID"),
            os.getenv("IONQ_NETSUITE_CONSUMER_KEY"),
            os.getenv("IONQ_NETSUITE_CONSUMER_SECRET"),
            os.getenv("IONQ_NETSUITE_TOKEN_ID"),
            os.getenv("IONQ_NETSUITE_TOKEN_SECRET"),
        ]
    ),
    "NetSuite credentials not available",
)
class NetSuiteIntegrationTests(TestCase):
    """Integration tests using real NetSuite API calls."""

    def setUp(self):
        """Set up NetSuite client with real credentials."""
        # Get credentials from environment
        provider = SimpleEnvCredentialProvider()
        # Using team ID 4 (IonQ test team)
        credentials = provider.get_credentials("4", "netsuite")

        # Create client
        self.client = NetSuiteClient(credentials=credentials)
        self.mapper = IonQNetSuitePOMapper()

        # Test PO numbers that should exist in IonQ's NetSuite
        # These are from the experiment files
        self.test_po_numbers = ["PO431", "PO432", "PO433"]

    def test_connection(self):
        """Test basic connection to NetSuite."""
        result = self.client.test_connection()
        self.assertTrue(result, "Failed to connect to NetSuite")
        print("✅ NetSuite connection successful")

    def test_search_purchase_orders(self):
        """Test searching for purchase orders."""
        try:
            # Search for recent POs
            results = self.client.search_purchase_orders()
            print(f"✅ Found {len(results)} purchase orders")

            # Print first few PO numbers for reference
            if results:
                print("Available PO numbers:")
                for i, po in enumerate(results[:10]):  # Show first 10
                    print(
                        f"  - {po.get('tranId', 'N/A')} (internal ID: {po.get('internalId', 'N/A')})"
                    )
                    if i >= 5:  # Limit output
                        print(f"  ... and {len(results) - 6} more")
                        break

            self.assertGreater(len(results), 0, "No purchase orders found")

        except Exception as e:
            self.fail(f"Search failed: {str(e)}")

    def test_get_purchase_order_basic(self):
        """Test getting a specific purchase order (basic method)."""
        for po_number in self.test_po_numbers:
            try:
                result = self.client.get_purchase_order(po_number)
                print(f"✅ Successfully retrieved basic data for {po_number}")
                print(f"   Internal ID: {result.get('internal_id')}")
                self.assertIn("internal_id", result)
                self.assertIn("po_number", result)
                self.assertEqual(result["po_number"], po_number)
                break  # Success, stop trying
            except Exception as e:
                print(f"❌ Failed to get {po_number}: {str(e)}")
                continue
        else:
            self.fail(
                f"Could not retrieve any of the test PO numbers: {self.test_po_numbers}"
            )

    def test_get_complete_purchase_order_real_api(self):
        """Test the new get_complete_purchase_order method with real API."""
        successful_po = None

        for po_number in self.test_po_numbers:
            try:
                print(f"\n🔍 Testing complete PO extraction for {po_number}...")
                result = self.client.get_complete_purchase_order(po_number)

                print(f"✅ Successfully extracted complete data for {po_number}")

                # Verify structure
                self.assertIsInstance(result, dict)
                self.assertIn("metadata", result)
                self.assertIn("header", result)
                self.assertIn("vendor", result)
                self.assertIn("addresses", result)
                self.assertIn("line_items", result)
                self.assertIn("custom_fields", result)

                # Verify header data
                header = result["header"]
                self.assertEqual(header.get("tranId"), po_number)
                print(f"   PO Number: {header.get('tranId')}")
                print(f"   Status: {header.get('status')}")
                print(f"   Total: {header.get('total')} {header.get('currencyName')}")
                print(f"   Date: {header.get('tranDate')}")

                # Verify vendor data
                vendor = result["vendor"]
                print(
                    f"   Vendor: {vendor.get('name')} (ID: {vendor.get('internalId')})"
                )
                self.assertIsNotNone(vendor.get("name"))

                # Verify line items
                line_items = result["line_items"]
                print(f"   Line Items: {len(line_items)}")
                for i, item in enumerate(line_items[:3]):  # Show first 3 items
                    item_ref = item.get("item_reference", {})
                    fields = item.get("fields", {})
                    print(
                        f"     {i+1}. {item_ref.get('name')} - {fields.get('description')}"
                    )
                    print(
                        f"        Qty: {fields.get('quantity')}, Price: {fields.get('rate')}"
                    )

                # Verify addresses
                addresses = result["addresses"]
                billing = addresses.get("billing", {})
                shipping = addresses.get("shipping", {})
                print(f"   Billing Address: {billing.get('addressee', 'N/A')}")
                print(f"   Shipping Address: {shipping.get('addressee', 'N/A')}")

                successful_po = po_number
                break  # Success, stop trying

            except Exception as e:
                print(f"❌ Failed to get complete data for {po_number}: {str(e)}")
                continue

        if not successful_po:
            self.fail(
                f"Could not extract complete data for any test PO: {self.test_po_numbers}"
            )

    def test_full_extraction_and_mapping_pipeline(self):
        """Test the complete pipeline: NetSuite extraction → Mapper → PurchaseOrderDetails."""
        successful_po = None

        for po_number in self.test_po_numbers:
            try:
                print(f"\n🔄 Testing full pipeline for {po_number}...")

                # Step 1: Extract from NetSuite
                netsuite_data = self.client.get_complete_purchase_order(po_number)
                print("✅ Step 1: NetSuite extraction successful")

                # Step 2: Map to PurchaseOrderDetails
                po_details = self.mapper.map_to_purchase_order_details(netsuite_data)
                print("✅ Step 2: Mapping to PurchaseOrderDetails successful")

                # Step 3: Validate mapped data
                self.assertEqual(po_details.po_number, po_number)
                self.assertIsNotNone(po_details.supplier_name)
                print(f"   Mapped PO Number: {po_details.po_number}")
                print(f"   Mapped Supplier: {po_details.supplier_name}")
                print(f"   Mapped Currency: {po_details.currency}")
                print(f"   Mapped Items: {len(po_details.items or [])}")

                if po_details.supplier_address:
                    addr = po_details.supplier_address
                    print(
                        f"   Supplier Address: {addr.line1}, {addr.city}, {addr.state} {addr.zip} ({addr.country})"
                    )

                if po_details.shipping_address:
                    addr = po_details.shipping_address
                    print(
                        f"   Shipping Address: {addr.line1}, {addr.city}, {addr.state} {addr.zip} ({addr.country})"
                    )

                # Validate line items
                if po_details.items:
                    for i, item in enumerate(po_details.items[:3]):  # Show first 3
                        print(
                            f"   Item {i+1}: {item.item_number} - {item.item_description}"
                        )
                        print(
                            f"            Qty: {item.quantity} {item.unit_of_measure}, Price: {item.unit_price}"
                        )
                        if item.requested_date:
                            print(f"            Requested: {item.requested_date}")

                # Validate that the mapped data has required fields for create_po_from_extracted_data
                self.assertIsNotNone(po_details.po_number)
                self.assertIsNotNone(po_details.supplier_name)

                print("✅ Step 3: Data validation successful")
                print(f"🎉 Full pipeline test successful for {po_number}")

                successful_po = po_number
                break  # Success, stop trying

            except Exception as e:
                print(f"❌ Pipeline failed for {po_number}: {str(e)}")
                import traceback

                print(f"   Full error: {traceback.format_exc()}")
                continue

        if not successful_po:
            self.fail(f"Full pipeline failed for all test POs: {self.test_po_numbers}")

    def test_error_handling_nonexistent_po(self):
        """Test error handling for non-existent PO."""
        with self.assertRaises(Exception) as context:
            self.client.get_complete_purchase_order("NONEXISTENT_PO_12345")

        error_message = str(context.exception)
        self.assertIn("Failed to fetch complete PO", error_message)
        print(f"✅ Proper error handling for non-existent PO: {error_message}")

    def test_export_sample_data_for_development(self):
        """Export real NetSuite data for development purposes."""
        for po_number in self.test_po_numbers:
            try:
                print(f"\n📄 Exporting sample data for {po_number}...")

                # Get complete extraction
                netsuite_data = self.client.get_complete_purchase_order(po_number)

                # Save to file for development reference
                filename = f"sample_netsuite_extraction_{po_number.lower()}.json"
                with open(filename, "w") as f:
                    json.dump(netsuite_data, f, indent=2, default=str)

                print(f"✅ Exported NetSuite data to {filename}")

                # Also create mapped version
                po_details = self.mapper.map_to_purchase_order_details(netsuite_data)
                mapped_filename = f"sample_mapped_po_details_{po_number.lower()}.json"
                with open(mapped_filename, "w") as f:
                    json.dump(po_details.model_dump(), f, indent=2, default=str)

                print(f"✅ Exported mapped data to {mapped_filename}")

                break  # Just export one successful example

            except Exception as e:
                print(f"❌ Failed to export data for {po_number}: {str(e)}")
                continue
