"""Tests for SOAP XML builder utilities."""

from datetime import datetime
from unittest.mock import patch

from django.test import TestCase

from didero.integrations.erp.soap_builder import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, XMLElement
from didero.integrations.erp.types import LineItemData


class XMLElementTests(TestCase):
    """Test cases for XMLElement class."""

    def test_simple_element_with_content(self):
        """Test creating simple XML element with content."""
        element = XMLElement(tag="test", content="value")
        result = element.to_xml()

        self.assertEqual(result, "<test>value</test>")

    def test_self_closing_element(self):
        """Test creating self-closing XML element."""
        element = XMLElement(tag="test")
        result = element.to_xml()

        self.assertEqual(result, "<test/>")

    def test_element_with_attributes(self):
        """Test creating XML element with attributes."""
        element = XMLElement(
            tag="test", content="value", attributes={"attr1": "val1", "attr2": "val2"}
        )
        result = element.to_xml()

        # Should contain both attributes
        self.assertIn('attr1="val1"', result)
        self.assertIn('attr2="val2"', result)
        self.assertIn(">value</test>", result)

    def test_self_closing_element_with_attributes(self):
        """Test creating self-closing XML element with attributes."""
        element = XMLElement(tag="test", attributes={"attr1": "val1"})
        result = element.to_xml()

        self.assertEqual(result, '<test attr1="val1"/>')

    def test_element_with_namespace(self):
        """Test creating XML element with namespace."""
        element = XMLElement(tag="test", content="value", namespace="ns")
        result = element.to_xml()

        self.assertEqual(result, "<ns:test>value</ns:test>")

    def test_element_with_namespace_and_attributes(self):
        """Test creating XML element with namespace and attributes."""
        element = XMLElement(
            tag="test", content="value", namespace="ns", attributes={"attr": "val"}
        )
        result = element.to_xml()

        self.assertEqual(result, '<ns:test attr="val">value</ns:test>')

    def test_element_with_empty_content(self):
        """Test creating XML element with empty string content."""
        element = XMLElement(tag="test", content="")
        result = element.to_xml()

        self.assertEqual(result, "<test></test>")

    def test_element_with_special_characters(self):
        """Test creating XML element with special characters in content."""
        element = XMLElement(tag="test", content="value with spaces & symbols")
        result = element.to_xml()

        self.assertEqual(result, "<test>value with spaces & symbols</test>")


class SOAPBuilderTests(TestCase):
    """Test cases for SOAPBuilder class."""

    def setUp(self):
        """Set up test data."""
        self.builder = SOAPBuilder(api_version="2023_2")

    def test_initialization(self):
        """Test SOAPBuilder initialization."""
        self.assertEqual(self.builder.namespaces.api_version, "2023_2")
        self.assertEqual(self.builder.elements, [])
        self.assertEqual(self.builder.indent_level, 0)

    def test_indentation(self):
        """Test indentation calculation."""
        self.assertEqual(self.builder._indent(), "")

        self.builder.indent_level = 1
        self.assertEqual(self.builder._indent(), "    ")

        self.builder.indent_level = 2
        self.assertEqual(self.builder._indent(), "        ")

    def test_add_element(self):
        """Test adding XML element."""
        element = XMLElement(tag="test", content="value")
        result = self.builder.add_element(element)

        # Should return self for chaining
        self.assertEqual(result, self.builder)
        self.assertEqual(len(self.builder.elements), 1)
        self.assertEqual(self.builder.elements[0], "<test>value</test>")

    def test_add_element_with_indentation(self):
        """Test adding XML element with indentation."""
        self.builder.indent_level = 1
        element = XMLElement(tag="test", content="value")
        self.builder.add_element(element)

        self.assertEqual(self.builder.elements[0], "    <test>value</test>")

    def test_add_raw(self):
        """Test adding raw XML."""
        result = self.builder.add_raw("<custom>xml</custom>")

        # Should return self for chaining
        self.assertEqual(result, self.builder)
        self.assertEqual(len(self.builder.elements), 1)
        self.assertEqual(self.builder.elements[0], "<custom>xml</custom>")

    def test_add_raw_with_indentation(self):
        """Test adding raw XML with indentation."""
        self.builder.indent_level = 2
        self.builder.add_raw("<custom>xml</custom>")

        self.assertEqual(self.builder.elements[0], "        <custom>xml</custom>")

    def test_element_block_basic(self):
        """Test element_block context manager."""
        with self.builder.element_block("parent"):
            self.builder.add_raw("<child>content</child>")

        expected = ["<parent>", "    <child>content</child>", "</parent>"]
        self.assertEqual(self.builder.elements, expected)

    def test_element_block_with_namespace(self):
        """Test element_block with namespace."""
        with self.builder.element_block("parent", namespace="ns"):
            self.builder.add_raw("<child>content</child>")

        expected = ["<ns:parent>", "    <child>content</child>", "</ns:parent>"]
        self.assertEqual(self.builder.elements, expected)

    def test_element_block_with_attributes(self):
        """Test element_block with attributes."""
        with self.builder.element_block("parent", attributes={"attr": "value"}):
            self.builder.add_raw("<child>content</child>")

        expected = ['<parent attr="value">', "    <child>content</child>", "</parent>"]
        self.assertEqual(self.builder.elements, expected)

    def test_element_block_nested(self):
        """Test nested element_block calls."""
        with self.builder.element_block("level1"):
            with self.builder.element_block("level2"):
                self.builder.add_raw("<level3>content</level3>")

        expected = [
            "<level1>",
            "    <level2>",
            "        <level3>content</level3>",
            "    </level2>",
            "</level1>",
        ]
        self.assertEqual(self.builder.elements, expected)

    def test_element_block_exception_handling(self):
        """Test element_block properly handles exceptions."""
        try:
            with self.builder.element_block("parent"):
                self.builder.add_raw("<child>content</child>")
                raise ValueError("Test exception")
        except ValueError:
            pass

        # Should still close the element properly
        expected = ["<parent>", "    <child>content</child>", "</parent>"]
        self.assertEqual(self.builder.elements, expected)

    def test_build_custom_field_default(self):
        """Test building custom field with default type."""
        result = self.builder.build_custom_field("test_field", "test_value")

        # Should return self for chaining
        self.assertEqual(result, self.builder)

        xml_output = self.builder.to_string()
        self.assertIn('scriptId="test_field"', xml_output)
        self.assertIn('xsi:type="platformCore:StringCustomFieldRef"', xml_output)
        self.assertIn("<platformCore:value>test_value</platformCore:value>", xml_output)

    def test_build_custom_field_date_type(self):
        """Test building custom field with date type."""
        self.builder.build_custom_field(
            "date_field", "2023-12-01", "DateCustomFieldRef"
        )

        xml_output = self.builder.to_string()
        self.assertIn('scriptId="date_field"', xml_output)
        self.assertIn('xsi:type="platformCore:DateCustomFieldRef"', xml_output)
        self.assertIn("<platformCore:value>2023-12-01</platformCore:value>", xml_output)

    def test_to_string(self):
        """Test converting builder to string."""
        self.builder.add_raw("<line1>")
        self.builder.add_raw("<line2>")

        result = self.builder.to_string()
        self.assertEqual(result, "<line1>\n<line2>")

    def test_to_string_empty(self):
        """Test converting empty builder to string."""
        result = self.builder.to_string()
        self.assertEqual(result, "")


class SOAPBuilderLineItemTests(TestCase):
    """Test cases for SOAPBuilder line item methods."""

    def setUp(self):
        """Set up test data."""
        self.builder = SOAPBuilder(api_version="2023_2")

    @patch("didero.integrations.erp.soap_builder.format_netsuite_date")
    def test_build_line_item_basic(self, mock_format_date):
        """Test building basic line item."""
        mock_format_date.return_value = "2023-12-01T09:00:00.000-08:00"

        line_data: LineItemData = {
            "line": 1,
            "estimated_delivery_date": datetime(2023, 12, 1),
        }

        result = self.builder.build_line_item(line_data)

        # Should return self for chaining
        self.assertEqual(result, self.builder)

        xml_output = self.builder.to_string()
        self.assertIn("<tranPurch:line>1</tranPurch:line>", xml_output)
        self.assertIn(
            "<tranPurch:expectedReceiptDate>2023-12-01T09:00:00.000-08:00</tranPurch:expectedReceiptDate>",
            xml_output,
        )

    @patch("didero.integrations.erp.soap_builder.format_netsuite_date")
    def test_build_line_item_with_promised_ship_date(self, mock_format_date):
        """Test building line item with promised ship date."""
        mock_format_date.return_value = "2023-12-01T09:00:00.000-08:00"

        # Import constants for promised ship date
        from didero.integrations.erp.customers.ionq.constants import PROMISED_SHIP_DATE

        line_data: LineItemData = {"line": 2, PROMISED_SHIP_DATE: datetime(2023, 12, 1)}

        self.builder.build_line_item(line_data)

        xml_output = self.builder.to_string()
        self.assertIn("<tranPurch:line>2</tranPurch:line>", xml_output)
        self.assertIn("customFieldList", xml_output)

    @patch("didero.integrations.erp.soap_builder.format_netsuite_date")
    def test_build_line_item_no_dates(self, mock_format_date):
        """Test building line item without dates."""
        line_data: LineItemData = {"line": 3}

        self.builder.build_line_item(line_data)

        xml_output = self.builder.to_string()
        self.assertIn("<tranPurch:line>3</tranPurch:line>", xml_output)
        self.assertNotIn("expectedReceiptDate", xml_output)
        self.assertNotIn("customFieldList", xml_output)

    @patch("didero.integrations.erp.soap_builder.format_netsuite_date")
    def test_build_line_item_with_none_date(self, mock_format_date):
        """Test building line item when format_netsuite_date returns None."""
        mock_format_date.return_value = None

        line_data: LineItemData = {
            "line": 4,
            "estimated_delivery_date": datetime(2023, 12, 1),
        }

        self.builder.build_line_item(line_data)

        xml_output = self.builder.to_string()
        self.assertIn("<tranPurch:line>4</tranPurch:line>", xml_output)
        self.assertNotIn("expectedReceiptDate", xml_output)

    def test_build_header_custom_fields_empty(self):
        """Test building header custom fields with empty dict."""
        result = self.builder.build_header_custom_fields({})

        # Should return self for chaining
        self.assertEqual(result, self.builder)
        self.assertEqual(self.builder.to_string(), "")

    def test_build_header_custom_fields_none(self):
        """Test building header custom fields with None."""
        result = self.builder.build_header_custom_fields(None)

        # Should return self for chaining
        self.assertEqual(result, self.builder)
        self.assertEqual(self.builder.to_string(), "")

    def test_build_header_custom_fields_with_data(self):
        """Test building header custom fields with data."""
        header_fields = {"field1": "value1", "field2": "value2"}

        self.builder.build_header_custom_fields(header_fields)

        xml_output = self.builder.to_string()
        self.assertIn("<tranPurch:customFieldList>", xml_output)
        self.assertIn('scriptId="field1"', xml_output)
        self.assertIn('scriptId="field2"', xml_output)
        self.assertIn("<platformCore:value>value1</platformCore:value>", xml_output)
        self.assertIn("<platformCore:value>value2</platformCore:value>", xml_output)

    def test_build_line_items_list(self):
        """Test building line items list."""
        line_items = [{"line": 1}, {"line": 2}]

        result = self.builder.build_line_items_list(line_items)

        # Should return self for chaining
        self.assertEqual(result, self.builder)

        xml_output = self.builder.to_string()
        self.assertIn('<tranPurch:itemList replaceAll="false">', xml_output)
        self.assertIn("<tranPurch:line>1</tranPurch:line>", xml_output)
        self.assertIn("<tranPurch:line>2</tranPurch:line>", xml_output)

    def test_build_line_items_list_empty(self):
        """Test building empty line items list."""
        self.builder.build_line_items_list([])

        xml_output = self.builder.to_string()
        self.assertIn('<tranPurch:itemList replaceAll="false">', xml_output)
        self.assertIn("</tranPurch:itemList>", xml_output)


class SOAPBuilderFlatLineUpdatesTests(TestCase):
    """Test cases for SOAPBuilder flat line updates method."""

    def setUp(self):
        """Set up test data."""
        self.builder = SOAPBuilder(api_version="2023_2")

    @patch("didero.integrations.erp.soap_builder.format_netsuite_date")
    def test_build_flat_line_updates_basic(self, mock_format_date):
        """Test building flat line updates with basic fields."""
        mock_format_date.return_value = "2023-12-01T09:00:00.000-08:00"

        # Import constants
        from didero.integrations.erp.customers.ionq.constants import (
            NS_LINE_EXPECTED_RECEIPT_DATE,
            NS_LINE_PROMISED_SHIP_DATE,
        )

        line_numbers = ["1", "2"]
        line_fields = {
            NS_LINE_EXPECTED_RECEIPT_DATE: datetime(2023, 12, 1),
            NS_LINE_PROMISED_SHIP_DATE: datetime(2023, 12, 2),
        }

        result = self.builder.build_flat_line_updates(line_numbers, line_fields)

        # Should return self for chaining
        self.assertEqual(result, self.builder)

        xml_output = self.builder.to_string()
        self.assertIn('<tranPurch:itemList replaceAll="false">', xml_output)
        self.assertIn("<tranPurch:line>1</tranPurch:line>", xml_output)
        self.assertIn("<tranPurch:line>2</tranPurch:line>", xml_output)
        self.assertIn("expectedReceiptDate", xml_output)
        self.assertIn("customFieldList", xml_output)

    @patch("didero.integrations.erp.soap_builder.format_netsuite_date")
    def test_build_flat_line_updates_expected_receipt_only(self, mock_format_date):
        """Test building flat line updates with only expected receipt date."""
        mock_format_date.return_value = "2023-12-01T09:00:00.000-08:00"

        from didero.integrations.erp.customers.ionq.constants import (
            NS_LINE_EXPECTED_RECEIPT_DATE,
        )

        line_numbers = ["1"]
        line_fields = {NS_LINE_EXPECTED_RECEIPT_DATE: datetime(2023, 12, 1)}

        self.builder.build_flat_line_updates(line_numbers, line_fields)

        xml_output = self.builder.to_string()
        self.assertIn("expectedReceiptDate", xml_output)
        self.assertNotIn("customFieldList", xml_output)

    @patch("didero.integrations.erp.soap_builder.format_netsuite_date")
    def test_build_flat_line_updates_promised_ship_only(self, mock_format_date):
        """Test building flat line updates with only promised ship date."""
        mock_format_date.return_value = "2023-12-01T09:00:00.000-08:00"

        from didero.integrations.erp.customers.ionq.constants import (
            NS_LINE_PROMISED_SHIP_DATE,
        )

        line_numbers = ["1"]
        line_fields = {NS_LINE_PROMISED_SHIP_DATE: datetime(2023, 12, 1)}

        self.builder.build_flat_line_updates(line_numbers, line_fields)

        xml_output = self.builder.to_string()
        self.assertNotIn("expectedReceiptDate", xml_output)
        self.assertIn("customFieldList", xml_output)

    @patch("didero.integrations.erp.soap_builder.format_netsuite_date")
    def test_build_flat_line_updates_none_dates(self, mock_format_date):
        """Test building flat line updates when format returns None."""
        mock_format_date.return_value = None

        from didero.integrations.erp.customers.ionq.constants import (
            NS_LINE_EXPECTED_RECEIPT_DATE,
            NS_LINE_PROMISED_SHIP_DATE,
        )

        line_numbers = ["1"]
        line_fields = {
            NS_LINE_EXPECTED_RECEIPT_DATE: datetime(2023, 12, 1),
            NS_LINE_PROMISED_SHIP_DATE: datetime(2023, 12, 2),
        }

        self.builder.build_flat_line_updates(line_numbers, line_fields)

        xml_output = self.builder.to_string()
        self.assertIn("<tranPurch:line>1</tranPurch:line>", xml_output)
        self.assertNotIn("expectedReceiptDate", xml_output)
        self.assertNotIn("customFieldList", xml_output)

    def test_build_flat_line_updates_empty(self):
        """Test building flat line updates with empty inputs."""
        self.builder.build_flat_line_updates([], {})

        xml_output = self.builder.to_string()
        self.assertIn('<tranPurch:itemList replaceAll="false">', xml_output)
        self.assertIn("</tranPurch:itemList>", xml_output)


class SOAPBuilderIntegrationTests(TestCase):
    """Integration tests for complete XML generation workflows."""

    def setUp(self):
        """Set up test data."""
        self.builder = SOAPBuilder(api_version="2023_2")

    @patch("didero.integrations.erp.soap_builder.format_netsuite_date")
    def test_complete_purchase_order_update_workflow(self, mock_format_date):
        """Test complete workflow for updating purchase order."""
        mock_format_date.return_value = "2023-12-01T09:00:00.000-08:00"

        # Simulate building a complete PO update
        with self.builder.element_block("purchaseOrder", "tranPurch"):
            # Header fields
            header_fields = {"header_field": "header_value"}
            self.builder.build_header_custom_fields(header_fields)

            # Line items
            line_items = [
                {"line": 1, "estimated_delivery_date": datetime(2023, 12, 1)},
                {"line": 2, "estimated_delivery_date": datetime(2023, 12, 2)},
            ]
            self.builder.build_line_items_list(line_items)

        xml_output = self.builder.to_string()

        # Verify structure
        self.assertIn("<tranPurch:purchaseOrder>", xml_output)
        self.assertIn("</tranPurch:purchaseOrder>", xml_output)
        self.assertIn("customFieldList", xml_output)
        self.assertIn("itemList", xml_output)
        self.assertIn("expectedReceiptDate", xml_output)

    def test_chaining_methods(self):
        """Test method chaining functionality."""
        result = (
            self.builder.add_raw("<header>")
            .add_element(XMLElement("test", "value"))
            .add_raw("</header>")
        )

        # Should return self for chaining
        self.assertEqual(result, self.builder)

        xml_output = self.builder.to_string()
        expected = "<header>\n<test>value</test>\n</header>"
        self.assertEqual(xml_output, expected)

    def test_complex_nested_structure(self):
        """Test building complex nested XML structure."""
        with self.builder.element_block("envelope", "soap"):
            with self.builder.element_block("body", "soap"):
                with self.builder.element_block("update", "platformMsgs"):
                    self.builder.add_element(
                        XMLElement(
                            "recordType", "purchaseOrder", namespace="platformMsgs"
                        )
                    )

        xml_output = self.builder.to_string()

        # Verify proper nesting and namespaces
        lines = xml_output.split("\n")
        self.assertEqual(lines[0], "<soap:envelope>")
        self.assertEqual(lines[1], "    <soap:body>")
        self.assertEqual(lines[2], "        <platformMsgs:update>")
        self.assertIn("platformMsgs:recordType", lines[3])
        self.assertEqual(lines[4], "        </platformMsgs:update>")
        self.assertEqual(lines[5], "    </soap:body>")
        self.assertEqual(lines[6], "</soap:envelope>")
