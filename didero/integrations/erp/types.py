"""Type definitions for ERP integrations."""

from datetime import datetime
from typing import Any, List, Optional, TypedDict


class NetSuiteCredentials(TypedDict):
    """NetSuite OAuth credentials."""

    account_id: str
    consumer_key: str
    consumer_secret: str
    token_id: str
    token_secret: str


class NetSuiteConfig(TypedDict, total=False):
    """NetSuite configuration options."""

    api_version: str
    endpoint: Optional[str]


class PurchaseOrderDetails(TypedDict):
    """NetSuite purchase order details."""

    internal_id: str
    po_number: str
    raw_response: str


class SOAPResponse(TypedDict):
    """NetSuite SOAP response structure."""

    success: bool
    raw_response: str


class LineItemData(TypedDict, total=False):
    """Line item data structure."""

    line: int
    item_number: str
    item_id: Optional[int]
    quantity: float
    promised_ship_date: Optional[datetime]
    estimated_delivery_date: Optional[datetime]


class ShipmentFieldData(TypedDict, total=False):
    """Shipment data for ERP sync."""

    tracking_number: Optional[str]
    estimated_delivery_date: Optional[datetime]
    promised_ship_date: Optional[datetime]
    line_items: Optional[List[LineItemData]]
