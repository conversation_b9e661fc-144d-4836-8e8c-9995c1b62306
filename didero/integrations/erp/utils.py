"""Utility functions for ERP integrations."""

from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Union

import structlog

from didero.integrations.erp.schemas import ERPType
from didero.integrations.models import ERPIntegrationConfig

if TYPE_CHECKING:
    from didero.orders.models import PurchaseOrder, Shipment

logger = structlog.get_logger(__name__)


def get_team_erp_config(team_id: int) -> Optional[ERPIntegrationConfig]:
    """
    Get the active ERP configuration for a team.

    Args:
        team_id: Team ID

    Returns:
        ERPIntegrationConfig if found and enabled, None otherwise
    """
    try:
        config = ERPIntegrationConfig.objects.get(
            team_id=team_id,
            enabled=True,
        )
        return config
    except ERPIntegrationConfig.DoesNotExist:
        logger.debug("No active ERP config found for team", team_id=team_id)
        return None


def extract_promised_dates_from_oa(purchase_order: PurchaseOrder) -> List[datetime]:
    """
    Extract promised ship dates from order acknowledgment items.

    Args:
        purchase_order: PurchaseOrder instance with prefetched OA data

    Returns:
        List of promised ship dates found in OA items
    """
    promised_dates = []

    # Get all order acknowledgments for this PO
    for oa in purchase_order.order_acknowledgements.all():
        # Get items with promised ship dates
        for item in oa.items.filter(promised_ship_date__isnull=False):
            if item.promised_ship_date:
                promised_dates.append(item.promised_ship_date)

    # Remove duplicates and sort
    unique_dates = sorted(list(set(promised_dates)))

    logger.debug(
        "Extracted promised dates from OA",
        po_id=purchase_order.id,
        po_number=purchase_order.po_number,
        date_count=len(unique_dates),
    )

    return unique_dates


def format_shipment_data_for_erp(
    shipment: Shipment, purchase_order: PurchaseOrder
) -> Dict[str, Any]:
    """
    Format shipment data for ERP update.

    Args:
        shipment: Shipment instance
        purchase_order: Related PurchaseOrder instance

    Returns:
        Dictionary with formatted data
    """
    # Get promised dates from OA
    promised_dates = extract_promised_dates_from_oa(purchase_order)

    # Use the earliest promised date if available
    promised_ship_date = promised_dates[0] if promised_dates else None

    return {
        "po_number": purchase_order.po_number,
        "po_internal_id": purchase_order.id,
        "tracking_number": shipment.tracking_number,
        "estimated_delivery_date": shipment.estimated_delivery_date,
        "promised_ship_date": promised_ship_date,
        "shipment_id": shipment.id,
        "team_id": purchase_order.team_id,
    }


def validate_erp_type(erp_type: str) -> ERPType:
    """
    Validate and convert ERP type string to enum.

    Args:
        erp_type: ERP type as string

    Returns:
        ERPType enum value

    Raises:
        ValueError: If ERP type is invalid
    """
    try:
        return ERPType(erp_type.lower())
    except ValueError:
        valid_types = [t.value for t in ERPType]
        raise ValueError(
            f"Invalid ERP type: {erp_type}. Valid types are: {valid_types}"
        )


class NetSuiteDateFormat:
    """NetSuite date formatting constants."""

    # Use early PST times to prevent date boundary crossing for EST customers
    # 9am PST converts to 12pm EST (same date), avoiding +1 day display issues
    FORMAT = "%Y-%m-%dT09:00:00.000-08:00"


def format_netsuite_date(date_value: Union[datetime, str, None]) -> Optional[str]:
    """
    Format a date value for NetSuite with timezone-safe PST format.

    Uses 9am PST to ensure dates display correctly for EST customers.
    9am PST = 12pm EST (same date), preventing +1 day display issues.

    Args:
        date_value: Date to format (datetime object or string)

    Returns:
        Formatted date string or None if input is None
    """
    if date_value is None:
        return None

    if isinstance(date_value, str):
        return date_value  # Assume already formatted

    if hasattr(date_value, "strftime"):
        return date_value.strftime(NetSuiteDateFormat.FORMAT)

    return str(date_value)


class FieldType(Enum):
    """Types of NetSuite fields."""

    HEADER = "header"
    LINE = "line"


@dataclass
class FieldMapping:
    """Type-safe field mapping configuration."""

    internal_name: str
    netsuite_field: str
    field_type: FieldType
    description: str = ""


class NetSuiteFieldClassifier:
    """Utility to classify NetSuite fields by type."""

    @staticmethod
    def classify_field(field_name: str) -> FieldType:
        """Classify a NetSuite field as header or line level."""
        from didero.integrations.erp.customers.ionq.constants import (
            IONQ_HEADER_FIELDS,
            IONQ_LINE_FIELDS,
        )

        if field_name in IONQ_HEADER_FIELDS:
            return FieldType.HEADER
        elif field_name in IONQ_LINE_FIELDS:
            return FieldType.LINE
        else:
            # Default to header for unknown fields
            return FieldType.HEADER

    @staticmethod
    def separate_fields(
        erp_updates: Dict[str, Any],
    ) -> tuple[Dict[str, Any], Dict[str, Any]]:
        """Separate ERP updates into header and line fields."""
        header_fields = {}
        line_fields = {}

        for field, value in erp_updates.items():
            if field == "line_items":  # Skip structured data
                continue

            field_type = NetSuiteFieldClassifier.classify_field(field)
            if field_type == FieldType.HEADER:
                header_fields[field] = value
            else:
                line_fields[field] = value

        return header_fields, line_fields


@dataclass
class SOAPNamespaces:
    """NetSuite SOAP namespace configuration."""

    api_version: str

    @property
    def soap(self) -> str:
        return "http://schemas.xmlsoap.org/soap/envelope/"

    @property
    def xsi(self) -> str:
        return "http://www.w3.org/2001/XMLSchema-instance"

    @property
    def platform_core(self) -> str:
        return f"urn:core_{self.api_version}.platform.webservices.netsuite.com"

    @property
    def platform_msgs(self) -> str:
        return f"urn:messages_{self.api_version}.platform.webservices.netsuite.com"

    @property
    def tran_purch(self) -> str:
        return f"urn:purchases_{self.api_version}.transactions.webservices.netsuite.com"
