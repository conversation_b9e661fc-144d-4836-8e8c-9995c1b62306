"""Registry for ERP client implementations."""

from typing import Any, Dict, Optional, Type

import structlog

from .clients.base import ERPClientBase
from .clients.netsuite import NetSuiteClient
from .schemas import ERPType

logger = structlog.get_logger(__name__)


class ERPClientRegistry:
    """Registry for ERP client implementations."""

    _clients: Dict[ERPType, Type[ERPClientBase]] = {
        ERPType.NETSUITE: NetSuiteClient,
        # Future: Add other ERP clients here
        # ERPType.SAP: SAPClient,
        # ERPType.ORACLE: OracleClient,
    }

    @classmethod
    def get_client_class(cls, erp_type: ERPType) -> Type[ERPClientBase]:
        """
        Get the client class for a specific ERP type.

        Args:
            erp_type: Type of ERP system

        Returns:
            Client class

        Raises:
            ValueError: If ERP type is not supported
        """
        client_class = cls._clients.get(erp_type)
        if not client_class:
            raise ValueError(f"Unsupported ERP type: {erp_type}")

        return client_class

    @classmethod
    def create_client(
        cls,
        erp_type: ERPType,
        credentials: Dict[str, str],
        config: Optional[Dict[str, Any]] = None,
        field_mappings: Optional[Dict[str, str]] = None,
    ) -> ERPClientBase:
        """
        Create an ERP client instance.

        Args:
            erp_type: Type of ERP system
            credentials: ERP-specific credentials
            config: Additional configuration
            field_mappings: Field mapping configuration

        Returns:
            Configured ERP client instance
        """
        client_class = cls.get_client_class(erp_type)

        logger.info(
            "Creating ERP client",
            erp_type=erp_type,
            client_class=client_class.__name__,
        )

        return client_class(
            credentials=credentials,
            config=config,
            field_mappings=field_mappings,
        )

    @classmethod
    def list_supported_erps(cls) -> list[ERPType]:
        """Get list of supported ERP types."""
        return list(cls._clients.keys())
