# Generated by Django 4.2.7 on 2025-06-26 22:53

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("integrations", "0004_add_shipment_erp_sync_model"),
    ]

    operations = [
        migrations.AddField(
            model_name="shipmenterpsync",
            name="erp_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("netsuite", "NETSUITE"),
                    ("sap", "SAP"),
                    ("oracle", "ORACLE"),
                    ("dynamics", "DYNAMICS"),
                ],
                help_text="ERP system used for this sync (e.g., netsuite, sap)",
                max_length=20,
                null=True,
            ),
        ),
        migrations.AddIndex(
            model_name="shipmenterpsync",
            index=models.Index(
                fields=["erp_type"], name="shipment_er_erp_typ_8c54d4_idx"
            ),
        ),
    ]
