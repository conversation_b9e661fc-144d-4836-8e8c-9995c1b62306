# Generated by Django 4.2.7 on 2025-06-26 22:14

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0091_add_erp_sync_fields_to_shipment"),
        ("integrations", "0003_erpintegrationconfig"),
    ]

    operations = [
        migrations.AlterField(
            model_name="erpintegrationconfig",
            name="field_mappings",
            field=models.JSONField(
                default=dict, help_text="Mapping of Didero fields to ERP fields"
            ),
        ),
        migrations.CreateModel(
            name="ShipmentErpSync",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "sync_status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("in_progress", "In Progress"),
                            ("success", "Success"),
                            ("failed", "Failed"),
                            ("not_configured", "Not Configured"),
                        ],
                        default="not_configured",
                        help_text="Status of ERP synchronization",
                        max_length=20,
                    ),
                ),
                (
                    "attempted_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="Last ERP sync attempt timestamp",
                        null=True,
                    ),
                ),
                (
                    "completed_at",
                    models.DateTimeField(
                        blank=True, help_text="Successful ERP sync timestamp", null=True
                    ),
                ),
                (
                    "error",
                    models.TextField(
                        blank=True, help_text="Last ERP sync error message"
                    ),
                ),
                (
                    "synced_fields",
                    models.JSONField(
                        default=list,
                        help_text="List of fields that were successfully synced",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "shipment",
                    models.OneToOneField(
                        help_text="Shipment this sync record tracks",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="erp_sync",
                        to="orders.shipment",
                    ),
                ),
            ],
            options={
                "verbose_name": "Shipment ERP Sync",
                "verbose_name_plural": "Shipment ERP Syncs",
                "db_table": "shipment_erp_sync",
                "indexes": [
                    models.Index(
                        fields=["sync_status"], name="shipment_er_sync_st_125e64_idx"
                    ),
                    models.Index(
                        fields=["attempted_at"], name="shipment_er_attempt_52202a_idx"
                    ),
                    models.Index(
                        fields=["completed_at"], name="shipment_er_complet_1db63c_idx"
                    ),
                ],
            },
        ),
    ]
