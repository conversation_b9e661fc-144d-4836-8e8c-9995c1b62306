from typing import TYPE_CHECKING

from django.contrib import admin

from didero.integrations.models import (
    ERPIntegrationConfig,
    TeamIntegratedAppCredential,
    TeamIntegrationProvider,
)

if TYPE_CHECKING:
    TypedTeamIntegrationProviderAdmin = admin.ModelAdmin[TeamIntegrationProvider]
    TypedTeamIntegratedAppCredentialAdmin = admin.ModelAdmin[
        TeamIntegratedAppCredential
    ]
    TypedERPIntegrationConfigAdmin = admin.ModelAdmin[ERPIntegrationConfig]
else:
    TypedTeamIntegrationProviderAdmin = admin.ModelAdmin
    TypedTeamIntegratedAppCredentialAdmin = admin.ModelAdmin
    TypedERPIntegrationConfigAdmin = admin.ModelAdmin


class TeamIntegrationProviderAdmin(TypedTeamIntegrationProviderAdmin):
    list_display = ["team", "provider", "provider_user_id"]


admin.site.register(TeamIntegrationProvider, TeamIntegrationProviderAdmin)


class TeamIntegratedAppCredentialAdmin(TypedTeamIntegratedAppCredentialAdmin):
    list_display = ["team", "app_name", "credential_id"]
    autocomplete_fields = ["team"]


admin.site.register(TeamIntegratedAppCredential, TeamIntegratedAppCredentialAdmin)


@admin.register(ERPIntegrationConfig)
class ERPIntegrationConfigAdmin(TypedERPIntegrationConfigAdmin):
    """
    Django Admin interface for ERP Integration Configuration.

    This admin interface allows management of ERP integration settings for teams,
    specifically designed for configuring IonQ's NetSuite integration. It provides
    a user-friendly interface for:

    - Enabling/disabling ERP integration per team
    - Configuring NetSuite connection settings
    - Managing field mappings for customer-specific requirements
    - Monitoring ERP integration status and usage

    Key features:
    - Team-based filtering and search
    - JSON field editing for complex configurations
    - Validation of ERP settings before saving
    - Clear organization of settings by logical groups
    """

    list_display = [
        "team",
        "erp_type",
        "enabled",
        "created_at",
        "updated_at",
    ]

    list_filter = [
        "erp_type",
        "enabled",
        "created_at",
        "updated_at",
    ]

    search_fields = [
        "team__name",
        "team__id",
        "erp_type",
    ]

    autocomplete_fields = [
        "team",
    ]

    readonly_fields = [
        "created_at",
        "updated_at",
    ]

    fieldsets = [
        (
            "Basic Configuration",
            {
                "fields": [
                    "team",
                    "erp_type",
                    "enabled",
                ],
                "description": "Core ERP integration settings for the team.",
            },
        ),
        (
            "Field Mappings",
            {
                "fields": [
                    "field_mappings",
                ],
                "description": (
                    "JSON mapping of Didero fields to ERP fields. "
                    "Example: {'po_number': 'tranId', 'supplier_name': 'vendor_name'}"
                ),
                "classes": ["collapse"],
            },
        ),
        (
            "Advanced Configuration",
            {
                "fields": [
                    "config",
                ],
                "description": (
                    "ERP-specific configuration like endpoints, API versions, etc. "
                    "JSON format."
                ),
                "classes": ["collapse"],
            },
        ),
        (
            "Timestamps",
            {
                "fields": [
                    "created_at",
                    "updated_at",
                ],
                "classes": ["collapse"],
            },
        ),
    ]

    def get_queryset(self, request):
        """Optimize queryset with team prefetch."""
        return super().get_queryset(request).select_related("team")

    def has_change_permission(self, request, obj=None):
        """Allow superusers and staff to modify ERP configs."""
        return request.user.is_superuser or request.user.is_staff

    def has_add_permission(self, request):
        """Allow superusers and staff to add ERP configs."""
        return request.user.is_superuser or request.user.is_staff

    def has_delete_permission(self, request, obj=None):
        """Allow only superusers to delete ERP configs."""
        return request.user.is_superuser
