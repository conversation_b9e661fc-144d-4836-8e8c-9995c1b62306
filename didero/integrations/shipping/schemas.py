from datetime import datetime, timezone
from typing import Optional

from pydantic import BaseModel, Field, computed_field


class StagehandShipmentInfo(BaseModel):
    """Structured shipment information from Stagehand-based carrier tracking (ZIM, ONE, etc)."""

    tracking_number: str = Field(..., description="The tracking number queried")
    eta: Optional[datetime] = Field(None, description="Estimated Time of Arrival")
    shipment_date: Optional[datetime] = Field(
        None, description="Sailing/departure date from port of loading"
    )
    actual_arrival_date: Optional[datetime] = Field(
        None, description="Actual arrival date (only if delivered)"
    )
    port_of_departure: str = Field(default="", description="Port of Loading (POL)")
    port_of_arrival: str = Field(default="", description="Port of Discharge (POD)")

    @computed_field
    @property
    def is_delivered(self) -> bool:
        """
        Determine if shipment is delivered based on:
        1. ETA is None (shows as N/A on the page)
        2. OR actual_arrival_date exists and is in the past
        """
        if self.eta is None:
            return True

        if self.actual_arrival_date:
            # Get current time - handle both timezone-aware and naive datetimes
            now = datetime.now()

            # If arrival date has timezone info, make 'now' timezone-aware too
            if self.actual_arrival_date.tzinfo is not None:
                now = datetime.now(timezone.utc)

            # Check if arrival date is in the past
            return self.actual_arrival_date <= now

        return False
