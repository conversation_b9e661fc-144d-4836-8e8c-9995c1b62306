from datetime import datetime
from typing import Optional

import structlog

from didero.integrations.shipping.schemas import StagehandShipmentInfo
from didero.integrations.stagehand.stagehand import (
    StagehandJobFailedException,
    StagehandTimeoutException,
    poll_stagehand_job_sync,
    submit_stagehand_job_sync,
)
from didero.orders.schemas import CarrierType

logger = structlog.get_logger(__name__)

# Mapping of carrier types to their Stagehand job types
CARRIER_TO_JOB_TYPE = {
    CarrierType.ZIM: "zim.getTrackingInfo",
    CarrierType.ONE: "one.getTrackingInfo",
    CarrierType.MSC: "msc.getTrackingInfo",
    CarrierType.COSCO: "cosco.getTrackingInfo",
}


def parse_date(date_str: Optional[str]) -> Optional[datetime]:
    """Parse date string from Stagehand response into datetime object."""
    if not date_str:
        return None
    try:
        # Try ISO format first (YYYY-MM-DD)
        return datetime.fromisoformat(date_str.replace("Z", "+00:00"))
    except ValueError:
        try:
            # Try date only format
            return datetime.strptime(date_str, "%Y-%m-%d")
        except ValueError:
            logger.warning(f"Could not parse date: {date_str}")
            return None


def get_stagehand_tracking_info(
    carrier_type: CarrierType, tracking_number: str
) -> Optional[StagehandShipmentInfo]:
    """
    Fetch tracking information for Stagehand-based carriers (ZIM, ONE, etc).

    Args:
        carrier_type: The carrier type (must be in CARRIER_TO_JOB_TYPE mapping)
        tracking_number: The tracking number (BOL number for ocean carriers)

    Returns:
        StagehandShipmentInfo object or None if tracking failed
    """
    if carrier_type not in CARRIER_TO_JOB_TYPE:
        logger.error(f"Unsupported carrier type for Stagehand tracking: {carrier_type}")
        return None

    job_type = CARRIER_TO_JOB_TYPE[carrier_type]

    try:
        logger.info(
            f"Starting {carrier_type} tracking for {tracking_number} using Stagehand service",
            carrier_type=carrier_type,
            tracking_number=tracking_number,
            job_type=job_type,
        )

        # Submit Stagehand job
        job_result = submit_stagehand_job_sync(
            customer="", job=job_type, params={"tracking_number": tracking_number}
        )

        logger.info(
            f"Stagehand job submitted for {carrier_type} tracking",
            carrier_type=carrier_type,
            tracking_number=tracking_number,
            job_id=job_result.job_id,
        )

        # Poll for completion (default timeout: 3 minutes)
        try:
            poll_result = poll_stagehand_job_sync(
                job_id=job_result.job_id,
                max_attempts=36,  # 36 * 5 seconds = 3 minutes
                polling_interval=5,
            )
        except StagehandJobFailedException as e:
            logger.error(
                f"Stagehand job failed for {carrier_type} tracking",
                carrier_type=carrier_type,
                tracking_number=tracking_number,
                job_id=e.job_id,
                status=e.status,
                message=e.message,
            )
            return None
        except StagehandTimeoutException as e:
            logger.error(
                f"Stagehand job timed out for {carrier_type} tracking",
                carrier_type=carrier_type,
                tracking_number=tracking_number,
                job_id=e.job_id,
                timeout_seconds=e.timeout_seconds,
            )
            return None

        # Extract result data
        if not poll_result.is_success:
            logger.error(
                f"Stagehand job returned unsuccessful result for {carrier_type} tracking",
                carrier_type=carrier_type,
                tracking_number=tracking_number,
                status=poll_result.status,
            )
            return None

        # The tracking data should be in result
        data = poll_result.result.get("data", {})
        if not data:
            logger.error(
                f"Stagehand job completed but no data returned",
                carrier_type=carrier_type,
                tracking_number=tracking_number,
            )
            return None

        # Create StagehandShipmentInfo from Stagehand response
        shipment_info = StagehandShipmentInfo(
            tracking_number=data.get("tracking_number", tracking_number),
            eta=parse_date(data.get("eta")),
            shipment_date=parse_date(data.get("shipment_date")),
            actual_arrival_date=parse_date(data.get("actual_arrival_date")),
            port_of_departure=data.get("port_of_departure", ""),
            port_of_arrival=data.get("port_of_arrival", ""),
        )

        logger.info(
            f"Successfully extracted {carrier_type} tracking info",
            carrier_type=carrier_type,
            tracking_number=tracking_number,
            is_delivered=shipment_info.is_delivered,
        )

        return shipment_info

    except Exception as e:
        logger.error(
            f"Error fetching {carrier_type} tracking via Stagehand",
            carrier_type=carrier_type,
            tracking_number=tracking_number,
            error=str(e),
        )
        return None
