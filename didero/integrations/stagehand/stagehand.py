import os
import time
from typing import Any, Dict, Optional

import requests
import structlog
from asgiref.sync import sync_to_async
from pydantic import BaseModel
from temporalio import workflow

logger = structlog.get_logger(__name__)

with workflow.unsafe.imports_passed_through():
    from didero.settings.common import STAGEHAND_API_URL


class StagehandException(Exception):
    """Base exception for Stagehand-related errors."""

    pass


class StagehandJobFailedException(StagehandException):
    """Exception raised when a Stagehand job fails."""

    def __init__(self, job_id: str, status: str, message: Optional[str] = None):
        self.job_id = job_id
        self.status = status
        self.message = message or f"Stagehand job {job_id} failed with status {status}"
        super().__init__(self.message)


class StagehandTimeoutException(StagehandException):
    """Exception raised when a Stagehand job times out."""

    def __init__(self, job_id: str, max_attempts: int, polling_interval: int):
        self.job_id = job_id
        self.timeout_seconds = max_attempts * polling_interval
        self.message = (
            f"Stagehand job {job_id} timed out after {self.timeout_seconds} seconds"
        )
        super().__init__(self.message)


class StagehandJobSubmitResponse(BaseModel):
    """Response from submitting a Stagehand job."""

    job_id: str


class StagehandJobResult(BaseModel):
    """Generic result from any Stagehand job."""

    job_id: str
    status: str
    result: Dict[str, Any]  # Raw result data from the Stagehand service
    message: Optional[str] = None

    @property
    def is_success(self) -> bool:
        """Check if the job completed successfully."""
        return self.status.lower() in ["completed", "success"]

    @property
    def is_failure(self) -> bool:
        """Check if the job failed."""
        return self.status.lower() in ["failed", "error"]


# Job type mapping for backward compatibility
JOB_TYPE_MAPPING = {
    "get_netsuite_purchase_order": "getPurchaseOrder",
    "enter_shipment_in_netsuite": "enterShipmentInNetsuite",
    "update_po_unit_price": "updatePOUnitPrice",
    "zim_tracking": "getTrackingInfo",
}

# Customer-specific job prefixes
CUSTOMER_JOB_PREFIXES = {
    "total_home_supply": "total-home-supply",
    "didero": "didero",
}


# Sync implementations (these are the core implementations)
def submit_stagehand_job_sync(
    customer: str,
    job: str,
    params: Dict[str, Any],
) -> StagehandJobSubmitResponse:
    """Submit a job to the Stagehand service (synchronous version for Celery).

    Args:
        customer: Customer identifier (optional if job is fully-qualified)
        job: Job type - either a simple name (e.g., "enterShipmentInNetsuite")
             or a fully-qualified job type (e.g., "zim.getTrackingInfo")
        params: Parameters to pass to the job
    """
    # Validate parameters
    if not job:
        raise ValueError("Job type is required")
    if not isinstance(params, dict):
        raise ValueError("Params must be a dictionary")

    # Check if job is already a fully-qualified job type (contains a dot)
    if "." in job:
        # Use the job type directly
        job_type = job
    else:
        # Apply the existing transformation logic
        if not customer:
            raise ValueError("Customer is required when job is not fully-qualified")

        # Map old job names to new job types
        job_type = JOB_TYPE_MAPPING.get(job, job)

        # Add customer prefix if configured
        if customer in CUSTOMER_JOB_PREFIXES:
            job_type = f"{CUSTOMER_JOB_PREFIXES[customer]}.{job_type}"

    url = f"{STAGEHAND_API_URL}/jobs/run"

    payload = {
        "type": job_type,
        "params": params,
    }

    api_key = os.environ.get("STAGEHAND_API_KEY")
    if not api_key:
        raise StagehandException("STAGEHAND_API_KEY environment variable not set")

    try:
        response = requests.post(
            url,
            headers={
                "x-api-key": api_key,
                "Content-Type": "application/json",
            },
            json=payload,
            timeout=10,
        )
        response.raise_for_status()

        result = response.json()

        # Check for success and message properties for better error diagnostics
        if not result.get("success", True):  # Default to True if not present
            error_msg = result.get("message", "Job submission failed")
            raise StagehandException(f"Stagehand job submission failed: {error_msg}")

        return StagehandJobSubmitResponse(job_id=result["jobId"])
    except Exception as e:
        logger.exception("Failed to submit Stagehand job", error=str(e), params=params)
        raise


def poll_stagehand_job_sync(
    job_id: str,
    max_attempts: int = 24,
    polling_interval: int = 5,
) -> StagehandJobResult:
    """
    Poll until Stagehand job completes or times out (synchronous version for Celery).

    Raises:
        StagehandJobFailedException: If the Stagehand job failed with an error
        StagehandTimeoutException: If the polling times out
    """
    url = f"{STAGEHAND_API_URL}/jobs/status/{job_id}"
    attempt = 0

    api_key = os.environ.get("STAGEHAND_API_KEY")
    if not api_key:
        raise StagehandException("STAGEHAND_API_KEY environment variable not set")

    while attempt < max_attempts:
        try:
            response = requests.get(
                url,
                headers={
                    "x-api-key": api_key,
                    "Content-Type": "application/json",
                },
                timeout=10,
            )
            response.raise_for_status()

            result = response.json()
            job_data = result["job"]
            status = job_data["status"]

            if status == "completed":
                return StagehandJobResult(
                    job_id=job_id,
                    status=status,
                    result=job_data["result"],
                    message=job_data.get("message"),
                )
            elif status == "failed":
                raise StagehandJobFailedException(
                    job_id=job_id,
                    status=status,
                    message=job_data.get("error", "Job failed"),
                )

            time.sleep(polling_interval)
            attempt += 1

        except StagehandException:
            # Let custom Stagehand exceptions pass through
            raise
        except Exception as e:
            # For backward compatibility, check for the old error string pattern
            if "Stagehand job failed" in str(e):
                raise StagehandJobFailedException(
                    job_id=job_id, status="ERROR", message=str(e)
                )
            time.sleep(polling_interval)
            attempt += 1

    # Instead of returning a timeout status, raise a specific exception
    # for better error handling in callers
    raise StagehandTimeoutException(
        job_id=job_id, max_attempts=max_attempts, polling_interval=polling_interval
    )


# Async implementations (simple wrappers around sync versions)
async def submit_stagehand_job(
    customer: str,
    job: str,
    params: Dict[str, Any],
) -> StagehandJobSubmitResponse:
    """Submit a job to the Stagehand service.

    Args:
        customer: Customer identifier (optional if job is fully-qualified)
        job: Job type - either a simple name (e.g., "enterShipmentInNetsuite")
             or a fully-qualified job type (e.g., "zim.getTrackingInfo")
        params: Parameters to pass to the job
    """
    return await sync_to_async(submit_stagehand_job_sync)(customer, job, params)


async def poll_stagehand_job(
    job_id: str,
    max_attempts: int = 24,
    polling_interval: int = 5,
) -> StagehandJobResult:
    """
    Poll until Stagehand job completes or times out.

    Raises:
        StagehandJobFailedException: If the Stagehand job failed with an error
        StagehandTimeoutException: If the polling times out
    """
    return await sync_to_async(poll_stagehand_job_sync)(
        job_id, max_attempts, polling_interval
    )
