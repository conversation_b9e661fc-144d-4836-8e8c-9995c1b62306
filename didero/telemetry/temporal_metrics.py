"""
Temporal SDK metrics integration with OpenTelemetry.
Configures Temporal runtime to export metrics directly to OpenTelemetry collector.
"""

import os
from typing import Optional

import structlog
from django.conf import settings
from temporalio.runtime import OpenTelemetryConfig, Runtime, TelemetryConfig

logger = structlog.get_logger(__name__)


def create_temporal_runtime_with_metrics(
    otlp_endpoint: Optional[str] = None,
    headers: Optional[dict[str, str]] = None,
    metrics_enabled: Optional[bool] = None,
) -> Runtime:
    """
    Create a Temporal Runtime with OpenTelemetry metrics export.

    This configures Temporal to send metrics directly to the OpenTelemetry
    collector using the same OTLP endpoint as the rest of the application.

    Args:
        otlp_endpoint: OTLP endpoint URL. If not provided, uses OTEL_EXPORTER_OTLP_ENDPOINT
                      environment variable or defaults to "http://localhost:4317"
        headers: Optional headers to include with metric exports
        metrics_enabled: Whether to enable metrics. If not provided, uses OTEL_METRICS_ENABLED setting

    Returns:
        Runtime configured with or without OpenTelemetry metrics export
    """
    # Check if metrics are enabled
    if metrics_enabled is None:
        metrics_enabled = getattr(settings, "OTEL_METRICS_ENABLED", False)

    if not metrics_enabled:
        # Return a runtime with empty telemetry configuration
        logger.info("Temporal runtime configured without metrics (metrics disabled)")
        # Create a runtime with an empty telemetry config to avoid initialization errors
        telemetry_config = TelemetryConfig()
        return Runtime(telemetry=telemetry_config)

    # Use the same OTLP endpoint as the rest of the application
    if otlp_endpoint is None:
        otlp_endpoint = os.environ.get(
            "OTEL_EXPORTER_OTLP_ENDPOINT", "http://localhost:4317"
        )

    # Configure OpenTelemetry metrics export
    telemetry_config = TelemetryConfig(
        metrics=OpenTelemetryConfig(
            url=otlp_endpoint,
            headers=headers or {},
        )
    )

    # Create runtime with telemetry
    runtime = Runtime(telemetry=telemetry_config)

    logger.info(
        "Temporal runtime configured with OpenTelemetry metrics",
        otlp_endpoint=otlp_endpoint,
    )

    return runtime


# Global runtime instance to be shared across the application
_temporal_runtime: Optional[Runtime] = None


def get_temporal_runtime() -> Runtime:
    """
    Get or create the global Temporal runtime with metrics enabled.

    This ensures we use a single runtime instance across the application.
    The runtime will use the same OTLP endpoint as configured for the
    application's OpenTelemetry setup.
    """
    global _temporal_runtime

    if _temporal_runtime is None:
        _temporal_runtime = create_temporal_runtime_with_metrics()

    return _temporal_runtime
