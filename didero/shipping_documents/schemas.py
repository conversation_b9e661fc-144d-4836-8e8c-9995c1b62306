from typing import Any, Dict, List, Optional, Type

from django.db import models
from pydantic import BaseModel, Field, field_validator


# Django Model Choices/Enums
class ShippingDocumentType(models.TextChoices):
    BILL_OF_LADING = "bill_of_lading", "Bill of Lading"
    PACKING_LIST = "packing_list", "Packing List"
    ADVANCE_SHIPPING_NOTICE = "advance_shipping_notice", "Advance Shipping Notice"
    GOODS_RECEIPT_NOTE = "goods_receipt_note", "Goods Receipt Note"
    DELIVERY_NOTE = "delivery_note", "Delivery Note"
    OTHER = "other", "Other"


class ShippingDocumentStatus(models.TextChoices):
    PENDING = "pending", "Pending Processing"
    PROCESSING = "processing", "Processing"
    PROCESSED = "processed", "Processed"
    MATCHED = "matched", "Matched to PO"
    REJECTED = "rejected", "Rejected"
    REQUIRES_REVIEW = "requires_review", "Requires Review"


# Pydantic Schemas for AI Extraction
class ShippingItemSchema(BaseModel):
    """Schema for items in shipping documents"""

    item_number: str = Field(..., description="Item SKU or part number")
    item_description: Optional[str] = Field(None, description="Item description")
    ordered_quantity: float = Field(..., description="Quantity ordered", ge=0)
    received_quantity: float = Field(..., description="Quantity received", ge=0)
    unit_of_measure: Optional[str] = Field(None, description="Unit of measure")
    condition: Optional[str] = Field("good", description="Condition of received items")
    notes: Optional[str] = Field(None, description="Notes about the item")

    @field_validator("condition")
    @classmethod
    def validate_condition(cls, v: Any) -> str:
        allowed_conditions = ["good", "damaged", "partial_damage"]
        if v not in allowed_conditions:
            raise ValueError(f"Condition must be one of: {allowed_conditions}")
        return v


class BaseShippingDocumentSchema(BaseModel):
    """Base schema with common fields for all shipping document types"""

    reference_number: str = Field(
        ..., description="Document reference number (BOL#, etc.)"
    )
    po_number: Optional[str] = Field(None, description="Related purchase order number")
    carrier_name: Optional[str] = Field(
        None, description="Carrier/shipping company name"
    )
    tracking_number: Optional[str] = Field(None, description="Shipment tracking number")
    received_date: str = Field(..., description="Date when goods were received")
    supplier_name: Optional[str] = Field(None, description="Supplier name")
    items: List[ShippingItemSchema] = Field(
        default_factory=list, description="Items received"
    )
    notes: Optional[str] = Field(None, description="General notes about the delivery")


# Document Type Specific Schemas
class BillOfLadingSchema(BaseShippingDocumentSchema):
    """Schema for Bill of Lading extraction"""

    vessel_name: Optional[str] = Field(None, description="Vessel/ship name")
    voyage_number: Optional[str] = Field(None, description="Voyage number")
    port_of_loading: Optional[str] = Field(
        None, description="Port where goods were loaded"
    )
    port_of_discharge: Optional[str] = Field(
        None, description="Port where goods will be discharged"
    )
    container_numbers: Optional[List[str]] = Field(
        default_factory=list, description="Container numbers"
    )
    seal_numbers: Optional[List[str]] = Field(
        default_factory=list, description="Container seal numbers"
    )
    bill_of_lading_date: Optional[str] = Field(None, description="Date BOL was issued")
    consignee_name: Optional[str] = Field(None, description="Consignee name")
    notify_party: Optional[str] = Field(None, description="Notify party information")


class PackingListSchema(BaseShippingDocumentSchema):
    """Schema for Packing List extraction"""

    package_count: Optional[int] = Field(None, description="Total number of packages")
    total_weight: Optional[float] = Field(None, description="Total shipment weight")
    weight_unit: Optional[str] = Field(None, description="Weight unit (kg, lbs, etc.)")
    total_volume: Optional[float] = Field(None, description="Total shipment volume")
    volume_unit: Optional[str] = Field(None, description="Volume unit (m³, ft³, etc.)")
    packing_method: Optional[str] = Field(
        None, description="How items are packed (pallets, boxes, etc.)"
    )
    package_dimensions: Optional[List[Dict[str, float]]] = Field(
        default_factory=list, description="Dimensions of packages"
    )


class AdvanceShippingNoticeSchema(BaseShippingDocumentSchema):
    """Schema for Advance Shipping Notice (ASN) extraction"""

    ship_date: str = Field(..., description="Date when goods were shipped")
    estimated_arrival_date: Optional[str] = Field(
        None, description="Estimated arrival date"
    )
    shipment_method: Optional[str] = Field(
        None, description="Shipping method (air, ocean, ground)"
    )
    carrier_contact: Optional[str] = Field(
        None, description="Carrier contact information"
    )
    shipment_status: Optional[str] = Field(None, description="Current shipment status")
    pro_number: Optional[str] = Field(None, description="PRO/tracking number")


class GoodsReceiptNoteSchema(BaseShippingDocumentSchema):
    """Schema for Goods Receipt Note extraction"""

    receipt_number: str = Field(..., description="GRN number")
    inspection_date: Optional[str] = Field(None, description="Date of inspection")
    inspector_name: Optional[str] = Field(
        None, description="Name of person who inspected"
    )
    storage_location: Optional[str] = Field(None, description="Where goods are stored")
    condition_on_arrival: Optional[str] = Field(
        None, description="Overall condition when received"
    )
    discrepancies: Optional[List[str]] = Field(
        default_factory=list, description="List of discrepancies found"
    )
    quality_check_passed: Optional[bool] = Field(
        None, description="Whether quality check passed"
    )


class DeliveryNoteSchema(BaseShippingDocumentSchema):
    """Schema for Delivery Note extraction"""

    delivery_address: Optional[str] = Field(
        None, description="Address where goods were delivered"
    )
    delivery_date: str = Field(..., description="Date of delivery")
    delivery_time: Optional[str] = Field(None, description="Time of delivery")
    recipient_name: Optional[str] = Field(
        None, description="Name of person who received goods"
    )
    recipient_signature: Optional[bool] = Field(
        None, description="Whether recipient signed"
    )
    delivery_instructions: Optional[str] = Field(
        None, description="Special delivery instructions"
    )
    proof_of_delivery_number: Optional[str] = Field(
        None, description="POD reference number"
    )


class OtherShippingDocumentSchema(BaseShippingDocumentSchema):
    """Schema for other/miscellaneous shipping documents"""

    document_subtype: Optional[str] = Field(None, description="Specific type if known")
    custom_fields: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Any custom fields"
    )


# Schema Registry for AI Extraction
SHIPPING_DOCUMENT_SCHEMAS = {
    ShippingDocumentType.BILL_OF_LADING: BillOfLadingSchema,
    ShippingDocumentType.PACKING_LIST: PackingListSchema,
    ShippingDocumentType.ADVANCE_SHIPPING_NOTICE: AdvanceShippingNoticeSchema,
    ShippingDocumentType.GOODS_RECEIPT_NOTE: GoodsReceiptNoteSchema,
    ShippingDocumentType.DELIVERY_NOTE: DeliveryNoteSchema,
    ShippingDocumentType.OTHER: OtherShippingDocumentSchema,
}


def get_extraction_schema_for_type(
    doc_type: ShippingDocumentType,
) -> Type[BaseShippingDocumentSchema]:
    """Get the appropriate Pydantic schema for AI extraction based on document type"""
    return SHIPPING_DOCUMENT_SCHEMAS.get(doc_type, OtherShippingDocumentSchema)
