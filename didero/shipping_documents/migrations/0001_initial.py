# Generated by Django 4.2.7 on 2025-07-22 20:07

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        (
            "orders",
            "0105_remove_orderacknowledgement_global_promised_delivery_date_and_more",
        ),
        ("users", "0057_alter_teamsetting_name"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("suppliers", "0096_alter_supplier_available_shipping_terms_and_more"),
        ("documents", "0021_documentitemmatch"),
    ]

    operations = [
        migrations.CreateModel(
            name="ShippingDocument",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "document_type",
                    models.CharField(
                        choices=[
                            ("bill_of_lading", "Bill of Lading"),
                            ("packing_list", "Packing List"),
                            ("advance_shipping_notice", "Advance Shipping Notice"),
                            ("goods_receipt_note", "Goods Receipt Note"),
                            ("delivery_note", "Delivery Note"),
                            ("other", "Other"),
                        ],
                        db_index=True,
                        help_text="Type of shipping document",
                        max_length=50,
                    ),
                ),
                (
                    "reference_number",
                    models.CharField(
                        db_index=True,
                        help_text="Document reference number (BOL#, Packing List#, ASN#, etc.)",
                        max_length=255,
                    ),
                ),
                (
                    "carrier_name",
                    models.CharField(
                        blank=True,
                        help_text="Name of carrier/shipping company",
                        max_length=255,
                    ),
                ),
                (
                    "tracking_number",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        help_text="Shipment tracking number",
                        max_length=255,
                    ),
                ),
                (
                    "document_date",
                    models.DateField(
                        blank=True,
                        help_text="Date printed/issued on the document itself",
                        null=True,
                    ),
                ),
                (
                    "received_date",
                    models.DateTimeField(
                        default=django.utils.timezone.now,
                        help_text="Date when document was received/processed",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True, help_text="General notes about the shipment"
                    ),
                ),
                (
                    "extracted_data",
                    models.JSONField(
                        default=dict,
                        help_text="Document-type-specific fields extracted by AI",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Processing"),
                            ("processing", "Processing"),
                            ("processed", "Processed"),
                            ("matched", "Matched to PO"),
                            ("rejected", "Rejected"),
                            ("requires_review", "Requires Review"),
                        ],
                        db_index=True,
                        default="pending",
                        help_text="Current processing status",
                        max_length=20,
                    ),
                ),
                (
                    "processed_at",
                    models.DateTimeField(
                        blank=True, help_text="When document was processed", null=True
                    ),
                ),
                (
                    "document",
                    models.ForeignKey(
                        help_text="Associated PDF document",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shipping_documents",
                        to="documents.document",
                    ),
                ),
                (
                    "processed_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who processed the document",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="processed_shipping_documents",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "purchase_order",
                    models.ForeignKey(
                        blank=True,
                        help_text="Related purchase order if applicable",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="shipping_documents",
                        to="orders.purchaseorder",
                    ),
                ),
                (
                    "shipment",
                    models.ForeignKey(
                        blank=True,
                        help_text="Related shipment if applicable",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="shipping_documents",
                        to="orders.shipment",
                    ),
                ),
                (
                    "supplier",
                    models.ForeignKey(
                        help_text="Supplier associated with this document",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shipping_documents",
                        to="suppliers.supplier",
                    ),
                ),
                (
                    "team",
                    models.ForeignKey(
                        help_text="Team that owns this document",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shipping_documents",
                        to="users.team",
                    ),
                ),
            ],
            options={
                "verbose_name": "Shipping Document",
                "verbose_name_plural": "Shipping Documents",
                "ordering": ["-received_date"],
            },
        ),
        migrations.CreateModel(
            name="ShippingItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "received_quantity",
                    models.FloatField(help_text="Quantity actually received"),
                ),
                (
                    "condition",
                    models.CharField(
                        choices=[
                            ("good", "Good Condition"),
                            ("damaged", "Damaged"),
                            ("partial_damage", "Partially Damaged"),
                        ],
                        default="good",
                        help_text="Condition of received items",
                        max_length=20,
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Notes about condition, discrepancies, etc.",
                    ),
                ),
                (
                    "order_item",
                    models.ForeignKey(
                        help_text="Purchase order item that was received",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shipping_items",
                        to="orders.orderitem",
                    ),
                ),
                (
                    "shipping_document",
                    models.ForeignKey(
                        help_text="Shipping document this item was received with",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shipping_items",
                        to="shipping_documents.shippingdocument",
                    ),
                ),
            ],
            options={
                "verbose_name": "Shipping Item",
                "verbose_name_plural": "Shipping Items",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["order_item", "shipping_document"],
                        name="shipping_do_order_i_893ef4_idx",
                    ),
                    models.Index(
                        fields=["shipping_document"],
                        name="shipping_do_shippin_7ea3eb_idx",
                    ),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="shippingdocument",
            index=models.Index(
                fields=["team", "document_type", "received_date"],
                name="shipping_do_team_id_c37ebb_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="shippingdocument",
            index=models.Index(
                fields=["purchase_order", "status"],
                name="shipping_do_purchas_c8de69_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="shippingdocument",
            index=models.Index(
                fields=["supplier", "status"], name="shipping_do_supplie_7e68ff_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="shippingdocument",
            index=models.Index(
                fields=["tracking_number"], name="shipping_do_trackin_8dffb4_idx"
            ),
        ),
    ]
