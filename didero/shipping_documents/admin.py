from __future__ import annotations

from typing import TYPE_CHECKING

from django.contrib import admin
from django.db.models import QuerySet
from django.forms import ModelForm
from django.http import HttpRequest
from django.utils.html import format_html

from didero.shipping_documents.models import ShippingDocument, ShippingItem

if TYPE_CHECKING:
    from didero.users.models import User


class ShippingItemInline(admin.TabularInline):  # type: ignore[type-arg]
    model = ShippingItem
    extra = 0
    readonly_fields = ["order_item__item_number", "order_item__description"]
    fields = [
        "order_item",
        "order_item__item_number",
        "order_item__description",
        "received_quantity",
        "condition",
        "notes",
    ]

    def order_item__item_number(self, obj: ShippingItem) -> str:
        return (
            obj.order_item.item.item_number
            if obj.order_item and obj.order_item.item
            else "-"
        )

    order_item__item_number.short_description = "Item Number"  # type: ignore

    def order_item__description(self, obj: ShippingItem) -> str:
        return (
            obj.order_item.item.description
            if obj.order_item and obj.order_item.item
            else "-"
        )

    order_item__description.short_description = "Description"  # type: ignore


@admin.register(ShippingDocument)
class ShippingDocumentAdmin(admin.ModelAdmin):  # type: ignore[type-arg]
    list_display = [
        "reference_number",
        "document_type",
        "supplier",
        "purchase_order",
        "status",
        "received_date",
        "item_count",
    ]
    list_filter = [
        "document_type",
        "status",
        "received_date",
        ("supplier", admin.RelatedOnlyFieldListFilter),
        ("purchase_order", admin.RelatedOnlyFieldListFilter),
    ]
    search_fields = [
        "reference_number",
        "tracking_number",
        "supplier__name",
        "purchase_order__po_number",
    ]
    readonly_fields = [
        "created_at",
        "modified_at",
        "processed_at",
        "team",
        "item_count",
        "document_link",
    ]
    fieldsets = [
        (
            "Document Information",
            {
                "fields": [
                    "document_type",
                    "reference_number",
                    "document",
                    "document_link",
                    "status",
                ]
            },
        ),
        (
            "Relationships",
            {
                "fields": [
                    "supplier",
                    "purchase_order",
                    "shipment",
                    "team",
                ]
            },
        ),
        (
            "Shipping Details",
            {
                "fields": [
                    "carrier_name",
                    "tracking_number",
                    "document_date",
                    "received_date",
                    "notes",
                ]
            },
        ),
        (
            "Processing Information",
            {
                "fields": [
                    "processed_at",
                    "processed_by",
                    "extracted_data",
                ],
                "classes": ["collapse"],
            },
        ),
        (
            "Metadata",
            {
                "fields": [
                    "item_count",
                    "created_at",
                    "modified_at",
                ],
                "classes": ["collapse"],
            },
        ),
    ]
    inlines = [ShippingItemInline]

    def get_queryset(self, request: HttpRequest) -> QuerySet[ShippingDocument]:
        queryset = super().get_queryset(request)
        return queryset.select_related(
            "supplier", "purchase_order", "document", "team"
        ).prefetch_related("shipping_items__order_item__item")

    def item_count(self, obj: ShippingDocument) -> str:
        """Display count of received items"""
        count = getattr(obj, "shipping_items").count()
        return format_html(f"<strong>{count}</strong>")

    item_count.short_description = "Items"  # type: ignore

    def document_link(self, obj: ShippingDocument) -> str:
        """Display link to document"""
        if obj.document and obj.document.document:
            return format_html(
                '<a href="{}" target="_blank">View Document</a>',
                obj.document.document.url,
            )
        return "-"

    document_link.short_description = "Document"  # type: ignore

    def save_model(
        self, request: HttpRequest, obj: ShippingDocument, form: ModelForm, change: bool
    ) -> None:
        """Set team based on user if creating new object"""
        if not change and (not hasattr(obj, "team") or obj.team is None):
            user: User = request.user  # type: ignore
            obj.team = user.team  # type: ignore
        super().save_model(request, obj, form, change)


@admin.register(ShippingItem)
class ShippingItemAdmin(admin.ModelAdmin):  # type: ignore[type-arg]
    list_display = [
        "shipping_document",
        "order_item_number",
        "order_item_description",
        "received_quantity",
        "condition",
    ]
    list_filter = [
        "condition",
        ("shipping_document__document_type", admin.RelatedOnlyFieldListFilter),
        ("shipping_document__supplier", admin.RelatedOnlyFieldListFilter),
    ]
    search_fields = [
        "shipping_document__reference_number",
        "order_item__item__item_number",
        "order_item__item__description",
    ]
    readonly_fields = [
        "order_item_number",
        "order_item_description",
        "ordered_quantity",
    ]

    def get_queryset(self, request: HttpRequest) -> QuerySet[ShippingItem]:
        queryset = super().get_queryset(request)
        return queryset.select_related("shipping_document", "order_item__item")

    def order_item_number(self, obj: ShippingItem) -> str:
        return (
            obj.order_item.item.item_number
            if obj.order_item and obj.order_item.item
            else "-"
        )

    order_item_number.short_description = "Item Number"  # type: ignore

    def order_item_description(self, obj: ShippingItem) -> str:
        return (
            obj.order_item.item.description
            if obj.order_item and obj.order_item.item
            else "-"
        )

    order_item_description.short_description = "Description"  # type: ignore

    def ordered_quantity(self, obj: ShippingItem) -> str:
        return str(obj.order_item.quantity) if obj.order_item else "-"

    ordered_quantity.short_description = "Ordered Qty"  # type: ignore
