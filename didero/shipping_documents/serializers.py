from __future__ import annotations

from typing import Any, Dict

from rest_framework import serializers

from didero.documents.serializers import BasicDocumentSerializer
from didero.orders.serializers import OrderItemSerializer
from didero.serializers import PoppableModelSzlr
from didero.shipping_documents.models import ShippingDocument, ShippingItem
from didero.suppliers.serializers import BasicSupplierSerializer


class ShippingItemSerializer(PoppableModelSzlr):
    """Serializer for ShippingItem model"""

    order_item = OrderItemSerializer(read_only=True)
    item_number = serializers.CharField(
        read_only=True, source="order_item.item.item_number"
    )
    item_description = serializers.CharField(
        read_only=True, source="order_item.item.description"
    )
    ordered_quantity = serializers.FloatField(
        read_only=True, source="order_item.quantity"
    )

    class Meta(PoppableModelSzlr.Meta):
        model = ShippingItem
        fields = [
            "id",
            "order_item",
            "item_number",
            "item_description",
            "ordered_quantity",
            "received_quantity",
            "condition",
            "notes",
        ]

    def validate_received_quantity(self, value: float) -> float:
        """Validate received quantity is not negative"""
        if value < 0:
            raise serializers.ValidationError("Received quantity cannot be negative")
        return value


class ShippingDocumentSerializer(PoppableModelSzlr):
    """Main serializer for ShippingDocument"""

    # Nested serializers for related objects
    document = BasicDocumentSerializer(read_only=True)
    supplier = BasicSupplierSerializer(read_only=True)
    shipping_items = ShippingItemSerializer(many=True, read_only=True)

    # Computed fields
    total_items = serializers.SerializerMethodField()
    po_number = serializers.CharField(read_only=True, source="purchase_order.po_number")

    class Meta(PoppableModelSzlr.Meta):
        model = ShippingDocument
        fields = [
            "id",
            "document_type",
            "reference_number",
            "document",
            "purchase_order",
            "po_number",
            "shipment",
            "supplier",
            "carrier_name",
            "tracking_number",
            "document_date",
            "received_date",
            "notes",
            "extracted_data",
            "status",
            "processed_at",
            "processed_by",
            "shipping_items",
            "total_items",
            "created_at",
            "modified_at",
        ]
        read_only_fields = [
            "id",
            "processed_at",
            "processed_by",
            "created_at",
            "modified_at",
        ]

    def validate(self, attrs: Dict[str, Any]) -> Dict[str, Any]:
        """Model-level validation"""
        # Must have either purchase_order or supplier
        purchase_order = attrs.get("purchase_order")
        supplier = attrs.get("supplier")

        if not purchase_order and not supplier:
            raise serializers.ValidationError(
                "Must specify either purchase_order or supplier"
            )

        # If linked to PO, supplier should match
        if purchase_order and supplier:
            if purchase_order.supplier != supplier:
                raise serializers.ValidationError(
                    "Supplier must match purchase order supplier"
                )

        return attrs

    def get_total_items(self, obj: ShippingDocument) -> int:
        """Get count of shipping items using prefetched data to avoid N+1 queries"""
        return len(getattr(obj, "shipping_items").all())

    def create(self, validated_data: Dict[str, Any]) -> ShippingDocument:
        """Set team from user context"""
        user = self.context["request"].user
        validated_data["team"] = user.team  # type: ignore
        return super().create(validated_data)


class BasicShippingDocumentSerializer(serializers.ModelSerializer):
    """Basic serializer for use in other serializers"""

    po_number = serializers.CharField(read_only=True, source="purchase_order.po_number")
    supplier_name = serializers.CharField(read_only=True, source="supplier.name")

    class Meta(serializers.ModelSerializer.Meta):
        model = ShippingDocument
        fields = [
            "id",
            "document_type",
            "reference_number",
            "po_number",
            "supplier_name",
            "tracking_number",
            "received_date",
            "status",
        ]
