from __future__ import annotations

import structlog
from auditlog.registry import auditlog
from django.core.exceptions import ValidationError
from django.db import models
from django.utils import timezone

from didero.documents.models import Document
from didero.models import BaseModelWithWebsocketPublishing
from didero.orders.models import OrderItem, PurchaseOrder
from didero.shipments.models import Shipment
from didero.shipping_documents.schemas import (
    ShippingDocumentStatus,
    ShippingDocumentType,
)
from didero.suppliers.models import Supplier
from didero.users.models.team_models import Team
from didero.users.models.user_models import User

logger = structlog.get_logger(__name__)


class ShippingDocument(BaseModelWithWebsocketPublishing):
    """
    Model to represent shipping and receiving documents (BOL, Packing Lists, etc.)
    """

    # Core fields
    document_type = models.CharField(
        max_length=50,
        choices=ShippingDocumentType.choices,
        db_index=True,
        help_text="Type of shipping document",
    )
    document = models.ForeignKey(
        Document,
        on_delete=models.CASCADE,
        related_name="shipping_documents",
        help_text="Associated PDF document",
    )
    reference_number = models.CharField(
        max_length=255,
        db_index=True,
        help_text="Document reference number (BOL#, Packing List#, ASN#, etc.)",
    )

    # Relationships
    purchase_order = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="shipping_documents",
        help_text="Related purchase order if applicable",
    )
    shipment = models.ForeignKey(
        Shipment,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="shipping_documents",
        help_text="Related shipment if applicable",
    )
    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.CASCADE,
        related_name="shipping_documents",
        help_text="Supplier associated with this document",
    )
    team = models.ForeignKey(
        Team,
        on_delete=models.CASCADE,
        related_name="shipping_documents",
        help_text="Team that owns this document",
    )

    # Common fields across all document types
    carrier_name = models.CharField(
        max_length=255, blank=True, help_text="Name of carrier/shipping company"
    )
    tracking_number = models.CharField(
        max_length=255, blank=True, db_index=True, help_text="Shipment tracking number"
    )
    document_date = models.DateField(
        null=True, blank=True, help_text="Date printed/issued on the document itself"
    )
    received_date = models.DateTimeField(
        default=timezone.now, help_text="Date when document was received/processed"
    )
    notes = models.TextField(blank=True, help_text="General notes about the shipment")

    # Store type-specific extracted data
    extracted_data = models.JSONField(
        default=dict, help_text="Document-type-specific fields extracted by AI"
    )

    # Status tracking
    status = models.CharField(
        max_length=20,
        choices=ShippingDocumentStatus.choices,
        default=ShippingDocumentStatus.PENDING,
        db_index=True,
        help_text="Current processing status",
    )
    processed_at = models.DateTimeField(
        null=True, blank=True, help_text="When document was processed"
    )
    processed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="processed_shipping_documents",
        help_text="User who processed the document",
    )

    def clean(self):
        """Model validation"""
        super().clean()

        # If linked to PO, supplier should match
        if self.purchase_order and self.supplier:
            if self.purchase_order.supplier != self.supplier:
                raise ValidationError("Supplier must match purchase order supplier")

    @property
    def team_cached(self):
        """Cached team property for performance"""
        if not hasattr(self, "_team_cache"):
            self._team_cache = self.team
        return self._team_cache

    def __str__(self) -> str:
        return f"{self.document_type} - {self.reference_number}"

    class Meta(BaseModelWithWebsocketPublishing.Meta):
        ordering = ["-received_date"]
        indexes = [
            models.Index(fields=["team", "document_type", "received_date"]),
            models.Index(fields=["purchase_order", "status"]),
            models.Index(fields=["supplier", "status"]),
            models.Index(fields=["tracking_number"]),
        ]
        verbose_name = "Shipping Document"
        verbose_name_plural = "Shipping Documents"


class ShippingItem(BaseModelWithWebsocketPublishing):
    """
    Model to track received quantities for items in shipping documents.
    Multiple ShippingItems can reference the same OrderItem to track partial shipments.
    """

    shipping_document = models.ForeignKey(
        ShippingDocument,
        on_delete=models.CASCADE,
        related_name="shipping_items",
        help_text="Shipping document this item was received with",
    )
    order_item = models.ForeignKey(
        OrderItem,
        on_delete=models.CASCADE,
        related_name="shipping_items",
        help_text="Purchase order item that was received",
    )

    # Shipping/Receiving details
    received_quantity = models.FloatField(help_text="Quantity actually received")
    condition = models.CharField(
        max_length=20,
        choices=[
            ("good", "Good Condition"),
            ("damaged", "Damaged"),
            ("partial_damage", "Partially Damaged"),
        ],
        default="good",
        help_text="Condition of received items",
    )
    notes = models.TextField(
        blank=True, help_text="Notes about condition, discrepancies, etc."
    )

    def clean(self):
        """Model validation"""
        super().clean()

        # Validate received quantity is not negative
        if self.received_quantity < 0:
            raise ValidationError("Received quantity cannot be negative")

    @property
    def team(self):
        """Team property for consistency with other models"""
        return self.shipping_document.team

    def __str__(self) -> str:
        return f"{self.order_item} - Received: {self.received_quantity}"

    class Meta(BaseModelWithWebsocketPublishing.Meta):
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["order_item", "shipping_document"]),
            models.Index(fields=["shipping_document"]),
        ]
        verbose_name = "Shipping Item"
        verbose_name_plural = "Shipping Items"


# Register with auditlog for audit trails
auditlog.register(ShippingDocument)
auditlog.register(ShippingItem)
