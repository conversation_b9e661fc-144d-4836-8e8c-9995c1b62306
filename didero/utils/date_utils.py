"""
Date parsing and formatting utilities for the Didero API.

This module provides flexible date parsing functions that can handle various
date formats commonly found in business documents and user input.
"""

import re
from datetime import datetime
from typing import Optional

import structlog

logger = structlog.get_logger(__name__)


def parse_date_flexible(date_string: Optional[str]) -> Optional[str]:
    """
    Parse various date formats and return YYYY-MM-DD format for Django.

    This function can handle many common date formats including:
    - ISO format: 2024-03-15
    - US format: 03/15/2024, 03-15-2024
    - European format: 15/03/2024, 15-03-2024, 15.03.2024
    - Text formats: March 15, 2024, Mar 15, 2024, 15 March 2024
    - Short years: 03/15/24, 15/03/24
    - Embedded in text: "Invoice Date: March 15, 2024"

    Args:
        date_string: Date in various formats or None

    Returns:
        Date string in YYYY-MM-DD format or None if parsing fails

    Examples:
        >>> parse_date_flexible("03/15/2024")
        "2024-03-15"
        >>> parse_date_flexible("March 15, 2024")
        "2024-03-15"
        >>> parse_date_flexible("Invoice Date: 15.03.2024")
        "2024-03-15"
        >>> parse_date_flexible("")
        None
    """
    if not date_string or date_string.strip() == "":
        return None

    date_string = date_string.strip()

    # Common date formats to try
    formats = [
        "%Y-%m-%d",  # 2024-03-15
        "%m/%d/%Y",  # 03/15/2024
        "%d/%m/%Y",  # 15/03/2024
        "%m-%d-%Y",  # 03-15-2024
        "%d-%m-%Y",  # 15-03-2024
        "%B %d, %Y",  # March 15, 2024
        "%b %d, %Y",  # Mar 15, 2024
        "%d %B %Y",  # 15 March 2024
        "%d %b %Y",  # 15 Mar 2024
        "%Y/%m/%d",  # 2024/03/15
        "%d.%m.%Y",  # 15.03.2024
        "%Y.%m.%d",  # 2024.03.15
        "%m/%d/%y",  # 03/15/24
        "%d/%m/%y",  # 15/03/24
    ]

    for fmt in formats:
        try:
            parsed_date = datetime.strptime(date_string, fmt)
            return parsed_date.strftime("%Y-%m-%d")
        except ValueError:
            continue

    # Try to extract date from more complex strings
    # Look for patterns like "Invoice Date: March 15, 2024"
    date_patterns = [
        r"(\d{4}-\d{1,2}-\d{1,2})",  # 2024-03-15
        r"(\d{1,2}/\d{1,2}/\d{4})",  # 03/15/2024 or 15/03/2024
        r"(\d{1,2}-\d{1,2}-\d{4})",  # 03-15-2024 or 15-03-2024
        r"([A-Za-z]+ \d{1,2}, \d{4})",  # March 15, 2024
        r"(\d{1,2} [A-Za-z]+ \d{4})",  # 15 March 2024
        r"(\d{4}/\d{1,2}/\d{1,2})",  # 2024/03/15
        r"(\d{1,2}\.\d{1,2}\.\d{4})",  # 15.03.2024
        r"(\d{4}\.\d{1,2}\.\d{1,2})",  # 2024.03.15
    ]

    for pattern in date_patterns:
        match = re.search(pattern, date_string)
        if match:
            extracted_date = match.group(1)
            # Avoid infinite recursion by only recursing if extracted date is different
            if extracted_date != date_string:
                return parse_date_flexible(
                    extracted_date
                )  # Recursive call with extracted date

    logger.warning(f"Could not parse date: '{date_string}'")
    return None


def format_date_for_display(
    date_string: Optional[str], format_type: str = "US"
) -> Optional[str]:
    """
    Format a date string for display purposes.

    Args:
        date_string: Date in YYYY-MM-DD format
        format_type: "US" for MM/DD/YYYY, "ISO" for YYYY-MM-DD, "EU" for DD/MM/YYYY

    Returns:
        Formatted date string or None if parsing fails

    Examples:
        >>> format_date_for_display("2024-03-15", "US")
        "03/15/2024"
        >>> format_date_for_display("2024-03-15", "EU")
        "15/03/2024"
    """
    if not date_string:
        return None

    try:
        parsed_date = datetime.strptime(date_string, "%Y-%m-%d")

        if format_type.upper() == "US":
            return parsed_date.strftime("%m/%d/%Y")
        elif format_type.upper() == "EU":
            return parsed_date.strftime("%d/%m/%Y")
        elif format_type.upper() == "ISO":
            return parsed_date.strftime("%Y-%m-%d")
        else:
            return parsed_date.strftime("%m/%d/%Y")  # Default to US format

    except ValueError:
        logger.warning(f"Could not format date: '{date_string}'")
        return None
