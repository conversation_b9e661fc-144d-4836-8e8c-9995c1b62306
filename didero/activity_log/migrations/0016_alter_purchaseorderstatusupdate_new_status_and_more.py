# Generated by Django 4.2.7 on 2025-07-22 22:06

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("activity_log", "0015_alter_purchaseorderstatusupdate_new_status_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="purchaseorderstatusupdate",
            name="new_status",
            field=models.CharField(
                choices=[
                    ("draft", "DRAFT"),
                    ("approval_pending", "APPROVAL_PENDING"),
                    ("approval_denied", "APPROVAL_DENIED"),
                    ("approved", "APPROVED"),
                    ("issued", "ISSUED"),
                    ("pending_acceptance", "PENDING_ACCEPTANCE"),
                    ("oa_mismatch", "OA_MISMATCH"),
                    ("awaiting_shipment", "AWAITING_SHIPMENT"),
                    ("ready_for_pickup", "READY_FOR_PICKUP"),
                    ("supplier_rejected", "SUPPLIER_REJECTED"),
                    ("shipment_delayed", "SHIPMENT_DELAYED"),
                    ("shipped", "SHIPPED"),
                    ("partially_shipped", "PARTIALLY_SHIPPED"),
                    ("delivery_delayed", "DELIVERY_DELAYED"),
                    ("received", "RECEIVED"),
                    ("partially_received", "PARTIALLY_RECEIVED"),
                    ("canceled", "CANCELED"),
                    ("invoice_matched", "INVOICE_MATCHED"),
                    ("issue", "ISSUE"),
                    ("unpaid", "UNPAID"),
                    ("invoice_received", "INVOICE_RECEIVED"),
                    ("ready_to_pay", "READY_TO_PAY"),
                    ("paid", "PAID"),
                    ("partially_delivered", "PARTIALLY_DELIVERED"),
                    ("delivered", "DELIVERED"),
                    ("issue", "ISSUE"),
                ],
                max_length=100,
            ),
        ),
        migrations.AlterField(
            model_name="purchaseorderstatusupdate",
            name="old_status",
            field=models.CharField(
                choices=[
                    ("draft", "DRAFT"),
                    ("approval_pending", "APPROVAL_PENDING"),
                    ("approval_denied", "APPROVAL_DENIED"),
                    ("approved", "APPROVED"),
                    ("issued", "ISSUED"),
                    ("pending_acceptance", "PENDING_ACCEPTANCE"),
                    ("oa_mismatch", "OA_MISMATCH"),
                    ("awaiting_shipment", "AWAITING_SHIPMENT"),
                    ("ready_for_pickup", "READY_FOR_PICKUP"),
                    ("supplier_rejected", "SUPPLIER_REJECTED"),
                    ("shipment_delayed", "SHIPMENT_DELAYED"),
                    ("shipped", "SHIPPED"),
                    ("partially_shipped", "PARTIALLY_SHIPPED"),
                    ("delivery_delayed", "DELIVERY_DELAYED"),
                    ("received", "RECEIVED"),
                    ("partially_received", "PARTIALLY_RECEIVED"),
                    ("canceled", "CANCELED"),
                    ("invoice_matched", "INVOICE_MATCHED"),
                    ("issue", "ISSUE"),
                    ("unpaid", "UNPAID"),
                    ("invoice_received", "INVOICE_RECEIVED"),
                    ("ready_to_pay", "READY_TO_PAY"),
                    ("paid", "PAID"),
                    ("partially_delivered", "PARTIALLY_DELIVERED"),
                    ("delivered", "DELIVERED"),
                    ("issue", "ISSUE"),
                ],
                max_length=100,
            ),
        ),
    ]
