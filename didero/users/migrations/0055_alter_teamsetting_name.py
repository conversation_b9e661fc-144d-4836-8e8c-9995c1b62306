# Generated by Django 4.2.7 on 2025-06-11 18:37

from django.db import migrations, models


def remove_po_create_from_email_settings(apps, schema_editor):
    """Remove all instances of TEAM_PO_CREATE_FROM_EMAIL_ENABLED team settings."""
    TeamSetting = apps.get_model("users", "TeamSetting")

    # Delete all instances of this deprecated setting
    deleted_count = TeamSetting.objects.filter(
        name="TEAM_PO_CREATE_FROM_EMAIL_ENABLED"
    ).delete()[0]

    if deleted_count > 0:
        print(
            f"\nDeleted {deleted_count} instances of TEAM_PO_CREATE_FROM_EMAIL_ENABLED"
        )


def reverse_remove_settings(apps, schema_editor):
    """Reverse migration - does nothing since we can't restore deleted settings."""
    pass


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0054_add_supplier_followup_configuration"),
    ]

    operations = [
        # First run the data migration to remove old settings
        migrations.RunPython(
            remove_po_create_from_email_settings, reverse_remove_settings
        ),
        # Then update the field to remove the choice
        migrations.AlterField(
            model_name="teamsetting",
            name="name",
            field=models.CharField(
                choices=[
                    ("TEAM_AUTO_ARCHIVE_AFTER_DAYS", "TEAM_AUTO_ARCHIVE_AFTER_DAYS"),
                    (
                        "TEAM_PO_DEFAULT_EMAIL_TEMPLATE",
                        "TEAM_PO_DEFAULT_EMAIL_TEMPLATE",
                    ),
                    ("TEAM_PO_DEFAULT_SUPPLIER_NOTE", "TEAM_PO_DEFAULT_SUPPLIER_NOTE"),
                    ("TEAM_PO_DEFAULT_TERMS", "TEAM_PO_DEFAULT_TERMS"),
                    (
                        "TEAM_SUPPLIER_ONBOARDING_REQUIREMENTS",
                        "TEAM_SUPPLIER_ONBOARDING_REQUIREMENTS",
                    ),
                    (
                        "TEAM_AI_PROCESS_EMAILS_ENABLED",
                        "TEAM_AI_PROCESS_EMAILS_ENABLED",
                    ),
                    (
                        "TEAM_AI_PARSE_DOCUMENT_DETAILS_ENABLED",
                        "TEAM_AI_PARSE_DOCUMENT_DETAILS_ENABLED",
                    ),
                    ("TEAM_QUOTE_TO_PO_ENABLED", "TEAM_QUOTE_TO_PO_ENABLED"),
                    (
                        "TEAM_TASK_EMAIL_NOTIFICATIONS_ENABLED",
                        "TEAM_TASK_EMAIL_NOTIFICATIONS_ENABLED",
                    ),
                    (
                        "TEAM_FOLLOWUP_WORKFLOW_ENABLED",
                        "TEAM_FOLLOWUP_WORKFLOW_ENABLED",
                    ),
                    ("TEAM_FOLLOWUP_WAIT_TIME_HOURS", "TEAM_FOLLOWUP_WAIT_TIME_HOURS"),
                    ("TEAM_FOLLOWUP_GLOBAL_CONFIG", "TEAM_FOLLOWUP_GLOBAL_CONFIG"),
                    ("TEAM_FOLLOWUP_OA_CONFIG", "TEAM_FOLLOWUP_OA_CONFIG"),
                    ("TEAM_FOLLOWUP_SHIPMENT_CONFIG", "TEAM_FOLLOWUP_SHIPMENT_CONFIG"),
                    ("TEAM_FOLLOWUP_DELIVERY_CONFIG", "TEAM_FOLLOWUP_DELIVERY_CONFIG"),
                    (
                        "TEAM_PO_CREATION_HUMAN_VALIDATION_ENABLED",
                        "TEAM_PO_CREATION_HUMAN_VALIDATION_ENABLED",
                    ),
                    (
                        "TEAM_ORDER_ACK_HUMAN_VALIDATION_ENABLED",
                        "TEAM_ORDER_ACK_HUMAN_VALIDATION_ENABLED",
                    ),
                    (
                        "TEAM_SHIPMENT_HUMAN_VALIDATION_ENABLED",
                        "TEAM_SHIPMENT_HUMAN_VALIDATION_ENABLED",
                    ),
                    (
                        "USER_EMAIL_NOTIFICATIONS_REMINDER_ENABLED",
                        "USER_EMAIL_NOTIFICATIONS_REMINDER_ENABLED",
                    ),
                    (
                        "USER_EMAIL_PO_APPROVAL_REMINDER_ENABLED",
                        "USER_EMAIL_PO_APPROVAL_REMINDER_ENABLED",
                    ),
                    (
                        "USER_EMAIL_PO_APPROVAL_REQUEST_ENABLED",
                        "USER_EMAIL_PO_APPROVAL_REQUEST_ENABLED",
                    ),
                    ("USER_EMAIL_PO_UPDATE_ENABLED", "USER_EMAIL_PO_UPDATE_ENABLED"),
                    (
                        "USER_EMAIL_TASK_NOTIFICATIONS_ENABLED",
                        "USER_EMAIL_TASK_NOTIFICATIONS_ENABLED",
                    ),
                ],
                max_length=255,
            ),
        ),
    ]
