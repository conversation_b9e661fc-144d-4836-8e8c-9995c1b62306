"""
Utility function for country code mappings.
"""


def get_country_codes_for_prompt() -> str:
    """
    Get a formatted string of ALL country names mapped to their ISO codes for use in AI prompts.

    Returns:
        A formatted string containing all country mappings that can be inserted into prompts.
    """
    from django_countries import countries

    # Create the formatted string with all mappings
    mappings = []

    # Add all official country names from django_countries
    for code, name in countries:
        mappings.append(f'    "{name}": "{code}"')

    # Add common aliases and variations
    additional_mappings = {
        "USA": "US",
        "United States of America": "US",
        "United States": "US",
        "America": "US",
        "UK": "GB",
        "United Kingdom": "GB",
        "Great Britain": "GB",
        "England": "GB",
        "Scotland": "GB",
        "Wales": "GB",
        "Northern Ireland": "GB",
        "Holland": "NL",
        "The Netherlands": "NL",
        "South Korea": "KR",
        "North Korea": "KP",
        "Republic of Korea": "KR",
        "Democratic People's Republic of Korea": "KP",
        "PRC": "CN",
        "People's Republic of China": "CN",
        "Mainland China": "CN",
        "Republic of China": "TW",
        "ROC": "TW",
        "Deutschland": "DE",
        "Bundesrepublik Deutschland": "DE",
        "Nederland": "NL",
        "Czech Republic": "CZ",
        "Czechia": "CZ",
        "Slovak Republic": "SK",
        "Slovakia": "SK",
        "Russia": "RU",
        "Russian Federation": "RU",
        "UAE": "AE",
        "United Arab Emirates": "AE",
        "Vatican": "VA",
        "Vatican City": "VA",
        "Hong Kong": "HK",
        "Macau": "MO",
        "Macao": "MO",
    }

    for name, code in additional_mappings.items():
        mappings.append(f'    "{name}": "{code}"')

    # Sort all mappings for consistency
    mappings = sorted(set(mappings))

    # Format as a string that can be inserted into prompts
    return "\n".join(mappings)
