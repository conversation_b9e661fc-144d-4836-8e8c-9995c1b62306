from decimal import Decimal
from typing import Any
from unittest import TestCase

from didero.ai.purchase_order.po_extraction import (
    _calculate_estimated_total,
    _has_valid_price,
    _has_valid_quantity,
    is_po_data_sufficient,
)
from didero.ai.purchase_order.schemas import PurchaseOrderDetails, PurchaseOrderItem


class TestPurchaseOrderValidation(TestCase):
    """Test validation functions for PO creation requirements."""

    def create_test_po_item(self, **kwargs: Any) -> PurchaseOrderItem:
        """Create a test PO item with default valid values."""
        defaults = {
            "item_number": "TEST-001",
            "item_description": "Test Item",
            "quantity": 1.0,
            "unit_of_measure": "Each",
            "unit_price": "10.00",
            "total_price": "10.00",
        }
        defaults.update(kwargs)
        return PurchaseOrderItem(**defaults)

    def create_test_po_details(self, **kwargs: Any) -> PurchaseOrderDetails:
        """Create test PO details with default valid values."""
        defaults = {
            "po_number": "PO-123",
            "supplier_name": "Test Supplier",
            "items": [self.create_test_po_item()],
            "currency": "USD",
        }
        defaults.update(kwargs)
        return PurchaseOrderDetails(**defaults)

    def test_has_valid_price_positive_price(self):
        """Test that positive prices are considered valid."""
        item = self.create_test_po_item(unit_price="10.00")
        self.assertTrue(_has_valid_price(item))

    def test_has_valid_price_zero_price(self):
        """Test that zero prices are invalid."""
        item = self.create_test_po_item(unit_price="0.00")
        self.assertFalse(_has_valid_price(item))

    def test_has_valid_price_negative_price(self):
        """Test that negative prices are invalid."""
        item = self.create_test_po_item(unit_price="-5.00")
        self.assertFalse(_has_valid_price(item))

    def test_has_valid_price_missing_price(self):
        """Test that missing prices are invalid."""
        item = self.create_test_po_item(unit_price="")
        self.assertFalse(_has_valid_price(item))

    def test_has_valid_price_empty_price(self):
        """Test that empty string prices are invalid."""
        item = self.create_test_po_item(unit_price="")
        self.assertFalse(_has_valid_price(item))

    def test_has_valid_price_invalid_format(self):
        """Test that non-numeric prices are invalid."""
        item = self.create_test_po_item(unit_price="abc")
        self.assertFalse(_has_valid_price(item))

    def test_has_valid_quantity_positive_quantity(self):
        """Test that positive quantities are valid."""
        item = self.create_test_po_item(quantity=5.0)
        self.assertTrue(_has_valid_quantity(item))

    def test_has_valid_quantity_zero_quantity(self):
        """Test that zero quantities are invalid."""
        item = self.create_test_po_item(quantity=0)
        self.assertFalse(_has_valid_quantity(item))

    def test_has_valid_quantity_negative_quantity(self):
        """Test that negative quantities are invalid."""
        item = self.create_test_po_item(quantity=-1)
        self.assertFalse(_has_valid_quantity(item))

    def test_has_valid_quantity_missing_quantity(self):
        """Test that missing quantities are invalid."""
        # Since quantity is required, test with 0 instead of None
        item = self.create_test_po_item(quantity=0)
        self.assertFalse(_has_valid_quantity(item))

    def test_calculate_estimated_total_single_item(self):
        """Test total calculation with single item."""
        po_details = self.create_test_po_details()
        po_details.items[0].unit_price = "10.00"
        po_details.items[0].quantity = 2

        total = _calculate_estimated_total(po_details)
        self.assertEqual(total, Decimal("20.00"))

    def test_calculate_estimated_total_multiple_items(self):
        """Test total calculation with multiple items."""
        item1 = self.create_test_po_item(unit_price="10.00", quantity=2)
        item2 = self.create_test_po_item(unit_price="15.00", quantity=3)
        po_details = self.create_test_po_details(items=[item1, item2])

        total = _calculate_estimated_total(po_details)
        self.assertEqual(total, Decimal("65.00"))  # (10*2) + (15*3) = 65

    def test_calculate_estimated_total_with_tax(self):
        """Test total calculation includes tax amount."""
        po_details = self.create_test_po_details()
        po_details.items[0].unit_price = "10.00"
        po_details.items[0].quantity = 1
        po_details.tax_amount = "2.50"

        total = _calculate_estimated_total(po_details)
        self.assertEqual(total, Decimal("12.50"))  # 10 + 2.50

    def test_calculate_estimated_total_with_shipping(self):
        """Test total calculation includes shipping charges."""
        po_details = self.create_test_po_details()
        po_details.items[0].unit_price = "10.00"
        po_details.items[0].quantity = 1
        po_details.shipping_charges = "5.00"

        total = _calculate_estimated_total(po_details)
        self.assertEqual(total, Decimal("15.00"))  # 10 + 5

    def test_calculate_estimated_total_with_tax_and_shipping(self):
        """Test total calculation with both tax and shipping."""
        po_details = self.create_test_po_details()
        po_details.items[0].unit_price = "10.00"
        po_details.items[0].quantity = 1
        po_details.tax_amount = "2.00"
        po_details.shipping_charges = "3.00"

        total = _calculate_estimated_total(po_details)
        self.assertEqual(total, Decimal("15.00"))  # 10 + 2 + 3

    def test_calculate_estimated_total_skips_invalid_items(self):
        """Test that invalid items cause immediate False return."""
        item1 = self.create_test_po_item(unit_price="10.00", quantity=1)
        item2 = self.create_test_po_item(unit_price="0.00", quantity=1)  # Invalid price
        item3 = self.create_test_po_item(
            unit_price="15.00", quantity=0
        )  # Invalid quantity
        po_details = self.create_test_po_details(items=[item1, item2, item3])

        # The function should return False immediately when it finds an invalid item
        self.assertFalse(is_po_data_sufficient(po_details))

    def test_is_po_data_sufficient_valid_data(self):
        """Test that valid PO data is considered sufficient."""
        po_details = self.create_test_po_details()
        po_details.items[0].unit_price = "10.00"
        po_details.items[0].quantity = 1

        self.assertTrue(is_po_data_sufficient(po_details))

    def test_is_po_data_sufficient_missing_po_number(self):
        """Test that missing PO number makes data insufficient."""
        po_details = self.create_test_po_details(po_number="")
        self.assertFalse(is_po_data_sufficient(po_details))

    def test_is_po_data_sufficient_missing_supplier(self):
        """Test that missing supplier makes data insufficient."""
        po_details = self.create_test_po_details(supplier_name="")
        self.assertFalse(is_po_data_sufficient(po_details))

    def test_is_po_data_sufficient_no_items(self):
        """Test that no items makes data insufficient."""
        po_details = self.create_test_po_details(items=[])
        self.assertFalse(is_po_data_sufficient(po_details))

    def test_is_po_data_sufficient_items_missing_prices(self):
        """Test that items without prices cause immediate False return."""
        item = self.create_test_po_item(unit_price="")
        po_details = self.create_test_po_details(items=[item])
        self.assertFalse(is_po_data_sufficient(po_details))

    def test_is_po_data_sufficient_items_zero_prices(self):
        """Test that items with zero prices cause immediate False return."""
        item = self.create_test_po_item(unit_price="0.00")
        po_details = self.create_test_po_details(items=[item])
        self.assertFalse(is_po_data_sufficient(po_details))

    def test_is_po_data_sufficient_items_missing_quantities(self):
        """Test that items without quantities cause immediate False return."""
        # Since quantity is required, test with 0 instead of None
        item = self.create_test_po_item(quantity=0)
        po_details = self.create_test_po_details(items=[item])
        self.assertFalse(is_po_data_sufficient(po_details))

    def test_is_po_data_sufficient_items_zero_quantities(self):
        """Test that items with zero quantities cause immediate False return."""
        item = self.create_test_po_item(quantity=0)
        po_details = self.create_test_po_details(items=[item])
        self.assertFalse(is_po_data_sufficient(po_details))

    def test_is_po_data_sufficient_below_minimum_value(self):
        """Test that orders below $1.00 minimum are insufficient."""
        item = self.create_test_po_item(unit_price="0.50", quantity=1)
        po_details = self.create_test_po_details(items=[item])
        self.assertFalse(is_po_data_sufficient(po_details))

    def test_is_po_data_sufficient_exactly_minimum_value(self):
        """Test that orders exactly at $1.00 minimum are sufficient."""
        item = self.create_test_po_item(unit_price="1.00", quantity=1)
        po_details = self.create_test_po_details(items=[item])
        self.assertTrue(is_po_data_sufficient(po_details))

    def test_is_po_data_sufficient_above_minimum_value(self):
        """Test that orders above $1.00 minimum are sufficient."""
        item = self.create_test_po_item(unit_price="10.00", quantity=1)
        po_details = self.create_test_po_details(items=[item])
        self.assertTrue(is_po_data_sufficient(po_details))

    def test_is_po_data_sufficient_mixed_valid_invalid_items(self):
        """Test that mixed valid and invalid items are insufficient."""
        item1 = self.create_test_po_item(unit_price="10.00", quantity=1)
        item2 = self.create_test_po_item(unit_price="20.00", quantity=0)  # Invalid
        po_details = self.create_test_po_details(items=[item1, item2])

        # Even with one valid item, the presence of an invalid one makes it insufficient
        self.assertFalse(is_po_data_sufficient(po_details))

    def test_is_po_data_sufficient_all_items_invalid(self):
        """Test that PO with all invalid items is insufficient."""
        item1 = self.create_test_po_item(unit_price="0.00", quantity=1)  # Invalid price
        item2 = self.create_test_po_item(
            unit_price="5.00", quantity=0
        )  # Invalid quantity
        po_details = self.create_test_po_details(items=[item1, item2])

        self.assertFalse(is_po_data_sufficient(po_details))
