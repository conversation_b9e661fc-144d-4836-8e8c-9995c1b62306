"""
Types for AI tool parameters.
These are lightweight types for PydanticAI tool function signatures.
"""

from pydantic import BaseModel


class ItemMatchParams(BaseModel):
    """Simple parameters for item matching tool"""

    item1_number: str
    item1_description: str
    item2_number: str
    item2_description: str
    quantity1: str = ""
    quantity2: str = ""
    unit_price1: str = ""
    unit_price2: str = ""


class AddressMatchParams(BaseModel):
    """Simple parameters for address matching tool"""

    addr1_line1: str
    addr1_city: str
    addr1_state: str
    addr2_line1: str
    addr2_city: str
    addr2_state: str
    addr1_line2: str = ""
    addr1_zip: str = ""
    addr1_country: str = ""
    addr2_line2: str = ""
    addr2_zip: str = ""
    addr2_country: str = ""
