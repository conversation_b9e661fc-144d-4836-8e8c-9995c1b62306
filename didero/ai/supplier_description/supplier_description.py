import json
from typing import Op<PERSON>, <PERSON><PERSON>
from pathlib import Path
from urllib.parse import urlparse, urlunparse

import bs4
import requests
import structlog
from langfuse.decorators import observe

from didero.ai.ai_integrations import get_anthropic_client
from didero.ai.schemas import AIModelNames, AIPromptKey
from didero.ai.utils.utils import get_langfuse_prompt, AIOperationContext
from didero.ai.supplier_description.schemas import SupplierDescriptionResponse

logger = structlog.get_logger(__name__)

# Get path to prompts config
prompts_path = Path(__file__).parent / "prompts_config.yaml"
if not prompts_path.exists():
    logger.error(f"Prompts file not found at {prompts_path}")
    raise FileNotFoundError(f"Prompts file not found at {prompts_path}")


# Headers to avoid bot detection
SCRAPING_HEADERS = {
    "accept-language": "en-US,en;q=0.9",
    "accept-encoding": "gzip, deflate, br, zstd",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "sec-ch-ua": '"Chromium";v="131", "Not_A Brand";v="24"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "macOS",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "same-origin",
    "sec-fetch-user": "?1",
}


def _normalize_url(url: str) -> str:
    """
    Normalize a URL to ensure it has a proper scheme and format.

    Args:
        url: The URL to normalize (e.g., "amazon.com", "www.example.com", "https://google.com")

    Returns:
        Normalized URL with proper scheme

    Examples:
        "amazon.com" -> "https://amazon.com"
        "www.example.com" -> "https://www.example.com"
        "http://google.com" -> "http://google.com" (unchanged)
        "https://github.com" -> "https://github.com" (unchanged)
    """
    # Strip whitespace
    url = url.strip()

    # Parse the URL
    parsed = urlparse(url)

    # If no scheme is present, add https://
    if not parsed.scheme:
        # If the URL doesn't start with a scheme, add https://
        url = f"https://{url}"
        parsed = urlparse(url)

    # Ensure we have a netloc (domain)
    if not parsed.netloc:
        raise ValueError(f"Invalid URL format: {url}")

    # Return the normalized URL
    return urlunparse(parsed)


def _fetch_webpage_content(url: str) -> Tuple[bool, str]:
    """
    Fetch and clean webpage content from the given URL.

    Args:
        url: The URL to fetch content from (will be normalized if needed)

    Returns:
        Tuple of (success, content) where success is bool and content is cleaned text
    """
    try:
        # Normalize the URL to ensure it has a proper scheme
        normalized_url = _normalize_url(url)

        response = requests.get(normalized_url, headers=SCRAPING_HEADERS, timeout=10)
        if response.status_code != 200:
            logger.error(
                f"Failed to fetch URL {normalized_url}: HTTP {response.status_code}"
            )
            return False, ""

        soup = bs4.BeautifulSoup(response.content, "html.parser")

        # Remove script and style elements
        for script in soup(["script", "style", "nav", "footer", "header"]):
            script.decompose()

        # Get page title
        title = soup.title.string if soup.title else "No title found"

        # Get main content text
        content = soup.get_text()

        # Clean up whitespace
        lines = (line.strip() for line in content.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        content = " ".join(chunk for chunk in chunks if chunk)

        # Limit content length to avoid token limits
        if len(content) > 8000:
            content = content[:8000] + "..."

        formatted_content = f"""
        Page URL: {normalized_url}
        Page Title: {title}
        
        Website Content:
        {content}
        """

        return True, formatted_content

    except ValueError as e:
        # Handle URL normalization errors
        logger.error(f"Invalid URL format: {url}: {e}")
        return False, ""
    except requests.RequestException as e:
        logger.error(f"Request failed for URL {url}: {e}")
        return False, ""
    except Exception as e:
        logger.error(f"Unexpected error fetching URL {url}: {e}")
        return False, ""


@observe()
def generate_supplier_description_from_url(
    url: str, team_id: str
) -> SupplierDescriptionResponse:
    """
    Generate a business description for a supplier from their website URL.

    Args:
        url: The supplier's website URL
        team_id: The team ID for context tracking

    Returns:
        SupplierDescriptionResponse with business description and success status
    """

    success, content = _fetch_webpage_content(url)
    if not success:
        return SupplierDescriptionResponse(business_description="", success=False)

    system_prompt = get_langfuse_prompt(
        prompts_path=prompts_path,
        prompt_key=AIPromptKey.SUPPLIER_BUSINESS_DESCRIPTION,
        prompt_type="text",
    )

    system_prompt = str(system_prompt.compile())

    with AIOperationContext(
        trace_name="generate_supplier_description",
        team_id=team_id,
        metadata={"url": url},
        tags=["supplier_description", "business_analysis"],
        input_data={"url": url, "content_length": len(content)},
    ) as ctx:
        try:
            anthropic_client = get_anthropic_client()

            model_config = {"temperature": 0.1, "max_tokens": 1000}

            generation = ctx.create_generation(
                name="generate_business_description_anthropic",
                model=AIModelNames.ANTHROPIC_MINI,
                input_data={"system": system_prompt, "user": content},
                model_config=model_config,
            )

            response = anthropic_client.messages.create(
                model=AIModelNames.ANTHROPIC_MINI,
                max_tokens=model_config["max_tokens"],
                temperature=model_config["temperature"],
                system=system_prompt,
                messages=[
                    {"role": "user", "content": content},
                ],
            )

            try:
                response_text = response.content[0].text  # pyright: ignore - response.content[0].text is not None
                result_dict = json.loads(response_text)
                result = SupplierDescriptionResponse(**result_dict)

                if result.success and result.business_description:
                    generation.end(
                        output=result.model_dump_json(),
                        level="DEFAULT",
                        status_message="Successfully generated supplier description",
                    )
                    logger.info(
                        f"Successfully generated description for URL {url}: {result.business_description}"
                    )
                    return result
                else:
                    generation.end(
                        output=result.model_dump_json(),
                        level="WARNING",
                        status_message="Failed to generate supplier description",
                    )
                    logger.warning(
                        f"Failed to generate supplier description for URL {url}"
                    )
                    return SupplierDescriptionResponse(
                        business_description="", success=False
                    )

            except (json.JSONDecodeError, KeyError, TypeError) as e:
                generation.end(
                    output=str(e),
                    level="ERROR",
                    status_message=f"Failed to parse response: {str(e)}",
                )
                logger.error(f"Failed to parse response for URL {url}: {e}")
                return SupplierDescriptionResponse(
                    business_description="", success=False
                )

        except Exception as e:
            logger.error(f"Error generating supplier description for URL {url}: {e}")
            raise


def generate_supplier_description(url: str, team_id: str) -> Optional[str]:
    """
    Simple wrapper function to generate supplier description from URL.

    Args:
        url: The supplier's website URL
        team_id: The team ID for context tracking

    Returns:
        The business description string if successful, None otherwise
    """
    result = generate_supplier_description_from_url(url, team_id)
    if result.success and result.business_description:
        return result.business_description
    return None
