#!/usr/bin/env python3
"""
Experiment 1: Adapt existing NetSuite client patterns
Goal: Test if we can reuse existing search and authentication from NetSuiteClient
"""

import re
from typing import Dict, Any, Optional
from decimal import Decimal


class ExperimentV1:
    """Test extraction using existing NetSuite client patterns"""

    def extract_po_from_xml(self, xml_text: str) -> Dict[str, Any]:
        """Extract PO data mimicking Stagehand's flattened structure"""

        # Initialize result structure similar to netsuite_sync.py expectations
        result = {"purchase_order": {}, "line_items": []}

        # Extract basic PO fields (similar to update_basic_fields pattern)
        po_data = result["purchase_order"]

        # Direct field extractions
        field_patterns = {
            "tranId": r"<tranPurch:tranId>([^<]*)</tranPurch:tranId>",
            "memo": r"<tranPurch:memo>([^<]*)</tranPurch:memo>",
            "status": r"<tranPurch:status>([^<]*)</tranPurch:status>",
            "total": r"<tranPurch:total>([^<]*)</tranPurch:total>",
            "currencyName": r"<tranPurch:currencyName>([^<]*)</tranPurch:currencyName>",
            "payment_terms": r"<tranPurch:terms[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>",
            "shipping_method": r"<tranPurch:shipMethod[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>",
            "tranDate": r"<tranPurch:tranDate>([^<]*)</tranPurch:tranDate>",
            "email": r"<tranPurch:email>([^<]*)</tranPurch:email>",
        }

        for field, pattern in field_patterns.items():
            match = re.search(pattern, xml_text, re.DOTALL)
            if match:
                po_data[field] = match.group(1).strip()

        # Extract vendor_address (flattened structure like Stagehand)
        vendor_match = re.search(
            r"<tranPurch:entity[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>",
            xml_text,
            re.DOTALL,
        )
        if vendor_match:
            # Create vendor_address structure matching netsuite_sync expectations
            po_data["vendor_address"] = {"company": vendor_match.group(1).strip()}

        # Extract billing address and add to vendor_address
        billing_match = re.search(
            r"<tranPurch:billingAddress[^>]*>(.*?)</tranPurch:billingAddress>",
            xml_text,
            re.DOTALL,
        )
        if billing_match and "vendor_address" in po_data:
            billing_xml = billing_match.group(1)
            # Extract billing fields
            billing_fields = {
                "addr1": r"<platformCommon:addr1>([^<]*)</platformCommon:addr1>",
                "addr2": r"<platformCommon:addr2>([^<]*)</platformCommon:addr2>",
                "city": r"<platformCommon:city>([^<]*)</platformCommon:city>",
                "state": r"<platformCommon:state>([^<]*)</platformCommon:state>",
                "zip": r"<platformCommon:zip>([^<]*)</platformCommon:zip>",
                "country": r"<platformCommon:country>([^<]*)</platformCommon:country>",
            }

            for field, pattern in billing_fields.items():
                match = re.search(pattern, billing_xml)
                if match:
                    po_data["vendor_address"][field] = match.group(1).strip()

        # Extract raw ship_to text (minimal address)
        ship_to_match = re.search(
            r"<tranPurch:shippingAddress[^>]*>.*?<platformCommon:addrText>([^<]*)</platformCommon:addrText>",
            xml_text,
            re.DOTALL,
        )
        if ship_to_match:
            po_data["ship_to"] = ship_to_match.group(1).strip()

        # Convert total to Stagehand format
        if "total" in po_data and "currencyName" in po_data:
            po_data["total_cost_0"] = po_data["total"]
            po_data["total_cost_currency"] = po_data["currencyName"]

        # Extract line items
        itemlist_match = re.search(
            r"<tranPurch:itemList>(.*?)</tranPurch:itemList>", xml_text, re.DOTALL
        )
        if itemlist_match:
            items_xml = itemlist_match.group(1)

            # Find all items
            item_pattern = r"<tranPurch:item>(.*?)</tranPurch:item>"
            for item_match in re.finditer(item_pattern, items_xml, re.DOTALL):
                item_xml = item_match.group(1)
                line_item = self._extract_line_item_stagehand_format(item_xml)
                if line_item:
                    result["line_items"].append(line_item)

        return result

    def _extract_line_item_stagehand_format(
        self, item_xml: str
    ) -> Optional[Dict[str, Any]]:
        """Extract line item in Stagehand's flattened format"""
        line_item = {}

        # Extract item number
        item_ref_match = re.search(
            r"<tranPurch:item[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>",
            item_xml,
            re.DOTALL,
        )
        if item_ref_match:
            line_item["item_number"] = item_ref_match.group(1).strip()

        # Extract basic fields
        field_patterns = {
            "description": r"<tranPurch:description>([^<]*)</tranPurch:description>",
            "quantity": r"<tranPurch:quantity>([^<]*)</tranPurch:quantity>",
            "vendor_name": r"<tranPurch:vendorName>([^<]*)</tranPurch:vendorName>",
        }

        for field, pattern in field_patterns.items():
            match = re.search(pattern, item_xml)
            if match:
                value = match.group(1).strip()
                if field == "quantity":
                    # Keep as float for Stagehand format
                    try:
                        line_item[field] = float(value)
                    except:
                        line_item[field] = 1.0
                else:
                    line_item[field] = value

        # Extract price in Stagehand format
        rate_match = re.search(r"<tranPurch:rate>([^<]*)</tranPurch:rate>", item_xml)
        if rate_match:
            line_item["unit_price_0"] = rate_match.group(1).strip()
            # Assume USD if currency not specified
            line_item["unit_price_currency"] = "USD"

        # Only return if we have minimum required fields
        if "item_number" in line_item and "unit_price_0" in line_item:
            return line_item

        return None


# Test function
def test_extraction():
    """Test the extraction with sample XML"""
    sample_xml = """
    <tranPurch:purchaseOrder>
        <tranPurch:tranId>PO431</tranPurch:tranId>
        <tranPurch:memo>WEB NUW1334695</tranPurch:memo>
        <tranPurch:status>Fully Billed</tranPurch:status>
        <tranPurch:total>7291.42</tranPurch:total>
        <tranPurch:currencyName>USD</tranPurch:currencyName>
        <tranPurch:entity internalId="550">
            <platformCore:name>V10072 ThorLabs</platformCore:name>
        </tranPurch:entity>
        <tranPurch:billingAddress>
            <platformCommon:addr1>56 Sparta Avenue</platformCommon:addr1>
            <platformCommon:city>Newton</platformCommon:city>
            <platformCommon:state>NJ</platformCommon:state>
            <platformCommon:zip>07860</platformCommon:zip>
        </tranPurch:billingAddress>
        <tranPurch:shippingAddress>
            <platformCommon:addrText>CP Tooling (NI)\r\nUnited States</platformCommon:addrText>
        </tranPurch:shippingAddress>
        <tranPurch:itemList>
            <tranPurch:item>
                <tranPurch:item internalId="2761">
                    <platformCore:name>502-00097</platformCore:name>
                </tranPurch:item>
                <tranPurch:description>Compact Power and Energy Meter Console</tranPurch:description>
                <tranPurch:quantity>1.0</tranPurch:quantity>
                <tranPurch:rate>1220.57</tranPurch:rate>
                <tranPurch:vendorName>PM100D</tranPurch:vendorName>
            </tranPurch:item>
        </tranPurch:itemList>
    </tranPurch:purchaseOrder>
    """

    extractor = ExperimentV1()
    result = extractor.extract_po_from_xml(sample_xml)

    print("=== Experiment V1 Results ===")
    print(f"PO Data Keys: {list(result['purchase_order'].keys())}")
    print(f"Line Items Count: {len(result['line_items'])}")
    print(f"Vendor Address: {result['purchase_order'].get('vendor_address', {})}")
    print(f"Ship To: {result['purchase_order'].get('ship_to', 'Not found')}")

    return result


if __name__ == "__main__":
    test_extraction()
