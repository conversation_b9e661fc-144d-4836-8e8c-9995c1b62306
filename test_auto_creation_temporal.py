#!/usr/bin/env python
"""
Real end-to-end test for Smart PO Auto-Creation using Temporal.

This test will:
1. Create an Order Acknowledgement referencing a PO that exists in NetSuite but not our DB
2. Run the workflow with auto-creation enabled
3. Verify the PO gets created from NetSuite

Usage:
    uv run python test_auto_creation_temporal.py
"""

import asyncio
import json
import os
import sys
import uuid
from datetime import datetime

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")

try:
    import django
    django.setup()
except ImportError:
    print("Django not imported properly")
    sys.exit(1)

from django.utils import timezone
from temporalio.client import Client

from didero.addresses.models import Address
from didero.emails.models import EmailThread
from didero.orders.models import PurchaseOrder, OrderAcknowledgement
from didero.suppliers.models import Communication, Supplier
from didero.users.models.team_models import Team
from didero.workflows.models import UserWorkflow, WorkflowBehaviorConfig
from didero.workflows.schemas import WorkflowType, WorkflowTrigger
from didero.workflows.shared_activities.purchase_order_operations import (
    team_has_erp_auto_creation_capability,
)

# Configuration
TEAM_ID = 4  # IonQ - has NetSuite configured
TEMPORAL_HOST = os.environ.get("TEMPORAL_HOST", "localhost:7233")

# Use a real PO number that exists in NetSuite
# Based on the test files, these POs exist in IonQ's NetSuite:
NETSUITE_PO_NUMBERS = [
    "PO-669382",  # From demo configs
    "PO431",      # From test files
]

print("=" * 80)
print("SMART PO AUTO-CREATION - TEMPORAL END-TO-END TEST")
print("=" * 80)

# Get team
team = Team.objects.get(id=TEAM_ID)
print(f"\nUsing team: {team.name} (ID: {team.id})")
print(f"Has ERP capability: {team_has_erp_auto_creation_capability(team.id)}")

# Test run ID
test_run_id = uuid.uuid4().hex[:8]

# Create test supplier
supplier, _ = Supplier.objects.get_or_create(
    name=f"NetSuite Test Supplier {test_run_id}",
    team=team,
    defaults={
        "website_url": f"https://netsuite-test-{test_run_id}.example.com",
        "description": "Test supplier for NetSuite integration",
    }
)

# Ensure supplier has address
if not Address.objects.filter(supplier=supplier).exists():
    Address.objects.create(
        supplier=supplier,
        team=team,
        line_1="123 Test Street",
        city="San Mateo",
        state_or_province="CA",
        postal_code="94402",
        country="US",
        is_default=True,
    )

print(f"\nCreated supplier: {supplier.name}")

# Check which PO to use - find one that doesn't exist in our DB
po_number_to_use = None
for po_num in NETSUITE_PO_NUMBERS:
    if not PurchaseOrder.objects.filter(po_number=po_num, team=team).exists():
        po_number_to_use = po_num
        print(f"\nUsing NetSuite PO that doesn't exist in our DB: {po_num}")
        break

if not po_number_to_use:
    print("\n⚠️  WARNING: All test POs already exist in database!")
    print("Using PO431 anyway for testing...")
    po_number_to_use = "PO431"
    # Delete it if it exists
    PurchaseOrder.objects.filter(po_number=po_number_to_use, team=team).delete()
    print(f"Deleted existing {po_number_to_use} from database")

# Create Order Acknowledgement email
email_thread = EmailThread.objects.create(
    team=team,
    thread_id=f"thread-temporal-{test_run_id}"
)

oa_email = Communication.objects.create(
    team=team,
    supplier=supplier,
    email_thread=email_thread,
    email_subject=f"Order Acknowledgement - {po_number_to_use}",
    email_content=f"""
Order Acknowledgement

Order Number: OA-{test_run_id}
PO Reference: {po_number_to_use}
Date: {timezone.now().strftime("%Y-%m-%d")}

Dear Customer,

We acknowledge receipt of your purchase order {po_number_to_use}.

Items confirmed as per your order.

Total Amount: As per PO

Estimated Delivery: 7-10 business days

Thank you for your order.

Best regards,
Supplier Team
""",
    direction="INBOUND",
    email_from="<EMAIL>",
    email_message_id=f"oa-temporal-{test_run_id}@example.com",
    comm_time=timezone.now(),
)

print(f"Created OA email referencing NetSuite PO: {po_number_to_use}")

# Setup workflow with auto-creation ENABLED
workflow, _ = UserWorkflow.objects.get_or_create(
    workflow_type=WorkflowType.PURCHASE_ORDER_ACKNOWLEDGEMENT.value,
    trigger=WorkflowTrigger.INBOUND_EMAIL.value,
    team=team,
    defaults={
        "current_snapshot": None,
    },
)

config_data = {
    "enabled": True,
    "require_human_validation": False,
    "max_retry_attempts": 3,
    "retry_backoff_seconds": 10,
    "enable_po_auto_creation": True,  # ENABLED for auto-creation
}

behavior_config, created = WorkflowBehaviorConfig.objects.update_or_create(
    workflow=workflow,
    defaults={"config": config_data}
)

print(f"\nWorkflow configuration:")
print(f"  - enable_po_auto_creation: {config_data['enable_po_auto_creation']}")
print(f"  - require_human_validation: {config_data['require_human_validation']}")

async def run_oa_workflow():
    """Run the Order Acknowledgement workflow"""
    print("\n" + "-" * 60)
    print("RUNNING ORDER ACKNOWLEDGEMENT WORKFLOW")
    print("-" * 60)
    
    client = await Client.connect(TEMPORAL_HOST)
    
    from didero.workflows.core_workflows.order_ack.workflow import (
        OrderAcknowledgementWorkflow,
        OrderAcknowledgementParams,
    )
    
    params = OrderAcknowledgementParams(
        email_id=str(oa_email.pk),
        team_id=str(team.id),
        workflow_id=str(workflow.id),
    )
    
    temporal_workflow_id = f"oa-auto-creation-test-{test_run_id}"
    
    print(f"Starting workflow: {temporal_workflow_id}")
    print(f"Parameters: {json.dumps(params.model_dump(), indent=2)}")
    
    try:
        handle = await client.start_workflow(
            OrderAcknowledgementWorkflow.run,
            args=[str(workflow.id), params.model_dump()],
            id=temporal_workflow_id,
            task_queue="user_workflows",
        )
        
        print("\nWorkflow started, waiting for completion...")
        print("This may take 30-60 seconds as it fetches from NetSuite...")
        
        result = await handle.result()
        
        print(f"\nWorkflow completed!")
        print(f"Result: {json.dumps(result, indent=2)}")
        
        return result
        
    except Exception as e:
        print(f"\nWorkflow failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}

# Run the workflow
result = asyncio.run(run_oa_workflow())

# Check results
print("\n" + "-" * 60)
print("CHECKING RESULTS")
print("-" * 60)

# Check if PO was created
po = PurchaseOrder.objects.filter(po_number=po_number_to_use, team=team).first()

if po:
    print(f"\n✅ SUCCESS! PO was auto-created from NetSuite:")
    print(f"  - PO Number: {po.po_number}")
    print(f"  - PO ID: {po.pk}")
    print(f"  - Source: {po.source}")
    print(f"  - Status: {po.order_status}")
    print(f"  - Supplier: {po.supplier.name if po.supplier else 'None'}")
    print(f"  - Created at: {po.created_at}")
    
    # Check if OA was created
    oas = OrderAcknowledgement.objects.filter(purchase_order=po)
    if oas.exists():
        oa = oas.first()
        print(f"\n✅ Order Acknowledgement created:")
        print(f"  - OA ID: {oa.pk}")
        print(f"  - Status: {oa.oa_status}")
    
    # Check items
    if po.items.exists():
        print(f"\n✅ PO Items created: {po.items.count()} items")
        for item in po.items.all()[:3]:  # Show first 3
            print(f"  - {item.item.item_number}: {item.item.description}")
else:
    print(f"\n❌ FAILED! PO was not created")
    print(f"Expected to find PO with number: {po_number_to_use}")

# Summary
print("\n" + "=" * 80)
print("TEST SUMMARY")
print("=" * 80)

if result.get("success") and po:
    print("✅ AUTO-CREATION FROM NETSUITE SUCCESSFUL!")
    print("\nWhat happened:")
    print("1. Created OA email referencing a PO that exists in NetSuite")
    print("2. Workflow extracted the OA and looked for the PO")
    print("3. PO not found in database, but auto-creation was enabled")
    print("4. Workflow triggered PO creation from NetSuite")
    print("5. NetSuite API returned the PO details")
    print("6. PO was created in our database")
    print("7. OA processing continued with the newly created PO")
else:
    print("❌ TEST FAILED")
    if not result.get("success"):
        print(f"Workflow error: {result.get('error', 'Unknown')}")
    if not po:
        print("PO was not created in database")

print("\n" + "=" * 80)
print("END-TO-END TEST COMPLETED")
print("=" * 80)