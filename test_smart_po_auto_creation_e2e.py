#!/usr/bin/env python
"""
End-to-end test for Smart PO Auto-Creation feature.

This script tests the complete flow:
1. Creates test Order Acknowledgement and Shipment emails
2. Triggers workflows via Temporal with auto-creation enabled/disabled
3. Verifies PO creation behavior based on configuration
4. Checks all results and side effects

Usage:
    uv run python test_smart_po_auto_creation_e2e.py
"""

import asyncio
import json
import os
import sys
import time
import uuid
from datetime import datetime

# Setup Django first before importing any Django models
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")

try:
    import django
    django.setup()
except ImportError:
    print("Django not imported properly, make sure you're running this with 'uv run python test_smart_po_auto_creation_e2e.py'")
    sys.exit(1)

# Now import Django models
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone

from didero.addresses.models import Address
from didero.emails.models import EmailThread, EmailThreadToPurchaseOrderLink
from didero.integrations.models import ERPIntegrationConfig
from didero.orders.models import PurchaseOrder, OrderAcknowledgement
from didero.suppliers.models import Communication, Supplier
from didero.tasks.models import Task
from didero.users.models.team_models import Team
from didero.workflows.models import UserWorkflow, WorkflowBehaviorConfig
from didero.workflows.schemas import WorkflowType, WorkflowTrigger
from didero.workflows.shared_activities.purchase_order_operations import (
    team_has_erp_auto_creation_capability,
    get_po_auto_creation_eligibility
)

# Import Temporal client
from temporalio.client import Client

# Configuration
TEAM_WITH_ERP_ID = 4  # IonQ - has ERP configured
TEAM_WITHOUT_ERP_ID = 999  # Test team without ERP
TEMPORAL_HOST = os.environ.get("TEMPORAL_HOST", "localhost:7233")

print("=" * 80)
print("SMART PO AUTO-CREATION - END-TO-END TEST")
print("=" * 80)

# Test run ID for unique identifiers
test_run_id = uuid.uuid4().hex[:8]

# Get teams
team_with_erp = Team.objects.get(id=TEAM_WITH_ERP_ID)
print(f"\nTeam WITH ERP: {team_with_erp.name} (ID: {team_with_erp.id})")

# Check ERP capability
capability = team_has_erp_auto_creation_capability(TEAM_WITH_ERP_ID)
eligibility = get_po_auto_creation_eligibility(TEAM_WITH_ERP_ID)
print(f"  - Has ERP capability: {capability}")
print(f"  - ERP type: {eligibility.get('erp_type', 'None')}")
print(f"  - ERP enabled: {eligibility.get('erp_enabled', False)}")

# Use a team that exists but doesn't have ERP configured
# Let's use team 2 (Didero) which exists but we'll ensure it doesn't have ERP for this test
team_without_erp = Team.objects.get(id=2)  # Didero team

# Temporarily disable any ERP config for this team
from didero.integrations.models import ERPIntegrationConfig
ERPIntegrationConfig.objects.filter(team_id=2).update(enabled=False)

print(f"\nTeam WITHOUT ERP: {team_without_erp.name} (ID: {team_without_erp.id})")
capability_no_erp = team_has_erp_auto_creation_capability(team_without_erp.id)
print(f"  - Has ERP capability: {capability_no_erp}")

# Cleanup previous test data
print("\n" + "-" * 60)
print("CLEANUP")
print("-" * 60)
deleted_pos, _ = PurchaseOrder.objects.filter(
    po_number__startswith="E2E-TEST-",
).delete()
print(f"Deleted {deleted_pos} previous test POs")

# Create suppliers for both teams
def create_test_supplier(team, suffix):
    """Create a test supplier for a team"""
    supplier, created = Supplier.objects.get_or_create(
        name=f"Test Supplier {suffix}",
        team=team,
        defaults={
            "website_url": f"https://testsupplier-{suffix}.example.com",
            "description": "Test supplier for E2E testing",
        }
    )
    
    # Ensure supplier has an address
    if not Address.objects.filter(supplier=supplier).exists():
        Address.objects.create(
            supplier=supplier,
            team=team,
            line_1="123 Test Street",
            city="New York",
            state_or_province="NY",
            postal_code="10001",
            country="US",
            is_default=True,
        )
    
    return supplier

supplier_with_erp = create_test_supplier(team_with_erp, f"ERP-{test_run_id}")
supplier_without_erp = create_test_supplier(team_without_erp, f"NoERP-{test_run_id}")

def create_oa_email(team, supplier, po_number):
    """Create an Order Acknowledgement email"""
    email_body = f"""
Order Acknowledgement
Order Number: OA-{uuid.uuid4().hex[:8]}
PO Reference: {po_number}
Date: {timezone.now().strftime("%Y-%m-%d")}

We acknowledge receipt of your purchase order.

Items:
1. TEST-ITEM-001 - Test Product A - Qty: 5 - Unit Price: $100.00
2. TEST-ITEM-002 - Test Product B - Qty: 3 - Unit Price: $200.00

Total: $1,100.00
Estimated Delivery: 7-10 business days
"""

    email_thread = EmailThread.objects.create(
        team=team,
        thread_id=f"thread-oa-{uuid.uuid4()}"
    )
    
    email = Communication.objects.create(
        team=team,
        supplier=supplier,
        email_thread=email_thread,
        email_subject=f"Order Acknowledgement - PO {po_number}",
        email_content=email_body,
        direction="INBOUND",
        email_from="<EMAIL>",
        email_message_id=f"oa-{uuid.uuid4()}@example.com",
        comm_time=timezone.now(),
    )
    
    return email

def create_shipment_email(team, supplier, po_number):
    """Create a Shipment notification email"""
    email_body = f"""
Shipment Notification

Your order has been shipped!

PO Number: {po_number}
Tracking Number: TRACK-{uuid.uuid4().hex[:8]}
Carrier: FedEx Ground
Ship Date: {timezone.now().strftime("%Y-%m-%d")}

Items Shipped:
- TEST-ITEM-001: Test Product A (Qty: 5)
- TEST-ITEM-002: Test Product B (Qty: 2)

Estimated Delivery: 3-5 business days
"""

    email_thread = EmailThread.objects.create(
        team=team,
        thread_id=f"thread-ship-{uuid.uuid4()}"
    )
    
    email = Communication.objects.create(
        team=team,
        supplier=supplier,
        email_thread=email_thread,
        email_subject=f"Shipment Notification - PO {po_number}",
        email_content=email_body,
        direction="INBOUND",
        email_from="<EMAIL>",
        email_message_id=f"ship-{uuid.uuid4()}@example.com",
        comm_time=timezone.now(),
    )
    
    return email

def setup_workflow(team, workflow_type, enable_auto_creation=False):
    """Setup workflow with configuration"""
    workflow, _ = UserWorkflow.objects.get_or_create(
        workflow_type=workflow_type,
        trigger=WorkflowTrigger.INBOUND_EMAIL.value,
        team=team,
        defaults={
            "current_snapshot": None,
        },
    )
    
    config_data = {
        "enabled": True,
        "require_human_validation": False,
        "max_retry_attempts": 3,
        "retry_backoff_seconds": 10,
        "enable_po_auto_creation": enable_auto_creation,
    }
    
    behavior_config, created = WorkflowBehaviorConfig.objects.update_or_create(
        workflow=workflow,
        defaults={"config": config_data}
    )
    
    if not created:
        # Update existing config
        behavior_config.config.update(config_data)
        behavior_config.save()
    
    return workflow

async def run_workflow_test(workflow_class, workflow_id, params, expected_success, test_name):
    """Run a workflow and check results"""
    print(f"\n{'='*60}")
    print(f"TEST: {test_name}")
    print(f"{'='*60}")
    
    client = await Client.connect(TEMPORAL_HOST)
    
    temporal_workflow_id = f"e2e-test-{workflow_id}-{test_run_id}"
    
    print(f"Starting workflow: {temporal_workflow_id}")
    print(f"Parameters: {json.dumps(params, indent=2)}")
    
    try:
        handle = await client.start_workflow(
            workflow_class.run,
            args=[str(params['workflow_id']), params],
            id=temporal_workflow_id,
            task_queue="user_workflows",
        )
        
        print("Waiting for workflow to complete...")
        result = await handle.result()
        
        print(f"\nWorkflow completed!")
        print(f"Result: {json.dumps(result, indent=2)}")
        
        if expected_success:
            assert result.get('success'), f"Expected success but got: {result}"
            print("✓ Workflow succeeded as expected")
        else:
            assert not result.get('success'), f"Expected failure but got success: {result}"
            print("✓ Workflow failed as expected")
            
        return result
        
    except Exception as e:
        if expected_success:
            print(f"✗ Unexpected error: {str(e)}")
            raise
        else:
            print(f"✓ Expected failure: {str(e)}")
            return {"success": False, "error": str(e)}

async def main():
    """Run all test scenarios"""
    
    # Test scenarios
    test_scenarios = [
        {
            "name": "OA - Team WITH ERP, auto-creation ENABLED, PO missing",
            "team": team_with_erp,
            "supplier": supplier_with_erp,
            "workflow_type": WorkflowType.ORDER_ACKNOWLEDGEMENT_CORE.value,
            "workflow_class": "OrderAcknowledgementWorkflow",
            "create_email": create_oa_email,
            "po_number": f"E2E-TEST-OA-ERP-{test_run_id}",
            "enable_auto_creation": True,
            "expected_success": True,
            "expect_po_created": True,
        },
        {
            "name": "OA - Team WITH ERP, auto-creation DISABLED, PO missing",
            "team": team_with_erp,
            "supplier": supplier_with_erp,
            "workflow_type": WorkflowType.ORDER_ACKNOWLEDGEMENT_CORE.value,
            "workflow_class": "OrderAcknowledgementWorkflow",
            "create_email": create_oa_email,
            "po_number": f"E2E-TEST-OA-NO-AUTO-{test_run_id}",
            "enable_auto_creation": False,
            "expected_success": False,
            "expect_po_created": False,
        },
        {
            "name": "Shipment - Team WITH ERP, auto-creation ENABLED, PO missing",
            "team": team_with_erp,
            "supplier": supplier_with_erp,
            "workflow_type": WorkflowType.SHIPMENTS_CORE.value,
            "workflow_class": "ShipmentWorkflow",
            "create_email": create_shipment_email,
            "po_number": f"E2E-TEST-SHIP-ERP-{test_run_id}",
            "enable_auto_creation": True,
            "expected_success": True,
            "expect_po_created": True,
        },
        {
            "name": "OA - Team WITHOUT ERP, auto-creation ENABLED, PO missing",
            "team": team_without_erp,
            "supplier": supplier_without_erp,
            "workflow_type": WorkflowType.ORDER_ACKNOWLEDGEMENT_CORE.value,
            "workflow_class": "OrderAcknowledgementWorkflow",
            "create_email": create_oa_email,
            "po_number": f"E2E-TEST-OA-NOERP-{test_run_id}",
            "enable_auto_creation": True,
            "expected_success": False,
            "expect_po_created": False,
        },
    ]
    
    # Import workflow classes
    from didero.workflows.core_workflows.order_ack.workflow import OrderAcknowledgementWorkflow
    from didero.workflows.core_workflows.shipments.workflow import ShipmentWorkflow
    
    workflow_classes = {
        "OrderAcknowledgementWorkflow": OrderAcknowledgementWorkflow,
        "ShipmentWorkflow": ShipmentWorkflow,
    }
    
    results = []
    
    for scenario in test_scenarios:
        # Setup
        email = scenario["create_email"](
            scenario["team"],
            scenario["supplier"],
            scenario["po_number"]
        )
        
        workflow = setup_workflow(
            scenario["team"],
            scenario["workflow_type"],
            scenario["enable_auto_creation"]
        )
        
        params = {
            "email_id": str(email.pk),
            "team_id": str(scenario["team"].id),
            "workflow_id": str(workflow.id),
        }
        
        # Run test
        result = await run_workflow_test(
            workflow_classes[scenario["workflow_class"]],
            workflow.id,
            params,
            scenario["expected_success"],
            scenario["name"]
        )
        
        # Check if PO was created
        po_exists = PurchaseOrder.objects.filter(
            po_number=scenario["po_number"],
            team=scenario["team"]
        ).exists()
        
        if scenario["expect_po_created"]:
            assert po_exists, f"Expected PO to be created but it wasn't"
            print(f"✓ PO was created as expected: {scenario['po_number']}")
            
            # Check if it was created from ERP
            po = PurchaseOrder.objects.get(po_number=scenario["po_number"])
            print(f"  - PO source: {po.source}")
            print(f"  - PO status: {po.order_status}")
        else:
            assert not po_exists, f"Expected NO PO but found one"
            print(f"✓ PO was NOT created as expected")
        
        results.append({
            "scenario": scenario["name"],
            "success": result.get("success", False) == scenario["expected_success"],
            "po_created": po_exists == scenario["expect_po_created"],
        })
    
    return results

# Run the tests
print("\n" + "=" * 80)
print("RUNNING END-TO-END TESTS")
print("=" * 80)

results = asyncio.run(main())

# Summary
print("\n" + "=" * 80)
print("TEST SUMMARY")
print("=" * 80)

all_passed = True
for result in results:
    status = "✓ PASS" if result["success"] and result["po_created"] else "✗ FAIL"
    print(f"{status}: {result['scenario']}")
    if not (result["success"] and result["po_created"]):
        all_passed = False

print("\n" + "-" * 60)
if all_passed:
    print("ALL TESTS PASSED! 🎉")
    print("\nThe Smart PO Auto-Creation feature is working correctly:")
    print("- Permission (WorkflowBehaviorConfig) controls whether to attempt auto-creation")
    print("- Capability (ERPIntegrationConfig) determines if auto-creation is possible")
    print("- Both Order Ack and Shipment workflows support auto-creation")
    print("- The unified extraction activity handles all document types")
else:
    print("SOME TESTS FAILED! ❌")

print("\n" + "=" * 80)
print("END-TO-END TEST COMPLETED")
print("=" * 80)