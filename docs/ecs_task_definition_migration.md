# ECS Task Definition Migration Guide

## Overview

We're moving from dynamically modifying ECS task definitions in deployment scripts to maintaining them as code in the repository. This change provides better visibility, version control, and reviewability for infrastructure changes.

## What's Changing

### Before
- Deployment script fetches current task definition from AWS
- Uses `jq` to modify container images in-place
- Registers the modified definition back to AWS

### After
- Task definition template stored in `ecs/task-definitions/didero-app-template.json`
- Generation script creates environment-specific definitions
- Deployment script uses the generated definition

## Migration Steps

### 1. Testing the New Script (Optional)

You can test the new deployment script alongside the existing one:

```bash
# Deployment using task definition from code
./scripts/deploy/deploy_generic_hash_to_prod.sh <git-hash>
```

### 2. Switching to the New Process

Once comfortable with the new approach:

1. Use `deploy_generic_hash_to_prod_v2.sh` for deployments
2. The script will:
   - Generate task definition from template
   - Register it with AWS
   - Update the ECS service

### 3. Making Changes to Task Definitions

To modify the task definition:

1. Edit `ecs/task-definitions/didero-app-template.json`
2. Test your changes:
   ```bash
   ./scripts/deploy/generate-task-definition.sh staging test123 test.json
   jq . test.json  # Validate JSON
   ```
3. Commit and create a PR
4. Changes are now reviewable in version control!

## Benefits

1. **Visibility**: Task definition changes appear in PRs
2. **Version Control**: Full history of infrastructure changes
3. **Consistency**: Same definition structure across environments
4. **Safety**: Can review changes before deployment
5. **Future-Ready**: Foundation for Terraform/CDK migration

## Changes from Current Production

The template includes:
- **New**: 4 Temporal workers that aren't in production yet:
  - `temporal-worker-po`: Purchase order workflows
  - `temporal-worker-oa`: Order acknowledgment workflows
  - `temporal-worker-shipment`: Shipment workflows
  - `temporal-worker-general`: General workflows
- **Removed**: Metabase container (not being used)

These changes will be applied automatically when using the new script.

## Rollback

If issues arise, the original deployment script remains available:
```bash
./scripts/deploy/deploy_generic_hash_to_prod.sh <git-hash>
```

## Questions?

- The template is in `ecs/task-definitions/didero-app-template.json`
- Full documentation in `ecs/README.md`
- Generation script: `scripts/deploy/generate-task-definition.sh`