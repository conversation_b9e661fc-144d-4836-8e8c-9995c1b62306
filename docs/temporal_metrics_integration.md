# Temporal SDK Metrics Integration with OpenTelemetry

This document describes how Temporal SDK metrics are integrated with our OpenTelemetry observability stack.

## Overview

We've configured Temporal SDK to export metrics directly to our OpenTelemetry collector using the OTLP protocol. This means all Temporal metrics flow through the same pipeline as our application metrics and end up in AWS CloudWatch.

## Architecture

```
Temporal SDK → OTLP → OpenTelemetry Collector → AWS CloudWatch
```

## Configuration

### Temporal Runtime Setup

The metrics-enabled runtime is configured in `didero/telemetry/temporal_metrics.py`:

```python
from didero.telemetry.temporal_metrics import get_temporal_runtime

# This creates a runtime that sends metrics to the OTLP endpoint
runtime = get_temporal_runtime()
```

The runtime automatically:
- Uses the `OTEL_EXPORTER_OTLP_ENDPOINT` environment variable (default: `http://localhost:4317`)
- Exports metrics using the OpenTelemetry protocol
- Integrates with our existing telemetry setup

### Usage

The metrics runtime is automatically used in:
- **Temporal Worker**: `temporal_worker.py` management command
- **Workflow Execution**: All workflow starts via `get_temporal_client()`

## Available Metrics

### Temporal SDK Metrics (Automatic)

These metrics are automatically collected by Temporal SDK:

#### Workflow Metrics
- `temporal_workflow_completed` - Successfully completed workflows
- `temporal_workflow_failed` - Failed workflows
- `temporal_workflow_cancelled` - Cancelled workflows
- `temporal_workflow_task_execution_latency` - Time to execute workflow tasks
- `temporal_workflow_task_schedule_to_start_latency` - Queue wait time

#### Activity Metrics
- `temporal_activity_execution_latency` - Activity execution time
- `temporal_activity_task_scheduled` - Number of activities scheduled
- `temporal_activity_task_completed` - Successfully completed activities
- `temporal_activity_task_failed` - Failed activities
- `temporal_activity_retry` - Activity retry attempts

#### Worker Metrics
- `temporal_worker_start` - Worker startup events
- `temporal_worker_task_slots_available` - Available worker capacity
- `temporal_sticky_cache_hit` - Workflow cache hit rate
- `temporal_sticky_cache_miss` - Workflow cache miss rate
- `temporal_sticky_cache_eviction` - Cache evictions

### Custom Workflow Metrics

In addition to SDK metrics, workflows can emit custom metrics:

```python
@workflow.defn
class POCreationWorkflow:
    def _init_metrics(self):
        meter = workflow.metric_meter()
        self.po_created_counter = meter.create_counter(
            "po_created_total",
            description="Total number of POs created",
            unit="1"
        )
```

## Viewing Metrics

### AWS CloudWatch

Temporal metrics are separated into their own CloudWatch namespace for better organization:

- **Temporal Metrics**: `Didero/Temporal` namespace
- **Application Metrics**: `Didero/Dev` namespace (or your configured namespace)

This separation makes it easy to:

1. Create CloudWatch dashboards
2. Set up CloudWatch alarms
3. Query metrics using CloudWatch Insights

### Example CloudWatch Queries

**Workflow Success Rate**:
```
SELECT AVG(temporal_workflow_completed) / 
       (AVG(temporal_workflow_completed) + AVG(temporal_workflow_failed)) * 100
FROM "Didero/Temporal"
WHERE WorkflowType = 'POCreationWorkflow'
```

**P99 Activity Latency**:
```
SELECT PERCENTILE(temporal_activity_execution_latency, 99)
FROM "Didero/Temporal"
WHERE ActivityType = 'extract_po_details'
```

**All Temporal Metrics**:
```
SELECT *
FROM "Didero/Temporal"
```

## Troubleshooting

### Metrics Not Appearing

1. **Check OTLP Endpoint**: Ensure `OTEL_EXPORTER_OTLP_ENDPOINT` is set correctly
2. **Verify Collector**: Check that the OpenTelemetry collector is running and accessible
3. **Review Logs**: Look for "Temporal runtime configured with OpenTelemetry metrics" in logs

### Common Issues

1. **Connection Refused**: The OTLP endpoint is not reachable
   - Solution: Verify the collector is running and the endpoint is correct

2. **No Metrics in CloudWatch**: Metrics are being sent but not appearing
   - Solution: Check the ADOT collector configuration and CloudWatch permissions

3. **High Cardinality**: Too many unique metric label combinations
   - Solution: Review custom metrics and reduce label cardinality

## Best Practices

1. **Use Consistent Labels**: Keep metric labels consistent with existing patterns
2. **Avoid High Cardinality**: Don't use unbounded values (like user IDs) as labels
3. **Meaningful Names**: Use descriptive metric names that indicate what's being measured
4. **Units**: Always specify units in metric descriptions
5. **Reuse Meters**: Use `workflow.metric_meter()` to get the workflow's meter

## Environment Variables

- `OTEL_EXPORTER_OTLP_ENDPOINT`: OTLP endpoint URL (default: `http://localhost:4317`)
- `TEMPORAL_METRICS_BIND_ADDRESS`: (Not used with OTLP, only for Prometheus mode)

## Future Enhancements

1. **Traces**: Add OpenTelemetry tracing support with `TracingInterceptor`
2. **Custom Dashboards**: Pre-built CloudWatch dashboards for Temporal metrics
3. **Alerting**: Standard alerts for workflow failures and high latencies
4. **Metric Aggregation**: Business-level metrics aggregated from workflow metrics