# PO Creation Workflow Migration Guide

This guide explains how to migrate existing DAG-based PO Creation workflows to the new core workflow implementation.

## Overview

The migration process transitions workflows from:
- **DAG-based**: Generic workflow executor using graph definitions
- **Core workflow**: Type-safe, dedicated workflow implementation

## Key Differences

### DAG-based Workflows
- Uses `WorkflowSnapshot` with JSON graph definition
- Executed by `DagBFSWorkflow`
- Configuration scattered in team settings
- String-based node resolution

### Core Workflows
- No `WorkflowSnapshot` needed (current_snapshot = None)
- Executed by `POCreationWorkflow` class
- Configuration in `WorkflowBehaviorConfig`
- Type-safe, direct execution

## Migration Process

### 1. Check Current Status

```bash
# Run the test script to check workflow status
python test_po_workflow_migration.py <team_id>
```

This will show:
- Whether the workflow exists
- Current implementation type (DAG or Core)
- Configuration details

### 2. Run Migration

```bash
# Dry run first (analyze without changes)
python manage.py migrate_po_workflow_to_core --team-id <team_id> --dry-run

# Actual migration
python manage.py migrate_po_workflow_to_core --team-id <team_id>

# With human validation override
python manage.py migrate_po_workflow_to_core --team-id <team_id> --require-human-validation true
```

### 3. What Migration Does

1. **Clears DAG Snapshot**: Sets `current_snapshot = None`
2. **Creates Behavior Config**: Extracts settings and creates `WorkflowBehaviorConfig`
3. **Preserves Settings**: Maintains existing team settings by default

### 4. Configuration Mapping

The migration maps settings as follows:

| Team Setting | Behavior Config Field |
|--------------|----------------------|
| `TEAM_PO_CREATION_HUMAN_VALIDATION_ENABLED` | `require_human_validation` |
| `TEAM_TASK_EMAIL_NOTIFICATIONS_ENABLED` | `enable_notifications` |
| (Default) | `validate_supplier = True` |
| (Default) | `validate_duplicate_po = True` |

## Verification

After migration, verify:

1. **Workflow has no snapshot**: `workflow.current_snapshot is None`
2. **Has behavior config**: `workflow.behavior_config` exists
3. **Uses core workflow**: `workflow.uses_core_workflow == True`
4. **Config is valid**: `workflow.behavior_config.get_config_as_pydantic()` works

## Rollback

If needed, you can rollback by:
1. Restoring the workflow snapshot reference
2. Deleting the behavior config
3. Using the original DAG-based execution

## Example Migration Session

```bash
# 1. Check status
$ python test_po_workflow_migration.py abc-123-def

CURRENT STATUS
==================================================
Checking PO Creation workflow for team: Acme Corp

Workflow found:
  - ID: 789-xyz
  - Type: PURCHASE_ORDER_CREATION
  - Trigger: ON_PURCHASE_ORDER_EMAIL_RECEIVED
  - Implementation: DAG-based
  - Snapshot ID: 456-uvw
  - Number of nodes: 4
  - Node types:
    • ExtractPoDetails: Extract PO Details
    • CreatePo: Create Purchase Order
    • LinkEmail: Link Email to PO
    • CreateTaskActivity: Human Validation

Do you want to migrate this workflow? (y/n): y

# 2. Dry run
DRY RUN MIGRATION
==================================================
Analyzing DAG workflow for team: Acme Corp
  - Snapshot ID: 456-uvw
  - Nodes in DAG: 4
  - Using existing human validation setting: True

Extracted configuration:
  - enabled: True
  - enable_notifications: True
  - validate_supplier: True
  - validate_duplicate_po: True
  - notification_channels: ['email']
  - require_human_validation: True

DRY RUN - No changes will be made

Proceed with actual migration? (y/n): y

# 3. Actual migration
ACTUAL MIGRATION
==================================================
...
✓ Successfully migrated workflow to core implementation
  - Behavior config created
  - Workflow ID: 789-xyz

# 4. Verify
POST-MIGRATION STATUS
==================================================
Workflow found:
  - ID: 789-xyz
  - Implementation: Core workflow
  - Has behavior config: Yes
  - Config details:
    • Enabled: True
    • Require human validation: True
    • Validate supplier: True
    • Validate duplicate PO: True
    • Enable notifications: True
  - Uses core workflow: True
  - Core workflow class: POCreationWorkflow
```

## Troubleshooting

### Workflow Not Found
- Ensure the team has a PO Creation workflow
- Check the team ID is correct

### Migration Fails
- Check Django logs for detailed errors
- Ensure database permissions are correct
- Verify no active workflow runs

### Config Issues
- Review extracted configuration
- Use `--require-human-validation` to override if needed
- Check team settings are properly set