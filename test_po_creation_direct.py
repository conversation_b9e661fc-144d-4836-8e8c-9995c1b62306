#!/usr/bin/env python
"""
Direct test of POCreationWorkflow with po_number parameter
"""

import asyncio
import os
import sys

# Setup Django first
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")

try:
    import django

    django.setup()
except ImportError:
    print("Django not imported properly")
    sys.exit(1)

from didero.workflows.core_workflows.po_creation.workflow import (
    POCreationParams,
    POCreationWorkflow,
)
from didero.workflows.queue_config import get_queue_for_workflow_type
from didero.workflows.schemas import WorkflowType
from didero.workflows.utils import get_temporal_client


async def test_po_creation_direct():
    print("=== DIRECT PO CREATION WORKFLOW TEST ===")

    # Create POCreationParams with po_number
    workflow_params = POCreationParams(
        team_id="4",  # IonQ team
        workflow_id="",  # Empty for default config
        po_number="PO999",  # Direct PO number
        email_id=None,  # No email
        document_id=None,  # No document
    )

    print(f"✅ Created POCreationParams:")
    print(f"   - team_id: {workflow_params.team_id}")
    print(f"   - workflow_id: '{workflow_params.workflow_id}'")
    print(f"   - po_number: {workflow_params.po_number}")
    print(f"   - email_id: {workflow_params.email_id}")
    print(f"   - document_id: {workflow_params.document_id}")

    # Connect to Temporal
    client = await get_temporal_client()
    task_queue = get_queue_for_workflow_type(WorkflowType.PURCHASE_ORDER_CREATION)

    print(f"✅ Connected to Temporal, task_queue: {task_queue}")

    # Generate workflow ID
    workflow_id = f"test-direct-po-creation-{asyncio.get_event_loop().time()}"

    try:
        print(f"🚀 Starting POCreationWorkflow with ID: {workflow_id}")

        # Start the workflow
        handle = await client.start_workflow(
            POCreationWorkflow.run,
            args=("", workflow_params),  # Empty workflow_id as first arg
            id=workflow_id,
            task_queue=task_queue,
        )

        print(f"✅ Workflow started successfully: {handle.id}")
        print("⏳ Waiting for result...")

        # Wait for result with timeout
        result = await asyncio.wait_for(handle.result(), timeout=120)

        print(f"🎯 SUCCESS: Workflow completed!")
        print(f"   Result: {result}")

    except Exception as e:
        print(f"❌ FAILURE: Workflow failed: {str(e)}")
        print(f"   Error type: {type(e).__name__}")

        # Try to get more details
        try:
            import traceback

            print(f"   Traceback: {traceback.format_exc()}")
        except:
            pass


if __name__ == "__main__":
    asyncio.run(test_po_creation_direct())
