#!/usr/bin/env python3
"""
Debug script to analyze NetSuite PO XML structure
Saves raw XML and analyzes address patterns
"""

import re
import json
import xml.dom.minidom
from datetime import datetime
from ionq_api_extraction_enhanced import EnhancedPOExtractor


def save_raw_xml(xml_text, po_number):
    """Save raw XML for analysis"""
    # Save raw XML
    with open(f"DEBUG_PO_{po_number}_raw.xml", "w", encoding="utf-8") as f:
        f.write(xml_text)

    # Save prettified XML
    try:
        dom = xml.dom.minidom.parseString(xml_text)
        pretty_xml = dom.toprettyxml(indent="  ")
        with open(f"DEBUG_PO_{po_number}_pretty.xml", "w", encoding="utf-8") as f:
            f.write(pretty_xml)
        print(f"✅ Saved raw and prettified XML for {po_number}")
    except Exception as e:
        print(f"❌ Error prettifying XML: {e}")


def analyze_address_patterns(xml_text):
    """Analyze all address-related patterns in the XML"""
    print("\n🔍 ANALYZING ADDRESS PATTERNS IN XML...")

    # Search for any ship-related tags
    ship_patterns = [
        r"<[^>]*ship[^>]*>([^<]+)</[^>]*>",
        r"<[^>]*Ship[^>]*>([^<]+)</[^>]*>",
        r"ship[^<]*",
        r"address[^<]*",
        r"Address[^<]*",
    ]

    print("\n📍 Searching for shipping/address patterns:")
    for pattern in ship_patterns:
        matches = re.findall(pattern, xml_text, re.IGNORECASE | re.DOTALL)
        if matches:
            print(f"  Pattern '{pattern}' found {len(matches)} matches")
            for i, match in enumerate(matches[:3]):  # Show first 3
                if isinstance(match, str) and len(match.strip()) > 0:
                    preview = match[:100] + "..." if len(match) > 100 else match
                    print(f"    [{i+1}] {preview}")

    # Look for specific NetSuite address structures
    print("\n📍 Looking for NetSuite address structures:")

    # Check for shipAddress
    if "<tranPurch:shipAddress>" in xml_text:
        print("  ✅ Found <tranPurch:shipAddress>")
        ship_match = re.search(
            r"<tranPurch:shipAddress>(.*?)</tranPurch:shipAddress>", xml_text, re.DOTALL
        )
        if ship_match:
            print(f"    Content length: {len(ship_match.group(1))} chars")
    else:
        print("  ❌ No <tranPurch:shipAddress> found")

    # Check for billAddress
    if "<tranPurch:billAddress>" in xml_text:
        print("  ✅ Found <tranPurch:billAddress>")
        bill_match = re.search(
            r"<tranPurch:billAddress>(.*?)</tranPurch:billAddress>", xml_text, re.DOTALL
        )
        if bill_match:
            print(f"    Content length: {len(bill_match.group(1))} chars")
    else:
        print("  ❌ No <tranPurch:billAddress> found")

    # Check for shippingAddress (different tag)
    if "<tranPurch:shippingAddress>" in xml_text:
        print("  ✅ Found <tranPurch:shippingAddress>")
    else:
        print("  ❌ No <tranPurch:shippingAddress> found")

    # Check for location
    location_match = re.search(
        r"<tranPurch:location[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>",
        xml_text,
        re.DOTALL,
    )
    if location_match:
        print(f"  ✅ Found location: {location_match.group(1)}")

    # Check for subsidiary
    subsidiary_match = re.search(
        r"<tranPurch:subsidiary[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>",
        xml_text,
        re.DOTALL,
    )
    if subsidiary_match:
        print(f"  ✅ Found subsidiary: {subsidiary_match.group(1)}")


def extract_all_references(xml_text):
    """Extract all reference fields to understand data structure"""
    print("\n🔍 EXTRACTING ALL REFERENCE FIELDS...")

    # Find all RecordRef patterns
    ref_pattern = r'<([^:]+:[^>]+)\s+[^>]*internalId="([^"]*)"[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>'
    refs = re.findall(ref_pattern, xml_text, re.DOTALL)

    print(f"\n📊 Found {len(refs)} reference fields:")
    for tag, internal_id, name in refs:
        print(f"  • {tag}: {name} (ID: {internal_id})")


def main():
    # Credentials (same as before)
    credentials = {
        "account_id": "7581852_SB1",
        "consumer_key": "****************************************************************",
        "consumer_secret": "****************************************************************",
        "token_id": "5ec4bd683099249601f69808f2455a804e70b480c0801ea987a14b72f793e7c8",
        "token_secret": "87e6863c2192bf8c53f410e0271bc6ed245d94651e66ed1fe8f9a40dbf6c7272",
    }

    # Target PO
    target_po = {"po_number": "PO431", "internal_id": "18816"}

    print("🔍 DEBUG MODE: Analyzing NetSuite PO XML Structure")
    print("=" * 80)

    # Create extractor
    extractor = EnhancedPOExtractor(**credentials)

    # Get raw XML
    xml_text = extractor.get_po_xml(target_po["internal_id"])
    if not xml_text:
        print("❌ Failed to retrieve XML")
        return

    # Save raw XML for analysis
    save_raw_xml(xml_text, target_po["po_number"])

    # Analyze address patterns
    analyze_address_patterns(xml_text)

    # Extract all references
    extract_all_references(xml_text)

    # Run enhanced extraction
    print("\n" + "=" * 80)
    print("🚀 Running enhanced extraction...")
    result = extractor.extract_all_fields_enhanced(
        xml_text, target_po["po_number"], target_po["internal_id"]
    )

    # Analyze why addresses are null
    print("\n🔍 DEBUGGING ADDRESS EXTRACTION:")
    print(f"  ship_to raw: {result['address_data']['ship_to']}")
    print(f"  shipping_address: {result['address_data']['shipping_address']}")
    print(f"  billing_address: {result['address_data']['billing_address']}")
    print(
        f"  vendor_address: {json.dumps(result['address_data']['vendor_address'], indent=2)}"
    )

    # Check if we need different patterns
    print("\n💡 SUGGESTIONS:")
    if not result["address_data"]["ship_to"]:
        print("  • ship_to field might be stored differently in NetSuite")
        print("  • Check if address is in location or custom fields")
        print("  • May need to extract from line items or other fields")


if __name__ == "__main__":
    main()
