# Analysis of Three ERP PO Creation Plans

## Key Differences Analysis

After analyzing all three plans, here are the critical differences and my recommendations:

### 1. **Architecture Approach**
- **plan_cline.md**: Clean separation with new mapper module, reuses existing activities
- **plan_gemini.md**: Customer-specific integration directory, generic ERP client wrapper
- **plan.md**: Service-oriented approach with comprehensive field mapping experiments

**Recommendation**: Combine plan_cline's clean separation with plan.md's comprehensive mapping approach.

### 2. **Data Flow Strategy**
- **plan_cline.md**: Direct mapping to `PurchaseOrderDetails`, reuse `create_po_from_extracted_data()`
- **plan_gemini.md**: Custom PO creation logic in customer-specific modules
- **plan.md**: Service orchestration with existing supplier/item matching

**Recommendation**: Use plan_cline's approach - it maximizes code reuse and maintains consistency.

### 3. **Workflow Integration**
- **plan_cline.md**: New activity called conditionally based on ERP config
- **plan_gemini.md**: Workflow configuration flag with conditional logic
- **plan.md**: Service-based integration with fallback mechanisms

**Recommendation**: Combine plan_cline's activity approach with plan.md's robust error handling.

---

# Unified ERP PO Creation Integration Plan

## Overview

This plan implements a third path in the PO creation workflow: **ERP-based PO creation**. When a team has ERP configuration enabled, the system will fetch existing PO data from their ERP system (initially NetSuite for IonQ) and recreate it in Didero using existing infrastructure.

## Architecture & Data Flow

```mermaid
graph TB
    EMAIL[Email with PO Number] --> WORKFLOW[PO Creation Workflow]
    WORKFLOW --> CHECK{Check Team ERP Config}
    
    CHECK -->|No ERP Config| EXISTING[Existing Logic<br/>AI Direct or Stagehand]
    CHECK -->|ERP Config Enabled| ERP_PATH[ERP Integration Path]
    
    ERP_PATH --> EXTRACT_PO[Extract PO Number]
    EXTRACT_PO --> CREATE_ACTIVITY[create_po_from_erp Activity]
    
    CREATE_ACTIVITY --> GET_CREDS[Get ERP Credentials<br/>from existing system]
    GET_CREDS --> CREATE_CLIENT[Create NetSuite Client<br/>using didero/integrations]
    CREATE_CLIENT --> FETCH_DATA[Fetch Complete PO Data<br/>from NetSuite SOAP API]
    FETCH_DATA --> MAP_DATA[Map NetSuite Data to<br/>PurchaseOrderDetails]
    MAP_DATA --> CREATE_PO[Call existing<br/>create_po_from_extracted_data]
    
    CREATE_PO --> SUCCESS[PO Created in Didero]
    
    subgraph "Error Handling"
        CREATE_ACTIVITY -.->|On Failure| FALLBACK[Fallback to AI Extraction]
        CREATE_ACTIVITY -.->|Critical Error| TASK[Create Manual Task]
    end
```

## Implementation Components

### Phase 1: Core ERP Infrastructure (Days 1-3)

#### 1.1 Enhanced NetSuite Client

**File: `didero/integrations/erp/clients/netsuite.py`**

Add new methods to existing NetSuiteClient class:

```python
def fetch_purchase_order_data(self, po_number: str) -> Dict[str, Any]:
    """
    Fetch complete PO data from NetSuite using PO number.
    Uses existing SOAP infrastructure and expands on get_purchase_order().
    
    Returns raw NetSuite PO data similar to ionq_api_extraction_enhanced_fixed.py output.
    """
    # 1. Search for PO by number using existing search logic
    search_result = self._search_purchase_order(po_number)
    if not search_result:
        raise ValueError(f"PO {po_number} not found in NetSuite")
    
    # 2. Get complete PO data using internal ID
    internal_id = search_result['internalId']
    po_xml = self._get_purchase_order_xml(internal_id)
    
    # 3. Extract all fields using patterns from ionq_api_extraction_enhanced_fixed.py
    return self._extract_complete_po_data(po_xml)

def extract_po_for_didero(self, po_number: str) -> 'PurchaseOrderDetails':
    """
    High-level method that fetches PO data and maps it to Didero structure.
    Main entry point for workflow activity.
    """
    raw_data = self.fetch_purchase_order_data(po_number)
    mapper = NetSuitePOMapper()
    return mapper.map_to_didero_format(raw_data)
```

#### 1.2 Data Mapping Module

**File: `didero/integrations/erp/mappers/__init__.py`**

```python
from .base import BaseMapper
from .netsuite import NetSuitePOMapper

__all__ = ['BaseMapper', 'NetSuitePOMapper']
```

**File: `didero/integrations/erp/mappers/base.py`**

```python
from abc import ABC, abstractmethod
from typing import Dict, Any
from didero.workflows.core.nodes.purchase_orders.po_creation.schemas import PurchaseOrderDetails

class BaseMapper(ABC):
    """Base class for ERP data mappers."""
    
    @abstractmethod
    def map_to_didero_format(self, erp_data: Dict[str, Any]) -> PurchaseOrderDetails:
        """Map ERP data to Didero PurchaseOrderDetails format."""
        pass
```

**File: `didero/integrations/erp/mappers/netsuite.py`**

```python
from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime
from .base import BaseMapper
from didero.workflows.core.nodes.purchase_orders.po_creation.schemas import (
    PurchaseOrderDetails, OrderItemDetails, SupplierDetails, AddressDetails
)

class NetSuitePOMapper(BaseMapper):
    """Maps NetSuite PO data to Didero PurchaseOrderDetails format."""
    
    def map_to_didero_format(self, netsuite_data: Dict[str, Any]) -> PurchaseOrderDetails:
        """
        Convert NetSuite PO structure to Didero PurchaseOrderDetails.
        Uses field mapping from FIXED_PO_PO431_extraction_20250721_171239.json as template.
        """
        return PurchaseOrderDetails(
            po_number=netsuite_data['header']['standard']['tranId'],
            supplier=self._map_supplier(netsuite_data['vendor']),
            billing_address=self._map_address(netsuite_data['addresses']['billing']),
            shipping_address=self._map_address(netsuite_data['addresses']['shipping']),
            order_items=self._map_line_items(netsuite_data['line_items']),
            total_cost=self._parse_money(netsuite_data['header']['standard']['total']),
            currency=netsuite_data['header']['standard'].get('currency', 'USD'),
            order_date=self._parse_date(netsuite_data['header']['standard']['tranDate']),
            # Map additional fields as needed
        )
    
    def _map_supplier(self, vendor_data: Dict[str, Any]) -> SupplierDetails:
        # Implementation details...
        pass
    
    def _map_line_items(self, line_items: List[Dict[str, Any]]) -> List[OrderItemDetails]:
        # Implementation details...
        pass
```

#### 1.3 Schema Updates

**File: `didero/integrations/erp/schemas.py`**

```python
class CreatePOFromERPParams(BaseModel):
    """Parameters for ERP-based PO creation"""
    email_id: Optional[str] = None
    document_id: Optional[str] = None  
    po_number: str
    team_id: int

class ERPPOFetchResult(BaseModel):
    """Result from fetching PO data from ERP"""
    success: bool
    po_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    erp_type: str
    fallback_required: bool = False
```

### Phase 2: Workflow Integration (Days 4-5)

#### 2.1 ERP PO Creation Activity

**File: `didero/workflows/core/nodes/purchase_orders/po_creation/activities.py`**

Add new activity:

```python
@activity.defn
async def create_po_from_erp(params: CreatePOFromERPParams) -> POCreationResult:
    """
    Create PO by fetching data from team's configured ERP system.
    
    Process:
    1. Get team's ERP configuration 
    2. Get ERP credentials from existing credential system
    3. Create ERP client (NetSuite for IonQ)
    4. Fetch complete PO data from ERP
    5. Map ERP data to PurchaseOrderDetails
    6. Call existing create_po_from_extracted_data() with mapped data
    """
    try:
        # Get ERP client for team
        erp_client = await get_erp_client_for_team(params.team_id)
        if not erp_client:
            return POCreationResult(
                success=False,
                error_message="No ERP configuration found for team",
                fallback_required=True
            )
        
        # Extract PO data from ERP
        po_details = await sync_to_async(erp_client.extract_po_for_didero)(params.po_number)
        
        # Create PO using existing activity
        creation_params = CreatePOFromExtractedDataParams(
            po_details=po_details,
            email_id=params.email_id,
            document_id=params.document_id,
            team_id=params.team_id,
            source="erp_netsuite",
            human_validation_enabled=False  # ERP data is trusted
        )
        
        return await create_po_from_extracted_data(creation_params)
        
    except Exception as e:
        logger.error(f"ERP PO creation failed for {params.po_number}: {e}")
        await create_task(
            task_type_v2__name="ERP_PO_CREATION_FAILED",
            team_id=params.team_id,
            metadata={
                "po_number": params.po_number,
                "error": str(e),
                "email_id": params.email_id
            }
        )
        return POCreationResult(
            success=False,
            error_message=str(e),
            fallback_required=True
        )

async def get_erp_client_for_team(team_id: int) -> Optional[ERPClientBase]:
    """Factory function to create appropriate ERP client for team"""
    from didero.integrations.models import ERPIntegrationConfig
    from didero.integrations.erp.registry import ERPClientRegistry
    
    config = await sync_to_async(
        ERPIntegrationConfig.objects.filter(team_id=team_id, enabled=True).first
    )()
    
    if not config:
        return None
    
    return ERPClientRegistry.create_client_for_team(team_id)

async def has_erp_config_enabled(team_id: int) -> bool:
    """Check if team has ERP integration enabled"""
    from didero.integrations.models import ERPIntegrationConfig
    
    return await sync_to_async(
        ERPIntegrationConfig.objects.filter(team_id=team_id, enabled=True).exists
    )()
```

#### 2.2 Workflow Schema Updates

**File: `didero/workflows/core/nodes/purchase_orders/po_creation/schemas.py`**

```python
class CreatePOFromERPParams(TypedDict):
    email_id: Optional[str]
    document_id: Optional[str] 
    po_number: str
    team_id: int
```

### Phase 3: Core Workflow Decision Logic (Days 6-7)

#### 3.1 Modified PO Creation Workflow

**File: `didero/workflows/core_workflows/po_creation/workflow.py`**

Update the main workflow logic to add ERP path decision:

```python
async def run(self, request: POCreationRequest) -> POCreationResult:
    """Enhanced PO creation workflow with ERP integration."""
    
    # Check if PO already exists
    existing_po = await self.check_existing_po(request.po_number, request.team_id)
    if existing_po:
        return POCreationResult(success=True, po_id=existing_po.id, source="existing")
    
    # NEW: Check for ERP configuration and try ERP creation first
    if request.po_number and await self.has_erp_integration_enabled(request.team_id):
        erp_result = await workflow.execute_activity(
            create_po_from_erp,
            CreatePOFromERPParams(
                email_id=request.email_id,
                document_id=request.document_id,
                po_number=request.po_number,
                team_id=request.team_id
            ),
            start_to_close_timeout=timedelta(seconds=60)
        )
        
        if erp_result.success:
            return POCreationResult(
                success=True,
                po_id=erp_result.po_id,
                source="erp_netsuite",
                metadata={"used_erp": True}
            )
        elif not erp_result.fallback_required:
            # Critical error, don't fallback
            return erp_result
    
    # Existing logic for AI direct vs Stagehand/RPA
    return await self.standard_po_creation_flow(request)

async def has_erp_integration_enabled(self, team_id: int) -> bool:
    """Check if team has ERP integration enabled."""
    return await workflow.execute_activity(
        has_erp_config_enabled,
        team_id,
        start_to_close_timeout=timedelta(seconds=10)
    )
```

## Implementation Steps

### Step 1: Enhance NetSuite Client (Day 1)
- [ ] Add `fetch_purchase_order_data()` method to NetSuiteClient
- [ ] Add `extract_po_for_didero()` method 
- [ ] Test PO fetching with IonQ credentials using PO431

### Step 2: Create Data Mapping Layer (Day 2)
- [ ] Create `didero/integrations/erp/mappers/` module structure
- [ ] Implement `NetSuitePOMapper` with field mapping logic
- [ ] Test mapping with sample NetSuite PO data from FIXED_PO_PO431_extraction_20250721_171239.json

### Step 3: Add ERP Schemas (Day 2)
- [ ] Update `didero/integrations/erp/schemas.py` with new schemas
- [ ] Update workflow schemas in `po_creation/schemas.py`

### Step 4: Create ERP PO Activity (Day 3)
- [ ] Add `create_po_from_erp()` activity to activities.py
- [ ] Add helper functions for ERP config detection
- [ ] Test activity in isolation with mock data

### Step 5: Integrate with Main Workflow (Days 4-5)
- [ ] Modify PO creation workflow decision logic  
- [ ] Add ERP path to workflow routing with fallback
- [ ] Test complete flow with IonQ team configuration

### Step 6: Testing & Validation (Days 6-7)
- [ ] Unit tests for mapping logic and NetSuite client methods
- [ ] Integration tests with NetSuite sandbox environment
- [ ] End-to-end test with IonQ PO creation emails
- [ ] Error handling and fallback scenario testing

## Key Design Decisions & Rationale

### 1. **Reuse Existing Activities Pattern**
**Decision**: Map ERP data to `PurchaseOrderDetails` and call existing `create_po_from_extracted_data()`
**Rationale**: Maximizes code reuse, maintains consistency, leverages existing supplier/item matching logic

### 2. **Clean Separation of Concerns**
**Decision**: NetSuite client handles SOAP, mapper handles conversion, activity orchestrates
**Rationale**: Modular design, easier testing, clear responsibilities

### 3. **Fallback Strategy**
**Decision**: Try ERP first, fallback to AI extraction on recoverable errors
**Rationale**: Provides reliability while maximizing ERP data usage

### 4. **No Human Validation for ERP**
**Decision**: ERP path bypasses human validation entirely
**Rationale**: ERP data is considered authoritative and complete

### 5. **Error Handling with Tasks**
**Decision**: Create manual tasks for critical ERP failures
**Rationale**: Ensures visibility and manual intervention when needed

## Testing Strategy

### Unit Tests
- [ ] NetSuite client PO fetching methods
- [ ] Data mapping functions (NetSuite → Didero)
- [ ] ERP configuration detection helpers
- [ ] Activity error handling scenarios

### Integration Tests
- [ ] Full PO creation from sample NetSuite data
- [ ] Fallback mechanisms (ERP failure → AI extraction)
- [ ] Credential handling and client creation

### End-to-End Tests
- [ ] Email trigger → ERP fetch → PO creation workflow
- [ ] Verify all fields mapped correctly using PO431 test data
- [ ] Performance testing (< 30 seconds total time)

## Success Criteria

1. **Functional**: IonQ team receives PO creation email → System detects ERP config → Fetches PO from NetSuite → Creates complete PO in Didero
2. **Data Integrity**: All PO fields properly mapped matching FIXED_PO_PO431_extraction_20250721_171239.json structure
3. **Error Handling**: Graceful handling of NetSuite errors, missing POs, credential issues with appropriate fallback
4. **Performance**: ERP PO creation completes within 60 seconds
5. **Reliability**: >95% success rate for ERP PO creation when NetSuite data is available

## File Changes Summary

**New Files:**
- `didero/integrations/erp/mappers/__init__.py`
- `didero/integrations/erp/mappers/base.py` 
- `didero/integrations/erp/mappers/netsuite.py`

**Modified Files:**
- `didero/integrations/erp/clients/netsuite.py` (add PO fetching methods)
- `didero/integrations/erp/schemas.py` (add new schemas)
- `didero/workflows/core/nodes/purchase_orders/po_creation/activities.py` (add ERP activity)
- `didero/workflows/core/nodes/purchase_orders/po_creation/schemas.py` (add ERP schemas)
- `didero/workflows/core_workflows/po_creation/workflow.py` (add ERP routing logic)

**No Changes Needed:**
- Existing credential system (reuse as-is)
- `ERPIntegrationConfig` model (already suitable)
- Existing PO creation activities (reuse via composition)

This unified plan combines the best architectural decisions from all three plans while providing a clear, actionable roadmap that leverages existing Didero infrastructure effectively.