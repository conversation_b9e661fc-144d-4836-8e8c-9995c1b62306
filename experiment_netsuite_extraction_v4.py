#!/usr/bin/env python3
"""
Experiment 4: Clean extraction-only approach
Goal: Extract all necessary data in a structured way, but leave ALL mapping/conversion to the mapper
"""

import re
from typing import Dict, Any, List, Optional
from datetime import datetime


class ExperimentV4:
    """Pure extraction - no conversion or mapping logic"""

    def extract_purchase_order(self, xml_text: str) -> Dict[str, Any]:
        """
        Extract all PO data from NetSuite XML.

        Returns raw but structured data - mapper handles all conversions.
        """

        result = {
            "metadata": {
                "extraction_timestamp": datetime.now().isoformat(),
                "extractor_version": "v4",
            },
            "header": self._extract_header(xml_text),
            "vendor": self._extract_vendor(xml_text),
            "addresses": {
                "billing": self._extract_address(xml_text, "billingAddress"),
                "shipping": self._extract_address(xml_text, "shippingAddress"),
            },
            "line_items": self._extract_line_items(xml_text),
            "custom_fields": {
                "header": self._extract_custom_fields(xml_text, "purchaseOrder"),
                "line_items": [],  # Populated during line item extraction
            },
        }

        return result

    def _extract_header(self, xml_text: str) -> Dict[str, str]:
        """Extract all header-level fields"""
        header = {}

        # Simple fields that are direct children of purchaseOrder
        simple_fields = [
            "tranId",
            "memo",
            "status",
            "total",
            "email",
            "currencyName",
            "tranDate",
            "createdDate",
            "lastModifiedDate",
            "nexus",
            "subsidiary",
            "location",
            "department",
        ]

        for field in simple_fields:
            pattern = f"<tranPurch:{field}>([^<]*)</tranPurch:{field}>"
            match = re.search(pattern, xml_text, re.DOTALL)
            if match and match.group(1).strip():
                header[field] = match.group(1).strip()

        # Fields with nested structure
        # Payment terms
        terms_match = re.search(
            r'<tranPurch:terms[^>]*internalId="([^"]*)"[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>',
            xml_text,
            re.DOTALL,
        )
        if terms_match:
            header["terms"] = {
                "internalId": terms_match.group(1),
                "name": terms_match.group(2).strip(),
            }

        # Shipping method
        ship_method_match = re.search(
            r'<tranPurch:shipMethod[^>]*internalId="([^"]*)"[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>',
            xml_text,
            re.DOTALL,
        )
        if ship_method_match:
            header["shipMethod"] = {
                "internalId": ship_method_match.group(1),
                "name": ship_method_match.group(2).strip(),
            }

        return header

    def _extract_vendor(self, xml_text: str) -> Dict[str, str]:
        """Extract vendor/entity information"""
        vendor = {}

        vendor_match = re.search(
            r'<tranPurch:entity[^>]*internalId="([^"]*)"[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>',
            xml_text,
            re.DOTALL,
        )
        if vendor_match:
            vendor = {
                "internalId": vendor_match.group(1),
                "name": vendor_match.group(2).strip(),
            }

        return vendor

    def _extract_address(self, xml_text: str, address_type: str) -> Dict[str, str]:
        """Extract address fields without any conversion"""
        address = {}

        pattern = f"<tranPurch:{address_type}[^>]*>(.*?)</tranPurch:{address_type}>"
        match = re.search(pattern, xml_text, re.DOTALL)
        if not match:
            return address

        addr_xml = match.group(1)

        # Extract all available address fields
        address_fields = [
            "country",
            "addressee",
            "addr1",
            "addr2",
            "addr3",
            "city",
            "state",
            "zip",
            "addrText",
            "override",
        ]

        for field in address_fields:
            field_pattern = f"<platformCommon:{field}>([^<]*)</platformCommon:{field}>"
            field_match = re.search(field_pattern, addr_xml, re.DOTALL)
            if field_match and field_match.group(1).strip():
                address[field] = field_match.group(1).strip()

        return address

    def _extract_line_items(self, xml_text: str) -> List[Dict[str, Any]]:
        """Extract all line items with their fields"""
        items = []

        itemlist_match = re.search(
            r"<tranPurch:itemList>(.*?)</tranPurch:itemList>", xml_text, re.DOTALL
        )
        if not itemlist_match:
            return items

        items_xml = itemlist_match.group(1)

        # The structure is actually flat - each item in the list has all fields as siblings
        # Split by the item reference tags to get each complete item
        item_blocks = re.split(r"(?=<tranPurch:item\s+internalId)", items_xml)

        for block in item_blocks:
            if not block.strip():
                continue
            # Each block contains the item reference and all its fields
            item = self._extract_single_item(block)
            if item["item_reference"]:  # Only add if we found a valid item
                items.append(item)

        return items

    def _extract_single_item(self, item_xml: str) -> Dict[str, Any]:
        """Extract all fields from a single line item"""
        item = {"item_reference": {}, "fields": {}, "custom_fields": {}}

        # Extract item reference (the linked item) - it's a nested item tag
        ref_pattern = r'<tranPurch:item\s+internalId="([^"]*)"[^>]*>\s*<platformCore:name>([^<]*)</platformCore:name>'
        ref_match = re.search(ref_pattern, item_xml)
        if ref_match:
            item["item_reference"] = {
                "internalId": ref_match.group(1),
                "name": ref_match.group(2).strip(),
            }

        # Extract standard fields - they're siblings to the item reference
        standard_fields = [
            "line",
            "description",
            "vendorName",
            "quantity",
            "units",
            "rate",
            "amount",
            "expectedReceiptDate",
            "isClosed",
            "taxCode",
            "taxRate1",
            "taxRate2",
            "grossAmt",
        ]

        for field in standard_fields:
            pattern = f"<tranPurch:{field}>([^<]*)</tranPurch:{field}>"
            match = re.search(pattern, item_xml)
            if match and match.group(1).strip():
                item["fields"][field] = match.group(1).strip()

        # Extract custom fields for this line item
        custom_fields_match = re.search(
            r"<tranPurch:customFieldList[^>]*>(.*?)</tranPurch:customFieldList>",
            item_xml,
            re.DOTALL,
        )
        if custom_fields_match:
            custom_xml = custom_fields_match.group(1)
            custom_pattern = r'scriptId="([^"]+)"[^>]*>.*?<platformCore:value[^>]*>([^<]*)</platformCore:value>'
            for match in re.finditer(custom_pattern, custom_xml, re.DOTALL):
                if match.group(2).strip():
                    item["custom_fields"][match.group(1)] = match.group(2).strip()

        return item

    def _extract_custom_fields(
        self, xml_text: str, level: str = "purchaseOrder"
    ) -> Dict[str, str]:
        """Extract custom fields at header level"""
        custom_fields = {}

        # Look for customFieldList at the purchase order level
        if level == "purchaseOrder":
            pattern = (
                r"<tranPurch:customFieldList[^>]*>(.*?)</tranPurch:customFieldList>"
            )
            match = re.search(pattern, xml_text, re.DOTALL)
            if match:
                custom_xml = match.group(1)
                field_pattern = r'scriptId="([^"]+)"[^>]*>.*?<platformCore:value[^>]*>([^<]*)</platformCore:value>'
                for field_match in re.finditer(field_pattern, custom_xml, re.DOTALL):
                    if field_match.group(2).strip():
                        custom_fields[field_match.group(1)] = field_match.group(
                            2
                        ).strip()

        return custom_fields


def test_clean_extraction():
    """Test the clean extraction approach"""
    sample_xml = """
    <tranPurch:purchaseOrder xmlns:tranPurch="urn:purchases_2023_2.transactions.webservices.netsuite.com"
                             xmlns:platformCore="urn:core_2023_2.platform.webservices.netsuite.com"
                             xmlns:platformCommon="urn:common_2023_2.platform.webservices.netsuite.com">
        <tranPurch:tranId>PO431</tranPurch:tranId>
        <tranPurch:memo>WEB NUW1334695</tranPurch:memo>
        <tranPurch:status>Fully Billed</tranPurch:status>
        <tranPurch:total>7291.42</tranPurch:total>
        <tranPurch:currencyName>USD</tranPurch:currencyName>
        <tranPurch:tranDate>2022-10-03T21:00:00.000-07:00</tranPurch:tranDate>
        <tranPurch:email><EMAIL></tranPurch:email>
        <tranPurch:terms internalId="4">
            <platformCore:name>Net 30</platformCore:name>
        </tranPurch:terms>
        <tranPurch:customFieldList>
            <tranPurch:customField scriptId="custbody_ionq_tracking_number">
                <platformCore:value>1Z07X6270360947105</platformCore:value>
            </tranPurch:customField>
            <tranPurch:customField scriptId="custbody_ionq_promise_ship_date">
                <platformCore:value>2022-10-15T00:00:00.000-07:00</platformCore:value>
            </tranPurch:customField>
        </tranPurch:customFieldList>
        <tranPurch:entity internalId="550">
            <platformCore:name>V10072 ThorLabs</platformCore:name>
        </tranPurch:entity>
        <tranPurch:billingAddress>
            <platformCommon:addressee>ThorLabs Inc.</platformCommon:addressee>
            <platformCommon:addr1>56 Sparta Avenue</platformCommon:addr1>
            <platformCommon:addr2>Suite 200</platformCommon:addr2>
            <platformCommon:city>Newton</platformCommon:city>
            <platformCommon:state>NJ</platformCommon:state>
            <platformCommon:zip>07860</platformCommon:zip>
            <platformCommon:country>_unitedStates</platformCommon:country>
        </tranPurch:billingAddress>
        <tranPurch:shippingAddress>
            <platformCommon:addressee>CP Tooling (NI)</platformCommon:addressee>
            <platformCommon:addrText>CP Tooling (NI)\r\nUnited States</platformCommon:addrText>
            <platformCommon:country>_unitedStates</platformCommon:country>
        </tranPurch:shippingAddress>
        <tranPurch:itemList>
            <tranPurch:item>
                <tranPurch:item internalId="2761">
                    <platformCore:name>502-00097</platformCore:name>
                </tranPurch:item>
                <tranPurch:description>Compact Power and Energy Meter Console</tranPurch:description>
                <tranPurch:quantity>1.0</tranPurch:quantity>
                <tranPurch:rate>1220.57</tranPurch:rate>
                <tranPurch:amount>1220.57</tranPurch:amount>
                <tranPurch:vendorName>PM100D</tranPurch:vendorName>
                <tranPurch:expectedReceiptDate>2022-10-03T21:00:00.000-07:00</tranPurch:expectedReceiptDate>
                <tranPurch:customFieldList>
                    <tranPurch:customField scriptId="custcol_ionq_supplierpromisedatefield">
                        <platformCore:value>2022-11-03T21:00:00.000-07:00</platformCore:value>
                    </tranPurch:customField>
                </tranPurch:customFieldList>
            </tranPurch:item>
            <tranPurch:item>
                <tranPurch:item internalId="2762">
                    <platformCore:name>502-00098</platformCore:name>
                </tranPurch:item>
                <tranPurch:description>S120C Standard Photodiode Power Sensor</tranPurch:description>
                <tranPurch:quantity>2.0</tranPurch:quantity>
                <tranPurch:rate>366.71</tranPurch:rate>
                <tranPurch:amount>733.42</tranPurch:amount>
                <tranPurch:vendorName>S120C</tranPurch:vendorName>
                <tranPurch:expectedReceiptDate>2022-10-05T21:00:00.000-07:00</tranPurch:expectedReceiptDate>
            </tranPurch:item>
        </tranPurch:itemList>
    </tranPurch:purchaseOrder>
    """

    extractor = ExperimentV4()
    result = extractor.extract_purchase_order(sample_xml)

    print("=== Experiment V4 Results (Clean Extraction) ===")
    print("\n--- Header ---")
    print(f"Fields extracted: {list(result['header'].keys())}")
    print(f"PO Number: {result['header'].get('tranId')}")
    print(f"Terms: {result['header'].get('terms', {})}")

    print("\n--- Vendor ---")
    print(f"Vendor: {result['vendor']}")

    print("\n--- Custom Fields ---")
    print(f"Header custom fields: {result['custom_fields']['header']}")

    print("\n--- Addresses ---")
    print(f"Billing fields: {list(result['addresses']['billing'].keys())}")
    print(f"Shipping fields: {list(result['addresses']['shipping'].keys())}")

    print("\n--- Line Items ---")
    print(f"Total items: {len(result['line_items'])}")
    for idx, item in enumerate(result["line_items"]):
        print(f"\nItem {idx + 1}:")
        print(f"  Reference: {item['item_reference']}")
        print(f"  Fields: {list(item['fields'].keys())}")
        print(f"  Custom fields: {item['custom_fields']}")

    # Show the clean structure
    print("\n--- Clean Structure (Full Line Item Example) ---")
    import json

    if result["line_items"]:
        print("First line item:")
        print(json.dumps(result["line_items"][0], indent=2))

    return result


if __name__ == "__main__":
    test_clean_extraction()
