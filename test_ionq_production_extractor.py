#!/usr/bin/env python3
"""
Test script for the production NetSuite extractor
This demonstrates the JSON output structure without requiring actual NetSuite connection
"""

import json
from datetime import datetime
from decimal import Decimal


# Import from the existing successful extraction
def load_test_data():
    """Load test data from previous successful extraction"""
    # This simulates what the production extractor would return
    return {
        "metadata": {
            "po_number": "PO431",
            "internal_id": "18816",
            "extraction_time": datetime.now().isoformat(),
            "extractor_version": "1.0.0",
            "account_id": "7581852_SB1",
        },
        "vendor": {"internal_id": "550", "name": "V10072 ThorLabs"},
        "addresses": {
            "billing": {
                "country": "_unitedStates",
                "addressee": "ThorLabs",
                "addr1": "56 Sparta Avenue",
                "city": "Newton",
                "state": "NJ",
                "zip": "07860",
                "addrText": "Thor<PERSON>abs\n56 Sparta Avenue\nNewton NJ 07860\nUnited States",
                "override": "false",
            },
            "shipping": {
                "country": "_unitedStates",
                "addressee": "CP Tooling (NI)",
                "addrText": "CP Tooling (NI)\r\nUnited States",
                "override": "false",
            },
            "vendor_primary": {
                "country": "_unitedStates",
                "addressee": "ThorLabs",
                "addr1": "56 Sparta Avenue",
                "city": "Newton",
                "state": "NJ",
                "zip": "07860",
                "addrText": "ThorLabs\n56 Sparta Avenue\nNewton NJ 07860\nUnited States",
                "override": "false",
            },
        },
        "header": {
            "standard": {
                "tranId": "PO431",
                "memo": "WEB NUW1334695",
                "status": "Fully Billed",
                "total": "7291.42",
                "email": "<EMAIL>",
                "currencyName": "USD",
                "exchangeRate": "1.0",
                "createdDate": "2022-10-04T17:23:47.000-07:00",
                "lastModifiedDate": "2022-11-20T15:24:36.000-08:00",
                "tranDate": "2022-10-03T21:00:00.000-07:00",
                "paymentTerms": "Net 30",
                "location": "CP Tooling (NI)",
                "subsidiary": "IonQ Inc.",
                "department": "Engineering",
            },
            "custom": {
                "custbody_ionq_sent_date_to_supplier": "2022-10-05T21:00:00.000-07:00",
                "custbody_bill_approver_approved": "false",
                "custbody_ionq_tracking_number": "1Z07X6270360947105",
                "custbody_ionq_po_req_num": "1651",
                "custbody_esc_created_date": "2022-10-03T21:00:00.000-07:00",
                "custbody_status_rejected": "false",
                "custbody_tf_tx_po_status": "false",
                "custbody_is_po": "true",
                "custbody_fpa_approval": "false",
            },
        },
        "line_items": [
            {
                "line_number": 1,
                "item_internal_id": "2761",
                "item_name": "502-00097",
                "fields": {
                    "line": "1",
                    "vendorName": "PM100D",
                    "description": 'Compact Power and Energy Meter Console, Digital 4" LCD',
                    "quantity": "1.0",
                    "rate": "1220.57",
                    "amount": "1220.57",
                    "expectedReceiptDate": "2022-10-03T21:00:00.000-07:00",
                    "units": "Each",
                    "department": "Engineering",
                },
                "custom_fields": {
                    "custcol_ionq_purchasetypefield": "2022-11-03T21:00:00.000-07:00",
                    "custcol_ionq_receivinginspectionfield": "false",
                    "custcol_scm_lc_autocalc": "false",
                    "cseg_ionq_project": "DO NOT USE - P00158 Integrated Devices",
                },
            },
            {
                "line_number": 2,
                "item_internal_id": "2760",
                "item_name": "425-00006",
                "fields": {
                    "line": "2",
                    "vendorName": "S120C",
                    "description": "Standard Photodiode Power Sensor, Si, 400 - 1100 nm, 50 mW",
                    "quantity": "1.0",
                    "rate": "342.41",
                    "amount": "342.41",
                    "expectedReceiptDate": "2022-10-03T21:00:00.000-07:00",
                    "units": "Each",
                },
                "custom_fields": {
                    "custcol_ionq_purchasetypefield": "2022-10-09T21:00:00.000-07:00",
                    "cseg_ionq_project": "DO NOT USE - P00158 Integrated Devices",
                },
            },
        ],
        "summary": {
            "vendor_found": True,
            "vendor_name": "V10072 ThorLabs",
            "has_billing_address": True,
            "has_shipping_address": True,
            "primary_vendor_address": True,
            "total_amount": "7291.42",
            "currency": "USD",
            "line_count": 19,
            "has_tracking_number": True,
            "tracking_number": "1Z07X6270360947105",
            "all_items_have_dates": True,
        },
        "rpa_format": {
            "purchase_order": {
                "tranId": "PO431",
                "memo": "WEB NUW1334695",
                "status": "Fully Billed",
                "total": "7291.42",
                "email": "<EMAIL>",
                "currencyName": "USD",
                "vendor": "V10072 ThorLabs",
                "vendor_address": {
                    "company": "V10072 ThorLabs",
                    "address1": "56 Sparta Avenue",
                    "address2": "",
                    "city": "Newton",
                    "state": "NJ",
                    "zip": "07860",
                    "country": "_unitedStates",
                    "addressee": "ThorLabs",
                    "full_address": "ThorLabs\n56 Sparta Avenue\nNewton NJ 07860\nUnited States",
                },
                "ship_to": "CP Tooling (NI)\r\nUnited States",
                "custbody_ionq_tracking_number": "1Z07X6270360947105",
            },
            "line_items": [
                {
                    "item_number": "502-00097",
                    "description": 'Compact Power and Energy Meter Console, Digital 4" LCD',
                    "quantity": 1.0,
                    "unit_price_0": "1220.57",
                    "unit_price_currency": "USD",
                    "vendor_name": "PM100D",
                    "custcol_ionq_purchasetypefield": "2022-11-03T21:00:00.000-07:00",
                }
            ],
        },
        "didero_mapping": {
            "purchase_order": {
                "po_number": "PO431",
                "total_cost": "7291.42",
                "placement_time": "2022-10-03T21:00:00.000-07:00",
                "requested_date": "2022-10-03T21:00:00.000-07:00",
                "internal_notes": "WEB NUW1334695",
                "payment_terms": "Net 30",
                "metadata": {
                    "netsuite_internal_id": "18816",
                    "currency": "USD",
                    "location": "CP Tooling (NI)",
                    "subsidiary": "IonQ Inc.",
                    "department": "Engineering",
                    "custom_fields": {
                        "custbody_ionq_tracking_number": "1Z07X6270360947105",
                        "custbody_ionq_po_req_num": "1651",
                    },
                    "extraction_metadata": {
                        "po_number": "PO431",
                        "internal_id": "18816",
                        "extraction_time": datetime.now().isoformat(),
                        "extractor_version": "1.0.0",
                        "account_id": "7581852_SB1",
                    },
                },
            },
            "line_items": [
                {
                    "item_number": "502-00097",
                    "description": 'Compact Power and Energy Meter Console, Digital 4" LCD',
                    "quantity": 1.0,
                    "price": "1220.57",
                    "unit_of_measure": "Each",
                    "metadata": {
                        "netsuite_line_number": 1,
                        "netsuite_item_internal_id": "2761",
                        "vendor_item_name": "PM100D",
                        "expected_receipt_date": "2022-10-03T21:00:00.000-07:00",
                        "department": "Engineering",
                        "custom_fields": {
                            "custcol_ionq_purchasetypefield": "2022-11-03T21:00:00.000-07:00"
                        },
                    },
                }
            ],
            "vendor_address": {
                "country": "_unitedStates",
                "addressee": "ThorLabs",
                "addr1": "56 Sparta Avenue",
                "city": "Newton",
                "state": "NJ",
                "zip": "07860",
                "addrText": "ThorLabs\n56 Sparta Avenue\nNewton NJ 07860\nUnited States",
            },
        },
    }


def print_json_structure(data, indent=0):
    """Pretty print the JSON structure with descriptions"""
    spaces = "  " * indent

    if isinstance(data, dict):
        for key, value in data.items():
            if isinstance(value, (dict, list)):
                print(f"{spaces}{key}:")
                print_json_structure(value, indent + 1)
            else:
                print(f"{spaces}{key}: {type(value).__name__} = {repr(value)[:50]}...")
    elif isinstance(data, list):
        print(f"{spaces}[{len(data)} items]")
        if data:
            print(f"{spaces}Sample item:")
            print_json_structure(data[0], indent + 1)


def main():
    """Demonstrate the production extractor output"""

    print("=" * 80)
    print("🎯 IonQ NetSuite Production Extractor - JSON Output Structure")
    print("=" * 80)
    print()

    # Load test data
    extraction_result = load_test_data()

    # Save to file
    output_file = "ionq_production_extraction_sample.json"
    with open(output_file, "w") as f:
        json.dump(extraction_result, f, indent=2, default=str)

    print("📄 Complete JSON saved to:", output_file)
    print()

    # Print structure overview
    print("📊 JSON STRUCTURE OVERVIEW:")
    print("-" * 40)
    print_json_structure(extraction_result)

    print("\n" + "=" * 80)
    print("🔍 KEY INSIGHTS:")
    print("-" * 40)
    print("1. VENDOR DATA: Extracted from entity reference")
    print(f"   - Name: {extraction_result['vendor']['name']}")
    print(f"   - ID: {extraction_result['vendor']['internal_id']}")
    print()

    print("2. ADDRESS STRATEGY: Billing address used for vendor (complete data)")
    billing = extraction_result["addresses"]["billing"]
    print(f"   - Company: {billing['addressee']}")
    print(f"   - Street: {billing['addr1']}")
    print(f"   - City/State: {billing['city']}, {billing['state']} {billing['zip']}")
    print()

    print("3. SHIPPING ADDRESS: Minimal (just location name)")
    shipping = extraction_result["addresses"]["shipping"]
    print(f"   - Raw Text: {repr(shipping['addrText'])}")
    print("   - This is why AI parsing is needed!")
    print()

    print("4. RPA FORMAT: Ready for integration")
    rpa_vendor = extraction_result["rpa_format"]["purchase_order"]["vendor_address"]
    print("   - Matches expected structure for update_supplier()")
    print(f"   - Company field: {rpa_vendor['company']}")
    print()

    print("5. DIDERO MAPPING: Direct model compatibility")
    didero_po = extraction_result["didero_mapping"]["purchase_order"]
    print(f"   - PO Number: {didero_po['po_number']}")
    print(f"   - Total Cost: ${didero_po['total_cost']}")
    print(
        f"   - Tracking: {didero_po['metadata']['custom_fields'].get('custbody_ionq_tracking_number')}"
    )

    print("\n" + "=" * 80)
    print("✅ This is the exact structure the production extractor produces!")
    print("=" * 80)


if __name__ == "__main__":
    main()
