#!/usr/bin/env python
"""
End-to-End Test Script for Smart PO Auto-Creation Feature

This script comprehensively tests the Smart PO Auto-Creation functionality that:
1. Automatically creates missing POs from NetSuite when referenced in Order Acknowledgements
2. Automatically creates missing POs from NetSuite when referenced in Shipment notifications
3. Verifies configuration-based enablement (IonQ vs other teams)
4. Tests the complete workflow integration end-to-end

Test Scenarios:
A. Order Acknowledgement with missing PO (IonQ team) -> Should auto-create PO
B. Shipment notification with missing PO (IonQ team) -> Should auto-create PO
C. Order Acknowledgement with missing PO (non-IonQ team) -> Should fail gracefully
D. Order Acknowledgement with existing PO (IonQ team) -> Should use existing PO

Usage:
    uv run python test_smart_po_auto_creation.py

Prerequisites:
- IonQ NetSuite credentials must be configured in .env
- Temporal worker must be running
- Database must be accessible
- Auto-creation feature must be enabled in ERP config
"""

import asyncio
import json
import os
import sys
import time
import uuid
from datetime import datetime, timedelta

from asgiref.sync import sync_to_async

# Setup Django first before importing any Django models
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")

try:
    import django

    django.setup()
except ImportError:
    print(
        "Django not imported properly, make sure you're running this with 'uv run python test_smart_po_auto_creation.py'"
    )
    sys.exit(1)

# Now import Django models
from django.utils import timezone

# Import Temporal client
from temporalio.client import Client

from didero.emails.models import EmailThread
from didero.integrations.models import ERPIntegrationConfig
from didero.orders.models import OrderAcknowledgement, PurchaseOrder, Shipment
from didero.suppliers.models import Communication, CommunicationEmailRecipient, Supplier
from didero.users.models.team_models import Team
from didero.workflows.models import UserWorkflow
from didero.workflows.schemas import WorkflowTrigger, WorkflowType

# Configuration
IONQ_TEAM_ID = 4  # IonQ test team ID
NON_IONQ_TEAM_ID = 1  # Non-IonQ team for negative testing
WAIT_TIME = 90  # Extended wait time for auto-creation operations
TEMPORAL_HOST = os.environ.get("TEMPORAL_HOST", "localhost:7233")

# Test PO numbers that should exist in IonQ's NetSuite
# Using known POs from previous testing
TEST_PO_MISSING = (
    "PO999"  # PO that doesn't exist in Didero but should exist in NetSuite
)
TEST_PO_EXISTING = "PO431"  # PO that exists in both Didero and NetSuite

print("=" * 80)
print("SMART PO AUTO-CREATION END-TO-END TEST")
print("=" * 80)

# Get test teams
try:
    ionq_team = Team.objects.get(id=IONQ_TEAM_ID)
    print(f"\nUsing IonQ team: {ionq_team.name} (ID: {ionq_team.id})")
except Team.DoesNotExist:
    print(f"\nERROR: IonQ team with ID {IONQ_TEAM_ID} not found!")
    sys.exit(1)

try:
    non_ionq_team = Team.objects.get(id=NON_IONQ_TEAM_ID)
    print(f"Using non-IonQ team: {non_ionq_team.name} (ID: {non_ionq_team.id})")
except Team.DoesNotExist:
    print(f"\nERROR: Non-IonQ team with ID {NON_IONQ_TEAM_ID} not found!")
    sys.exit(1)

print("\n" + "-" * 60)
print("VERIFYING PREREQUISITES")
print("-" * 60)

# Check NetSuite credentials
required_env_vars = [
    "IONQ_NETSUITE_ACCOUNT_ID",
    "IONQ_NETSUITE_CONSUMER_KEY",
    "IONQ_NETSUITE_CONSUMER_SECRET",
    "IONQ_NETSUITE_TOKEN_ID",
    "IONQ_NETSUITE_TOKEN_SECRET",
]

missing_vars = []
for var in required_env_vars:
    if not os.getenv(var):
        missing_vars.append(var)
    else:
        print(f"✅ {var} is configured")

if missing_vars:
    print(f"\n❌ Missing required environment variables: {missing_vars}")
    print("Please configure IonQ NetSuite credentials in .env file")
    sys.exit(1)

print("✅ All NetSuite credentials are configured")

print("\n" + "-" * 60)
print("SETTING UP ERP INTEGRATION CONFIGS")
print("-" * 60)

# Configure IonQ team with auto-creation enabled
ionq_erp_config, created = ERPIntegrationConfig.objects.get_or_create(
    team=ionq_team,
    defaults={
        "erp_type": "netsuite",
        "enabled": True,
        "auto_create_missing_pos": True,  # Enable smart auto-creation
        "field_mappings": {
            "po_number": "tranId",
            "supplier_name": "vendor_name",
            "memo": "vendor_notes",
        },
        "config": {
            "api_version": "2023_1",
            "test_mode": True,
        },
    },
)

if not created:
    # Ensure auto-creation is enabled
    ionq_erp_config.enabled = True
    ionq_erp_config.auto_create_missing_pos = True
    ionq_erp_config.save()

print(
    f"✅ IonQ ERP Config: enabled={ionq_erp_config.enabled}, auto_create={ionq_erp_config.auto_create_missing_pos}"
)

# Configure non-IonQ team with auto-creation disabled
non_ionq_erp_config, created = ERPIntegrationConfig.objects.get_or_create(
    team=non_ionq_team,
    defaults={
        "erp_type": "netsuite",
        "enabled": True,
        "auto_create_missing_pos": False,  # Disable auto-creation for comparison
        "field_mappings": {},
        "config": {},
    },
)

if not created:
    non_ionq_erp_config.auto_create_missing_pos = False
    non_ionq_erp_config.save()

print(
    f"✅ Non-IonQ ERP Config: enabled={non_ionq_erp_config.enabled}, auto_create={non_ionq_erp_config.auto_create_missing_pos}"
)

print("\n" + "-" * 60)
print("CLEANUP: REMOVING EXISTING TEST DATA")
print("-" * 60)

# Clean up any existing test POs
test_po_numbers = [TEST_PO_MISSING, TEST_PO_EXISTING]
for team in [ionq_team, non_ionq_team]:
    for po_number in test_po_numbers:
        deleted_pos, _ = PurchaseOrder.objects.filter(
            po_number=po_number, team=team
        ).delete()
        if deleted_pos > 0:
            print(
                f"🗑️  Deleted {deleted_pos} existing POs with number {po_number} for team {team.name}"
            )

# Clean up any existing test shipments and order acknowledgements
Shipment.objects.filter(purchase_order__po_number__in=test_po_numbers).delete()
OrderAcknowledgement.objects.filter(
    purchase_order__po_number__in=test_po_numbers
).delete()

print("✅ Cleaned up existing test data")

# Generate unique test run identifier
test_run_id = uuid.uuid4().hex[:8]
print(f"🆔 Test Run ID: {test_run_id}")

# Test results storage
test_results = {}


async def test_order_acknowledgement_auto_creation():
    """Test Scenario A: Order Acknowledgement with missing PO (IonQ team) -> Should auto-create PO"""
    print("\n" + "=" * 60)
    print("TEST SCENARIO A: ORDER ACKNOWLEDGEMENT AUTO-CREATION")
    print("=" * 60)

    scenario_id = f"oa-auto-{test_run_id}"

    # Create test email with order acknowledgement for missing PO
    email_body = f"""
Dear Didero Team,

This is to confirm that we have received and processed your purchase order {TEST_PO_MISSING}.

ORDER ACKNOWLEDGEMENT DETAILS:
- PO Number: {TEST_PO_MISSING}
- Order Number: OA{TEST_PO_MISSING}-{test_run_id}
- Confirmation Date: {datetime.now().strftime('%Y-%m-%d')}
- Expected Ship Date: {(datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d')}
- Total Amount: $1,250.00

Your order has been accepted and will be processed according to the delivery schedule.

Best regards,
IonQ Supplier Team
<EMAIL>

---
Test Scenario A - Auto-creation via Order Acknowledgement
Test Run ID: {test_run_id}
"""

    # Create supplier and email
    supplier = await sync_to_async(Supplier.objects.create)(
        name=f"IonQ Test Supplier A-{test_run_id}",
        team=ionq_team,
        website_url=f"https://ionq-test-a-{test_run_id}.example.com",
        description="Test supplier for auto-creation via Order Acknowledgement",
    )

    email_thread = await sync_to_async(EmailThread.objects.create)(
        team=ionq_team, thread_id=f"oa-auto-thread-{test_run_id}"
    )

    email = await sync_to_async(Communication.objects.create)(
        team=ionq_team,
        supplier=supplier,
        email_thread=email_thread,
        email_subject=f"Order Acknowledgement for PO {TEST_PO_MISSING}",
        email_content=email_body,
        direction="INBOUND",
        email_from="<EMAIL>",
        email_message_id=f"oa-auto-{test_run_id}@ionq-test.com",
        comm_time=timezone.now(),
        comm_type=Communication.TYPE_EMAIL,
    )

    await sync_to_async(CommunicationEmailRecipient.objects.create)(
        email_address="<EMAIL>", communication_to=email
    )

    print(f"✅ Created order acknowledgement email for PO {TEST_PO_MISSING}")
    print(f"   - Email ID: {email.pk}")
    print(f"   - Team: {ionq_team.name} (auto-creation enabled)")

    # Trigger Order Acknowledgement workflow
    from didero.workflows.core_workflows.order_ack.workflow import (
        OrderAcknowledgementWorkflow,
    )
    from didero.workflows.schemas import WorkflowTrigger, WorkflowType

    # Create workflow configuration
    workflow, created = await sync_to_async(UserWorkflow.objects.get_or_create)(
        workflow_type=WorkflowType.PURCHASE_ORDER_ACKNOWLEDGEMENT.value,
        trigger=WorkflowTrigger.ON_PURCHASE_ORDER_ACKNOWLEDGEMENT_EMAIL_RECEIVED.value,
        team=ionq_team,
        defaults={"current_snapshot": None},
    )

    # Connect to Temporal and run workflow
    client = await Client.connect(TEMPORAL_HOST)
    workflow_id = f"test-oa-auto-{scenario_id}"

    start_time = time.time()
    try:
        handle = await client.start_workflow(
            OrderAcknowledgementWorkflow.run,
            args=[
                str(workflow.id),
                {
                    "email_id": str(email.pk),
                    "team_id": str(ionq_team.id),
                    "workflow_id": str(workflow.id),
                },
            ],
            id=workflow_id,
            task_queue="user_workflows",
        )

        print(f"🚀 Started Order Acknowledgement workflow: {handle.id}")
        print("⏳ Waiting for auto-creation to complete...")

        await handle.result()
        end_time = time.time()
        execution_time = end_time - start_time

        print(f"✅ Workflow completed in {execution_time:.2f} seconds")

        # Verify PO was auto-created
        created_pos = await sync_to_async(list)(
            PurchaseOrder.objects.filter(po_number=TEST_PO_MISSING, team=ionq_team)
        )
        if created_pos:
            po = created_pos[0]
            print(f"🎯 SUCCESS: PO {TEST_PO_MISSING} was auto-created!")
            print(f"   - PO ID: {po.pk}")
            print(f"   - Source: {po.source}")
            print(f"   - Items: {await sync_to_async(po.items.count)()}")
            print(f"   - Total Cost: {po.total_cost}")

            # Verify Order Acknowledgement was created
            oa_created = await sync_to_async(
                OrderAcknowledgement.objects.filter(purchase_order=po).exists
            )()
            print(f"   - Order Acknowledgement Created: {oa_created}")

            test_results["scenario_a"] = {
                "success": True,
                "po_created": True,
                "po_id": po.pk,
                "execution_time": execution_time,
                "oa_created": oa_created,
            }
        else:
            print(f"❌ FAILURE: PO {TEST_PO_MISSING} was not auto-created")
            test_results["scenario_a"] = {
                "success": False,
                "po_created": False,
                "execution_time": execution_time,
            }

    except Exception as e:
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"❌ Workflow failed: {str(e)}")
        print(f"   Error type: {type(e).__name__}")

        # Try to get more detailed error information
        try:
            import traceback

            print(f"   Traceback: {traceback.format_exc()}")
        except:
            pass

        test_results["scenario_a"] = {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "execution_time": execution_time,
        }


async def test_shipment_auto_creation():
    """Test Scenario B: Shipment notification with missing PO (IonQ team) -> Should auto-create PO"""
    print("\n" + "=" * 60)
    print("TEST SCENARIO B: SHIPMENT AUTO-CREATION")
    print("=" * 60)

    scenario_id = f"shipment-auto-{test_run_id}"

    # Use a different PO number for shipment test
    shipment_po_number = f"{TEST_PO_MISSING}S"

    # Create test email with shipment notification for missing PO
    email_body = f"""
Dear Didero Team,

Your order has been shipped and is on its way!

SHIPMENT DETAILS:
- PO Number: {shipment_po_number}
- Tracking Number: 1Z999AA1234567890
- Carrier: FedEx
- Ship Date: {datetime.now().strftime('%Y-%m-%d')}
- Expected Delivery: {(datetime.now() + timedelta(days=3)).strftime('%Y-%m-%d')}
- Number of Packages: 2

Please use the tracking number above to monitor your shipment progress.

Best regards,
IonQ Logistics Team
<EMAIL>

---
Test Scenario B - Auto-creation via Shipment Notification
Test Run ID: {test_run_id}
"""

    # Create supplier and email
    supplier = await sync_to_async(Supplier.objects.create)(
        name=f"IonQ Test Supplier B-{test_run_id}",
        team=ionq_team,
        website_url=f"https://ionq-test-b-{test_run_id}.example.com",
        description="Test supplier for auto-creation via Shipment",
    )

    email_thread = await sync_to_async(EmailThread.objects.create)(
        team=ionq_team, thread_id=f"shipment-auto-thread-{test_run_id}"
    )

    email = await sync_to_async(Communication.objects.create)(
        team=ionq_team,
        supplier=supplier,
        email_thread=email_thread,
        email_subject=f"Shipment Notification for PO {shipment_po_number}",
        email_content=email_body,
        direction="INBOUND",
        email_from="<EMAIL>",
        email_message_id=f"shipment-auto-{test_run_id}@ionq-test.com",
        comm_time=timezone.now(),
        comm_type=Communication.TYPE_EMAIL,
    )

    await sync_to_async(CommunicationEmailRecipient.objects.create)(
        email_address="<EMAIL>", communication_to=email
    )

    print(f"✅ Created shipment notification email for PO {shipment_po_number}")
    print(f"   - Email ID: {email.pk}")
    print(f"   - Team: {ionq_team.name} (auto-creation enabled)")

    # Trigger Shipment workflow
    from didero.workflows.core_workflows.shipments.workflow import ShipmentWorkflow
    from didero.workflows.schemas import WorkflowTrigger, WorkflowType

    # Create workflow configuration
    workflow, created = await sync_to_async(UserWorkflow.objects.get_or_create)(
        workflow_type=WorkflowType.PURCHASE_ORDER_SHIPPED.value,
        trigger=WorkflowTrigger.ON_ORDER_SHIPPED_EMAIL_RECEIVED.value,
        team=ionq_team,
        defaults={"current_snapshot": None},
    )

    # Connect to Temporal and run workflow
    client = await Client.connect(TEMPORAL_HOST)
    workflow_id = f"test-shipment-auto-{scenario_id}"

    start_time = time.time()
    try:
        handle = await client.start_workflow(
            ShipmentWorkflow.run,
            args=[
                str(workflow.id),
                {
                    "email_id": str(email.pk),
                    "team_id": str(ionq_team.id),
                    "workflow_id": str(workflow.id),
                },
            ],
            id=workflow_id,
            task_queue="user_workflows",
        )

        print(f"🚀 Started Shipment workflow: {handle.id}")
        print("⏳ Waiting for auto-creation to complete...")

        await handle.result()
        end_time = time.time()
        execution_time = end_time - start_time

        print(f"✅ Workflow completed in {execution_time:.2f} seconds")

        # Verify PO was auto-created
        created_pos = await sync_to_async(list)(
            PurchaseOrder.objects.filter(po_number=shipment_po_number, team=ionq_team)
        )
        if created_pos:
            po = created_pos[0]
            print(f"🎯 SUCCESS: PO {shipment_po_number} was auto-created!")
            print(f"   - PO ID: {po.pk}")
            print(f"   - Source: {po.source}")
            print(f"   - Items: {await sync_to_async(po.items.count)()}")
            print(f"   - Total Cost: {po.total_cost}")

            # Verify Shipment was created
            shipment_created = await sync_to_async(
                Shipment.objects.filter(purchase_order=po).exists
            )()
            print(f"   - Shipment Created: {shipment_created}")

            test_results["scenario_b"] = {
                "success": True,
                "po_created": True,
                "po_id": po.pk,
                "execution_time": execution_time,
                "shipment_created": shipment_created,
            }
        else:
            print(f"❌ FAILURE: PO {shipment_po_number} was not auto-created")
            test_results["scenario_b"] = {
                "success": False,
                "po_created": False,
                "execution_time": execution_time,
            }

    except Exception as e:
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"❌ Workflow failed: {str(e)}")
        test_results["scenario_b"] = {
            "success": False,
            "error": str(e),
            "execution_time": execution_time,
        }


async def test_non_ionq_team_failure():
    """Test Scenario C: Order Acknowledgement with missing PO (non-IonQ team) -> Should fail gracefully"""
    print("\n" + "=" * 60)
    print("TEST SCENARIO C: NON-IONQ TEAM (AUTO-CREATION DISABLED)")
    print("=" * 60)

    scenario_id = f"non-ionq-{test_run_id}"

    # Use different PO number for non-IonQ test
    non_ionq_po_number = f"{TEST_PO_MISSING}N"

    # Create test email for non-IonQ team
    email_body = f"""
Dear Didero Team,

Order acknowledgement for PO {non_ionq_po_number}.

This email is sent to a non-IonQ team where auto-creation should be disabled.

Best regards,
Non-IonQ Supplier
"""

    # Create supplier and email for non-IonQ team
    supplier = await sync_to_async(Supplier.objects.create)(
        name=f"Non-IonQ Test Supplier C-{test_run_id}",
        team=non_ionq_team,
        website_url=f"https://non-ionq-test-c-{test_run_id}.example.com",
        description="Test supplier for non-IonQ team",
    )

    email_thread = await sync_to_async(EmailThread.objects.create)(
        team=non_ionq_team, thread_id=f"non-ionq-thread-{test_run_id}"
    )

    email = await sync_to_async(Communication.objects.create)(
        team=non_ionq_team,
        supplier=supplier,
        email_thread=email_thread,
        email_subject=f"Order Acknowledgement for PO {non_ionq_po_number}",
        email_content=email_body,
        direction="INBOUND",
        email_from="<EMAIL>",
        email_message_id=f"non-ionq-{test_run_id}@non-ionq-test.com",
        comm_time=timezone.now(),
        comm_type=Communication.TYPE_EMAIL,
    )

    print(f"✅ Created order acknowledgement email for PO {non_ionq_po_number}")
    print(f"   - Email ID: {email.pk}")
    print(f"   - Team: {non_ionq_team.name} (auto-creation disabled)")

    # Trigger Order Acknowledgement workflow
    from didero.workflows.core_workflows.order_ack.workflow import (
        OrderAcknowledgementWorkflow,
    )

    workflow, created = await sync_to_async(UserWorkflow.objects.get_or_create)(
        workflow_type=WorkflowType.PURCHASE_ORDER_ACKNOWLEDGEMENT.value,
        trigger=WorkflowTrigger.ON_PURCHASE_ORDER_ACKNOWLEDGEMENT_EMAIL_RECEIVED.value,
        team=non_ionq_team,
        defaults={"current_snapshot": None},
    )

    client = await Client.connect(TEMPORAL_HOST)
    workflow_id = f"test-non-ionq-{scenario_id}"

    start_time = time.time()
    try:
        handle = await client.start_workflow(
            OrderAcknowledgementWorkflow.run,
            args=[
                str(workflow.id),
                {
                    "email_id": str(email.pk),
                    "team_id": str(non_ionq_team.id),
                    "workflow_id": str(workflow.id),
                },
            ],
            id=workflow_id,
            task_queue="user_workflows",
        )

        print(f"🚀 Started Order Acknowledgement workflow: {handle.id}")
        print("⏳ Expecting workflow to fail due to missing PO...")

        result = await handle.result()
        end_time = time.time()
        execution_time = end_time - start_time

        # This workflow should fail because the PO doesn't exist and auto-creation is disabled
        print(f"⚠️  Workflow completed unexpectedly: {result}")
        test_results["scenario_c"] = {
            "success": False,  # We expect this to fail
            "unexpected_success": True,
            "execution_time": execution_time,
        }

    except Exception as e:
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"✅ Expected failure occurred: {str(e)}")
        print(
            "   - This confirms auto-creation is properly disabled for non-IonQ teams"
        )

        # Verify no PO was created
        created_pos = await sync_to_async(list)(
            PurchaseOrder.objects.filter(
                po_number=non_ionq_po_number, team=non_ionq_team
            )
        )
        po_not_created = len(created_pos) == 0

        test_results["scenario_c"] = {
            "success": True,  # Expected failure is success
            "failed_as_expected": True,
            "po_not_created": po_not_created,
            "execution_time": execution_time,
            "error_message": str(e),
        }


async def test_existing_po_workflow():
    """Test Scenario D: Order Acknowledgement with existing PO (IonQ team) -> Should use existing PO"""
    print("\n" + "=" * 60)
    print("TEST SCENARIO D: EXISTING PO (NO AUTO-CREATION NEEDED)")
    print("=" * 60)

    scenario_id = f"existing-po-{test_run_id}"

    # Create an existing PO first
    existing_po = await sync_to_async(PurchaseOrder.objects.create)(
        team=ionq_team,
        po_number=TEST_PO_EXISTING,
        total_cost=500.00,
        order_status="PENDING",
        placement_time=timezone.now(),
        source="manual",
    )

    print(f"✅ Created existing PO {TEST_PO_EXISTING} with ID {existing_po.pk}")

    # Create test email with order acknowledgement for existing PO
    email_body = f"""
Dear Didero Team,

Order acknowledgement for existing PO {TEST_PO_EXISTING}.

This should use the existing PO and not trigger auto-creation.

Best regards,
IonQ Supplier
"""

    # Create supplier and email
    supplier = await sync_to_async(Supplier.objects.create)(
        name=f"IonQ Test Supplier D-{test_run_id}",
        team=ionq_team,
        website_url=f"https://ionq-test-d-{test_run_id}.example.com",
        description="Test supplier for existing PO scenario",
    )

    email_thread = await sync_to_async(EmailThread.objects.create)(
        team=ionq_team, thread_id=f"existing-po-thread-{test_run_id}"
    )

    email = await sync_to_async(Communication.objects.create)(
        team=ionq_team,
        supplier=supplier,
        email_thread=email_thread,
        email_subject=f"Order Acknowledgement for PO {TEST_PO_EXISTING}",
        email_content=email_body,
        direction="INBOUND",
        email_from="<EMAIL>",
        email_message_id=f"existing-po-{test_run_id}@ionq-test.com",
        comm_time=timezone.now(),
        comm_type=Communication.TYPE_EMAIL,
    )

    print(f"✅ Created order acknowledgement email for existing PO {TEST_PO_EXISTING}")
    print(f"   - Email ID: {email.pk}")

    # Trigger Order Acknowledgement workflow
    from didero.workflows.core_workflows.order_ack.workflow import (
        OrderAcknowledgementWorkflow,
    )

    workflow, created = await sync_to_async(UserWorkflow.objects.get_or_create)(
        workflow_type=WorkflowType.PURCHASE_ORDER_ACKNOWLEDGEMENT.value,
        trigger=WorkflowTrigger.ON_PURCHASE_ORDER_ACKNOWLEDGEMENT_EMAIL_RECEIVED.value,
        team=ionq_team,
        defaults={"current_snapshot": None},
    )

    client = await Client.connect(TEMPORAL_HOST)
    workflow_id = f"test-existing-po-{scenario_id}"

    start_time = time.time()
    try:
        handle = await client.start_workflow(
            OrderAcknowledgementWorkflow.run,
            args=[
                str(workflow.id),
                {
                    "email_id": str(email.pk),
                    "team_id": str(ionq_team.id),
                    "workflow_id": str(workflow.id),
                },
            ],
            id=workflow_id,
            task_queue="user_workflows",
        )

        print(f"🚀 Started Order Acknowledgement workflow: {handle.id}")
        print("⏳ Should complete quickly using existing PO...")

        result = await handle.result()
        end_time = time.time()
        execution_time = end_time - start_time

        print(f"✅ Workflow completed in {execution_time:.2f} seconds")

        # Verify it used the existing PO (should be only one PO with this number)
        pos_count = await sync_to_async(
            PurchaseOrder.objects.filter(
                po_number=TEST_PO_EXISTING, team=ionq_team
            ).count
        )()
        used_existing = pos_count == 1

        print(f"✅ Used existing PO: {used_existing} (PO count: {pos_count})")

        test_results["scenario_d"] = {
            "success": True,
            "used_existing_po": used_existing,
            "execution_time": execution_time,
            "po_count": pos_count,
        }

    except Exception as e:
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"❌ Workflow failed: {str(e)}")
        print(f"   Error type: {type(e).__name__}")

        # Try to get more detailed error information
        try:
            import traceback

            print(f"   Traceback: {traceback.format_exc()}")
        except:
            pass

        test_results["scenario_d"] = {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "execution_time": execution_time,
        }


async def run_all_tests():
    """Run all test scenarios"""
    print("🚀 Starting comprehensive Smart PO Auto-Creation tests...")

    await test_order_acknowledgement_auto_creation()  # Scenario A
    await test_shipment_auto_creation()  # Scenario B
    await test_non_ionq_team_failure()  # Scenario C
    await test_existing_po_workflow()  # Scenario D


# Run all tests
start_time = time.time()
asyncio.run(run_all_tests())
end_time = time.time()
total_time = end_time - start_time

print("\n" + "=" * 80)
print("COMPREHENSIVE TEST RESULTS SUMMARY")
print("=" * 80)

print(f"\n⏱️  Total Test Execution Time: {total_time:.2f} seconds")

# Analyze results
scenarios = [
    ("A", "Order Acknowledgement Auto-Creation (IonQ)", "scenario_a"),
    ("B", "Shipment Auto-Creation (IonQ)", "scenario_b"),
    ("C", "Non-IonQ Team Graceful Failure", "scenario_c"),
    ("D", "Existing PO Workflow", "scenario_d"),
]

passed_tests = 0
total_tests = len(scenarios)

for letter, description, key in scenarios:
    result = test_results.get(key, {})
    success = result.get("success", False)
    status = "✅ PASS" if success else "❌ FAIL"
    exec_time = result.get("execution_time", 0)

    print(f"\n{status} Scenario {letter}: {description}")
    print(f"   - Execution Time: {exec_time:.2f}s")

    if key == "scenario_a":
        if result.get("po_created"):
            print(f"   - PO Auto-Created: ✅ (ID: {result.get('po_id')})")
            print(
                "   - Order Acknowledgement: ✅"
                if result.get("oa_created")
                else "   - Order Acknowledgement: ❌"
            )
        else:
            print("   - PO Auto-Created: ❌")

    elif key == "scenario_b":
        if result.get("po_created"):
            print(f"   - PO Auto-Created: ✅ (ID: {result.get('po_id')})")
            print(
                "   - Shipment Created: ✅"
                if result.get("shipment_created")
                else "   - Shipment Created: ❌"
            )
        else:
            print("   - PO Auto-Created: ❌")

    elif key == "scenario_c":
        if result.get("failed_as_expected"):
            print("   - Failed as expected: ✅ (auto-creation disabled)")
            print(
                "   - No PO created: ✅"
                if result.get("po_not_created")
                else "   - Unexpected PO creation: ❌"
            )
        else:
            print("   - Should have failed: ❌")

    elif key == "scenario_d":
        if result.get("used_existing_po"):
            print(f"   - Used existing PO: ✅ (count: {result.get('po_count', 0)})")
        else:
            print("   - Used existing PO: ❌")

    if result.get("error"):
        print(f"   - Error: {result['error']}")

    if success:
        passed_tests += 1

print(f"\n🎯 OVERALL RESULT: {passed_tests}/{total_tests} tests passed")

if passed_tests == total_tests:
    print("\n🎉 ALL TESTS PASSED!")
    print("✅ Smart PO Auto-Creation feature is working correctly")
    print("✅ Order Acknowledgement auto-creation works")
    print("✅ Shipment auto-creation works")
    print("✅ Non-IonQ teams properly blocked from auto-creation")
    print("✅ Existing POs are used instead of creating duplicates")
else:
    print(f"\n🚨 {total_tests - passed_tests} TESTS FAILED")
    print("❌ Smart PO Auto-Creation feature needs investigation")

print("\n" + "=" * 80)
print("SMART PO AUTO-CREATION TEST COMPLETED")
print("=" * 80)
