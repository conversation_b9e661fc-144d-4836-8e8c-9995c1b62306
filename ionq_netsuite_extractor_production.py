#!/usr/bin/env python3
"""
🎯 PRODUCTION NETSUITE PO EXTRACTOR FOR IONQ
Complete field extraction optimized for IonQ NetSuite integration
Prioritizes billing address for complete vendor information
Ready for production deployment in Didero workflows

Author: Didero Engineering
Version: 1.0.0
Last Updated: 2025-07-21
"""

import re
import json
import logging
import requests
import time
import hashlib
import hmac
import base64
import random
import string
from datetime import datetime
from typing import Dict, Any, Optional, Tuple, List
from decimal import Decimal

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class IonQNetSuiteExtractor:
    """
    Production-ready NetSuite PO extractor for IonQ integration.
    Handles complete field extraction with proper address prioritization.
    """

    def __init__(
        self,
        account_id: str,
        consumer_key: str,
        consumer_secret: str,
        token_id: str,
        token_secret: str,
    ):
        """
        Initialize with NetSuite credentials.

        Args:
            account_id: NetSuite account ID (e.g., '7581852_SB1' for sandbox)
            consumer_key: OAuth consumer key
            consumer_secret: OAuth consumer secret
            token_id: Token ID
            token_secret: Token secret
        """
        self.config = {
            "account_id": account_id,
            "consumer_key": consumer_key,
            "consumer_secret": consumer_secret,
            "token_id": token_id,
            "token_secret": token_secret,
        }

        self.session = requests.Session()
        account_url = account_id.lower().replace("_", "-")
        self.endpoint = f"https://{account_url}.suitetalk.api.netsuite.com/services/NetSuitePort_2023_2"

        logger.info(f"Initialized IonQ NetSuite Extractor for account: {account_id}")

    def generate_oauth_signature(self) -> Tuple[str, str, str]:
        """Generate OAuth 1.0a signature for NetSuite authentication."""
        timestamp = str(int(time.time()))
        nonce = "".join(random.choices(string.ascii_letters + string.digits, k=20))

        base_string = f"{self.config['account_id']}&{self.config['consumer_key']}&{self.config['token_id']}&{nonce}&{timestamp}"
        key = f"{self.config['consumer_secret']}&{self.config['token_secret']}"

        signature = base64.b64encode(
            hmac.new(
                key.encode("utf-8"), base_string.encode("utf-8"), hashlib.sha256
            ).digest()
        ).decode("utf-8")

        return timestamp, nonce, signature

    def make_soap_request(self, action: str, body: str) -> Optional[requests.Response]:
        """Make authenticated SOAP request to NetSuite API."""
        timestamp, nonce, signature = self.generate_oauth_signature()

        soap_request = f"""<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"
               xmlns:platformMsgs="urn:messages_2023_2.platform.webservices.netsuite.com"
               xmlns:platformCore="urn:core_2023_2.platform.webservices.netsuite.com"
               xmlns:platformCommon="urn:common_2023_2.platform.webservices.netsuite.com"
               xmlns:tranPurch="urn:purchases_2023_2.transactions.webservices.netsuite.com"
               xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <soap:Header>
        <platformMsgs:tokenPassport>
            <platformCore:account>{self.config['account_id']}</platformCore:account>
            <platformCore:consumerKey>{self.config['consumer_key']}</platformCore:consumerKey>
            <platformCore:token>{self.config['token_id']}</platformCore:token>
            <platformCore:nonce>{nonce}</platformCore:nonce>
            <platformCore:timestamp>{timestamp}</platformCore:timestamp>
            <platformCore:signature algorithm="HMAC-SHA256">{signature}</platformCore:signature>
        </platformMsgs:tokenPassport>
    </soap:Header>
    <soap:Body>
        {body}
    </soap:Body>
</soap:Envelope>"""

        headers = {"Content-Type": "text/xml; charset=utf-8", "SOAPAction": action}

        try:
            response = self.session.post(
                self.endpoint, data=soap_request, headers=headers, timeout=60
            )
            return response
        except Exception as e:
            logger.error(f"SOAP request failed: {e}")
            return None

    def test_connection(self) -> bool:
        """Test NetSuite connection."""
        logger.info("Testing NetSuite connection...")

        body = """<platformMsgs:getServerTime/>"""
        response = self.make_soap_request("getServerTime", body)

        if response and response.status_code == 200 and "serverTime" in response.text:
            logger.info("✅ NetSuite connection successful")
            return True
        else:
            logger.error(
                f"❌ Connection failed: {response.status_code if response else 'No response'}"
            )
            return False

    def search_po_by_number(self, po_number: str) -> Optional[str]:
        """
        Search for PO by number and return internal ID.

        Args:
            po_number: Purchase order number (e.g., 'PO431')

        Returns:
            Internal ID if found, None otherwise
        """
        logger.info(f"Searching for PO: {po_number}")

        body = f"""
        <platformMsgs:search>
            <platformMsgs:searchRecord xsi:type="tranPurch:TransactionSearchBasic">
                <tranPurch:tranId operator="is" xsi:type="platformCore:SearchStringField">
                    <platformCore:searchValue>{po_number}</platformCore:searchValue>
                </tranPurch:tranId>
                <tranPurch:type operator="anyOf" xsi:type="platformCore:SearchEnumMultiSelectField">
                    <platformCore:searchValue>purchaseOrder</platformCore:searchValue>
                </tranPurch:type>
            </platformMsgs:searchRecord>
        </platformMsgs:search>
        """

        response = self.make_soap_request("search", body)

        if not response or response.status_code != 200:
            logger.error(
                f"Search failed: {response.status_code if response else 'No response'}"
            )
            return None

        # Extract internal ID from search results
        internal_id_match = re.search(
            r'<platformCore:recordList>.*?internalId="(\d+)".*?</platformCore:recordList>',
            response.text,
            re.DOTALL,
        )

        if internal_id_match:
            internal_id = internal_id_match.group(1)
            logger.info(f"✅ Found PO {po_number} with internal ID: {internal_id}")
            return internal_id
        else:
            logger.warning(f"❌ PO {po_number} not found in NetSuite")
            return None

    def get_po_xml(self, internal_id: str) -> Optional[str]:
        """Retrieve complete PO XML from NetSuite."""
        logger.info(f"Retrieving PO XML for internal ID: {internal_id}")

        body = f"""<platformMsgs:get>
            <platformMsgs:baseRef xsi:type="platformCore:RecordRef" type="purchaseOrder" internalId="{internal_id}">
            </platformMsgs:baseRef>
        </platformMsgs:get>"""

        response = self.make_soap_request("get", body)

        if not response or response.status_code != 200:
            logger.error(
                f"Failed to retrieve PO: {response.status_code if response else 'No response'}"
            )
            return None

        if "faultstring" in response.text.lower():
            logger.error("NetSuite error in response")
            fault_match = re.search(
                r"<faultstring>([^<]*)</faultstring>", response.text
            )
            if fault_match:
                logger.error(f"Error: {fault_match.group(1)}")
            return None

        logger.info("✅ PO XML retrieved successfully")
        return response.text

    def extract_vendor_info(self, xml_text: str) -> Dict[str, Any]:
        """Extract vendor/entity information from PO XML."""
        vendor_data = {}

        # Extract entity reference (vendor)
        entity_match = re.search(
            r'<tranPurch:entity[^>]*internalId="([^"]*)"[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>',
            xml_text,
            re.DOTALL,
        )

        if entity_match:
            vendor_data["internal_id"] = entity_match.group(1)
            vendor_data["name"] = entity_match.group(2).strip()
            logger.info(
                f"Found vendor: {vendor_data['name']} (ID: {vendor_data['internal_id']})"
            )
        else:
            logger.warning("No vendor/entity reference found")

        return vendor_data

    def extract_addresses(self, xml_text: str) -> Dict[str, Any]:
        """
        Extract and prioritize addresses from PO XML.
        Billing address is prioritized for complete vendor information.
        """
        addresses = {}

        # Extract billing address (usually complete)
        billing_match = re.search(
            r"<tranPurch:billingAddress[^>]*>(.*?)</tranPurch:billingAddress>",
            xml_text,
            re.DOTALL,
        )

        if billing_match:
            billing_address = self._parse_address_fields(billing_match.group(1))
            if billing_address:
                addresses["billing"] = billing_address
                logger.info(
                    f"Found billing address: {billing_address.get('addressee', 'Unknown')}"
                )

        # Extract shipping address (often minimal)
        shipping_match = re.search(
            r"<tranPurch:shippingAddress[^>]*>(.*?)</tranPurch:shippingAddress>",
            xml_text,
            re.DOTALL,
        )

        if shipping_match:
            shipping_address = self._parse_address_fields(shipping_match.group(1))
            if shipping_address:
                addresses["shipping"] = shipping_address
                logger.info(
                    f"Found shipping address: {shipping_address.get('addressee', 'Unknown')}"
                )

        # Use billing address as primary vendor address (more complete)
        if "billing" in addresses:
            addresses["vendor_primary"] = addresses["billing"]
            logger.info("Using billing address as primary vendor address")
        elif "shipping" in addresses:
            addresses["vendor_primary"] = addresses["shipping"]
            logger.info("Using shipping address as vendor address (no billing found)")

        return addresses

    def _parse_address_fields(self, address_xml: str) -> Optional[Dict[str, Any]]:
        """Parse individual address fields from XML block."""
        address = {}

        field_patterns = {
            "country": r"<platformCommon:country>([^<]*)</platformCommon:country>",
            "addressee": r"<platformCommon:addressee>([^<]*)</platformCommon:addressee>",
            "addr1": r"<platformCommon:addr1>([^<]*)</platformCommon:addr1>",
            "addr2": r"<platformCommon:addr2>([^<]*)</platformCommon:addr2>",
            "addr3": r"<platformCommon:addr3>([^<]*)</platformCommon:addr3>",
            "city": r"<platformCommon:city>([^<]*)</platformCommon:city>",
            "state": r"<platformCommon:state>([^<]*)</platformCommon:state>",
            "zip": r"<platformCommon:zip>([^<]*)</platformCommon:zip>",
            "attention": r"<platformCommon:attention>([^<]*)</platformCommon:attention>",
            "addrText": r"<platformCommon:addrText>([^<]*)</platformCommon:addrText>",
        }

        for field_name, pattern in field_patterns.items():
            match = re.search(pattern, address_xml, re.DOTALL)
            if match and match.group(1).strip():
                address[field_name] = match.group(1).strip()

        return address if address else None

    def extract_header_fields(self, xml_text: str) -> Dict[str, Any]:
        """Extract all header-level fields."""
        header_fields = {}

        patterns = {
            "tranId": r"<tranPurch:tranId>([^<]*)</tranPurch:tranId>",
            "memo": r"<tranPurch:memo>([^<]*)</tranPurch:memo>",
            "status": r"<tranPurch:status>([^<]*)</tranPurch:status>",
            "total": r"<tranPurch:total>([^<]*)</tranPurch:total>",
            "email": r"<tranPurch:email>([^<]*)</tranPurch:email>",
            "currencyName": r"<tranPurch:currencyName>([^<]*)</tranPurch:currencyName>",
            "exchangeRate": r"<tranPurch:exchangeRate>([^<]*)</tranPurch:exchangeRate>",
            "createdDate": r"<tranPurch:createdDate>([^<]*)</tranPurch:createdDate>",
            "lastModifiedDate": r"<tranPurch:lastModifiedDate>([^<]*)</tranPurch:lastModifiedDate>",
            "tranDate": r"<tranPurch:tranDate>([^<]*)</tranPurch:tranDate>",
            "paymentTerms": r"<tranPurch:terms[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>",
            "location": r"<tranPurch:location[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>",
            "subsidiary": r"<tranPurch:subsidiary[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>",
            "department": r"<tranPurch:department[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>",
        }

        for field_name, pattern in patterns.items():
            match = re.search(pattern, xml_text, re.DOTALL)
            if match and match.group(1).strip():
                header_fields[field_name] = match.group(1).strip()

        # Extract custom header fields
        custom_fields = self._extract_custom_fields(xml_text, header_level=True)

        logger.info(
            f"Extracted {len(header_fields)} header fields and {len(custom_fields)} custom fields"
        )

        return {"standard": header_fields, "custom": custom_fields}

    def _extract_custom_fields(
        self, xml_text: str, header_level: bool = False
    ) -> Dict[str, Any]:
        """Extract custom fields from XML."""
        custom_fields = {}

        # Find all custom field lists
        all_custom_lists = re.findall(
            r"<tranPurch:customFieldList[^>]*>(.*?)</tranPurch:customFieldList>",
            xml_text,
            re.DOTALL,
        )

        if not all_custom_lists:
            return custom_fields

        # For header level, use the last custom field list
        custom_content = all_custom_lists[-1] if header_level else all_custom_lists[0]

        # Extract value-based custom fields
        value_matches = re.findall(
            r'scriptId="([^"]+)"[^>]*>.*?<platformCore:value[^>]*>([^<]*)</platformCore:value>',
            custom_content,
            re.DOTALL,
        )

        for field_name, value in value_matches:
            if value.strip():
                custom_fields[field_name] = value.strip()

        # Extract name-based custom fields (for select fields)
        name_matches = re.findall(
            r'scriptId="([^"]+)"[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>',
            custom_content,
            re.DOTALL,
        )

        for field_name, value in name_matches:
            if value.strip() and field_name not in custom_fields:
                custom_fields[field_name] = value.strip()

        return custom_fields

    def extract_line_items(self, xml_text: str) -> List[Dict[str, Any]]:
        """Extract all line items with complete field data."""
        line_items = []

        # Find the item list
        itemlist_match = re.search(
            r"<tranPurch:itemList>(.*?)</tranPurch:itemList>", xml_text, re.DOTALL
        )

        if not itemlist_match:
            logger.warning("No line items found")
            return line_items

        itemlist_content = itemlist_match.group(1)

        # Find all item start positions
        item_starts = []
        for match in re.finditer(r"<tranPurch:item>", itemlist_content):
            item_starts.append(match.start())

        # Process each item
        for i in range(len(item_starts)):
            start_pos = item_starts[i]
            end_pos = (
                item_starts[i + 1]
                if i + 1 < len(item_starts)
                else len(itemlist_content)
            )

            item_content = itemlist_content[start_pos:end_pos]
            line_item = self._extract_single_line_item(item_content, i + 1)

            if line_item:
                line_items.append(line_item)

        logger.info(f"Extracted {len(line_items)} line items")
        return line_items

    def _extract_single_line_item(
        self, item_content: str, line_number: int
    ) -> Dict[str, Any]:
        """Extract data from a single line item."""
        line_item = {"line_number": line_number, "fields": {}, "custom_fields": {}}

        # Extract item reference
        item_ref_match = re.search(
            r'<tranPurch:item[^>]*internalId="([^"]*)"[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>',
            item_content,
            re.DOTALL,
        )

        if item_ref_match:
            line_item["item_internal_id"] = item_ref_match.group(1)
            line_item["item_name"] = item_ref_match.group(2)

        # Extract standard fields
        field_patterns = {
            "line": r"<tranPurch:line>([^<]*)</tranPurch:line>",
            "vendorName": r"<tranPurch:vendorName>([^<]*)</tranPurch:vendorName>",
            "description": r"<tranPurch:description>([^<]*)</tranPurch:description>",
            "quantity": r"<tranPurch:quantity>([^<]*)</tranPurch:quantity>",
            "rate": r"<tranPurch:rate>([^<]*)</tranPurch:rate>",
            "amount": r"<tranPurch:amount>([^<]*)</tranPurch:amount>",
            "expectedReceiptDate": r"<tranPurch:expectedReceiptDate>([^<]*)</tranPurch:expectedReceiptDate>",
            "units": r"<tranPurch:units[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>",
            "department": r"<tranPurch:department[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>",
        }

        for field_name, pattern in field_patterns.items():
            match = re.search(pattern, item_content, re.DOTALL)
            if match and match.group(1).strip():
                line_item["fields"][field_name] = match.group(1).strip()

        # Extract custom fields for this line
        custom_field_match = re.search(
            r"<tranPurch:customFieldList[^>]*>(.*?)</tranPurch:customFieldList>",
            item_content,
            re.DOTALL,
        )

        if custom_field_match:
            line_item["custom_fields"] = self._extract_custom_fields(
                f"<tranPurch:customFieldList>{custom_field_match.group(1)}</tranPurch:customFieldList>",
                header_level=False,
            )

        return line_item

    def extract_complete_po(self, po_number: str) -> Optional[Dict[str, Any]]:
        """
        Main method: Extract complete PO data from NetSuite.

        Args:
            po_number: Purchase order number (e.g., 'PO431')

        Returns:
            Complete extraction result with all fields and RPA-compatible format
        """
        logger.info(f"Starting complete PO extraction for: {po_number}")

        # Test connection
        if not self.test_connection():
            logger.error("Cannot proceed without NetSuite connection")
            return None

        # Search for PO
        internal_id = self.search_po_by_number(po_number)
        if not internal_id:
            logger.error(f"PO {po_number} not found in NetSuite")
            return None

        # Get PO XML
        xml_text = self.get_po_xml(internal_id)
        if not xml_text:
            logger.error("Failed to retrieve PO XML")
            return None

        # Extract all data
        try:
            # Extract vendor info
            vendor_data = self.extract_vendor_info(xml_text)

            # Extract addresses (prioritizing billing for completeness)
            addresses = self.extract_addresses(xml_text)

            # Extract header fields
            header_data = self.extract_header_fields(xml_text)

            # Extract line items
            line_items = self.extract_line_items(xml_text)

            # Build complete result
            result = {
                "metadata": {
                    "po_number": po_number,
                    "internal_id": internal_id,
                    "extraction_time": datetime.now().isoformat(),
                    "extractor_version": "1.0.0",
                    "account_id": self.config["account_id"],
                },
                "vendor": vendor_data,
                "addresses": addresses,
                "header": header_data,
                "line_items": line_items,
                "summary": self._generate_summary(
                    vendor_data, addresses, header_data, line_items
                ),
            }

            # Add RPA-compatible format
            result["rpa_format"] = self._convert_to_rpa_format(result)

            # Add Didero model mapping
            result["didero_mapping"] = self._map_to_didero_models(result)

            logger.info(f"✅ Successfully extracted PO {po_number}")
            return result

        except Exception as e:
            logger.error(f"Extraction failed: {e}", exc_info=True)
            return None

    def _generate_summary(
        self, vendor: Dict, addresses: Dict, header: Dict, line_items: List
    ) -> Dict[str, Any]:
        """Generate extraction summary for validation."""
        return {
            "vendor_found": bool(vendor.get("name")),
            "vendor_name": vendor.get("name"),
            "has_billing_address": "billing" in addresses,
            "has_shipping_address": "shipping" in addresses,
            "primary_vendor_address": bool(addresses.get("vendor_primary")),
            "total_amount": header["standard"].get("total"),
            "currency": header["standard"].get("currencyName", "USD"),
            "line_count": len(line_items),
            "has_tracking_number": "custbody_ionq_tracking_number"
            in header.get("custom", {}),
            "tracking_number": header.get("custom", {}).get(
                "custbody_ionq_tracking_number"
            ),
            "all_items_have_dates": all(
                "expectedReceiptDate" in item.get("fields", {}) for item in line_items
            ),
        }

    def _convert_to_rpa_format(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Convert to RPA/Stagehand-compatible format."""
        vendor_address = result["addresses"].get("vendor_primary", {})

        # Build vendor address in RPA format
        rpa_vendor_address = {
            "company": result["vendor"].get("name", ""),
            "address1": vendor_address.get("addr1", ""),
            "address2": vendor_address.get("addr2", ""),
            "city": vendor_address.get("city", ""),
            "state": vendor_address.get("state", ""),
            "zip": vendor_address.get("zip", ""),
            "country": vendor_address.get("country", ""),
            "addressee": vendor_address.get("addressee", ""),
            "full_address": vendor_address.get("addrText", ""),
        }

        # Build PO data
        po_data = {
            **result["header"]["standard"],
            **result["header"]["custom"],
            "vendor": result["vendor"].get("name", ""),
            "vendor_address": rpa_vendor_address,
            "ship_to": result["addresses"].get("shipping", {}).get("addrText", ""),
        }

        # Convert line items
        line_items_data = []
        for item in result["line_items"]:
            line_item = {
                "item_number": item.get("item_name", ""),
                "description": item["fields"].get("description", ""),
                "quantity": float(item["fields"].get("quantity", "0")),
                "unit_price_0": item["fields"].get("rate", "0"),
                "unit_price_currency": result["header"]["standard"].get(
                    "currencyName", "USD"
                ),
                "vendor_name": item["fields"].get("vendorName", ""),
                **item.get("custom_fields", {}),
            }
            line_items_data.append(line_item)

        return {"purchase_order": po_data, "line_items": line_items_data}

    def _map_to_didero_models(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Map extracted data to Didero model fields."""
        header = result["header"]["standard"]
        custom = result["header"]["custom"]

        # Map to PurchaseOrder model
        po_mapping = {
            "po_number": header.get("tranId"),
            "total_cost": Decimal(header.get("total", "0")),
            "placement_time": header.get("tranDate"),
            "requested_date": None,  # Will be set from line items
            "internal_notes": header.get("memo", ""),
            "payment_terms": header.get("paymentTerms"),
            "metadata": {
                "netsuite_internal_id": result["metadata"]["internal_id"],
                "currency": header.get("currencyName", "USD"),
                "location": header.get("location"),
                "subsidiary": header.get("subsidiary"),
                "department": header.get("department"),
                "custom_fields": custom,
                "extraction_metadata": result["metadata"],
            },
        }

        # Set requested_date from first line item's expectedReceiptDate
        if result["line_items"] and result["line_items"][0]["fields"].get(
            "expectedReceiptDate"
        ):
            po_mapping["requested_date"] = result["line_items"][0]["fields"][
                "expectedReceiptDate"
            ]

        # Map line items to OrderItem model
        line_items_mapping = []
        for item in result["line_items"]:
            fields = item["fields"]
            item_mapping = {
                "item_number": item.get("item_name", ""),
                "description": fields.get("description", ""),
                "quantity": float(fields.get("quantity", "0")),
                "price": Decimal(fields.get("rate", "0")),
                "unit_of_measure": fields.get("units", "Each"),
                "metadata": {
                    "netsuite_line_number": int(fields.get("line", "0")),
                    "netsuite_item_internal_id": item.get("item_internal_id"),
                    "vendor_item_name": fields.get("vendorName", ""),
                    "expected_receipt_date": fields.get("expectedReceiptDate"),
                    "department": fields.get("department"),
                    "custom_fields": item.get("custom_fields", {}),
                },
            }
            line_items_mapping.append(item_mapping)

        return {
            "purchase_order": po_mapping,
            "line_items": line_items_mapping,
            "vendor_address": result["addresses"].get("vendor_primary", {}),
        }

    def save_extraction_result(
        self, result: Dict[str, Any], output_dir: str = "."
    ) -> str:
        """Save extraction result to JSON file."""
        if not result:
            return None

        po_number = result["metadata"]["po_number"]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{output_dir}/IONQ_PO_{po_number}_complete_{timestamp}.json"

        with open(filename, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False, default=str)

        logger.info(f"Saved extraction result to: {filename}")
        return filename


def main():
    """Example usage and testing."""
    # IonQ Sandbox credentials (replace with actual)
    credentials = {
        "account_id": "7581852_SB1",
        "consumer_key": "****************************************************************",
        "consumer_secret": "****************************************************************",
        "token_id": "5ec4bd683099249601f69808f2455a804e70b480c0801ea987a14b72f793e7c8",
        "token_secret": "87e6863c2192bf8c53f410e0271bc6ed245d94651e66ed1fe8f9a40dbf6c7272",
    }

    # Initialize extractor
    extractor = IonQNetSuiteExtractor(**credentials)

    # Extract PO
    po_number = "PO431"
    result = extractor.extract_complete_po(po_number)

    if result:
        # Save result
        filename = extractor.save_extraction_result(result)

        # Print summary
        summary = result["summary"]
        print("\n" + "=" * 80)
        print(f"✅ EXTRACTION COMPLETE: {po_number}")
        print("=" * 80)
        print(f"Vendor: {summary['vendor_name']}")
        print(f"Total: {summary['total_amount']} {summary['currency']}")
        print(f"Line Items: {summary['line_count']}")
        print(f"Primary Address: {'✅' if summary['primary_vendor_address'] else '❌'}")
        print(f"Tracking Number: {summary.get('tracking_number', 'N/A')}")
        print(
            f"All Items Have Dates: {'✅' if summary['all_items_have_dates'] else '❌'}"
        )
        print(f"\nSaved to: {filename}")
    else:
        print(f"\n❌ Failed to extract PO {po_number}")


if __name__ == "__main__":
    main()
