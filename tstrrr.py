#!/usr/bin/env python
"""
Test script for the new core PO creation workflow.

This script:
1. Creates a test email with PO details
2. Directly triggers the new POCreationWorkflow via Temporal
3. Monitors the workflow execution
4. Verifies the results

Usage:
    uv run python tstrrr_create_new.py
"""

import asyncio
import json
import os
import sys
import time
import uuid
from datetime import datetime

# Setup Django first before importing any Django models
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")

try:
    import django

    django.setup()
except ImportError:
    print(
        "Django not imported properly, make sure you're running this with 'uv run python tstrrr_create_new.py'"
    )
    sys.exit(1)

# Now import Django models
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone

from didero.addresses.models import Address
from didero.emails.models import EmailThread, EmailThreadToPurchaseOrderLink
from didero.orders.models import PurchaseOrder
from didero.suppliers.models import Communication, Supplier
from didero.tasks.models import Task
from didero.users.models.team_models import Team
from didero.workflows.models import UserWorkflow, WorkflowBehaviorConfig

# Import Temporal client
from temporalio.client import Client

# Configuration
TEAM_ID = 2  # Using Team ID 2 (Didero)
WAIT_TIME = 30  # 30 seconds wait time should be sufficient for new workflow
TEMPORAL_HOST = os.environ.get("TEMPORAL_HOST", "localhost:7233")

print("=" * 80)
print("NEW PO CREATION WORKFLOW TEST (Core Workflow)")
print("=" * 80)

# Get the team
team = Team.objects.get(id=TEAM_ID)
print(f"\nUsing team: {team.name} (ID: {team.id})")

# Generate a unique website URL to avoid unique constraint errors
unique_website = f"https://testsupplier-{uuid.uuid4().hex[:8]}.example.com"

# Get or create a test supplier
try:
    supplier = Supplier.objects.get(name="Test Supplier Inc.", team=team)
    created = False
    print(f"Found existing supplier: {supplier.name} (ID: {supplier.id})")
except Supplier.DoesNotExist:
    supplier = Supplier.objects.create(
        name="Test Supplier Inc.",
        team=team,
        website_url=unique_website,
        description="Test supplier for automated testing",
    )
    created = True
    print(f"Created new supplier: {supplier.name} (ID: {supplier.id})")

# Create a default address for the supplier if none exists
supplier_address = Address.objects.filter(supplier=supplier).first()
if not supplier_address:
    print("Creating default address for supplier")
    supplier_address = Address.objects.create(
        supplier=supplier,
        team=team,
        line_1="123 Test Street",
        city="New York",
        state_or_province="NY",
        postal_code="10001",
        country="US",
        is_default=True,
    )
    print(f"Created supplier address: {supplier_address}")
else:
    print(f"Using existing supplier address: {supplier_address}")

# --- Cleanup existing test POs ---
print("\n" + "-" * 40)
print("CLEANING UP PREVIOUS TEST POS")
print("-" * 40)
# Delete POs with the test number
po_number = f"TEST-PO-{uuid.uuid4().hex[:6]}"
deleted_pos, _ = PurchaseOrder.objects.filter(
    po_number__startswith="TEST-PO-", team=team
).delete()
print(f"Deleted {deleted_pos} existing test POs")
# --- End Cleanup ---

# Create a test email with PO details
email_body = f"""
Purchase Order: {po_number}
Date: {timezone.now().strftime("%Y-%m-%d")}

SUPPLIER ADDRESS:
Test Supplier Inc.
123 Test Street
New York, NY 10001
USA

Ship To ADDRESS:
Bob the builder
456 Main Avenue
Floor 12
New York, NY 10002
USA

Items:
1. Item Number: TEST-ITEM-1
   Description: Test Product
   Quantity: 5
   Unit Price: $100.00
   Total: $500.00

2. Item Number: TEST-ITEM-2
   Description: Another Test Product
   Quantity: 2
   Unit Price: $150.00
   Total: $300.00

Subtotal: $800.00
Tax: $64.00
Shipping: $35.00
Total: $899.00

Payment Terms: Net 30
Shipping Method: Ground
Notes: This is a test order.

Frieght Terms: FOB Destination

"""

print("\n" + "-" * 40)
print("CREATING TEST EMAIL WITH PO DETAILS")
print("-" * 40)

# Create email thread
email_thread = EmailThread.objects.create(team=team, thread_id=f"thread-{uuid.uuid4()}")

# Create the email with PO details
email = Communication.objects.create(
    team=team,
    supplier=supplier,
    email_thread=email_thread,
    email_subject=f"Purchase Order {po_number}",
    email_content=email_body,
    direction="INBOUND",
    email_from="<EMAIL>",
    email_message_id=f"test-{uuid.uuid4()}@example.com",
    comm_time=timezone.now(),
)

print(f"Created test email with ID: {email.pk}")
print(f"Email subject: {email.email_subject}")
print(f"Email direction: {email.direction}")

print("\n" + "-" * 40)
print("SETTING UP WORKFLOW CONFIGURATION")
print("-" * 40)

# Create or get a UserWorkflow for testing
# First check what workflow types are available
from didero.workflows.schemas import WorkflowType, WorkflowTrigger

workflow, created = UserWorkflow.objects.get_or_create(
    workflow_type=WorkflowType.PURCHASE_ORDER_CREATION.value,
    trigger=WorkflowTrigger.ON_PURCHASE_ORDER_EMAIL_RECEIVED.value,
    team=team,
    defaults={
        "current_snapshot": None,  # Core workflows don't need DAG representation
    },
)

if created:
    print(f"Created new UserWorkflow with ID: {workflow.id}")
else:
    print(f"Using existing UserWorkflow with ID: {workflow.id}")

# Create or update behavior config
behavior_config, created = WorkflowBehaviorConfig.objects.get_or_create(
    workflow=workflow,
    defaults={
        "config": {
            "enabled": True,
            "require_human_validation": False,  # Direct creation for testing
            "max_retry_attempts": 3,
            "retry_backoff_seconds": 10,
        }
    },
)

if not created:
    # Update the config to ensure human validation is disabled for testing
    behavior_config.config["require_human_validation"] = False
    behavior_config.config["enabled"] = True
    behavior_config.save()

print(
    f"Behavior config set: require_human_validation = {behavior_config.config.get('require_human_validation', False)}"
)

print("\n" + "-" * 40)
print("TRIGGERING NEW PO CREATION WORKFLOW")
print("-" * 40)


async def run_workflow():
    """Run the workflow using Temporal client"""
    # Connect to Temporal
    client = await Client.connect(TEMPORAL_HOST)

    # Prepare workflow parameters
    workflow_id = f"po-creation-test-{uuid.uuid4().hex[:8]}"
    params = {
        "email_id": str(email.pk),
        "team_id": str(team.id),
        "workflow_id": str(workflow.id),
    }

    print(f"Starting workflow with ID: {workflow_id}")
    print(f"Parameters: {json.dumps(params, indent=2)}")

    # Import the workflow class
    from didero.workflows.core_workflows.po_creation.workflow import POCreationWorkflow

    # Start the workflow
    handle = await client.start_workflow(
        POCreationWorkflow.run,
        args=[str(workflow.id), params],
        id=workflow_id,
        task_queue="user_workflows",  # Use the queue the worker is listening on
    )

    print(f"Workflow started with Temporal ID: {handle.id}")

    # Wait for the workflow to complete
    print("Waiting for workflow to complete...")
    result = await handle.result()

    print("\nWorkflow completed!")
    print(f"Result: {json.dumps(result, indent=2)}")

    return result


# Run the workflow
print("\nExecuting workflow...")
workflow_result = asyncio.run(run_workflow())

print("\n" + "-" * 40)
print("CHECKING WORKFLOW RESULTS")
print("-" * 40)

# Check if a PO was created
pos = PurchaseOrder.objects.filter(po_number=po_number, team=team)
if pos.exists():
    po = pos.first()

    print(f"\nPO created with ID: {po.pk}")
    print(f"PO number: {po.po_number}")
    print(f"Supplier: {po.supplier.name}")
    print(f"Order Status: {po.order_status}")
    print(f"Items count: {po.items.count()}")
    print(f"Source: {po.source}")

    # Check items
    print("\nItems:")
    for item in po.items.all():
        print(f"- {item.item.item_number}: {item.item.description}")
        print(f"  Quantity: {item.quantity}, Price: {item.price}")

    # Check line items (tax, shipping)
    print("\nLine Items:")
    if hasattr(po, "line_items"):
        for line_item in po.line_items.all():
            print(f"- {line_item.category}: {line_item.amount}")
    else:
        print("  (Could not access line_items attribute)")

    # Check if email is linked
    links = EmailThreadToPurchaseOrderLink.objects.filter(purchase_order=po)
    if links.exists():
        print("\nEmail is linked to the PO")
    else:
        print("\nEmail is NOT linked to the PO")

    # Check for associated task
    # Since human validation is disabled, check for success notification task
    tasks = Task.objects.filter(
        model_type=ContentType.objects.get_for_model(PurchaseOrder),
        model_id=str(po.pk),
        task_type_v2__name__in=[
            "PO_CREATION_CONFIRMATION",
            "PO_CREATION_WORKFLOW_SUCCESS_NOTIFICATION",
        ],
    ).order_by("-created_at")

    if tasks.exists():
        task = tasks.first()
        print("\nAssociated Task:")
        print(f"- Task ID: {task.pk}")
        print(f"- Task Type: {task.task_type_v2.name}")
        print(f"- Status: {task.status}")
        print(f"- Assigned User: {task.user.email}")

        # Print the actions
        actions = task.actions.all()
        if actions:
            print("\nTask Actions:")
            for action in actions:
                print(
                    f"- Action Type: {action.action_type.name if action.action_type else 'Unknown'}"
                )
                print(f"  Status: {action.status}")
                print(f"  Button Type: {action.button_type}")
                print("")
        else:
            print("\nNo actions found for this task.")
    else:
        print("\nNo associated task found.")
else:
    print(f"\nNo PO found with number {po_number}")

print("\n" + "-" * 40)
print("WORKFLOW EXECUTION SUMMARY")
print("-" * 40)

if workflow_result:
    print("Workflow execution result:")
    print(f"- Success: {workflow_result.get('success', False)}")
    if workflow_result.get("success"):
        print(f"- PO ID: {workflow_result.get('po_id', 'N/A')}")
        print(f"- PO Number: {workflow_result.get('po_number', 'N/A')}")
        print(f"- Created in Draft: {workflow_result.get('created_in_draft', False)}")
        print(f"- Task ID: {workflow_result.get('task_id', 'N/A')}")
    else:
        print(f"- Error: {workflow_result.get('error', 'Unknown error')}")
        if workflow_result.get("duplicate"):
            print("- Duplicate PO detected")

print("\n" + "=" * 80)
print("TEST COMPLETED")
print("=" * 80)
