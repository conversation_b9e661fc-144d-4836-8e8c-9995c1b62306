# IonQ NetSuite Integration - Product Requirements Document (PRD)

## Table of Contents
1. [Executive Summary](#executive-summary)
2. [Product Vision & Goals](#product-vision--goals)
3. [Architecture Overview](#architecture-overview)
4. [Implementation Components](#implementation-components)
5. [Technical Deep Dive](#technical-deep-dive)
6. [Integration Patterns](#integration-patterns)
7. [Deployment Strategy](#deployment-strategy)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Future Enhancements](#future-enhancements)
10. [Success Metrics](#success-metrics)
11. [Open Questions & Assumptions](#open-questions--assumptions)

---

## Executive Summary

This PRD defines the comprehensive ERP integration system for Didero, extending the existing `didero/integrations/erp/` infrastructure to enable full PO extraction and creation. The solution focuses on IonQ's NetSuite implementation, creating POs directly in Django models while maintaining the ability to update only specific custom fields back to NetSuite. AI parsing is used selectively only for minimal shipping address data.

### Key Achievements
- ✅ Leveraging existing `didero/integrations/erp/` infrastructure - no new framework needed
- ✅ Direct PO creation in Django models from NetSuite data
- ✅ Intelligent field mapping based on production test outputs
- ✅ AI parsing only where needed (shipping addresses)
- ✅ Maintaining existing update capability for 3 custom fields

### Product Impact
- **Direct Model Creation**: POs created directly in Django models, no intermediate formats
- **Selective AI Usage**: AI parsing only for minimal shipping address data
- **Field Updates**: Continue updating only tracking_number, estimated_delivery_date, promised_ship_date
- **Infrastructure Reuse**: Build on existing ERPClientBase and NetSuiteClient
- **Team Configuration**: Use existing ERPIntegrationConfig model

---

## Product Vision & Goals

### Vision Statement
Extend the existing ERP integration infrastructure in `didero/integrations/erp/` to enable full PO extraction and creation directly in Django models, while maintaining targeted field updates back to NetSuite. Focus on leveraging what's already built rather than creating new frameworks.

### Primary Goals
1. **Full PO Extraction**: Extract complete PO data from NetSuite using existing infrastructure
2. **Direct Model Creation**: Create POs directly in Django models, no intermediate formats
3. **Selective Field Updates**: Continue updating only 3 custom fields back to NetSuite
4. **Smart Address Handling**: Use AI parsing only for minimal shipping addresses
5. **Infrastructure Reuse**: Build on existing ERPClientBase, NetSuiteClient, and field mappers

### Success Criteria
- IonQ can extract full POs from NetSuite and create them in Didero
- AI parsing used only for shipping addresses (not billing addresses)
- Updates to NetSuite limited to: tracking_number, estimated_delivery_date, promised_ship_date
- Direct mapping from NetSuite fields to Django models
- Zero new infrastructure components - only extensions

### Critical Files and Locations

| Component | Path | Purpose | Status |
|-----------|------|---------|--------|
| **Test Extractor** | `ionq_netsuite_extractor_production.py` | Reference implementation showing field extraction patterns | ✅ Complete |
| **Test Output** | `test_ionq_production_extractor.py` | Shows NetSuite→Didero field mapping | ✅ Complete |
| **ERP Base Class** | `didero/integrations/erp/clients/base.py` | ERPClientBase interface | ✅ Existing |
| **NetSuite Client** | `didero/integrations/erp/clients/netsuite.py` | NetSuite implementation to extend | ✅ Existing |
| **Field Mapper** | `didero/integrations/erp/field_mapper.py` | NetSuiteFieldMapper for transformations | ✅ Existing |
| **IonQ Constants** | `didero/integrations/erp/customers/ionq/constants.py` | Field mappings for IonQ | ✅ Existing |
| **Config Model** | `didero/integrations/models.py` | ERPIntegrationConfig for team settings | ✅ Existing |
| **AI Parsers** | `didero/ai/` | Address parsing for shipping addresses only | ✅ Existing |

---

## Architecture Overview

### Leveraging Existing ERP Infrastructure

The solution builds upon the **existing modular ERP integration framework** in `didero/integrations/erp/` which already provides:

#### Core Components Already Available:
- ✅ **ERPClientBase**: Abstract base class defining the ERP interface
- ✅ **NetSuiteClient**: Full SOAP API implementation with OAuth 1.0a
- ✅ **Field Mapping System**: Dynamic field mapping between Didero and ERP fields
- ✅ **Registry Pattern**: Factory for creating ERP clients
- ✅ **ERPIntegrationConfig Model**: Team-based configuration storage
- ✅ **Credential Management**: Environment-based credential provider
- ✅ **IonQ Field Mappings**: Custom field definitions in `customers/ionq/constants.py`

#### What We Need to Add:
- 🔧 **PO Extraction Method**: Add `get_complete_purchase_order()` to NetSuiteClient
- 🔧 **PO Creation Method**: Add `create_purchase_order_in_didero()` for Django model creation
- 🔧 **Address Parser Integration**: Use AI parsing only for shipping addresses
- 🔧 **Workflow Activities**: Create activities that use the ERP framework directly

### Enhanced System Architecture

```mermaid
graph TB
    subgraph "Existing ERP Framework"
        ERPBASE[ERPClientBase<br/>✅ Already exists]
        NSCLIENT[NetSuiteClient<br/>✅ Exists - needs extension]
        MAPPER[NetSuiteFieldMapper<br/>✅ Already exists]
        REGISTRY[ERP Registry<br/>✅ Already exists]
        CONFIG[ERPIntegrationConfig<br/>✅ DB Model exists]
        CREDS[SimpleEnvCredentialProvider<br/>✅ Already exists]
    end
    
    subgraph "New Methods"
        EXTRACT[get_complete_purchase_order()<br/>🔧 Extract full PO]
        POCREATE[create_purchase_order_in_didero()<br/>🔧 Create Django models]
        AIPARSE[parse_shipping_address()<br/>🔧 AI for shipping only]
    end
    
    subgraph "Django Models"
        PO[PurchaseOrder Model<br/>✅ Existing]
        OI[OrderItem Model<br/>✅ Existing]
        VENDOR[Vendor Model<br/>✅ Existing]
        ADDR[Address Model<br/>✅ Existing]
    end
    
    subgraph "Workflow Integration"
        WF[Temporal Workflows<br/>✅ Existing]
        ACTIVITY[extract_and_create_po<br/>🔧 New activity]
        UPDATE[update_netsuite_fields<br/>🔧 Updates 3 fields only]
    end
    
    NSCLIENT --> EXTRACT
    EXTRACT --> POCREATE
    POCREATE --> AIPARSE
    
    POCREATE --> PO
    POCREATE --> OI
    POCREATE --> VENDOR
    POCREATE --> ADDR
    
    ACTIVITY --> EXTRACT
    WF --> ACTIVITY
    WF --> UPDATE
```

### Data Flow Using Existing Infrastructure

1. **Configuration Check** (Using existing models):
   ```python
   # Check if team has ERP configured
   erp_config = ERPIntegrationConfig.objects.filter(
       team_id=team_id, 
       enabled=True
   ).first()
   ```

2. **Client Creation** (Using existing registry):
   ```python
   # Get credentials from existing provider
   cred_provider = SimpleEnvCredentialProvider()
   credentials = cred_provider.get_credentials("173", ERPType.NETSUITE)
   
   # Create client using existing registry
   client = ERPClientRegistry.create_client(
       erp_type=ERPType.NETSUITE,
       credentials=credentials,
       config=erp_config.config,
       field_mappings=erp_config.field_mappings
   )
   ```

3. **PO Extraction** (New method on existing client):
   ```python
   # Add method to existing NetSuiteClient
   po_data = client.get_complete_purchase_order(po_number)
   ```

4. **Direct Model Creation** (No intermediate format):
   ```python
   # Create Django models directly from NetSuite data
   po = create_purchase_order_in_didero(po_data, team_id)
   
   # Use AI only for shipping address
   if po_data['addresses']['shipping']['minimal']:
       parsed_address = parse_shipping_address(po_data['addresses']['shipping']['addrText'])
       po.shipping_address = parsed_address
   ```

5. **Update NetSuite** (Only 3 fields):
   ```python
   # Update only specific fields back to NetSuite
   client.update_purchase_order(po_number, {
       'custbody_ionq_tracking_number': tracking_number,
       'expectedreceiptdate': delivery_date,
       'custcol_ionq_supplierpromisedatefield': ship_date
   })
   ```

### Key Design Benefits

1. **Direct Model Creation**: No intermediate formats - straight from NetSuite to Django models
2. **Selective AI Usage**: AI parsing only for minimal shipping addresses
3. **Limited NetSuite Updates**: Only update 3 specific fields, not entire PO
4. **Infrastructure Reuse**: Leverage all existing ERP framework components
5. **Clear Field Mapping**: Use test outputs to guide NetSuite→Didero mapping
6. **Team Configuration**: Existing ERPIntegrationConfig handles all settings

---

## Implementation Components

### 1. Extending the Existing NetSuiteClient

**Location**: `didero/integrations/erp/clients/netsuite.py`

**New Methods to Add**:
```python
class NetSuiteClient(ERPClientBase):
    # Existing methods remain unchanged
    
    def get_complete_purchase_order(self, po_number: str) -> Dict[str, Any]:
        """
        Extract complete PO data including header, vendor, addresses, and line items.
        Based on patterns from ionq_netsuite_extractor_production.py
        
        Returns:
            Dict containing full PO data matching test output structure
        """
        # Use patterns from ionq_netsuite_extractor_production.py:
        # - extract_vendor_info() 
        # - extract_addresses() with billing priority
        # - extract_header_fields() for standard and custom fields
        # - extract_line_items() with all details
```

**New Creation Function**:
```python
# didero/integrations/erp/po_creator.py
def create_purchase_order_in_didero(po_data: Dict[str, Any], team_id: int) -> PurchaseOrder:
    """
    Create PO directly in Django models from NetSuite data.
    
    Uses mapping from test_ionq_production_extractor.py:
    - po_data['header']['standard']['tranId'] → po.po_number
    - po_data['header']['standard']['total'] → po.total_cost
    - po_data['vendor']['name'] → vendor lookup/creation
    - po_data['addresses']['billing'] → vendor address (complete)
    - po_data['addresses']['shipping']['addrText'] → AI parsing needed
    """
    # Direct model creation - no RPA format needed
```

### 2. Integration Activity Using Existing Framework

**New Activity**: `didero/workflows/activities/erp_activities.py`

```python
from didero.integrations.erp.credentials import SimpleEnvCredentialProvider
from didero.integrations.erp.registry import ERPClientRegistry
from didero.integrations.erp.schemas import ERPType
from didero.integrations.models import ERPIntegrationConfig
from didero.ai.address_parser import parse_address  # For shipping only

@activity.defn(name="extract_and_create_po")
async def extract_and_create_po(
    po_number: str,
    team_id: int
) -> Dict[str, Any]:
    """Extract PO from NetSuite and create directly in Django models"""
    
    # Use existing ERPIntegrationConfig model
    erp_config = await sync_to_async(
        ERPIntegrationConfig.objects.filter(
            team_id=team_id,
            enabled=True
        ).first
    )()
    
    if not erp_config:
        return {"success": False, "reason": "No ERP configured"}
    
    try:
        # Use existing infrastructure to create client
        client = await sync_to_async(
            ERPClientRegistry.create_client_for_team
        )(team_id)
        
        # Extract complete PO data
        po_data = await sync_to_async(
            client.get_complete_purchase_order
        )(po_number)
        
        # Create PO directly in Django models
        po = await sync_to_async(
            create_purchase_order_in_didero
        )(po_data, team_id)
        
        # Parse shipping address with AI (only if minimal)
        shipping_data = po_data['addresses'].get('shipping', {})
        if shipping_data and len(shipping_data.get('addrText', '').split('\n')) <= 2:
            # Minimal address - needs AI parsing
            parsed_address = await parse_address(shipping_data['addrText'])
            await sync_to_async(po.set_shipping_address)(parsed_address)
        
        return {
            "success": True,
            "po_id": str(po.id),
            "po_number": po.po_number,
            "vendor": po_data['vendor']['name'],
            "used_ai_for_shipping": bool(shipping_data)
        }
        
    except Exception as e:
        logger.error(f"PO creation failed: {e}")
        await create_task(
            task_type_v2__name="ERP_PO_CREATION_FAILED",
            team_id=team_id,
            metadata={"po_number": po_number, "error": str(e)}
        )
        return {"success": False, "reason": str(e)}
```

### 3. Direct Model Creation Function

**Purpose**: Create Django models directly from NetSuite data

```python
# didero/integrations/erp/po_creator.py
from didero.orders.models import PurchaseOrder, OrderItem, Vendor, Address
from didero.ai.address_parser import parse_address
from decimal import Decimal

async def create_purchase_order_in_didero(po_data: Dict[str, Any], team_id: int) -> PurchaseOrder:
    """
    Create PO directly from NetSuite data based on field mappings.
    
    Field Mapping (from test_ionq_production_extractor.py):
    - header.standard.tranId → po_number
    - header.standard.total → total_cost
    - header.standard.tranDate → placement_time
    - header.standard.memo → internal_notes
    - vendor.name → vendor lookup/creation
    - addresses.billing → vendor address (complete data)
    - addresses.shipping.addrText → needs AI parsing
    """
    
    # Get or create vendor using billing address (complete data)
    vendor_data = po_data['vendor']
    billing_address = po_data['addresses']['billing']
    
    vendor, _ = await sync_to_async(Vendor.objects.get_or_create)(
        team_id=team_id,
        name=vendor_data['name'],
        defaults={
            'external_id': vendor_data['internal_id'],
            'address_line_1': billing_address.get('addr1', ''),
            'city': billing_address.get('city', ''),
            'state': billing_address.get('state', ''),
            'zip_code': billing_address.get('zip', ''),
            'country': billing_address.get('country', 'US')
        }
    )
    
    # Create PO
    header = po_data['header']['standard']
    custom = po_data['header']['custom']
    
    po = await sync_to_async(PurchaseOrder.objects.create)(
        team_id=team_id,
        po_number=header['tranId'],
        vendor=vendor,
        total_cost=Decimal(header.get('total', '0')),
        placement_time=header.get('tranDate'),
        requested_date=po_data['line_items'][0]['fields'].get('expectedReceiptDate') if po_data['line_items'] else None,
        internal_notes=header.get('memo', ''),
        payment_terms=header.get('paymentTerms'),
        metadata={
            'netsuite_internal_id': po_data['metadata']['internal_id'],
            'currency': header.get('currencyName', 'USD'),
            'location': header.get('location'),
            'subsidiary': header.get('subsidiary'),
            'department': header.get('department'),
            'custom_fields': custom
        }
    )
    
    # Create line items
    for item_data in po_data['line_items']:
        fields = item_data['fields']
        await sync_to_async(OrderItem.objects.create)(
            purchase_order=po,
            item_number=item_data.get('item_name', ''),
            description=fields.get('description', ''),
            quantity=float(fields.get('quantity', '0')),
            price=Decimal(fields.get('rate', '0')),
            unit_of_measure=fields.get('units', 'Each'),
            metadata={
                'netsuite_line_number': int(fields.get('line', '0')),
                'netsuite_item_internal_id': item_data.get('item_internal_id'),
                'vendor_item_name': fields.get('vendorName', ''),
                'expected_receipt_date': fields.get('expectedReceiptDate'),
                'custom_fields': item_data.get('custom_fields', {})
            }
        )
    
    return po
```

### 4. Using Existing Field Mappings

**IonQ Constants Already Defined**: `didero/integrations/erp/customers/ionq/constants.py`

```python
# Already defined for IonQ
IONQ_HEADER_FIELDS = ["custbody_ionq_tracking_number"]
IONQ_LINE_FIELDS = ["expectedreceiptdate", "custcol_ionq_supplierpromisedatefield"]

FIELD_MAP = {
    "tracking_number": "custbody_ionq_tracking_number",
    "estimated_delivery_date": "expectedreceiptdate",
    "promised_ship_date": "custcol_ionq_supplierpromisedatefield"
}
```

### 5. Django Admin Configuration

**Using Existing ERPIntegrationConfig Model**:

```python
# Admin already registered in didero/integrations/admin.py
# Just need to create configuration for IonQ team

# Via Django Admin or Management Command:
ERPIntegrationConfig.objects.create(
    team_id=173,  # IonQ's team ID
    erp_type="netsuite",
    enabled=True,
    field_mappings=FIELD_MAP,  # From ionq/constants.py
    config={
        "api_version": "2023_2",
        "auto_create_from_email": True,
        "auto_create_from_missing_po": True
    }
)
```

---

## Technical Deep Dive

### NetSuite API Integration

#### Authentication Flow
```python
# OAuth 1.0a signature generation
base_string = f"{account_id}&{consumer_key}&{token_id}&{nonce}&{timestamp}"
key = f"{consumer_secret}&{token_secret}"
signature = base64.b64encode(
    hmac.new(key.encode('utf-8'), base_string.encode('utf-8'), hashlib.sha256).digest()
).decode('utf-8')
```

#### SOAP Request Structure
```xml
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Header>
        <platformMsgs:tokenPassport>
            <platformCore:account>{account_id}</platformCore:account>
            <platformCore:consumerKey>{consumer_key}</platformCore:consumerKey>
            <platformCore:token>{token_id}</platformCore:token>
            <platformCore:nonce>{nonce}</platformCore:nonce>
            <platformCore:timestamp>{timestamp}</platformCore:timestamp>
            <platformCore:signature algorithm="HMAC-SHA256">{signature}</platformCore:signature>
        </platformMsgs:tokenPassport>
    </soap:Header>
    <soap:Body>
        {operation_specific_body}
    </soap:Body>
</soap:Envelope>
```

### Address Extraction Strategy

#### Problem Discovery
Initial extraction attempts failed because:
1. **Wrong XML Tags**: Searched for `<tranPurch:shipAddress>` instead of `<tranPurch:shippingAddress>`
2. **Missing Vendor Data**: No extraction of entity reference at header level
3. **Incomplete Structure**: Output didn't match RPA expectations

#### Solution Implementation

**Correct XML Patterns**:
```python
# Billing Address (Complete)
billing_match = re.search(
    r'<tranPurch:billingAddress[^>]*>(.*?)</tranPurch:billingAddress>',
    xml_text, re.DOTALL
)

# Shipping Address (Often Minimal)  
shipping_match = re.search(
    r'<tranPurch:shippingAddress[^>]*>(.*?)</tranPurch:shippingAddress>',
    xml_text, re.DOTALL
)
```

**Address Data Examples**:

Billing Address (Complete):
```json
{
  "country": "_unitedStates",
  "addressee": "ThorLabs",
  "addr1": "56 Sparta Avenue",
  "city": "Newton",
  "state": "NJ",
  "zip": "07860",
  "addrText": "ThorLabs\n56 Sparta Avenue\nNewton NJ 07860\nUnited States"
}
```

Shipping Address (Minimal):
```json
{
  "country": "_unitedStates",
  "addressee": "CP Tooling (NI)",
  "addrText": "CP Tooling (NI)\r\nUnited States"
}
```

**Key Insight**: Billing addresses contain complete vendor information while shipping addresses are often just location names, explaining why AI parsing is needed for shipping addresses.

### Custom Field Extraction

NetSuite uses two patterns for custom fields:

1. **Value-based fields** (dates, text, numbers):
```xml
<platformCore:customField scriptId="custbody_ionq_tracking_number">
    <platformCore:value>1Z123456789</platformCore:value>
</platformCore:customField>
```

2. **Select fields** (dropdowns):
```xml
<platformCore:customField scriptId="custcol_ionq_project">
    <platformCore:name>Project Alpha</platformCore:name>
</platformCore:customField>
```

### RPA Format Conversion

The RPA/Stagehand integration expects specific data structures:

```python
# Expected by update_supplier()
vendor_address = {
    "company": "V10072 ThorLabs",
    "address1": "56 Sparta Avenue",
    "city": "Newton",
    "state": "NJ",
    "zip": "07860",
    "full_address": "ThorLabs\n56 Sparta Avenue\nNewton NJ 07860\nUnited States"
}

# Expected by update_shipping_address()
ship_to = "CP Tooling (NI)\r\nUnited States"  # Raw text for AI parsing
```

---

## Integration Patterns

### 1. Integration with Existing ERP Infrastructure

**Step 1: Configure IonQ in Django Admin**

```python
# Via Django Admin or Management Command
from didero.integrations.models import ERPIntegrationConfig
from didero.integrations.erp.customers.ionq.constants import FIELD_MAP

# Create configuration for IonQ
ERPIntegrationConfig.objects.create(
    team_id=173,  # IonQ's team ID
    erp_type="netsuite",
    enabled=True,
    field_mappings=FIELD_MAP,
    config={
        "api_version": "2023_2",
        "auto_create_from_email": True,
        "auto_create_from_missing_po": True
    }
)
```

**Step 2: Extend NetSuiteClient with PO Extraction**

```python
# In didero/integrations/erp/clients/netsuite.py
def get_complete_purchase_order(self, po_number: str) -> Dict[str, Any]:
    """Extract complete PO data from NetSuite"""
    
    # Get internal ID
    po_info = self.get_purchase_order(po_number)
    internal_id = po_info["internal_id"]
    
    # Get full PO details
    po_data = self._get_purchase_order_details(internal_id)
    xml_text = po_data["raw_response"]
    
    # Extract all components (using patterns from ionq_netsuite_extractor_production.py)
    result = {
        "header": self._extract_header_fields(xml_text),
        "vendor": self._extract_vendor_info(xml_text),
        "addresses": self._extract_addresses(xml_text),
        "line_items": self._extract_line_items(xml_text),
        "custom_fields": self._extract_custom_fields(xml_text)
    }
    
    return result
```

**Step 3: Create Activity Using Framework**

```python
# didero/workflows/activities/erp_activities.py
from didero.integrations.erp.registry import ERPClientRegistry
from didero.integrations.models import ERPIntegrationConfig

@activity.defn(name="try_extract_po_from_erp")
async def try_extract_po_from_erp(
    po_number: str,
    team_id: int
) -> Dict[str, Any]:
    """Extract PO using team's configured ERP"""
    
    # Get team's ERP configuration
    erp_config = await sync_to_async(
        ERPIntegrationConfig.objects.filter(
            team_id=team_id,
            enabled=True
        ).first
    )()
    
    if not erp_config:
        return {"success": False, "reason": "No ERP configured"}
    
    try:
        # Create client using registry
        client = await sync_to_async(
            ERPClientRegistry.create_client_for_team
        )(team_id)
        
        # Extract PO
        po_data = await sync_to_async(
            client.get_complete_purchase_order
        )(po_number)
        
        # Convert to RPA format
        rpa_format = convert_to_rpa_format(po_data)
        
        return {
            "success": True,
            "extraction_result": po_data,
            "rpa_format": rpa_format,
            "source": erp_config.erp_type
        }
        
    except Exception as e:
        logger.error(f"ERP extraction failed: {e}")
        return {"success": False, "reason": str(e)}
```

### 2. NetSuite Field Update Activity

```python
# Only update specific fields back to NetSuite
@activity.defn(name="update_netsuite_fields")
async def update_netsuite_fields(
    po_number: str,
    team_id: int,
    updates: Dict[str, Any]
) -> Dict[str, Any]:
    """Update only specific fields in NetSuite - not entire PO"""
    
    # Only allow updates to these 3 fields
    ALLOWED_FIELDS = {
        'tracking_number': 'custbody_ionq_tracking_number',
        'estimated_delivery_date': 'expectedreceiptdate', 
        'promised_ship_date': 'custcol_ionq_supplierpromisedatefield'
    }
    
    try:
        # Get client
        client = await sync_to_async(
            ERPClientRegistry.create_client_for_team
        )(team_id)
        
        # Filter to only allowed fields
        netsuite_updates = {}
        for field, value in updates.items():
            if field in ALLOWED_FIELDS and value is not None:
                netsuite_updates[ALLOWED_FIELDS[field]] = value
        
        if not netsuite_updates:
            return {"success": True, "message": "No fields to update"}
        
        # Update only specific fields
        result = await sync_to_async(client.update_purchase_order)(
            po_number, 
            netsuite_updates
        )
        
        return {
            "success": True,
            "updated_fields": list(netsuite_updates.keys()),
            "po_number": po_number
        }
        
    except Exception as e:
        logger.error(f"Failed to update NetSuite fields: {e}")
        return {"success": False, "error": str(e)}
```

### 3. Modified PO Creation Workflow (Minimal Changes)

```python
# Modify existing POCreationWorkflow - add ERP check
@workflow.defn(name="po_creation_workflow")
class POCreationWorkflow:
    """Existing PO Creation workflow with ERP integration"""
    
    async def run(self, request: POCreationRequest) -> POCreationResult:
        # Existing check if PO exists
        existing_po = await check_po_exists(request.po_number, request.team_id)
        if existing_po:
            return POCreationResult(
                success=True,
                po_id=existing_po.id,
                created_from="existing"
            )
        
        # NEW: Try ERP extraction if we have a PO number
        if request.po_number and request.source_type == "email":
            erp_result = await workflow.execute_activity(
                extract_and_create_po,
                po_number=request.po_number,
                team_id=request.team_id,
                start_to_close_timeout=timedelta(seconds=30)
            )
            
            if erp_result.get('success'):
                return POCreationResult(
                    success=True,
                    po_id=erp_result['po_id'],
                    created_from="erp_netsuite",
                    used_ai_for_shipping=erp_result.get('used_ai_for_shipping', False)
                )
        
        # Continue with existing AI extraction flow
        return await self.existing_ai_extraction_method(request)
```

### 4. Order Acknowledgement Workflow Addition

```python
# Add to existing OA workflow when PO is missing
@workflow.defn(name="order_acknowledgement_workflow")
class OrderAcknowledgementWorkflow:
    
    async def run(self, request: OARequest) -> OAResult:
        # Existing: Check if PO exists
        po = await get_po_by_number(request.po_number, request.team_id)
        
        # NEW: If no PO, try to create from ERP
        if not po and request.po_number:
            erp_result = await workflow.execute_activity(
                extract_and_create_po,
                po_number=request.po_number,
                team_id=request.team_id,
                start_to_close_timeout=timedelta(seconds=30)
            )
            
            if erp_result.get('success'):
                po = await get_po_by_id(erp_result['po_id'])
                logger.info(f"Created PO {request.po_number} from NetSuite for OA processing")
        
        if not po:
            # Continue with existing error handling
            return OAResult(success=False, error="PO not found in Didero or NetSuite")
        
        # Continue with existing OA processing
        return await self.process_oa_with_po(po, request)
```

### 5. Shipping Address AI Parser Integration

```python
# Use AI parsing ONLY for minimal shipping addresses
from didero.ai.address_parser import parse_address

async def parse_shipping_address_if_needed(po: PurchaseOrder, shipping_data: Dict[str, Any]):
    """
    Parse shipping address with AI only if it's minimal.
    
    Example minimal address from NetSuite:
    {
        "country": "_unitedStates",
        "addressee": "CP Tooling (NI)",
        "addrText": "CP Tooling (NI)\r\nUnited States"  # This needs AI parsing
    }
    """
    if not shipping_data:
        return
    
    # Check if address is minimal (no street/city/state data)
    has_details = any(
        shipping_data.get(field) 
        for field in ['addr1', 'city', 'state', 'zip']
    )
    
    if not has_details and shipping_data.get('addrText'):
        # Minimal address - use AI to parse
        try:
            parsed = await parse_address(shipping_data['addrText'])
            
            # Update PO with parsed shipping address
            await sync_to_async(po.update_shipping_address)(
                address_line_1=parsed.get('street', ''),
                city=parsed.get('city', ''),
                state=parsed.get('state', ''),
                zip_code=parsed.get('zip', ''),
                country=parsed.get('country', 'US')
            )
            
            logger.info(f"Used AI to parse shipping address for PO {po.po_number}")
        except Exception as e:
            logger.warning(f"Failed to parse shipping address: {e}")
    else:
        # Address has details - use directly
        await sync_to_async(po.update_shipping_address)(
            address_line_1=shipping_data.get('addr1', ''),
            city=shipping_data.get('city', ''),
            state=shipping_data.get('state', ''),
            zip_code=shipping_data.get('zip', ''),
            country=shipping_data.get('country', 'US')
        )
```

---

## Deployment Strategy

### Phase 1: MVP Implementation (Week 1)

1. **Extend Existing Infrastructure**:
   - [x] IonQ credentials already in .env file ✅
   - [ ] Add `get_complete_purchase_order()` method to NetSuiteClient
   - [ ] Add `create_purchase_order()` method to NetSuiteClient  
   - [ ] Create RPA format converter function
   - [ ] Add extraction methods from ionq_netsuite_extractor_production.py

2. **Configure IonQ in Database**:
   ```python
   # Run via Django shell or management command
   from didero.integrations.models import ERPIntegrationConfig
   from didero.integrations.erp.customers.ionq.constants import FIELD_MAP
   
   ERPIntegrationConfig.objects.update_or_create(
       team_id=173,  # IonQ production
       defaults={
           'erp_type': 'netsuite',
           'enabled': True,
           'field_mappings': FIELD_MAP,
           'config': {
               'api_version': '2023_2',
               'auto_create_from_email': True,
               'auto_create_from_missing_po': True
           }
       }
   )
   ```

### Phase 2: Test & Deploy (Week 2)

1. **Integration Testing**:
   - [ ] Test with known POs (PO431, PO432) from sandbox
   - [ ] Verify vendor/address extraction with billing prioritization
   - [ ] Confirm direct Django model creation
   - [ ] Test AI parsing for minimal shipping addresses only
   - [ ] Verify only 3 fields update back to NetSuite

2. **Field Mapping Validation**:
   ```python
   # Validate mapping from test outputs
   test_data = load_test_data()  # From test_ionq_production_extractor.py
   
   # Verify key mappings:
   assert test_data['header']['standard']['tranId'] == 'PO431'  # → po.po_number
   assert test_data['vendor']['name'] == 'V10072 ThorLabs'  # → vendor lookup
   assert test_data['addresses']['billing']['addr1'] == '56 Sparta Avenue'  # → vendor address
   assert len(test_data['addresses']['shipping']['addrText'].split('\n')) == 2  # → needs AI
   ```

3. **AI Parser Testing**:
   ```python
   # Test AI parsing for minimal addresses
   minimal_shipping = {
       "country": "_unitedStates",
       "addressee": "CP Tooling (NI)", 
       "addrText": "CP Tooling (NI)\r\nUnited States"
   }
   
   # Should trigger AI parsing
   assert needs_ai_parsing(minimal_shipping) == True
   
   # Complete address should NOT trigger AI
   complete_billing = {
       "addr1": "56 Sparta Avenue",
       "city": "Newton",
       "state": "NJ",
       "zip": "07860"
   }
   assert needs_ai_parsing(complete_billing) == False
   ```

### Phase 3: Production Deployment (Week 3)

1. **Deploy for IonQ (Team 173)**:
   - [ ] Enable in production environment
   - [ ] Monitor first 10 POs closely
   - [ ] Track extraction success rate
   - [ ] Review any created tasks for failures

2. **Simple Monitoring**:
   - Use existing logging infrastructure
   - Check created tasks daily
   - Weekly success rate review
   - Adjust as needed based on results

---

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. Authentication Failures

**Symptom**: 401 Unauthorized or signature mismatch errors

**Solutions**:
- Verify OAuth credentials are current
- Check timestamp synchronization
- Ensure proper URL encoding
- Validate signature generation algorithm

#### 2. Missing Vendor/Address Data

**Symptom**: Vendor or address fields empty in extraction

**Root Causes**:
- Incorrect XML tag patterns
- Missing data in NetSuite
- Incomplete PO configuration

**Solutions**:
```python
# Verify correct tag patterns
billing_pattern = r'<tranPurch:billingAddress[^>]*>(.*?)</tranPurch:billingAddress>'
shipping_pattern = r'<tranPurch:shippingAddress[^>]*>(.*?)</tranPurch:shippingAddress>'

# Use billing address for vendor (more complete)
if 'billing' in addresses:
    vendor_address = addresses['billing']
elif 'shipping' in addresses:
    vendor_address = addresses['shipping']
```

#### 3. Field Mapping Mismatches

**Symptom**: Data not appearing in correct Didero fields

**Diagnostic Steps**:
1. Check extraction result JSON
2. Verify RPA format conversion
3. Validate Didero model mapping
4. Review sync function expectations

#### 4. Performance Issues

**Symptom**: Slow extraction or timeouts

**Optimizations**:
- Implement connection pooling
- Cache authentication tokens
- Batch multiple PO extractions
- Use async operations where possible

### Debug Logging

Enable detailed logging for troubleshooting:

```python
import logging

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('netsuite_extraction.log'),
        logging.StreamHandler()
    ]
)

# Log key extraction points
logger.debug(f"Searching for PO: {po_number}")
logger.debug(f"Found internal ID: {internal_id}")
logger.debug(f"Vendor extracted: {vendor_data}")
logger.debug(f"Addresses found: {list(addresses.keys())}")
```

---

## Success Metrics

### Primary KPIs

1. **Extraction Success Rate**
   - Target: 95%+ successful extractions
   - Measurement: (Successful Extractions / Total Attempts) * 100
   - Tracking: DataDog dashboard + weekly reports

2. **Field Completeness Score**
   - Target: 98%+ fields populated correctly
   - Measurement: (Fields with Valid Data / Total Expected Fields) * 100
   - Tracking: Daily validation reports

3. **Processing Time**
   - Target: < 30 seconds per PO
   - Measurement: Time from trigger to PO creation
   - Tracking: Performance monitoring dashboard

4. **Error Rate**
   - Target: < 2% extraction errors
   - Measurement: (Failed Extractions / Total Attempts) * 100
   - Tracking: Error logs and alerts

### Secondary Metrics

1. **Team Adoption Rate**
   - Target: 50% of eligible teams using ERP integration within 6 months
   - Measurement: Teams with enabled ERP / Total teams with ERP

2. **Manual Intervention Rate**
   - Target: < 5% of POs require manual correction
   - Measurement: POs edited after ERP creation / Total ERP POs

3. **Cost Savings**
   - Target: 80% reduction in manual data entry time
   - Measurement: Time saved per PO * Number of POs * Hourly rate

### IonQ-Specific Success Criteria

1. **Address Enhancement Resolution**
   - Target: 0 "Fields Needing Enhancement" errors
   - Current: Using billing address eliminates these errors
   - Validation: Weekly audit of created POs

2. **Vendor Data Completeness**
   - Target: 100% vendor information captured
   - Current: Billing address strategy ensures complete data
   - Validation: Automated checks on each extraction

---

## Open Questions & Assumptions

### Open Questions

1. **~~Credential Management~~** ✅ RESOLVED
   - Decision: Use .env file for now (already implemented for IonQ)
   - Future: Can migrate to Secrets Manager when needed

2. **~~Bulk Import Strategy~~** ✅ RESOLVED
   - Decision: No historical import needed
   - Focus on go-forward automation only

3. **~~Field Mapping UI~~** ✅ RESOLVED
   - Decision: Django admin is sufficient for MVP
   - Simple configuration per team

4. **~~Webhook Security~~** ✅ RESOLVED
   - Decision: No webhooks - triggered by workflows only
   - Two triggers: PO email categorization & missing PO in OA/shipment workflows

5. **~~Error Recovery~~** ✅ RESOLVED
   - Decision: Try once, create task on failure
   - Manual review through existing task system

### Key Assumptions

1. **Technical Assumptions**
   - NetSuite SOAP API remains stable
   - Teams have necessary ERP permissions
   - Network connectivity is reliable
   - AWS infrastructure scales as needed

2. **Business Assumptions**
   - Teams want automated PO creation
   - ERP data is source of truth
   - Manual validation can be minimized
   - Cost savings justify development effort

3. **Data Assumptions**
   - Billing addresses contain vendor info (validated for IonQ)
   - PO numbers are unique per supplier
   - Custom fields follow naming conventions
   - Currency conversion rates are available

4. **Integration Assumptions**
   - RPA/Stagehand functions remain stable
   - Temporal workflows can handle load
   - Django models support required fields
   - Existing sync functions are correct

---

## Future Enhancements

### 1. Intelligent Field Mapping System

**ML-Powered Field Discovery**:
```python
class SmartFieldMapper:
    """Machine learning-based field mapping"""
    
    def __init__(self, team_id: int):
        self.model = self.load_ml_model(team_id)
        self.confidence_threshold = 0.85
    
    async def suggest_mappings(self, sample_data: Dict) -> Dict[str, Any]:
        """Suggest field mappings based on data patterns"""
        suggestions = {}
        
        for source_field, value in sample_data.items():
            # Use ML to predict target field
            prediction = self.model.predict(source_field, value)
            
            if prediction.confidence > self.confidence_threshold:
                suggestions[source_field] = {
                    "target_field": prediction.field,
                    "confidence": prediction.confidence,
                    "transform": prediction.suggested_transform
                }
        
        return suggestions
```

### 2. Additional Teams Support

**Simple Configuration Extension**:
```python
# When ready to add more teams, just extend the config
TEAM_ERP_CONFIG = {
    173: {  # IonQ
        'enabled': True,
        'type': 'netsuite',
        'credentials': {...}  # From .env
    },
    # Future teams can be added here
    # 174: {  # Another NetSuite customer
    #     'enabled': True,
    #     'type': 'netsuite',
    #     'credentials': {...}
    # }
}
```

### 3. Performance Improvements (Future)

**Simple Caching**:
```python
# Can add basic caching when needed
from functools import lru_cache
from datetime import timedelta

@lru_cache(maxsize=100)
def get_cached_po(team_id: int, po_number: str) -> Optional[Dict]:
    """Simple in-memory cache for recently accessed POs"""
    # Implement when performance becomes an issue
    pass
```

### 4. Advanced Address Resolution

**ML-Based Address Parsing**:
```python
class AddressParser:
    """Enhanced address parsing with ML support"""
    
    def __init__(self):
        self.nlp_model = load_address_parsing_model()
        self.geocoder = GeocodeService()
    
    async def parse_address(self, raw_text: str) -> Dict[str, Any]:
        """Parse unstructured address text"""
        
        # Try NLP extraction
        nlp_result = self.nlp_model.extract_entities(raw_text)
        
        # Validate with geocoding
        geocoded = await self.geocoder.validate_address(nlp_result)
        
        # Return structured address
        return {
            "street": geocoded.street_address,
            "city": geocoded.city,
            "state": geocoded.state,
            "postal_code": geocoded.postal_code,
            "country": geocoded.country,
            "confidence": geocoded.confidence_score
        }
```

### 5. Performance Optimizations

**Caching Strategy**:
```python
class NetSuitePOCache:
    """Cache frequently accessed PO data"""
    
    def __init__(self, ttl_seconds: int = 900):
        self.cache = TTLCache(maxsize=1000, ttl=ttl_seconds)
        self.redis_client = redis.Redis()
    
    async def get_po(self, po_number: str, team_id: int) -> Optional[Dict]:
        """Get PO from cache or fetch from NetSuite"""
        
        cache_key = f"netsuite:po:{team_id}:{po_number}"
        
        # Check memory cache
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        # Check Redis cache
        redis_data = await self.redis_client.get(cache_key)
        if redis_data:
            po_data = json.loads(redis_data)
            self.cache[cache_key] = po_data
            return po_data
        
        # Fetch from NetSuite
        po_data = await self.fetch_from_netsuite(po_number, team_id)
        if po_data:
            # Cache in both layers
            self.cache[cache_key] = po_data
            await self.redis_client.setex(
                cache_key, 
                self.ttl_seconds, 
                json.dumps(po_data)
            )
        
        return po_data
```

---

## Implementation Approach (MVP)

### What We're Building
1. **Minimal Changes**: Integrate existing extractor with current workflows
2. **Two Triggers Only**:
   - PO emails from categorizer → Check ERP before AI extraction
   - OA/Shipment workflows → Create PO from ERP if missing
3. **Simple Configuration**: Team settings in Django admin or config file
4. **Error Handling**: Create task on failure, no retries

### What We're NOT Building (Yet)
- ❌ Complex credential management (using .env)
- ❌ Historical data import
- ❌ Custom UI (Django admin is fine)
- ❌ Webhooks or real-time sync
- ❌ Multiple retry logic
- ❌ Performance optimizations

### Implementation Steps

1. **Week 1: Core Integration**
   ```python
   # Add to PO creation workflow
   if po_number and team_has_erp_enabled(team_id):
       erp_result = await try_create_po_from_erp(po_number, team_id)
       if erp_result['success']:
           return erp_result
   # Fall back to existing AI extraction
   ```

2. **Week 2: Test & Refine**
   - Test with IonQ sandbox data
   - Verify field mappings
   - Ensure task creation works

3. **Week 3: Production**
   - Enable for Team 173
   - Monitor daily
   - Address any issues

---

## Leveraging Existing Infrastructure - Key Benefits

### What's Already Built

The `didero/integrations/erp/` framework provides a complete, production-ready infrastructure:

1. **Abstract Base Class** (`ERPClientBase`):
   - Standardized interface for all ERP clients
   - Built-in field mapping capabilities
   - Test connection methods
   - Update methods (can be extended for creation)

2. **NetSuite Client** (`NetSuiteClient`):
   - Full OAuth 1.0a implementation ✅
   - SOAP envelope construction ✅
   - PO search functionality ✅
   - Field update capabilities ✅
   - Just needs extraction/creation methods added

3. **Field Mapping System**:
   - `NetSuiteFieldMapper` handles complex field transformations
   - IonQ-specific mappings in `customers/ionq/constants.py`
   - Intelligent line-level vs header-level field handling

4. **Configuration Management**:
   - `ERPIntegrationConfig` model for team settings
   - `SimpleEnvCredentialProvider` for secure credential storage
   - Django admin interface for configuration

5. **Registry Pattern**:
   - `ERPClientRegistry` for client instantiation
   - Supports multiple ERP types
   - Easy to add new ERPs

### Integration Approach

Instead of building from scratch, we:

1. **Extend NetSuiteClient** with two methods:
   - `get_complete_purchase_order()` - Extract full PO data
   - `create_purchase_order()` - Create new POs

2. **Use Existing Models**:
   - Store config in `ERPIntegrationConfig`
   - No new models needed

3. **Leverage Registry**:
   - Client creation handled by existing registry
   - Automatic credential loading

4. **Maintain Compatibility**:
   - Convert to RPA format for existing sync functions
   - No changes to `netsuite_sync.py` required

## Summary: Direct Model Creation Approach

We're extending the existing `didero/integrations/erp/` infrastructure to enable full PO extraction and direct Django model creation:

### What We're Delivering

1. **Full PO Extraction**: Extract complete PO data from NetSuite using existing infrastructure
2. **Direct Model Creation**: Create POs directly in Django models - no intermediate formats
3. **Selective AI Usage**: AI parsing only for minimal shipping addresses
4. **Limited Field Updates**: Only update 3 specific fields back to NetSuite

### Key Implementation Points

1. **Extend NetSuiteClient**:
   - Add `get_complete_purchase_order()` method
   - Use extraction patterns from `ionq_netsuite_extractor_production.py`

2. **Create POs Directly**:
   - Map NetSuite fields to Django models based on test outputs
   - Use billing address for vendor (complete data)
   - Use AI only for minimal shipping addresses

3. **Update Only 3 Fields**:
   - `custbody_ionq_tracking_number`
   - `expectedreceiptdate`
   - `custcol_ionq_supplierpromisedatefield`

### Field Mapping Summary

From NetSuite → To Didero Models:
```python
# Header fields
po_data['header']['standard']['tranId'] → po.po_number
po_data['header']['standard']['total'] → po.total_cost
po_data['header']['standard']['tranDate'] → po.placement_time

# Vendor (use billing address - complete)
po_data['vendor']['name'] → vendor.name
po_data['addresses']['billing'] → vendor address fields

# Shipping (use AI if minimal)
po_data['addresses']['shipping']['addrText'] → parse_address() if minimal

# Line items
item['item_name'] → order_item.item_number
item['fields']['rate'] → order_item.price
```

### Why This Approach Works

1. **Leverages Existing Infrastructure**: Uses ERPClientBase, NetSuiteClient, field mappers
2. **Direct Integration**: No RPA format conversion needed
3. **Smart AI Usage**: Only parses what needs parsing (shipping addresses)
4. **Clean Separation**: Extract from NetSuite, create in Didero, update only specific fields

This approach maximizes code reuse while providing a clean, direct integration between NetSuite and Didero's Django models.