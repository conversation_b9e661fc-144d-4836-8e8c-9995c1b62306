#!/usr/bin/env python
"""
Test PO resolution with auto-creation using proper mocks.

This test ensures the resolve_purchase_order_with_auto_creation
function works correctly including the retry after auto-creation.

Usage:
    uv run python test_po_resolution_with_mocks.py
"""

import os
import sys
from unittest.mock import patch, MagicMock, call

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")

try:
    import django
    django.setup()
except ImportError:
    print("Django not imported properly, run with 'uv run python test_po_resolution_with_mocks.py'")
    sys.exit(1)

from didero.orders.models import PurchaseOrder
from didero.workflows.shared_activities.purchase_order_operations import (
    resolve_purchase_order_with_auto_creation,
)

print("=" * 80)
print("PO RESOLUTION WITH AUTO-CREATION - COMPLETE FLOW TEST")
print("=" * 80)

def test_successful_auto_creation():
    """Test successful auto-creation flow"""
    print("\n" + "-" * 60)
    print("TEST: Successful auto-creation and retry")
    print("-" * 60)
    
    # Create a mock PO that will be "created" by the workflow
    mock_po = MagicMock(spec=PurchaseOrder)
    mock_po.id = 456
    mock_po.pk = 456
    mock_po.po_number = "TEST-AUTO-123"
    
    with patch('didero.workflows.shared_activities.purchase_order_operations.retrieve_purchase_order_activity') as mock_retrieve:
        # First call returns not found, second call (after auto-creation) returns found
        mock_retrieve.side_effect = [
            # First lookup - not found
            {"success": False, "po_found": False, "error_message": "PO not found"},
            # Second lookup after auto-creation - found
            {
                "success": True,
                "po_found": True,
                "purchase_order_id": 456,
                "purchase_order_details": {"po_number": "TEST-AUTO-123"}
            }
        ]
        
        with patch('asgiref.sync.async_to_sync') as mock_async:
            # Mock the enqueue function
            mock_enqueue = MagicMock()
            mock_enqueue.return_value = {
                "success": True,
                "po_id": "456",
                "po_number": "TEST-AUTO-123",
                "workflow_id": "test-workflow-123",
                "created_in_draft": True,
            }
            mock_async.return_value = mock_enqueue
            
            # Test with IonQ team (has ERP capability)
            result = resolve_purchase_order_with_auto_creation(
                po_number="TEST-AUTO-123",
                team_id=4,  # IonQ
                source_email_id="email-123",
                source_context="test",
                enable_auto_creation=True,
            )
            
            # Verify results
            print(f"Success: {result['success']}")
            print(f"PO ID: {result.get('purchase_order_id')}")
            print(f"Auto-created: {result.get('auto_created')}")
            print(f"Auto-creation attempted: {result.get('auto_creation_attempted')}")
            
            # Assertions
            assert result['success'] == True, "Should succeed"
            assert result['purchase_order_id'] == 456, "Should have PO ID"
            assert result['auto_created'] == True, "Should be auto-created"
            assert result['auto_creation_attempted'] == True, "Should have attempted"
            
            # Verify retrieve was called twice
            assert mock_retrieve.call_count == 2, "Should call retrieve twice"
            
            # Verify enqueue was called
            assert mock_enqueue.called, "Should call enqueue"
            
            print("✓ Test passed!")

def test_auto_creation_fails():
    """Test when auto-creation workflow fails"""
    print("\n" + "-" * 60)
    print("TEST: Auto-creation workflow fails")
    print("-" * 60)
    
    with patch('didero.workflows.shared_activities.purchase_order_operations.retrieve_purchase_order_activity') as mock_retrieve:
        mock_retrieve.return_value = {"success": False, "po_found": False, "error_message": "PO not found"}
        
        with patch('asgiref.sync.async_to_sync') as mock_async:
            # Mock failed auto-creation
            mock_enqueue = MagicMock()
            mock_enqueue.return_value = {
                "success": False,
                "error_message": "NetSuite API error",
            }
            mock_async.return_value = mock_enqueue
            
            result = resolve_purchase_order_with_auto_creation(
                po_number="TEST-FAIL-123",
                team_id=4,
                source_email_id="email-123",
                source_context="test",
                enable_auto_creation=True,
            )
            
            # Verify results
            print(f"Success: {result['success']}")
            print(f"Auto-creation attempted: {result.get('auto_creation_attempted')}")
            print(f"Auto-creation error: {result.get('auto_creation_error')}")
            
            assert result['success'] == False, "Should fail"
            assert result['auto_creation_attempted'] == True, "Should have attempted"
            assert "NetSuite API error" in result.get('auto_creation_error', ''), "Should have error message"
            
            print("✓ Test passed!")

def test_no_capability():
    """Test team without ERP capability"""
    print("\n" + "-" * 60)
    print("TEST: Team without ERP capability")
    print("-" * 60)
    
    with patch('didero.workflows.shared_activities.purchase_order_operations.retrieve_purchase_order_activity') as mock_retrieve:
        mock_retrieve.return_value = {"success": False, "po_found": False, "error_message": "PO not found"}
        
        # Mock team_has_erp_auto_creation_capability to return False
        with patch('didero.workflows.shared_activities.purchase_order_operations.team_has_erp_auto_creation_capability') as mock_capability:
            mock_capability.return_value = False
            
            result = resolve_purchase_order_with_auto_creation(
                po_number="TEST-NO-CAP-123",
                team_id=2,  # Didero team
                source_email_id="email-123",
                source_context="test",
                enable_auto_creation=True,  # Permission granted but no capability
            )
            
            print(f"Success: {result['success']}")
            print(f"Auto-creation attempted: {result.get('auto_creation_attempted')}")
            print(f"Auto-creation error: {result.get('auto_creation_error')}")
            
            assert result['success'] == False, "Should fail"
            assert result['auto_creation_attempted'] == False, "Should NOT have attempted"
            assert "ERP integration not configured" in result.get('auto_creation_error', ''), "Should have capability error"
            
            print("✓ Test passed!")

def test_po_already_exists():
    """Test when PO already exists"""
    print("\n" + "-" * 60)
    print("TEST: PO already exists")
    print("-" * 60)
    
    with patch('didero.workflows.shared_activities.purchase_order_operations.retrieve_purchase_order_activity') as mock_retrieve:
        mock_retrieve.return_value = {
            "success": True,
            "po_found": True,
            "purchase_order_id": 789,
            "purchase_order_details": {"po_number": "TEST-EXISTS-123"}
        }
        
        result = resolve_purchase_order_with_auto_creation(
            po_number="TEST-EXISTS-123",
            team_id=4,
            source_email_id="email-123",
            source_context="test",
            enable_auto_creation=True,
        )
        
        print(f"Success: {result['success']}")
        print(f"PO ID: {result.get('purchase_order_id')}")
        print(f"Auto-created: {result.get('auto_created')}")
        print(f"Auto-creation attempted: {result.get('auto_creation_attempted')}")
        
        assert result['success'] == True, "Should succeed"
        assert result['purchase_order_id'] == 789, "Should have existing PO ID"
        assert result['auto_created'] == False, "Should NOT be auto-created"
        assert result['auto_creation_attempted'] == False, "Should NOT have attempted"
        
        # Verify retrieve was only called once
        assert mock_retrieve.call_count == 1, "Should only call retrieve once"
        
        print("✓ Test passed!")

# Run all tests
test_successful_auto_creation()
test_auto_creation_fails()
test_no_capability()
test_po_already_exists()

print("\n" + "=" * 80)
print("ALL TESTS PASSED!")
print("=" * 80)