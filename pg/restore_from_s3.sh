#!/bin/bash
set -e

unset AWS_PROFILE

echo "Restoring PostgreSQL database from S3..."

# Map S3 credentials to AWS standard names
export AWS_ACCESS_KEY_ID="${S3_ACCESS_KEY}"
export AWS_SECRET_ACCESS_KEY="${S3_SECRET_KEY}"
export AWS_DEFAULT_REGION="${AWS_DEFAULT_REGION:-us-east-1}"

# Download the dump file from S3
echo "Downloading backup from S3..."
aws s3 cp s3://didero-db-snapshot-dev/backup-20250711-172027.dump /tmp/backup.dump

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL to be ready..."
until pg_isready -U didero; do
  echo "Waiting for database..."
  sleep 2
done

# Restore the dump using the didero user
echo "Restoring database from dump file..."
pg_restore -U didero -d didero -v --clean --if-exists /tmp/backup.dump || {
    echo "pg_restore failed, trying alternative method..."
    # Fallback if it's a SQL format dump
    psql -U didero -d didero -f /tmp/backup.dump
}

# Clean up
rm /tmp/backup.dump

echo "Restore complete!"