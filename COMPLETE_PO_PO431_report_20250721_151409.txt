COMPLETE PO FIELD EXTRACTION REPORT - PO431\n================================================================================\n\nEXTRACTION SUMMARY:\n--------------------\nExtraction Time: 2025-07-21T15:14:09.274047\nHeader Fields: 15\nCustom Header Fields: 9\nLine Items: 19\nExpense Lines: 1\nTotal Custom Fields: 85\nexpectedReceiptDate Occurrences: 19\n\nIONQ TARGET FIELDS STATUS:\n------------------------------\ncustbody_ionq_tracking_number:\n  Found: ✅ YES\n  Value: 1Z07X6270360947105\n  Location: header\n\nexpectedReceiptDate:\n  Found: ✅ YES\n  Occurrences: 19\n  Location: line_items\n\ncustcol_ionq_supplierpromisedatefield:\n  Found: ❌ NO\n  Location: line_items\n\nHEADER FIELDS:\n---------------\ntranId: PO431\nmemo: WEB NUW1334695\nstatus: Fully Billed\ntotal: 7291.42\nemail: <EMAIL>\ncurrencyName: USD\nexchangeRate: 1.0\ncreatedDate: 2022-10-04T17:23:47.000-07:00\nlastModifiedDate: 2022-11-20T15:24:36.000-08:00\ntranDate: 2022-10-03T21:00:00.000-07:00\ntoBePrinted: false\ntoBeEmailed: false\ntoBeFaxed: false\nshipIsResidential: false\nisClosed: false\n\nCUSTOM HEADER FIELDS:\n----------------------\ncustbody_ionq_sent_date_to_supplier: 2022-10-05T21:00:00.000-07:00\ncustbody_bill_approver_approved: false\ncustbody_ionq_tracking_number: 1Z07X6270360947105\ncustbody_ionq_po_req_num: 1651\ncustbody_esc_created_date: 2022-10-03T21:00:00.000-07:00\ncustbody_status_rejected: false\ncustbody_tf_tx_po_status: false\ncustbody_is_po: true\ncustbody_fpa_approval: false\n\nSAMPLE LINE ITEMS (First 3 of 19):\n----------------------------------------\n\nLINE 1:\n  Item: 502-00097 (ID: 2761)\n  Basic: {'line': '1', 'vendorName': 'PM100D', 'description': 'Compact Power and Energy Meter Console, Digital 4&quot; LCD', 'isClosed': 'false', 'units': 'Ea'}\n  Financial: {'rate': '1220.57', 'amount': '1220.57'}\n  Dates: {'expectedReceiptDate': '2022-10-03T21:00:00.000-07:00'}\n  Custom: ['custcol_ionq_purchasetypefield', 'custcol_ionq_receivinginspectionfield', 'custcol_scm_lc_autocalc', 'cseg_ionq_project']\n\nLINE 2:\n  Item: 425-00006 (ID: 2760)\n  Basic: {'line': '2', 'vendorName': 'S120C', 'description': 'Standard Photodiode Power Sensor, Si, 400 - 1100 nm, 50 mW', 'isClosed': 'false', 'units': 'Ea'}\n  Financial: {'rate': '342.41', 'amount': '342.41'}\n  Dates: {'expectedReceiptDate': '2022-10-03T21:00:00.000-07:00'}\n  Custom: ['custcol_ionq_purchasetypefield', 'custcol_ionq_receivinginspectionfield', 'custcol_scm_lc_autocalc', 'cseg_ionq_project']\n\nLINE 3:\n  Item: 425-00007 (ID: 2762)\n  Basic: {'line': '3', 'vendorName': 'S122C', 'description': 'Standard Photodiode Power Sensor, Ge, 700 - 1800 nm, 40 mW', 'isClosed': 'false', 'units': 'Ea'}\n  Financial: {'rate': '683.66', 'amount': '683.66'}\n  Dates: {'expectedReceiptDate': '2022-10-03T21:00:00.000-07:00'}\n  Custom: ['custcol_ionq_purchasetypefield', 'custcol_ionq_receivinginspectionfield', 'custcol_scm_lc_autocalc', 'cseg_ionq_project']\n