#!/usr/bin/env python
"""
Real integration tests for the unified extraction activity.

This script tests the extract_and_resolve_supplier_document activity
without mocks to ensure everything works correctly.

Usage:
    uv run python test_unified_extraction_real.py
"""

import os
import sys
import uuid
from datetime import datetime

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")

try:
    import django
    django.setup()
except ImportError:
    print("Django not imported properly, run with 'uv run python test_unified_extraction_real.py'")
    sys.exit(1)

# Django imports
from django.utils import timezone
from didero.addresses.models import Address
from didero.emails.models import EmailThread
from didero.orders.models import PurchaseOrder
from didero.suppliers.models import Communication, Supplier
from didero.users.models.team_models import Team
from didero.workflows.models import WorkflowBehaviorConfig, UserWorkflow
from didero.workflows.schemas import WorkflowType, WorkflowTrigger

# Import the activity we're testing
from didero.workflows.shared_activities.supplier_document_operations import (
    extract_and_resolve_supplier_document
)

# Test configuration
TEAM_ID = 2  # Didero team (doesn't have ERP configured)
TEAM_WITH_ERP_ID = 4  # IonQ team (has ERP configured)

print("=" * 80)
print("UNIFIED EXTRACTION ACTIVITY - REAL INTEGRATION TEST")
print("=" * 80)

def create_test_supplier(team, test_id):
    """Create a test supplier"""
    supplier, created = Supplier.objects.get_or_create(
        name=f"Test Supplier {test_id}",
        team=team,
        defaults={
            "website_url": f"https://test-{test_id}.example.com",
            "description": "Test supplier for integration testing",
        }
    )
    
    # Ensure supplier has an address
    if not Address.objects.filter(supplier=supplier).exists():
        Address.objects.create(
            supplier=supplier,
            team=team,
            line_1="123 Test Street",
            city="New York",
            state_or_province="NY",
            postal_code="10001",
            country="US",
            is_default=True,
        )
    
    return supplier

def create_test_email(team, supplier, email_type, po_number, test_id):
    """Create a test email with document content"""
    
    if email_type == "order_acknowledgement":
        subject = f"Order Acknowledgement - PO {po_number}"
        body = f"""
Order Acknowledgement
Order Number: OA-{test_id}
PO Reference: {po_number}
Date: {timezone.now().strftime("%Y-%m-%d")}

We acknowledge receipt of your purchase order.

Items:
1. TEST-ITEM-001 - Test Product A - Qty: 5 - Unit Price: $100.00
2. TEST-ITEM-002 - Test Product B - Qty: 3 - Unit Price: $200.00

Total: $1,100.00

Estimated Delivery: 7-10 business days
"""
    else:  # shipment
        subject = f"Shipment Notification - PO {po_number}"
        body = f"""
Shipment Notification

Your order has been shipped!

PO Number: {po_number}
Tracking Number: TRACK-{test_id}
Carrier: FedEx Ground
Ship Date: {timezone.now().strftime("%Y-%m-%d")}

Items Shipped:
- TEST-ITEM-001: Test Product A (Qty: 5)
- TEST-ITEM-002: Test Product B (Qty: 3)

Estimated Delivery: 3-5 business days
"""
    
    email_thread = EmailThread.objects.create(
        team=team,
        thread_id=f"thread-{email_type}-{test_id}"
    )
    
    email = Communication.objects.create(
        team=team,
        supplier=supplier,
        email_thread=email_thread,
        email_subject=subject,
        email_content=body,
        direction="INBOUND",
        email_from="<EMAIL>",
        email_message_id=f"{email_type}-{test_id}@example.com",
        comm_time=timezone.now(),
    )
    
    return email

def create_test_po(team, supplier, po_number):
    """Create a test PO"""
    po = PurchaseOrder.objects.create(
        team=team,
        supplier=supplier,
        po_number=po_number,
        order_status="DRAFT",
        source="test",
    )
    return po

# Test 1: Order Acknowledgement - PO exists
print("\n" + "-" * 60)
print("TEST 1: Order Acknowledgement with existing PO")
print("-" * 60)

test_id = uuid.uuid4().hex[:8]
team = Team.objects.get(id=TEAM_ID)
supplier = create_test_supplier(team, test_id)
po_number = f"TEST-PO-{test_id}"

# Create the PO first
po = create_test_po(team, supplier, po_number)
print(f"Created PO: {po.po_number} (ID: {po.pk})")

# Create OA email
oa_email = create_test_email(team, supplier, "order_acknowledgement", po_number, test_id)
print(f"Created OA email: {oa_email.pk}")

# Test the activity
params = {
    "document_type": "order_acknowledgement",
    "email_id": oa_email.pk,
    "team_id": team.id,
    "enable_po_auto_creation": False,
}

try:
    result = extract_and_resolve_supplier_document(params)
    print(f"✓ SUCCESS: Extracted OA and found PO")
    print(f"  - Document type: {result['document_type']}")
    print(f"  - PO ID: {result['purchase_order_id']}")
    print(f"  - Auto-created: {result['auto_created_po']}")
except Exception as e:
    print(f"✗ FAILED: {str(e)}")

# Test 2: Shipment - PO exists
print("\n" + "-" * 60)
print("TEST 2: Shipment with existing PO")
print("-" * 60)

# Create shipment email for same PO
ship_email = create_test_email(team, supplier, "shipment", po_number, test_id)
print(f"Created Shipment email: {ship_email.pk}")

params = {
    "document_type": "shipment",
    "email_id": ship_email.pk,
    "team_id": team.id,
    "enable_po_auto_creation": False,
}

try:
    result = extract_and_resolve_supplier_document(params)
    print(f"✓ SUCCESS: Extracted shipment and found PO")
    print(f"  - Document type: {result['document_type']}")
    print(f"  - PO ID: {result['purchase_order_id']}")
    print(f"  - Auto-created: {result['auto_created_po']}")
except Exception as e:
    print(f"✗ FAILED: {str(e)}")

# Test 3: Order Acknowledgement - PO missing, auto-creation disabled
print("\n" + "-" * 60)
print("TEST 3: Order Acknowledgement - PO missing, auto-creation DISABLED")
print("-" * 60)

test_id_missing = uuid.uuid4().hex[:8]
po_number_missing = f"TEST-MISSING-{test_id_missing}"

oa_email_missing = create_test_email(team, supplier, "order_acknowledgement", po_number_missing, test_id_missing)
print(f"Created OA email for missing PO: {po_number_missing}")

params = {
    "document_type": "order_acknowledgement",
    "email_id": oa_email_missing.pk,
    "team_id": team.id,
    "enable_po_auto_creation": False,
}

try:
    result = extract_and_resolve_supplier_document(params)
    print(f"✗ UNEXPECTED SUCCESS: Should have failed for missing PO")
except Exception as e:
    if "Purchase order not found" in str(e):
        print(f"✓ EXPECTED FAILURE: {str(e)}")
    else:
        print(f"✗ UNEXPECTED ERROR: {str(e)}")

# Test 4: Check permission/capability separation
print("\n" + "-" * 60)
print("TEST 4: Permission/Capability separation")
print("-" * 60)

from didero.workflows.shared_activities.purchase_order_operations import (
    team_has_erp_auto_creation_capability,
    get_po_auto_creation_eligibility,
    should_auto_create_po_for_team
)

# Check team without ERP
print(f"\nTeam {TEAM_ID} (no ERP):")
capability = team_has_erp_auto_creation_capability(TEAM_ID)
eligibility = get_po_auto_creation_eligibility(TEAM_ID)
print(f"  - Has capability: {capability}")
print(f"  - Should auto-create (permission=False): {should_auto_create_po_for_team(TEAM_ID, False)}")
print(f"  - Should auto-create (permission=True): {should_auto_create_po_for_team(TEAM_ID, True)}")
print(f"  - Missing requirements: {eligibility.get('missing_requirements', [])}")

# Check team with ERP
print(f"\nTeam {TEAM_WITH_ERP_ID} (with ERP):")
capability = team_has_erp_auto_creation_capability(TEAM_WITH_ERP_ID)
eligibility = get_po_auto_creation_eligibility(TEAM_WITH_ERP_ID)
print(f"  - Has capability: {capability}")
print(f"  - Should auto-create (permission=False): {should_auto_create_po_for_team(TEAM_WITH_ERP_ID, False)}")
print(f"  - Should auto-create (permission=True): {should_auto_create_po_for_team(TEAM_WITH_ERP_ID, True)}")
print(f"  - ERP type: {eligibility.get('erp_type')}")

# Test 5: Invalid document type
print("\n" + "-" * 60)
print("TEST 5: Invalid document type")
print("-" * 60)

params = {
    "document_type": "invoice",  # Not supported yet
    "email_id": oa_email.pk,
    "team_id": team.id,
    "enable_po_auto_creation": False,
}

try:
    result = extract_and_resolve_supplier_document(params)
    print(f"✗ UNEXPECTED SUCCESS: Should have failed for unsupported document type")
except Exception as e:
    if "Unsupported document type" in str(e):
        print(f"✓ EXPECTED FAILURE: {str(e)}")
    else:
        print(f"✗ UNEXPECTED ERROR: {str(e)}")

# Test 6: Non-existent email
print("\n" + "-" * 60)
print("TEST 6: Non-existent email")
print("-" * 60)

params = {
    "document_type": "order_acknowledgement",
    "email_id": 999999,  # Doesn't exist
    "team_id": team.id,
    "enable_po_auto_creation": False,
}

try:
    result = extract_and_resolve_supplier_document(params)
    print(f"✗ UNEXPECTED SUCCESS: Should have failed for non-existent email")
except Exception as e:
    if "Communication not found" in str(e):
        print(f"✓ EXPECTED FAILURE: {str(e)}")
    else:
        print(f"✗ UNEXPECTED ERROR: {str(e)}")

# Cleanup
print("\n" + "-" * 60)
print("CLEANUP")
print("-" * 60)

# Delete test POs
deleted_pos, _ = PurchaseOrder.objects.filter(
    po_number__startswith="TEST-",
    team=team
).delete()
print(f"Deleted {deleted_pos} test POs")

# Delete test emails
deleted_emails, _ = Communication.objects.filter(
    email_message_id__contains="@example.com",
    team=team
).delete()
print(f"Deleted {deleted_emails} test emails")

print("\n" + "=" * 80)
print("INTEGRATION TEST COMPLETED")
print("=" * 80)