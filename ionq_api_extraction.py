#!/usr/bin/env python3
"""
🎯 COMPLETE PO EXTRACTOR - Fully Self-Sufficient NetSuite Field Extraction
Extracts ALL NetSuite PO fields with complete line item details
Can be run anywhere with just NetSuite credentials

CRITICAL: This version combines the working parser logic with NetSuite API calls
Ready for production use in any repository
"""

import re
import json
import requests
import time
import hashlib
import hmac
import base64
import random
import string
from datetime import datetime


class CompletePOExtractor:
    def __init__(
        self, account_id, consumer_key, consumer_secret, token_id, token_secret
    ):
        """
        Initialize with NetSuite credentials

        Args:
            account_id: NetSuite account ID (e.g., '7581852_SB1' for sandbox)
            consumer_key: OAuth consumer key
            consumer_secret: OAuth consumer secret
            token_id: Token ID
            token_secret: Token secret
        """
        self.config = {
            "account_id": account_id,
            "consumer_key": consumer_key,
            "consumer_secret": consumer_secret,
            "token_id": token_id,
            "token_secret": token_secret,
        }

        self.session = requests.Session()
        account_url = account_id.lower().replace("_", "-")
        self.endpoint = f"https://{account_url}.suitetalk.api.netsuite.com/services/NetSuitePort_2023_2"

    def print_header(self, po_number, internal_id):
        print("\n" + "=" * 100)
        print("🎯 COMPLETE PO EXTRACTOR - FULLY SELF-SUFFICIENT")
        print(f"🕐 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🏢 Account: {self.config['account_id']}")
        print(f"📋 Target: {po_number} (ID: {internal_id})")
        print("🎯 Extracting ALL fields with complete line item details")
        print("=" * 100)

    def generate_oauth_signature(self):
        """Generate OAuth 1.0a signature for NetSuite authentication"""
        timestamp = str(int(time.time()))
        nonce = "".join(random.choices(string.ascii_letters + string.digits, k=20))

        base_string = f"{self.config['account_id']}&{self.config['consumer_key']}&{self.config['token_id']}&{nonce}&{timestamp}"
        key = f"{self.config['consumer_secret']}&{self.config['token_secret']}"

        signature = base64.b64encode(
            hmac.new(
                key.encode("utf-8"), base_string.encode("utf-8"), hashlib.sha256
            ).digest()
        ).decode("utf-8")

        return timestamp, nonce, signature

    def make_soap_request(self, action, body):
        """Make SOAP request to NetSuite API"""
        timestamp, nonce, signature = self.generate_oauth_signature()

        soap_request = f"""<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"
               xmlns:platformMsgs="urn:messages_2023_2.platform.webservices.netsuite.com"
               xmlns:platformCore="urn:core_2023_2.platform.webservices.netsuite.com"
               xmlns:platformCommon="urn:common_2023_2.platform.webservices.netsuite.com"
               xmlns:tranPurch="urn:purchases_2023_2.transactions.webservices.netsuite.com"
               xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <soap:Header>
        <platformMsgs:tokenPassport>
            <platformCore:account>{self.config['account_id']}</platformCore:account>
            <platformCore:consumerKey>{self.config['consumer_key']}</platformCore:consumerKey>
            <platformCore:token>{self.config['token_id']}</platformCore:token>
            <platformCore:nonce>{nonce}</platformCore:nonce>
            <platformCore:timestamp>{timestamp}</platformCore:timestamp>
            <platformCore:signature algorithm="HMAC-SHA256">{signature}</platformCore:signature>
        </platformMsgs:tokenPassport>
    </soap:Header>
    <soap:Body>
        {body}
    </soap:Body>
</soap:Envelope>"""

        headers = {"Content-Type": "text/xml; charset=utf-8", "SOAPAction": action}

        try:
            response = self.session.post(
                self.endpoint, data=soap_request, headers=headers, timeout=60
            )
            return response
        except Exception as e:
            print(f"❌ Request error: {e}")
            return None

    def test_connection(self):
        """Test NetSuite connection"""
        print("🔌 Testing NetSuite connection...")

        body = """<platformMsgs:getServerTime/>"""
        response = self.make_soap_request("getServerTime", body)

        if response and response.status_code == 200 and "serverTime" in response.text:
            print("✅ NetSuite connection successful")
            return True
        else:
            print(
                f"❌ Connection failed: {response.status_code if response else 'No response'}"
            )
            if response:
                print(f"Response: {response.text[:500]}...")
            return False

    def get_po_xml(self, internal_id):
        """Retrieve PO XML from NetSuite"""
        print(f"📖 Retrieving PO XML (ID: {internal_id})...")

        body = f"""<platformMsgs:get>
            <platformMsgs:baseRef xsi:type="platformCore:RecordRef" type="purchaseOrder" internalId="{internal_id}">
            </platformMsgs:baseRef>
        </platformMsgs:get>"""

        response = self.make_soap_request("get", body)

        if not response or response.status_code != 200:
            print(
                f"❌ Failed to retrieve PO: {response.status_code if response else 'No response'}"
            )
            return None

        if "faultstring" in response.text.lower():
            print("❌ NetSuite error in response")
            # Extract fault message
            fault_match = re.search(
                r"<faultstring>([^<]*)</faultstring>", response.text
            )
            if fault_match:
                print(f"Error: {fault_match.group(1)}")
            return None

        print("✅ PO XML retrieved successfully")
        return response.text

    def extract_all_fields(self, xml_text, po_number, internal_id):
        """
        Extract ALL fields from PO XML using the working parser logic
        This is the CRITICAL function that does the complete field extraction
        """
        print("🚀 Extracting all fields with complete line item details...")

        result = {
            "po_metadata": {
                "po_number": po_number,
                "internal_id": internal_id,
                "extraction_time": datetime.now().isoformat(),
                "extractor_version": "complete_v1.0",
                "account_id": self.config["account_id"],
            },
            "header_fields": {},
            "custom_header_fields": {},
            "line_items": [],
            "expense_lines": [],
            "summary": {},
        }

        # Extract header fields
        print("  📋 Extracting header fields...")
        header_patterns = {
            "tranId": r"<tranPurch:tranId>([^<]*)</tranPurch:tranId>",
            "memo": r"<tranPurch:memo>([^<]*)</tranPurch:memo>",
            "status": r"<tranPurch:status>([^<]*)</tranPurch:status>",
            "total": r"<tranPurch:total>([^<]*)</tranPurch:total>",
            "email": r"<tranPurch:email>([^<]*)</tranPurch:email>",
            "currencyName": r"<tranPurch:currencyName>([^<]*)</tranPurch:currencyName>",
            "exchangeRate": r"<tranPurch:exchangeRate>([^<]*)</tranPurch:exchangeRate>",
            "createdDate": r"<tranPurch:createdDate>([^<]*)</tranPurch:createdDate>",
            "lastModifiedDate": r"<tranPurch:lastModifiedDate>([^<]*)</tranPurch:lastModifiedDate>",
            "tranDate": r"<tranPurch:tranDate>([^<]*)</tranPurch:tranDate>",
            "toBePrinted": r"<tranPurch:toBePrinted>([^<]*)</tranPurch:toBePrinted>",
            "toBeEmailed": r"<tranPurch:toBeEmailed>([^<]*)</tranPurch:toBeEmailed>",
            "toBeFaxed": r"<tranPurch:toBeFaxed>([^<]*)</tranPurch:toBeFaxed>",
            "shipIsResidential": r"<tranPurch:shipIsResidential>([^<]*)</tranPurch:shipIsResidential>",
            "isClosed": r"<tranPurch:isClosed>([^<]*)</tranPurch:isClosed>",
        }

        for field_name, pattern in header_patterns.items():
            match = re.search(pattern, xml_text)
            if match and match.group(1).strip():
                result["header_fields"][field_name] = match.group(1).strip()

        print(f"    ✅ Extracted {len(result['header_fields'])} header fields")

        # Extract header custom fields (the LAST customFieldList in the document)
        print("  🎯 Extracting header custom fields...")
        all_custom_lists = re.findall(
            r"<tranPurch:customFieldList[^>]*>(.*?)</tranPurch:customFieldList>",
            xml_text,
            re.DOTALL,
        )
        if all_custom_lists:
            header_custom_content = all_custom_lists[-1]  # Last one is header-level
            header_custom_matches = re.findall(
                r'scriptId="([^"]+)"[^>]*>.*?<platformCore:value[^>]*>([^<]*)</platformCore:value>',
                header_custom_content,
                re.DOTALL,
            )

            for field, value in header_custom_matches:
                if value.strip():
                    result["custom_header_fields"][field] = value.strip()

            print(
                f"    ✅ Extracted {len(result['custom_header_fields'])} header custom fields"
            )

        # CRITICAL: Extract line items using the WORKING approach
        print("  🚀 Extracting line items with complete field data...")

        itemlist_match = re.search(
            r"<tranPurch:itemList>(.*?)</tranPurch:itemList>", xml_text, re.DOTALL
        )
        if itemlist_match:
            itemlist_content = itemlist_match.group(1)

            # Use the WORKING approach: Split by line item boundaries
            item_starts = []
            for match in re.finditer(
                r"<tranPurch:item><tranPurch:item internalId=", itemlist_content
            ):
                item_starts.append(match.start())

            item_starts.append(len(itemlist_content))  # Add end position

            print(f"    📦 Found {len(item_starts)-1} line items")

            for i in range(len(item_starts) - 1):
                start_pos = item_starts[i]
                end_pos = item_starts[i + 1]

                # Extract full line item content
                full_item_content = itemlist_content[start_pos:end_pos]
                full_item_content = re.sub(
                    r"</tranPurch:item>\s*$", "", full_item_content
                )

                print(
                    f"      Processing line {i+1}... (content length: {len(full_item_content)})"
                )

                line_item = {
                    "line_number": i + 1,
                    "item_info": {},
                    "basic_fields": {},
                    "quantities": {},
                    "financials": {},
                    "dates": {},
                    "custom_fields": {},
                    "departments": {},
                }

                # Extract item reference
                item_ref_match = re.search(
                    r'<tranPurch:item internalId="([^"]*)"[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>',
                    full_item_content,
                    re.DOTALL,
                )
                if item_ref_match:
                    line_item["item_info"] = {
                        "internal_id": item_ref_match.group(1),
                        "item_name": item_ref_match.group(2),
                    }

                # Extract basic fields
                basic_patterns = {
                    "line": r"<tranPurch:line>([^<]*)</tranPurch:line>",
                    "vendorName": r"<tranPurch:vendorName>([^<]*)</tranPurch:vendorName>",
                    "description": r"<tranPurch:description>([^<]*)</tranPurch:description>",
                    "isClosed": r"<tranPurch:isClosed>([^<]*)</tranPurch:isClosed>",
                }

                for field, pattern in basic_patterns.items():
                    match = re.search(pattern, full_item_content)
                    if match and match.group(1).strip():
                        line_item["basic_fields"][field] = match.group(1).strip()

                # Extract quantities
                quantity_patterns = {
                    "quantity": r"<tranPurch:quantity>([^<]*)</tranPurch:quantity>",
                    "quantityReceived": r"<tranPurch:quantityReceived>([^<]*)</tranPurch:quantityReceived>",
                    "quantityBilled": r"<tranPurch:quantityBilled>([^<]*)</tranPurch:quantityBilled>",
                    "quantityAvailable": r"<tranPurch:quantityAvailable>([^<]*)</tranPurch:quantityAvailable>",
                    "quantityOnHand": r"<tranPurch:quantityOnHand>([^<]*)</tranPurch:quantityOnHand>",
                }

                for field, pattern in quantity_patterns.items():
                    match = re.search(pattern, full_item_content)
                    if match and match.group(1).strip():
                        line_item["quantities"][field] = match.group(1).strip()

                # Extract financial fields
                financial_patterns = {
                    "rate": r"<tranPurch:rate>([^<]*)</tranPurch:rate>",
                    "amount": r"<tranPurch:amount>([^<]*)</tranPurch:amount>",
                }

                for field, pattern in financial_patterns.items():
                    match = re.search(pattern, full_item_content)
                    if match and match.group(1).strip():
                        line_item["financials"][field] = match.group(1).strip()

                # CRITICAL: Extract expectedReceiptDate
                date_patterns = {
                    "expectedReceiptDate": r"<tranPurch:expectedReceiptDate>([^<]*)</tranPurch:expectedReceiptDate>"
                }

                for field, pattern in date_patterns.items():
                    match = re.search(pattern, full_item_content)
                    if match and match.group(1).strip():
                        line_item["dates"][field] = match.group(1).strip()
                        print(f"        ✅ Found {field}: {match.group(1).strip()}")

                # Extract units
                units_match = re.search(
                    r"<tranPurch:units[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>",
                    full_item_content,
                    re.DOTALL,
                )
                if units_match:
                    line_item["basic_fields"]["units"] = units_match.group(1)

                # Extract department
                dept_match = re.search(
                    r"<tranPurch:department[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>",
                    full_item_content,
                    re.DOTALL,
                )
                if dept_match:
                    line_item["departments"]["department"] = dept_match.group(1)

                # CRITICAL: Extract custom fields using BOTH patterns
                custom_field_match = re.search(
                    r"<tranPurch:customFieldList[^>]*>(.*?)</tranPurch:customFieldList>",
                    full_item_content,
                    re.DOTALL,
                )
                if custom_field_match:
                    custom_content = custom_field_match.group(1)

                    # Pattern 1: Value-based fields (DateCustomFieldRef, BooleanCustomFieldRef, etc.)
                    custom_value_matches = re.findall(
                        r'scriptId="([^"]+)"[^>]*>.*?<platformCore:value[^>]*>([^<]*)</platformCore:value>',
                        custom_content,
                        re.DOTALL,
                    )

                    for field_name, value in custom_value_matches:
                        if value.strip():
                            line_item["custom_fields"][field_name] = value.strip()
                            print(
                                f"        ✅ Custom field {field_name}: {value.strip()}"
                            )

                    # Pattern 2: Name-based fields (SelectCustomFieldRef)
                    custom_name_matches = re.findall(
                        r'scriptId="([^"]+)"[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>',
                        custom_content,
                        re.DOTALL,
                    )

                    for field_name, value in custom_name_matches:
                        if (
                            value.strip()
                            and field_name not in line_item["custom_fields"]
                        ):
                            line_item["custom_fields"][field_name] = value.strip()
                            print(
                                f"        ✅ Custom field (name) {field_name}: {value.strip()}"
                            )

                # Add to results
                result["line_items"].append(line_item)

                # Count fields for verification
                total_fields = (
                    len(line_item.get("basic_fields", {}))
                    + len(line_item.get("quantities", {}))
                    + len(line_item.get("financials", {}))
                    + len(line_item.get("dates", {}))
                    + len(line_item.get("custom_fields", {}))
                    + len(line_item.get("departments", {}))
                    + (1 if line_item.get("item_info") else 0)
                )

                print(f"        📊 Line {i+1} extracted {total_fields} total fields")

        # Extract expense lines (if any)
        expense_match = re.search(
            r"<tranPurch:expenseList>(.*?)</tranPurch:expenseList>", xml_text, re.DOTALL
        )
        if expense_match:
            print("  💰 Extracting expense lines...")
            expense_content = expense_match.group(1)
            expense_blocks = re.findall(
                r"<tranPurch:expense>(.*?)</tranPurch:expense>",
                expense_content,
                re.DOTALL,
            )

            for exp_block in expense_blocks:
                expense = {"fields": {}, "custom_fields": {}}

                exp_patterns = {
                    "line": r"<tranPurch:line>([^<]*)</tranPurch:line>",
                    "amount": r"<tranPurch:amount>([^<]*)</tranPurch:amount>",
                    "memo": r"<tranPurch:memo>([^<]*)</tranPurch:memo>",
                    "isClosed": r"<tranPurch:isClosed>([^<]*)</tranPurch:isClosed>",
                }

                for field, pattern in exp_patterns.items():
                    match = re.search(pattern, exp_block)
                    if match and match.group(1).strip():
                        expense["fields"][field] = match.group(1).strip()

                # Account name
                account_match = re.search(
                    r"<tranPurch:account[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>",
                    exp_block,
                    re.DOTALL,
                )
                if account_match:
                    expense["fields"]["account"] = account_match.group(1)

                # Expense custom fields
                exp_custom_match = re.search(
                    r"<tranPurch:customFieldList[^>]*>(.*?)</tranPurch:customFieldList>",
                    exp_block,
                    re.DOTALL,
                )
                if exp_custom_match:
                    exp_custom_content = exp_custom_match.group(1)
                    exp_custom_fields = re.findall(
                        r'scriptId="([^"]+)"[^>]*>.*?<platformCore:value[^>]*>([^<]*)</platformCore:value>',
                        exp_custom_content,
                        re.DOTALL,
                    )
                    for field, value in exp_custom_fields:
                        if value.strip():
                            expense["custom_fields"][field] = value.strip()

                result["expense_lines"].append(expense)

            print(f"    ✅ Extracted {len(result['expense_lines'])} expense lines")

        # Generate comprehensive summary
        expected_receipt_count = sum(
            1
            for line in result["line_items"]
            if "expectedReceiptDate" in line.get("dates", {})
        )
        total_line_custom = sum(
            len(line.get("custom_fields", {})) for line in result["line_items"]
        )
        total_custom_fields = len(result["custom_header_fields"]) + total_line_custom

        # Check for IonQ target fields
        ionq_tracking = (
            "custbody_ionq_tracking_number" in result["custom_header_fields"]
        )
        expected_receipt_found = expected_receipt_count > 0
        supplier_promise_found = any(
            "custcol_ionq_supplierpromisedatefield" in line.get("custom_fields", {})
            for line in result["line_items"]
        )

        result["summary"] = {
            "extraction_timestamp": datetime.now().isoformat(),
            "header_fields": len(result["header_fields"]),
            "custom_header_fields": len(result["custom_header_fields"]),
            "line_items": len(result["line_items"]),
            "expense_lines": len(result["expense_lines"]),
            "total_line_custom_fields": total_line_custom,
            "total_custom_fields": total_custom_fields,
            "expected_receipt_date_occurrences": expected_receipt_count,
            "ionq_target_fields_status": {
                "custbody_ionq_tracking_number": {
                    "found": ionq_tracking,
                    "value": result["custom_header_fields"].get(
                        "custbody_ionq_tracking_number"
                    )
                    if ionq_tracking
                    else None,
                    "location": "header",
                },
                "expectedReceiptDate": {
                    "found": expected_receipt_found,
                    "occurrences": expected_receipt_count,
                    "location": "line_items",
                },
                "custcol_ionq_supplierpromisedatefield": {
                    "found": supplier_promise_found,
                    "sample_values": [
                        line["custom_fields"].get(
                            "custcol_ionq_supplierpromisedatefield"
                        )
                        for line in result["line_items"]
                        if "custcol_ionq_supplierpromisedatefield"
                        in line.get("custom_fields", {})
                    ][:3],
                    "location": "line_items",
                },
            },
        }

        return result

    def save_results(self, result, po_number):
        """Save results to JSON files"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Main results file
        json_file = f"COMPLETE_PO_{po_number}_extraction_{timestamp}.json"
        with open(json_file, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)

        # Summary report
        report_file = f"COMPLETE_PO_{po_number}_report_{timestamp}.txt"
        with open(report_file, "w", encoding="utf-8") as f:
            f.write(f"COMPLETE PO FIELD EXTRACTION REPORT - {po_number}\\n")
            f.write("=" * 80 + "\\n\\n")

            # Summary
            f.write("EXTRACTION SUMMARY:\\n")
            f.write("-" * 20 + "\\n")
            summary = result["summary"]
            f.write(f"Extraction Time: {summary['extraction_timestamp']}\\n")
            f.write(f"Header Fields: {summary['header_fields']}\\n")
            f.write(f"Custom Header Fields: {summary['custom_header_fields']}\\n")
            f.write(f"Line Items: {summary['line_items']}\\n")
            f.write(f"Expense Lines: {summary['expense_lines']}\\n")
            f.write(f"Total Custom Fields: {summary['total_custom_fields']}\\n")
            f.write(
                f"expectedReceiptDate Occurrences: {summary['expected_receipt_date_occurrences']}\\n"
            )

            # IonQ target fields
            f.write("\\nIONQ TARGET FIELDS STATUS:\\n")
            f.write("-" * 30 + "\\n")
            for field_name, status in summary["ionq_target_fields_status"].items():
                f.write(f"{field_name}:\\n")
                f.write(f"  Found: {'✅ YES' if status['found'] else '❌ NO'}\\n")
                if status.get("value"):
                    f.write(f"  Value: {status['value']}\\n")
                if status.get("occurrences"):
                    f.write(f"  Occurrences: {status['occurrences']}\\n")
                if status.get("sample_values"):
                    f.write(f"  Sample Values: {status['sample_values']}\\n")
                f.write(f"  Location: {status['location']}\\n\\n")

            # Header fields
            f.write("HEADER FIELDS:\\n")
            f.write("-" * 15 + "\\n")
            for field, value in result["header_fields"].items():
                f.write(f"{field}: {value}\\n")

            # Custom header fields
            if result["custom_header_fields"]:
                f.write("\\nCUSTOM HEADER FIELDS:\\n")
                f.write("-" * 22 + "\\n")
                for field, value in result["custom_header_fields"].items():
                    f.write(f"{field}: {value}\\n")

            # Sample line items
            f.write(
                f"\\nSAMPLE LINE ITEMS (First 3 of {len(result['line_items'])}):\\n"
            )
            f.write("-" * 40 + "\\n")

            for line in result["line_items"][:3]:
                f.write(f"\\nLINE {line['line_number']}:\\n")
                if line.get("item_info"):
                    f.write(
                        f"  Item: {line['item_info'].get('item_name', 'N/A')} (ID: {line['item_info'].get('internal_id', 'N/A')})\\n"
                    )

                if line.get("basic_fields"):
                    f.write(f"  Basic: {line['basic_fields']}\\n")

                if line.get("financials"):
                    f.write(f"  Financial: {line['financials']}\\n")

                if line.get("dates"):
                    f.write(f"  Dates: {line['dates']}\\n")

                if line.get("custom_fields"):
                    f.write(f"  Custom: {list(line['custom_fields'].keys())}\\n")

        return json_file, report_file

    def extract_po_complete(self, po_number, internal_id):
        """
        Main method: Extract complete PO field data

        Args:
            po_number: PO number (e.g., 'PO431')
            internal_id: NetSuite internal ID (e.g., '18816')

        Returns:
            dict: Complete extraction results
        """
        self.print_header(po_number, internal_id)

        # Test connection
        if not self.test_connection():
            print("❌ Cannot proceed without NetSuite connection")
            return None

        # Get PO XML
        xml_text = self.get_po_xml(internal_id)
        if not xml_text:
            print("❌ Cannot proceed without PO XML")
            return None

        # Extract all fields
        result = self.extract_all_fields(xml_text, po_number, internal_id)

        # Save results
        json_file, report_file = self.save_results(result, po_number)

        # Print final summary
        print("\\n🎉 EXTRACTION COMPLETE!")
        print("📊 FINAL SUMMARY:")
        summary = result["summary"]
        print(f"  • Header fields: {summary['header_fields']}")
        print(f"  • Custom header fields: {summary['custom_header_fields']}")
        print(f"  • Line items: {summary['line_items']}")
        print(f"  • Line-level custom fields: {summary['total_line_custom_fields']}")
        print(
            f"  • expectedReceiptDate occurrences: {summary['expected_receipt_date_occurrences']} ⭐"
        )
        print(f"  • Total custom fields: {summary['total_custom_fields']}")

        print("\\n🎯 IONQ TARGET FIELDS STATUS:")
        for field_name, status in summary["ionq_target_fields_status"].items():
            status_text = "✅ FOUND" if status["found"] else "❌ MISSING"
            print(f"  • {field_name}: {status_text}")
            if status.get("occurrences"):
                print(f"    - Occurrences: {status['occurrences']}")
            if status.get("sample_values"):
                print(f"    - Sample values: {status['sample_values']}")

        print("\\n✅ FILES GENERATED:")
        print(f"📄 Complete data: {json_file}")
        print(f"📄 Summary report: {report_file}")
        print("\\n🎯 Ready for integration mapping!")

        return result


# Example usage and configuration
def main():
    """
    Main function with example usage
    Modify the credentials and target PO as needed
    """

    # SANDBOX CREDENTIALS - REPLACE WITH YOUR ACTUAL CREDENTIALS
    credentials = {
        "account_id": "7581852_SB1",  # Replace with your account ID
        "consumer_key": "b2700a883f5bc5ddac8c462ca8e4d633deaa22cc68dec7fdc965451b419c9521",  # Replace
        "consumer_secret": "98de89dc3317cbec7f3612661d7f7e99bb8525e63a9f754f9e0f029bbed661f0",  # Replace
        "token_id": "5ec4bd683099249601f69808f2455a804e70b480c0801ea987a14b72f793e7c8",  # Replace
        "token_secret": "87e6863c2192bf8c53f410e0271bc6ed245d94651e66ed1fe8f9a40dbf6c7272",  # Replace
    }

    # TARGET PO - CHANGE AS NEEDED
    target_po = {
        "po_number": "PO431",  # Change to your target PO
        "internal_id": "18816",  # Change to your target internal ID
    }

    # Create extractor and run
    extractor = CompletePOExtractor(**credentials)
    result = extractor.extract_po_complete(
        target_po["po_number"], target_po["internal_id"]
    )

    if result:
        print("\\n🎯 EXTRACTION SUCCESS! Ready to use in any repository.")
    else:
        print("\\n❌ EXTRACTION FAILED!")


if __name__ == "__main__":
    main()
