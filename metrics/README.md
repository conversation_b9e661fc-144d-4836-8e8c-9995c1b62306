# Temporal Metrics Configuration

## Overview

This directory contains configurations for Temporal SDK metrics filtering and AWS CloudWatch dashboards. The goal is to reduce AWS CloudWatch costs by filtering only essential metrics while maintaining critical operational visibility.

## Metric Filtering

### Previous Configuration
- Collected ALL Temporal metrics using regex pattern `^temporal_.*`
- Resulted in 50+ metrics being sent to CloudWatch
- High AWS CloudWatch costs

### Current Configuration
- Explicit allowlist of 17 essential metrics using `match_type: strict`
- 66% reduction in metrics volume
- Maintains all critical monitoring capabilities
- Configured metric declarations to match exact dimension combinations

### Essential Metrics Collected

#### Workflow Health (5 metrics)
- `temporal_workflow_completed` - Successful workflow completions
- `temporal_workflow_failed` - Failed workflows
- `temporal_workflow_task_execution_latency` - Workflow task processing time
- `temporal_workflow_endtoend_latency` - Total workflow execution time
- `temporal_workflow_task_schedule_to_start_latency` - Queue delay for workflow tasks

#### Activity Health (4 metrics)
- `temporal_activity_execution_failed` - Failed activities
- `temporal_activity_execution_latency` - Activity execution time
- `temporal_activity_schedule_to_start_latency` - Queue delay for activities
- `temporal_activity_task_error` - Activity task errors

#### Queue Health (4 metrics)
- `temporal_activity_poll_no_task` - Empty activity queue polls
- `temporal_workflow_task_queue_poll_empty` - Empty workflow queue polls
- `temporal_worker_task_slots_available` - Available worker capacity
- `temporal_worker_task_slots_used` - Current worker utilization

#### Worker Health (2 metrics)
- `temporal_sticky_cache_hit` - Cache efficiency
- `temporal_sticky_cache_size` - Current cache size

#### Client Health (2 metrics)
- `temporal_request_failure` - Failed client requests
- `temporal_request_latency` - Client request latency

### Custom Workflow Metrics
- `workflow_started`
- `workflow_completed`
- `workflow_failed`

## Configuration Files

### OpenTelemetry Collector Configurations
- `/docker/otel-collector-config.yaml` - Main collector configuration
- `/docker/otel-collector-config-temporal.yaml` - Temporal-specific configuration

Key configuration changes:
1. **Filter Processor**: Uses `match_type: strict` with explicit metric names
2. **Metric Declarations**: Updated to specify exact metrics with their dimension combinations:
   - Workflow metrics: `{namespace, workflow_type, task_queue}`
   - Activity metrics: Various combinations including `{namespace, activity_type, task_queue}`
   - Worker metrics: `{namespace, task_queue}`
   - Client metrics: `{namespace}`

## CloudWatch Dashboard

### Dashboard Name: `Temporal-Metrics-Overview`

The dashboard uses CloudWatch SEARCH expressions to aggregate metrics across different dimension combinations, as Temporal SDK emits metrics with varying dimensions.

### Dashboard Sections
1. **Workflow Performance** 
   - Success/failure counts aggregated across all workflows
   - Success rate percentage with division-by-zero handling
   - End-to-end and task execution latency percentiles

2. **Activity Performance**
   - Activity failures and errors (typically 0 if healthy)
   - Execution latency percentiles across all activities

3. **Queue Health**
   - Schedule-to-start latency for workflows and activities
   - Empty poll counts indicating worker availability

4. **Worker Health**
   - Task slot utilization (available vs used)
   - Sticky cache performance metrics

5. **Client Health**
   - Request failures to Temporal service
   - Client request latency percentiles

### Dashboard Technical Details
- Uses `SEARCH` expressions to handle varying metric dimensions
- Aggregates metrics across all workflow types and task queues
- Handles missing data gracefully with `IF` conditions
- Time period: 5-minute aggregation (300 seconds)

### Accessing the Dashboard
```bash
# View dashboard in AWS Console
aws cloudwatch get-dashboard --dashboard-name "Temporal-Metrics-Overview" --profile didero-dev

# Update dashboard
aws cloudwatch put-dashboard --dashboard-name "Temporal-Metrics-Overview" --dashboard-body file://metrics/dashboards/temporal-metrics-all.json --profile didero-dev

# Direct link to dashboard
https://us-east-2.console.aws.amazon.com/cloudwatch/home?region=us-east-2#dashboards:name=Temporal-Metrics-Overview
```

## Troubleshooting

### No Data in Dashboard
1. **Check time range**: Ensure the dashboard time range includes when workflows ran
2. **Verify credentials**: OTEL collector needs valid AWS credentials
3. **Check logs**: `docker logs didero-api-otel-collector-1`
4. **Metric dimensions**: Temporal emits metrics with varying dimension combinations

### Common Issues
- **Expired AWS credentials**: Restart Docker containers after refreshing credentials
- **Metric not appearing**: Check if metric is in the filter allowlist
- **Expression errors**: Dashboard uses `IF` conditions to handle division by zero

## Rollback Instructions

If you need to collect all Temporal metrics again (e.g., for debugging):

1. In both OpenTelemetry collector config files, uncomment the original configuration:
   ```yaml
   # Original configuration (kept for rollback):
   filter/temporal:
     metrics:
       include:
         match_type: regexp
         metric_names:
           - "^temporal_.*"
           - "^workflow_.*"
   ```
2. Comment out the current strict filter configuration
3. Restart the OpenTelemetry collector: `docker-compose restart otel-collector`
4. All Temporal metrics will be collected again

## Cost Impact

This configuration change achieves:
- **66% reduction** in Temporal metrics (from 50+ to 17)
- **Significant AWS CloudWatch cost savings**
- **Maintained operational visibility** for critical metrics
- **Easy rollback** capability for debugging

## Monitoring Best Practices

1. **Queue Health**: 
   - Monitor `schedule_to_start_latency` > 1000ms indicates queue backlog
   - High `empty_poll` counts suggest over-provisioned workers

2. **Worker Capacity**: 
   - `task_slots_used` / `task_slots_available` ratio should be 50-80%
   - Too high: scale up workers
   - Too low: scale down to save costs

3. **Success Rates**: 
   - Workflow success rate should be > 95%
   - Any activity failures warrant investigation

4. **Client Health**: 
   - `temporal_request_failure` > 0 indicates connectivity issues
   - Request latency p99 > 1000ms suggests performance problems

## Metric Emission Details

Temporal SDK emits metrics with different dimension combinations:
- Workflow metrics: Always include `namespace`, `workflow_type`, `task_queue`
- Activity metrics: May have different combinations:
  - `{namespace, activity_type, task_queue}`
  - `{namespace, activity_type, workflow_type}`
  - `{namespace, task_queue, workflow_type}`
- This is why the dashboard uses flexible SEARCH expressions