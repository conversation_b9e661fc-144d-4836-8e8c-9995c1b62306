{"widgets": [{"type": "metric", "x": 0, "y": 0, "width": 12, "height": 6, "properties": {"metrics": [["Didero/Temporal", "temporal_workflow_completed", {"stat": "Sum", "label": "Completed"}], [".", "temporal_workflow_failed", {"stat": "Sum", "label": "Failed"}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "Workflow Success/Failure Rate", "period": 300, "yAxis": {"left": {"min": 0}}}}, {"type": "metric", "x": 12, "y": 0, "width": 12, "height": 6, "properties": {"metrics": [[{"expression": "m1/(m1+m2)*100", "label": "Success Rate %", "id": "e1"}], ["Didero/Temporal", "temporal_workflow_completed", {"id": "m1", "visible": false}], [".", "temporal_workflow_failed", {"id": "m2", "visible": false}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "Workflow Success Rate Percentage", "period": 300, "yAxis": {"left": {"min": 0, "max": 100}}}}, {"type": "metric", "x": 0, "y": 6, "width": 12, "height": 6, "properties": {"metrics": [["Didero/Temporal", "temporal_workflow_endtoend_latency", {"stat": "p50", "label": "p50"}], ["...", {"stat": "p90", "label": "p90"}], ["...", {"stat": "p99", "label": "p99"}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "Workflow End-to-End Latency (ms)", "period": 300, "yAxis": {"left": {"min": 0}}}}, {"type": "metric", "x": 12, "y": 6, "width": 12, "height": 6, "properties": {"metrics": [["Didero/Temporal", "temporal_workflow_task_execution_latency", {"stat": "p50", "label": "p50"}], ["...", {"stat": "p90", "label": "p90"}], ["...", {"stat": "p99", "label": "p99"}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "Workflow Task Execution Latency (ms)", "period": 300, "yAxis": {"left": {"min": 0}}}}, {"type": "metric", "x": 0, "y": 12, "width": 24, "height": 6, "properties": {"metrics": [["Didero/Temporal", "temporal_workflow_task_schedule_to_start_latency", {"stat": "p50", "label": "p50"}], ["...", {"stat": "p90", "label": "p90"}], ["...", {"stat": "p99", "label": "p99"}], ["...", {"stat": "Maximum", "label": "Max"}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "Workflow Task Queue Delay (Schedule to Start Latency) (ms)", "period": 300, "yAxis": {"left": {"min": 0}}}}, {"type": "metric", "x": 0, "y": 18, "width": 24, "height": 6, "properties": {"metrics": [["Didero/Temporal", "workflow_completed", {"stat": "Sum"}], [".", "workflow_started", {"stat": "Sum"}], [".", "workflow_failed", {"stat": "Sum"}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "Custom Workflow Metrics", "period": 300, "yAxis": {"left": {"min": 0}}}}]}