{"widgets": [{"type": "text", "x": 0, "y": 0, "width": 24, "height": 1, "properties": {"markdown": "# 📊 Temporal Metrics Dashboard\n**Monitoring workflow and activity performance, queue health, and worker status**"}}, {"type": "text", "x": 0, "y": 1, "width": 24, "height": 1, "properties": {"markdown": "## 🔄 Workflow Performance"}}, {"type": "metric", "x": 0, "y": 2, "width": 12, "height": 6, "properties": {"metrics": [["Didero/Temporal", "temporal_workflow_completed", "namespace", "default", {"stat": "Sum", "label": "Completed"}], [".", "temporal_workflow_failed", ".", ".", {"stat": "Sum", "label": "Failed"}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "Workflow Success/Failure Count (All Types)", "period": 300, "yAxis": {"left": {"min": 0}}}}, {"type": "metric", "x": 12, "y": 2, "width": 12, "height": 6, "properties": {"metrics": [[{"expression": "IF(m1+m2>0, (m1/(m1+m2))*100, 100)", "label": "Success Rate %", "id": "e1"}], ["Didero/Temporal", "temporal_workflow_completed", "namespace", "default", {"id": "m1", "visible": false}], [".", "temporal_workflow_failed", ".", ".", {"id": "m2", "visible": false}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "Workflow Success Rate %", "period": 300, "yAxis": {"left": {"min": 0, "max": 100}}}}, {"type": "metric", "x": 0, "y": 8, "width": 24, "height": 6, "properties": {"metrics": [[{"expression": "SEARCH('{Didero/Temporal,namespace,workflow_type} MetricName=\"temporal_workflow_completed\" namespace=\"default\"', 'Sum')", "id": "m1"}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "Workflow Completions by Type", "period": 300, "yAxis": {"left": {"min": 0}}}}, {"type": "metric", "x": 0, "y": 14, "width": 12, "height": 6, "properties": {"metrics": [[{"expression": "SEARCH('{Didero/Temporal,namespace,workflow_type} MetricName=\"temporal_workflow_endtoend_latency\" namespace=\"default\"', 'p50')", "id": "m1"}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "Workflow End-to-End Latency p50 by Type (ms)", "period": 300, "yAxis": {"left": {"min": 0}}}}, {"type": "metric", "x": 12, "y": 14, "width": 12, "height": 6, "properties": {"metrics": [[{"expression": "SEARCH('{Didero/Temporal,namespace,workflow_type} MetricName=\"temporal_workflow_endtoend_latency\" namespace=\"default\"', 'p90')", "id": "m1"}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "Workflow End-to-End Latency p90 by Type (ms)", "period": 300, "yAxis": {"left": {"min": 0}}}}, {"type": "text", "x": 0, "y": 20, "width": 24, "height": 1, "properties": {"markdown": "## ⚡ Activity Performance"}}, {"type": "metric", "x": 0, "y": 21, "width": 12, "height": 6, "properties": {"metrics": [["Didero/Temporal", "temporal_activity_execution_failed", "namespace", "default", {"stat": "Sum", "label": "Failed Activities"}], [".", "temporal_activity_task_error", ".", ".", {"stat": "Sum", "label": "Activity Errors"}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "Activity Failures & Errors (All Types)", "period": 300, "yAxis": {"left": {"min": 0}}}}, {"type": "metric", "x": 12, "y": 21, "width": 12, "height": 6, "properties": {"metrics": [[{"expression": "SEARCH('{Didero/Temporal,namespace,activity_type} MetricName=\"temporal_activity_execution_failed\" namespace=\"default\"', 'Sum')", "id": "m1"}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "Activity Failures by Type", "period": 300, "yAxis": {"left": {"min": 0}}}}, {"type": "metric", "x": 0, "y": 27, "width": 12, "height": 6, "properties": {"metrics": [[{"expression": "SEARCH('{Didero/Temporal,namespace,activity_type} MetricName=\"temporal_activity_execution_latency\" namespace=\"default\"', 'p50')", "id": "m1"}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "Activity Execution Latency p50 by Type (ms)", "period": 300, "yAxis": {"left": {"min": 0}}}}, {"type": "metric", "x": 12, "y": 27, "width": 12, "height": 6, "properties": {"metrics": [[{"expression": "SEARCH('{Didero/Temporal,namespace,activity_type} MetricName=\"temporal_activity_execution_latency\" namespace=\"default\"', 'p90')", "id": "m1"}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "Activity Execution Latency p90 by Type (ms)", "period": 300, "yAxis": {"left": {"min": 0}}}}, {"type": "text", "x": 0, "y": 33, "width": 24, "height": 1, "properties": {"markdown": "## 📬 Queue Health"}}, {"type": "metric", "x": 0, "y": 34, "width": 12, "height": 6, "properties": {"metrics": [["Didero/Temporal", "temporal_workflow_task_schedule_to_start_latency", "namespace", "default", {"stat": "p90", "label": "Workflow Queue Delay (p90)"}], [".", "temporal_activity_schedule_to_start_latency", ".", ".", {"stat": "p90", "label": "Activity Queue Delay (p90)"}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "Queue Delays - Schedule to Start (ms)", "period": 300, "yAxis": {"left": {"min": 0}}}}, {"type": "metric", "x": 12, "y": 34, "width": 12, "height": 6, "properties": {"metrics": [["Didero/Temporal", "temporal_workflow_task_queue_poll_empty", "namespace", "default", {"stat": "Sum", "label": "Empty Workflow Polls"}], [".", "temporal_activity_poll_no_task", ".", ".", {"stat": "Sum", "label": "Empty Activity Polls"}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "Empty Queue Polls (Lower is Better)", "period": 300, "yAxis": {"left": {"min": 0}}}}, {"type": "text", "x": 0, "y": 40, "width": 24, "height": 1, "properties": {"markdown": "## 🖥️ Worker Health"}}, {"type": "metric", "x": 0, "y": 41, "width": 12, "height": 6, "properties": {"metrics": [["Didero/Temporal", "temporal_worker_task_slots_available", "namespace", "default", {"stat": "Average", "label": "Available Slots"}], [".", "temporal_worker_task_slots_used", ".", ".", {"stat": "Average", "label": "Used Slots"}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "Worker Task Slot Utilization", "period": 300, "yAxis": {"left": {"min": 0}}}}, {"type": "metric", "x": 12, "y": 41, "width": 12, "height": 6, "properties": {"metrics": [[{"expression": "IF(m1+m2>0, (m1/(m1+m2))*100, 0)", "label": "Cache Hit Rate %", "id": "e1"}], ["Didero/Temporal", "temporal_sticky_cache_hit", "namespace", "default", {"id": "m1", "visible": false}], [".", "temporal_sticky_cache_size", ".", ".", {"id": "m2", "visible": false}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "<PERSON><PERSON>", "period": 300, "yAxis": {"left": {"min": 0, "max": 100}}}}, {"type": "text", "x": 0, "y": 47, "width": 24, "height": 1, "properties": {"markdown": "## 🌐 Client Health"}}, {"type": "metric", "x": 0, "y": 48, "width": 12, "height": 6, "properties": {"metrics": [["Didero/Temporal", "temporal_request_failure", "namespace", "default", {"stat": "Sum", "label": "Failed Requests"}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "Client Request Failures", "period": 300, "yAxis": {"left": {"min": 0}}}}, {"type": "metric", "x": 12, "y": 48, "width": 12, "height": 6, "properties": {"metrics": [["Didero/Temporal", "temporal_request_latency", "namespace", "default", {"stat": "p50", "label": "p50"}], ["...", {"stat": "p90", "label": "p90"}], ["...", {"stat": "p99", "label": "p99"}]], "view": "timeSeries", "stacked": false, "region": "us-east-2", "title": "Client Request Latency (ms)", "period": 300, "yAxis": {"left": {"min": 0}}}}]}