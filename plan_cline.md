# ERP PO Creation Integration - Implementation Plan

## Overview

This plan implements a third path in the PO creation workflow: **ERP-based PO creation**. When a team has ERP configuration enabled, the system will fetch existing PO data from their ERP system (initially NetSuite for IonQ) and recreate it in Didero.

## Current State Summary

**Existing PO Creation Paths:**
1. **Direct AI**: Email data sufficient → Create PO directly from AI-extracted data
2. **Stagehand/RPA**: Email data insufficient + RPA setup → Fetch full PO via RPA service

**Adding:**
3. **ERP Integration**: Team has ERP config enabled → Fetch PO data from ERP system

## Architecture & Data Flow

```
Email with PO Number → PO Creation Workflow → Check Team ERP Config:
├── No ERP Config → Existing logic (AI direct or Stagehand)
└── ERP Config Enabled → NEW ERP PATH:
    └── Extract PO Number from Email
    └── create_po_from_erp() Activity:
        ├── Get ERP credentials from existing system
        ├── Create NetSuite client using didero/integrations framework  
        ├── Fetch complete PO data from NetSuite SOAP API
        ├── Map NetSuite data to Didero PurchaseOrderDetails structure
        └── Call existing create_po_from_extracted_data() with mapped data
```

## Implementation Plan

### Phase 1: Core ERP PO Fetching Infrastructure

#### 1.1 Enhanced NetSuite Client (`didero/integrations/erp/clients/netsuite.py`)

**Add new methods to existing NetSuiteClient class:**

```python
def fetch_purchase_order_data(self, po_number: str) -> Dict[str, Any]:
    """
    Fetch complete PO data from NetSuite using PO number.
    Uses existing SOAP infrastructure and expands on get_purchase_order().
    
    Returns raw NetSuite PO data similar to ionq_api_extraction_enhanced_fixed.py output.
    """

def extract_po_for_didero(self, po_number: str) -> 'PurchaseOrderDetails':
    """
    High-level method that fetches PO data and maps it to Didero structure.
    This is the main entry point for the workflow activity.
    
    Returns: PurchaseOrderDetails object ready for create_po_from_extracted_data()
    """
```

**Implementation Details:**
- Leverage existing `_get_purchase_order_details()` and `get_purchase_order()` methods
- Use extraction patterns from `ionq_api_extraction_enhanced_fixed.py` as reference for field parsing
- Extract vendor data, addresses, line items, custom fields, financial data
- Handle NetSuite's XML response structure for complete PO data

#### 1.2 Data Mapping Module (`didero/integrations/erp/mappers/`)

**Create new mapping infrastructure:**

```
didero/integrations/erp/mappers/
├── __init__.py
├── base.py          # Base mapper class
└── netsuite.py      # NetSuite-specific mapping logic
```

**Key mapper function:**
```python
def map_netsuite_po_to_didero(netsuite_data: Dict[str, Any]) -> PurchaseOrderDetails:
    """
    Convert NetSuite PO structure to Didero PurchaseOrderDetails.
    
    Uses field mapping from FIXED_PO_PO431_extraction_20250721_171239.json as template:
    - Map vendor_data to supplier info
    - Map address_data to shipping/billing addresses  
    - Map line_items to order items
    - Map header_fields to PO metadata
    - Handle currency, dates, financial data
    """
```

#### 1.3 Schema Updates (`didero/integrations/erp/schemas.py`)

**Add new schemas:**

```python
class CreatePOFromERPParams(BaseModel):
    """Parameters for ERP-based PO creation"""
    email_id: Optional[str] = None
    document_id: Optional[str] = None  
    po_number: str
    team_id: int

class ERPPOFetchResult(BaseModel):
    """Result from fetching PO data from ERP"""
    success: bool
    po_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    erp_type: str
```

### Phase 2: Workflow Integration

#### 2.1 ERP PO Creation Activity (`didero/workflows/core/nodes/purchase_orders/po_creation/activities.py`)

**Add new activity:**

```python
@activity.defn
async def create_po_from_erp(params: CreatePOFromERPParams) -> POCreationResult:
    """
    Create PO by fetching data from team's configured ERP system.
    
    Process:
    1. Get team's ERP configuration 
    2. Get ERP credentials from existing credential system (.env for IonQ)
    3. Create ERP client (NetSuite for IonQ)
    4. Fetch complete PO data from ERP
    5. Map ERP data to PurchaseOrderDetails
    6. Call create_po_from_extracted_data() with mapped data
    
    This activity is only called when team has ERPIntegrationConfig with enabled=True.
    No human validation - direct creation.
    """
```

**Helper functions:**
```python
def get_erp_client_for_team(team_id: int) -> ERPClientBase:
    """Factory function to create appropriate ERP client for team"""

def has_erp_config_enabled(team_id: int) -> bool:
    """Check if team has ERP integration enabled"""
```

#### 2.2 Workflow Schema Updates (`didero/workflows/core/nodes/purchase_orders/po_creation/schemas.py`)

**Add schema:**
```python
class CreatePOFromERPParams(TypedDict):
    email_id: Optional[str]
    document_id: Optional[str] 
    po_number: str
    team_id: int
```

### Phase 3: Core Workflow Decision Logic

#### 3.1 Modified PO Creation Workflow

**Update the main workflow logic to add ERP path decision:**

```python
# In POCreationWorkflow.create_purchase_order() method:

async def create_purchase_order(
    self, 
    team_id: str, 
    po_data: Dict,
    extraction_method: str,
    human_validation_enabled: bool
) -> POCreationResult:
    
    # NEW: Check for ERP configuration first
    if await self.has_erp_integration_enabled(team_id):
        return await self.create_po_via_erp(team_id, po_data)
    
    # Existing logic for AI direct vs NetSuite/Stagehand
    if extraction_method == "ai":
        # Direct creation with extracted data
        po_result = await workflow.execute_activity("create_po_direct", ...)
    else:
        # NetSuite integration for incomplete data  
        po_result = await workflow.execute_activity("create_po_via_netsuite", ...)
```

**Add new workflow methods:**
```python
async def has_erp_integration_enabled(self, team_id: str) -> bool:
    """Check if team has ERP integration configured and enabled"""

async def create_po_via_erp(self, team_id: str, po_data: Dict) -> POCreationResult:
    """Create PO using ERP integration path"""
```

### Phase 4: Credential Management Integration

#### 4.1 Leverage Existing Credential System

**For IonQ (using .env credentials):**
- Use existing credential loading mechanism that IonQ already has in place
- No changes needed to credential models - keep using what works
- ERP client will get credentials from the same source as current NetSuite operations

**Credential loading in ERP client:**
```python
def get_ionq_credentials() -> Dict[str, str]:
    """Get IonQ NetSuite credentials from existing system (.env)"""
    # Use whatever mechanism is currently working for IonQ
    # This will be team-specific credential loading
```

### Phase 5: Configuration Detection

#### 5.1 Team Configuration Check

**Use existing ERPIntegrationConfig model:**
```python
def has_erp_config(team_id: int) -> bool:
    """Check if team has ERP integration enabled"""
    return ERPIntegrationConfig.objects.filter(
        team_id=team_id, 
        enabled=True
    ).exists()

def get_erp_config(team_id: int) -> Optional[ERPIntegrationConfig]:
    """Get team's ERP configuration"""
    return ERPIntegrationConfig.objects.filter(
        team_id=team_id,
        enabled=True
    ).first()
```

## Implementation Steps

### Step 1: Enhance NetSuite Client
- [ ] Add `fetch_purchase_order_data()` method to NetSuiteClient
- [ ] Add `extract_po_for_didero()` method 
- [ ] Test PO fetching with IonQ credentials

### Step 2: Create Data Mapping Layer  
- [ ] Create `didero/integrations/erp/mappers/` module
- [ ] Implement NetSuite → Didero mapping logic
- [ ] Test mapping with sample NetSuite PO data (use FIXED_PO_PO431_extraction_20250721_171239.json)

### Step 3: Add ERP Schemas
- [ ] Update `didero/integrations/erp/schemas.py` with new schemas
- [ ] Update workflow schemas in `po_creation/schemas.py`

### Step 4: Create ERP PO Activity
- [ ] Add `create_po_from_erp()` activity to activities.py
- [ ] Add helper functions for ERP config detection
- [ ] Test activity in isolation

### Step 5: Integrate with Main Workflow
- [ ] Modify PO creation workflow decision logic  
- [ ] Add ERP path to workflow routing
- [ ] Test complete flow with IonQ team

### Step 6: Testing & Validation
- [ ] Unit tests for mapping logic
- [ ] Integration tests with NetSuite sandbox
- [ ] End-to-end test with IonQ PO creation emails

## Key Design Decisions

### 1. **ERP-Specific Activity Pattern**
- `create_po_from_erp()` is a separate activity called only when ERP config is enabled
- Clean separation of concerns - ERP logic stays in integrations module
- Workflow just orchestrates and routes based on team configuration

### 2. **Credential Management**
- Use existing credential system that IonQ already has (.env based)
- No changes to existing credential models or loading mechanisms
- Keep what works, extend where needed

### 3. **No Human Validation**  
- ERP path bypasses human validation entirely
- POs created via ERP are automatically issued (not draft)
- Assumes ERP data is authoritative and complete

### 4. **Generic Framework, IonQ-Specific Implementation**
- Core ERP infrastructure is generic (can support other teams/ERPs later)
- IonQ-specific parts: NetSuite client usage, field mappings, credentials
- Future teams can plug into same framework with different ERP clients

### 5. **Data Reuse**
- Leverage existing `create_po_from_extracted_data()` activity
- Map ERP data to `PurchaseOrderDetails` structure
- Reuse existing PO creation, supplier matching, item matching logic

## Testing Strategy

### Unit Tests
- [ ] NetSuite client PO fetching methods
- [ ] Data mapping functions (NetSuite → Didero)
- [ ] ERP configuration detection helpers

### Integration Tests  
- [ ] End-to-end ERP PO creation with sandbox data
- [ ] Error handling (PO not found, invalid credentials, etc.)
- [ ] Workflow routing logic (ERP vs non-ERP teams)

### Manual Testing with IonQ
- [ ] Test with real IonQ PO creation emails
- [ ] Verify complete PO data mapping
- [ ] Validate supplier, address, line item creation

## Success Criteria

1. **Functional**: IonQ team receives PO creation email → System detects ERP config → Fetches PO from NetSuite → Creates complete PO in Didero
2. **Data Integrity**: All PO fields properly mapped (vendor, addresses, line items, financials, custom fields)
3. **Error Handling**: Graceful handling of NetSuite errors, missing POs, credential issues
4. **Performance**: ERP PO creation completes within reasonable time (< 30 seconds)
5. **Extensible**: Framework ready for other teams/ERP systems in future

## File Changes Summary

**New Files:**
- `didero/integrations/erp/mappers/__init__.py`
- `didero/integrations/erp/mappers/base.py` 
- `didero/integrations/erp/mappers/netsuite.py`

**Modified Files:**
- `didero/integrations/erp/clients/netsuite.py` (add PO fetching methods)
- `didero/integrations/erp/schemas.py` (add new schemas)
- `didero/workflows/core/nodes/purchase_orders/po_creation/activities.py` (add ERP activity)
- `didero/workflows/core/nodes/purchase_orders/po_creation/schemas.py` (add ERP schemas)
- Core PO creation workflow file (add ERP routing logic)

**No Changes Needed:**
- Existing credential system (reuse as-is)
- `ERPIntegrationConfig` model (already suitable)
- Existing PO creation activities (reuse via composition)

This plan provides a clear roadmap for implementing ERP-based PO creation while leveraging existing infrastructure and maintaining clean separation of concerns.
