#!/usr/bin/env python
"""
Test the auto-creation logic with mocked PO creation workflow.

This tests the resolve_purchase_order_with_auto_creation function
to ensure it properly checks permissions and capabilities.

Usage:
    uv run python test_auto_creation_mock.py
"""

import os
import sys
from unittest.mock import patch, MagicMock

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")

try:
    import django
    django.setup()
except ImportError:
    print("Django not imported properly, run with 'uv run python test_auto_creation_mock.py'")
    sys.exit(1)

from didero.workflows.shared_activities.purchase_order_operations import (
    resolve_purchase_order_with_auto_creation,
    team_has_erp_auto_creation_capability,
    should_auto_create_po_for_team,
)

print("=" * 80)
print("AUTO-CREATION LOGIC TEST WITH MOCKS")
print("=" * 80)

# Test scenarios
test_cases = [
    {
        "name": "No permission, no capability",
        "team_id": 999,  # Team without ERP
        "enable_auto_creation": False,
        "expected_auto_creation_attempted": False,
        "expected_error": "Auto-creation disabled by workflow configuration",
    },
    {
        "name": "Permission granted, no capability",
        "team_id": 999,  # Team without ERP
        "enable_auto_creation": True,
        "expected_auto_creation_attempted": False,
        "expected_error": "ERP integration not configured for auto-creation",
    },
    {
        "name": "No permission, has capability", 
        "team_id": 4,  # IonQ with ERP
        "enable_auto_creation": False,
        "expected_auto_creation_attempted": False,
        "expected_error": "Auto-creation disabled by workflow configuration",
    },
    {
        "name": "Permission granted, has capability",
        "team_id": 4,  # IonQ with ERP
        "enable_auto_creation": True,
        "expected_auto_creation_attempted": True,
        "expected_error": None,
    },
]

# Mock the enqueue function to avoid actually creating workflows
with patch('didero.workflows.shared_activities.purchase_order_operations.enqueue_po_auto_creation_workflow') as mock_enqueue:
    # Mock successful auto-creation
    mock_enqueue.return_value = {
        "success": True,
        "po_id": "123",
        "po_number": "TEST-AUTO-CREATED",
        "workflow_id": "test-workflow-123",
    }
    
    # Mock retrieve_purchase_order_activity to return not found initially
    with patch('didero.workflows.shared_activities.purchase_order_operations.retrieve_purchase_order_activity') as mock_retrieve:
        mock_retrieve.return_value = {
            "success": False,
            "po_found": False,
            "error_message": "PO not found",
        }
        
        for test_case in test_cases:
            print(f"\n" + "-" * 60)
            print(f"TEST: {test_case['name']}")
            print(f"Team ID: {test_case['team_id']}")
            print(f"Enable auto-creation: {test_case['enable_auto_creation']}")
            print("-" * 60)
            
            # Reset mock call count
            mock_enqueue.reset_mock()
            
            # Test the function
            result = resolve_purchase_order_with_auto_creation(
                po_number="TEST-MISSING-PO",
                team_id=test_case['team_id'],
                source_email_id="123",
                source_context="test",
                enable_auto_creation=test_case['enable_auto_creation'],
            )
            
            # Check results
            print(f"Auto-creation attempted: {result.get('auto_creation_attempted', False)}")
            print(f"Auto-creation error: {result.get('auto_creation_error', 'None')}")
            
            # Verify expectations
            if test_case['expected_auto_creation_attempted']:
                assert mock_enqueue.called, f"Expected auto-creation to be attempted"
                print("✓ Auto-creation was attempted as expected")
            else:
                assert not mock_enqueue.called, f"Expected auto-creation NOT to be attempted"
                print("✓ Auto-creation was NOT attempted as expected")
                
            if test_case['expected_error']:
                assert test_case['expected_error'] in result.get('auto_creation_error', ''), \
                    f"Expected error message: {test_case['expected_error']}"
                print(f"✓ Got expected error message")

# Test the helper functions directly
print("\n" + "=" * 60)
print("HELPER FUNCTION TESTS")
print("=" * 60)

# Mock ERPIntegrationConfig to control capability
with patch('didero.workflows.shared_activities.purchase_order_operations.ERPIntegrationConfig') as mock_erp:
    # Test team without ERP
    mock_erp.objects.filter.return_value.exists.return_value = False
    
    print("\nTeam 999 (mocked no ERP):")
    print(f"  Has capability: {team_has_erp_auto_creation_capability(999)}")
    print(f"  Should auto-create (permission=False): {should_auto_create_po_for_team(999, False)}")
    print(f"  Should auto-create (permission=True): {should_auto_create_po_for_team(999, True)}")
    
    # Test team with ERP
    mock_erp.objects.filter.return_value.exists.return_value = True
    
    print("\nTeam 123 (mocked with ERP):")
    print(f"  Has capability: {team_has_erp_auto_creation_capability(123)}")
    print(f"  Should auto-create (permission=False): {should_auto_create_po_for_team(123, False)}")
    print(f"  Should auto-create (permission=True): {should_auto_create_po_for_team(123, True)}")

print("\n" + "=" * 60)
print("ALL TESTS PASSED!")
print("=" * 60)