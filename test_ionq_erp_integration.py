#!/usr/bin/env python
"""
End-to-End Test Script for IonQ ERP Integration

This script:
1. Creates a test email with a known PO number that exists in IonQ's NetSuite
2. Sets up ERP configuration for IonQ team
3. Triggers the POCreationWorkflow via Temporal
4. Monitors the workflow execution with detailed logging
5. Verifies that ERP extraction worked and PO was created correctly

Usage:
    uv run python test_ionq_erp_integration.py

Prerequisites:
- IonQ NetSuite credentials must be configured in .env
- Temporal worker must be running
- Database must be accessible
"""

import asyncio
import json
import os
import sys
import time
import uuid
from datetime import datetime

# Setup Django first before importing any Django models
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")

try:
    import django

    django.setup()
except ImportError:
    print(
        "Django not imported properly, make sure you're running this with 'uv run python test_ionq_erp_integration.py'"
    )
    sys.exit(1)

# Now import Django models
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone

from didero.addresses.models import Address
from didero.emails.models import EmailThread, EmailThreadToPurchaseOrderLink
from didero.integrations.models import ERPIntegrationConfig
from didero.orders.models import PurchaseOrder
from didero.suppliers.models import Communication, Supplier
from didero.tasks.models import Task
from didero.users.models.team_models import Team
from didero.workflows.models import UserWorkflow, WorkflowBehaviorConfig

# Import Temporal client
from temporalio.client import Client

# Configuration
IONQ_TEAM_ID = 4  # IonQ test team ID
WAIT_TIME = 60  # Extended wait time for ERP operations
TEMPORAL_HOST = os.environ.get("TEMPORAL_HOST", "localhost:7233")

# Test PO number that should exist in IonQ's NetSuite
# Using PO431 which we know exists from previous testing
TEST_PO_NUMBER = "PO431"

print("=" * 80)
print("IONQ ERP INTEGRATION END-TO-END TEST")
print("=" * 80)

# Get the IonQ team
try:
    team = Team.objects.get(id=IONQ_TEAM_ID)
    print(f"\nUsing IonQ team: {team.name} (ID: {team.id})")
except Team.DoesNotExist:
    print(f"\nERROR: IonQ team with ID {IONQ_TEAM_ID} not found!")
    print("Available teams:")
    for t in Team.objects.all()[:10]:
        print(f"  - {t.name} (ID: {t.id})")
    sys.exit(1)

print("\n" + "-" * 60)
print("VERIFYING NETSUITE CREDENTIALS")
print("-" * 60)

# Check that NetSuite credentials are available
required_env_vars = [
    "IONQ_NETSUITE_ACCOUNT_ID",
    "IONQ_NETSUITE_CONSUMER_KEY",
    "IONQ_NETSUITE_CONSUMER_SECRET",
    "IONQ_NETSUITE_TOKEN_ID",
    "IONQ_NETSUITE_TOKEN_SECRET",
]

missing_vars = []
for var in required_env_vars:
    if not os.getenv(var):
        missing_vars.append(var)
    else:
        print(f"✅ {var} is configured")

if missing_vars:
    print(f"\n❌ Missing required environment variables: {missing_vars}")
    print("Please configure IonQ NetSuite credentials in .env file")
    sys.exit(1)

print("✅ All NetSuite credentials are configured")

print("\n" + "-" * 60)
print("SETTING UP ERP INTEGRATION CONFIG")
print("-" * 60)

# Create or update ERP integration config for IonQ team
erp_config, created = ERPIntegrationConfig.objects.get_or_create(
    team=team,
    defaults={
        "erp_type": "netsuite",
        "enabled": True,
        "field_mappings": {
            "po_number": "tranId",
            "supplier_name": "vendor_name",
            "memo": "vendor_notes",
        },
        "config": {
            "api_version": "2023_1",
            "test_mode": True,
        },
    },
)

if created:
    print(f"✅ Created new ERP integration config for {team.name}")
else:
    # Ensure it's enabled for testing
    if not erp_config.enabled:
        erp_config.enabled = True
        erp_config.save()
        print(f"✅ Enabled existing ERP integration config for {team.name}")
    else:
        print(f"✅ Using existing ERP integration config for {team.name}")

print(f"   - ERP Type: {erp_config.erp_type}")
print(f"   - Enabled: {erp_config.enabled}")
print(f"   - Field Mappings: {erp_config.field_mappings}")

print("\n" + "-" * 60)
print("CLEANUP: REMOVING EXISTING TEST DATA")
print("-" * 60)

# Clean up any existing POs with our test number
deleted_pos, _ = PurchaseOrder.objects.filter(
    po_number=TEST_PO_NUMBER, team=team
).delete()
print(f"🗑️  Deleted {deleted_pos} existing test POs with number {TEST_PO_NUMBER}")

# Generate unique identifiers for this test run
test_run_id = uuid.uuid4().hex[:8]
unique_message_id = f"ionq-erp-test-{test_run_id}@didero.test"

print("\n" + "-" * 60)
print("CREATING TEST EMAIL WITH PO REFERENCE")
print("-" * 60)

# Create a realistic email that references the PO number
email_body = f"""
Dear Didero Team,

Please process the attached purchase order {TEST_PO_NUMBER} for immediate fulfillment.

The order details can be found in our NetSuite system under PO number {TEST_PO_NUMBER}.

Key details:
- PO Number: {TEST_PO_NUMBER}
- Requested by: IonQ Procurement
- Priority: Standard
- Expected delivery: As per standard terms

Please confirm receipt and processing.

Best regards,
IonQ Procurement Team
<EMAIL>

---
This is a test email for ERP integration testing.
Test Run ID: {test_run_id}
Timestamp: {datetime.now().isoformat()}
"""

print(f"Creating test email referencing PO {TEST_PO_NUMBER}")

# Get or create a supplier for the email
supplier, created = Supplier.objects.get_or_create(
    name="IonQ Test Supplier",
    team=team,
    defaults={
        "website_url": f"https://ionq-test-{test_run_id}.example.com",
        "description": "Test supplier for IonQ ERP integration testing",
    },
)
if created:
    print(f"✅ Created test supplier: {supplier.name}")
else:
    print(f"✅ Using existing supplier: {supplier.name}")

# Create email thread
email_thread = EmailThread.objects.create(
    team=team, thread_id=f"ionq-erp-thread-{test_run_id}"
)

# Create the email
email = Communication.objects.create(
    team=team,
    supplier=supplier,
    email_thread=email_thread,
    email_subject=f"Purchase Order {TEST_PO_NUMBER} - Processing Request",
    email_content=email_body,
    direction="INBOUND",
    email_from="<EMAIL>",
    email_message_id=unique_message_id,
    comm_time=timezone.now(),
    comm_type=Communication.TYPE_EMAIL,
)

# Create email recipient (TO field)
from didero.suppliers.models import CommunicationEmailRecipient

CommunicationEmailRecipient.objects.create(
    email_address="<EMAIL>", communication_to=email
)

print(f"✅ Created test email with ID: {email.pk}")
print(f"   - Subject: {email.email_subject}")
print(f"   - From: {email.email_from}")
print(f"   - Message ID: {email.email_message_id}")
print(f"   - Referenced PO: {TEST_PO_NUMBER}")

print("\n" + "-" * 60)
print("SETTING UP WORKFLOW CONFIGURATION")
print("-" * 60)

# Create or get a UserWorkflow for testing
from didero.workflows.schemas import WorkflowType, WorkflowTrigger

workflow, created = UserWorkflow.objects.get_or_create(
    workflow_type=WorkflowType.PURCHASE_ORDER_CREATION.value,
    trigger=WorkflowTrigger.ON_PURCHASE_ORDER_EMAIL_RECEIVED.value,
    team=team,
    defaults={
        "current_snapshot": None,  # Core workflows don't need DAG representation
    },
)

if created:
    print(f"✅ Created new UserWorkflow with ID: {workflow.id}")
else:
    print(f"✅ Using existing UserWorkflow with ID: {workflow.id}")

# Create or update behavior config
behavior_config, created = WorkflowBehaviorConfig.objects.get_or_create(
    workflow=workflow,
    defaults={
        "config": {
            "enabled": True,
            "require_human_validation": False,  # Direct creation for testing
            "max_retry_attempts": 3,
            "retry_backoff_seconds": 10,
        }
    },
)

if not created:
    # Update the config to ensure human validation is disabled for testing
    behavior_config.config["require_human_validation"] = False
    behavior_config.config["enabled"] = True
    behavior_config.save()

print("✅ Workflow behavior configured:")
print(f"   - Enabled: {behavior_config.config.get('enabled', True)}")
print(
    f"   - Human validation: {behavior_config.config.get('require_human_validation', False)}"
)

print("\n" + "-" * 60)
print("TRIGGERING ERP-ENABLED PO CREATION WORKFLOW")
print("-" * 60)


async def run_erp_workflow():
    """Run the workflow using Temporal client with detailed logging"""
    # Connect to Temporal
    client = await Client.connect(TEMPORAL_HOST)

    # Prepare workflow parameters
    workflow_id = f"ionq-erp-test-{test_run_id}"
    params = {
        "email_id": str(email.pk),
        "team_id": str(team.id),
        "workflow_id": str(workflow.id),
    }

    print("🚀 Starting ERP-enabled workflow:")
    print(f"   - Workflow ID: {workflow_id}")
    print(f"   - Email ID: {params['email_id']}")
    print(f"   - Team ID: {params['team_id']}")
    print(f"   - UserWorkflow ID: {params['workflow_id']}")
    print(f"   - Expected PO Number: {TEST_PO_NUMBER}")

    # Import the workflow class
    from didero.workflows.core_workflows.po_creation.workflow import POCreationWorkflow

    # Start the workflow
    try:
        handle = await client.start_workflow(
            POCreationWorkflow.run,
            args=[str(workflow.id), params],
            id=workflow_id,
            task_queue="user_workflows",
        )

        print(f"✅ Workflow started with Temporal ID: {handle.id}")
        print("\n⏳ Waiting for workflow to complete...")
        print("   This may take up to 60 seconds for ERP operations...")

        # Wait for the workflow to complete
        result = await handle.result()

        print("\n🎉 Workflow completed!")
        print(f"📋 Result: {json.dumps(result, indent=2, default=str)}")

        return result

    except Exception as e:
        print(f"\n❌ Workflow execution failed: {str(e)}")
        print(f"   Error type: {type(e).__name__}")
        import traceback

        print(f"   Traceback: {traceback.format_exc()}")
        return None


# Run the workflow
print("\n🔄 Executing ERP workflow...")
start_time = time.time()
workflow_result = asyncio.run(run_erp_workflow())
end_time = time.time()
execution_time = end_time - start_time

print(f"\n⏱️  Total execution time: {execution_time:.2f} seconds")

print("\n" + "-" * 60)
print("ANALYZING WORKFLOW RESULTS")
print("-" * 60)

# Check if a PO was created
pos = PurchaseOrder.objects.filter(po_number=TEST_PO_NUMBER, team=team)
if pos.exists():
    po = pos.first()

    print("\n🎯 SUCCESS: PO created from ERP data!")
    print(f"   - PO ID: {po.pk}")
    print(f"   - PO Number: {po.po_number}")
    print(
        f"   - Supplier: {po.supplier.name if po.supplier else 'No supplier assigned'}"
    )
    print(f"   - Order Status: {po.order_status}")
    print(f"   - Total Cost: {po.total_cost}")
    print(
        f"   - Currency: {po.total_cost.currency if hasattr(po.total_cost, 'currency') else 'N/A'}"
    )
    print(f"   - Items Count: {po.items.count()}")
    print(f"   - Source: {po.source}")
    print(f"   - Is Editable: {po.is_po_editable}")
    print(f"   - Created: {po.placement_time}")

    # Show detailed item information
    if po.items.exists():
        print(f"\n📦 Order Items ({po.items.count()}):")
        for i, order_item in enumerate(po.items.all()[:5], 1):  # Show first 5 items
            item = order_item.item
            print(f"   {i}. {item.item_number}")
            print(f"      Description: {item.description}")
            print(f"      Quantity: {order_item.quantity} {order_item.unit_of_measure}")
            print(f"      Unit Price: {order_item.price}")
            if order_item.requested_date:
                print(f"      Requested Date: {order_item.requested_date}")
            print()

        if po.items.count() > 5:
            print(f"   ... and {po.items.count() - 5} more items")

    # Show address information
    if po.shipping_address:
        print("\n🚚 Shipping Address:")
        addr = po.shipping_address
        print(f"   {addr.line_1}")
        if addr.line_2:
            print(f"   {addr.line_2}")
        print(f"   {addr.city}, {addr.state_or_province} {addr.postal_code}")
        print(f"   {addr.country}")

    if po.sender_address:
        print("\n🏢 Supplier Address:")
        addr = po.sender_address
        print(f"   {addr.line_1}")
        if addr.line_2:
            print(f"   {addr.line_2}")
        print(f"   {addr.city}, {addr.state_or_province} {addr.postal_code}")
        print(f"   {addr.country}")

    # Check if email is linked
    links = EmailThreadToPurchaseOrderLink.objects.filter(purchase_order=po)
    if links.exists():
        print("\n📧 Email is properly linked to the PO")
    else:
        print("\n⚠️  Email is NOT linked to the PO")

    # Check for associated tasks
    tasks = Task.objects.filter(
        model_type=ContentType.objects.get_for_model(PurchaseOrder),
        model_id=str(po.pk),
    ).order_by("-created_at")

    if tasks.exists():
        print(f"\n📋 Associated Tasks ({tasks.count()}):")
        for task in tasks:
            print(f"   - Task ID: {task.pk}")
            print(
                f"     Type: {task.task_type_v2.name if task.task_type_v2 else 'Legacy Task'}"
            )
            print(f"     Status: {task.status}")
            print(f"     Assigned to: {task.user.email if task.user else 'Unassigned'}")
            print(f"     Created: {task.created_at}")
    else:
        print("\n📋 No associated tasks found")

else:
    print(f"\n❌ FAILURE: No PO found with number {TEST_PO_NUMBER}")
    print("   This could indicate:")
    print("   - ERP extraction failed")
    print("   - AI extraction failed to find PO number")
    print("   - PO creation failed")
    print("   - Database transaction issue")

print("\n" + "-" * 60)
print("ERP INTEGRATION TEST SUMMARY")
print("-" * 60)

if workflow_result:
    success = workflow_result.get("success", False)
    print(f"🎯 Workflow Result: {'SUCCESS' if success else 'FAILURE'}")

    if success:
        print(f"   ✅ PO ID: {workflow_result.get('po_id', 'N/A')}")
        print(f"   ✅ PO Number: {workflow_result.get('po_number', 'N/A')}")
        print(
            f"   ✅ Created in Draft: {workflow_result.get('created_in_draft', False)}"
        )

        # Determine if ERP extraction was used
        if pos.exists():
            po = pos.first()
            # Check if the PO has rich data that would indicate ERP extraction
            has_rich_data = (
                po.items.count() > 0
                and po.supplier
                and (po.shipping_address or po.sender_address)
            )
            print(f"   📊 Rich PO Data Present: {has_rich_data}")
            print("      (Indicates successful ERP extraction)")

    else:
        print(f"   ❌ Error: {workflow_result.get('error', 'Unknown error')}")
        if workflow_result.get("duplicate"):
            print("   ⚠️  Duplicate PO detected")
else:
    print("❌ Workflow execution failed or returned no result")

print("\n📈 Performance Metrics:")
print(f"   - Execution Time: {execution_time:.2f} seconds")
print("   - Expected ERP Overhead: 10-30 seconds")
print(
    f"   - Performance: {'GOOD' if execution_time < 45 else 'ACCEPTABLE' if execution_time < 90 else 'SLOW'}"
)

print("\n" + "-" * 60)
print("VERIFICATION CHECKLIST")
print("-" * 60)

checklist = []

# Check 1: ERP config exists and enabled
erp_config_check = ERPIntegrationConfig.objects.filter(team=team, enabled=True).exists()
checklist.append(("ERP Configuration", erp_config_check))

# Check 2: Credentials available
creds_check = all(os.getenv(var) for var in required_env_vars)
checklist.append(("NetSuite Credentials", creds_check))

# Check 3: PO created
po_created_check = pos.exists()
checklist.append(("PO Created", po_created_check))

# Check 4: Email linked
email_linked_check = (
    EmailThreadToPurchaseOrderLink.objects.filter(
        purchase_order__po_number=TEST_PO_NUMBER, purchase_order__team=team
    ).exists()
    if po_created_check
    else False
)
checklist.append(("Email Linked", email_linked_check))

# Check 5: Task created
task_created_check = (
    Task.objects.filter(
        model_type=ContentType.objects.get_for_model(PurchaseOrder),
        model_id=str(pos.first().pk) if pos.exists() else None,
    ).exists()
    if po_created_check
    else False
)
checklist.append(("Task Created", task_created_check))

# Check 6: Rich data (indicates ERP extraction)
rich_data_check = False
if pos.exists():
    po = pos.first()
    rich_data_check = (
        po.items.count() > 0
        and po.supplier
        and bool(po.shipping_address or po.sender_address)
    )
checklist.append(("Rich PO Data (ERP Success)", rich_data_check))

for check_name, passed in checklist:
    status = "✅ PASS" if passed else "❌ FAIL"
    print(f"   {status} {check_name}")

overall_success = all(passed for _, passed in checklist)
print(f"\n🎯 OVERALL TEST RESULT: {'SUCCESS' if overall_success else 'FAILURE'}")

if overall_success:
    print("\n🎉 IonQ ERP Integration is working correctly!")
    print("   - ERP extraction successfully fetched PO data from NetSuite")
    print("   - Data was properly mapped to Didero format")
    print("   - PO was created with complete information")
    print("   - Email was linked and tasks were created")
else:
    print("\n🚨 IonQ ERP Integration has issues that need investigation")
    failed_checks = [name for name, passed in checklist if not passed]
    print(f"   Failed checks: {', '.join(failed_checks)}")

print("\n" + "=" * 80)
print("ERP INTEGRATION TEST COMPLETED")
print("=" * 80)

# Clean up test data if successful (optional)
if overall_success:
    print("\n🧹 Cleaning up test data...")
    if pos.exists():
        # Keep the PO for manual verification, but clean up the test email
        email.delete()
        email_thread.delete()
        print("   - Removed test email and thread")
        print(f"   - Kept PO {TEST_PO_NUMBER} for manual verification")
