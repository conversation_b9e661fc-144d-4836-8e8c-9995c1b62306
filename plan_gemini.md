
# Plan for IONQ PO Creation via NetSuite Integration

This plan outlines the steps to integrate IONQ's NetSuite API for Purchase Order (PO) creation within the Didero platform.

## 1. High-Level Goal

The primary objective is to add a new PO creation method to the existing workflow. When an email for IONQ arrives, the workflow should use the NetSuite API to create the PO, rather than relying on email parsing or stagehand.

## 2. Key Components to Modify

Based on my analysis, the following components will require modification:

*   **`didero/workflows/core_workflows/po_creation/workflow.py`**: The main PO creation workflow.
*   **`didero/workflows/core_workflows/po_creation/schemas.py`**: The schema for the workflow's configuration.
*   **`didero/integrations/erp/customers/ionq/`**: The IONQ-specific integration directory.
*   **`didero/integrations/erp/field_mapper.py`**: The field mapping module.
*   **`didero/integrations/erp/client.py`**: The ERP client module.

## 3. Detailed Implementation Plan

### Step 3.1: Update Workflow Configuration

1.  **Modify `POCreationBehaviorConfig`**: In `didero/workflows/core_workflows/po_creation/schemas.py`, I will add a new boolean field called `use_erp_for_po_creation` to the `POCreationBehaviorConfig` class. This will allow us to enable or disable the ERP integration on a per-team basis.

### Step 3.2: Enhance the PO Creation Workflow

1.  **Update `POCreationWorkflow`**: In `didero/workflows/core_workflows/po_creation/workflow.py`, I will modify the `run` method to check the `use_erp_for_po_creation` flag in the workflow's configuration.
2.  **Add a New Extraction Path**: If the flag is enabled, the workflow will call a new activity to handle PO creation via the ERP integration. This will be a new method in the workflow class, similar to `extract_po_details`. I'll call it `create_po_from_erp`.
3.  **Conditional Logic**: The workflow will have a conditional statement that decides whether to call `extract_po_details` or `create_po_from_erp` based on the team's configuration.

### Step 3.3: Implement the IONQ-Specific Logic

1.  **Create `po_creation.py`**: Inside `didero/integrations/erp/customers/ionq/`, I will create a new file named `po_creation.py`. This file will contain the logic for creating a PO in NetSuite.
2.  **Implement `create_ionq_po`**: In `po_creation.py`, I will create a function called `create_ionq_po`. This function will take the PO number as input and will be responsible for:
    *   Authenticating with the NetSuite API using the credentials stored in the system.
    *   Making a SOAP request to NetSuite to fetch the PO details.
    *   Mapping the NetSuite response to Didero's internal `PurchaseOrderDetails` model.
3.  **Leverage Existing Code**: I will adapt the code from `ionq_api_extraction_enhanced_fixed.py` to create the SOAP request and parse the response.

### Step 3.4: Implement Field Mapping

1.  **Create `IonqFieldMapper`**: In `didero/integrations/erp/field_mapper.py`, I will create a new class called `IonqFieldMapper`.
2.  **Map NetSuite to Didero**: This class will be responsible for mapping the fields from the NetSuite PO response to the Didero `PurchaseOrderDetails` model. This will ensure that the data is correctly transformed between the two systems.

### Step 3.5: Create the ERP Client

1.  **Create `ErpClient`**: In `didero/integrations/erp/client.py`, I will create a new class called `ErpClient`.
2.  **Abstract ERP Interaction**: This class will provide a generic interface for interacting with different ERP systems. It will have a method called `create_po` that takes the team's configuration and the PO number as input.
3.  **Route to Customer-Specific Logic**: The `create_po` method will use the team's configuration to determine which customer-specific integration to use (in this case, IONQ). It will then call the appropriate function (e.g., `create_ionq_po`) to handle the request.

## 4. Testing

I will add unit tests for the new functionality, including:

*   A test for the `create_ionq_po` function to ensure that it correctly interacts with the NetSuite API.
*   A test for the `IonqFieldMapper` to verify that the field mapping is accurate.
*   A test for the `ErpClient` to ensure that it correctly routes requests to the appropriate customer integration.
*   An end-to-end test for the PO creation workflow to ensure that the entire process works as expected.

## 5. Summary

This plan provides a clear path forward for implementing the IONQ PO creation integration. By following these steps, I will be able to deliver a robust and well-tested solution that meets the new requirements. I will start with Step 3.1.
