#!/usr/bin/env python3
"""
🎯 ENHANCED PO EXTRACTOR - Complete NetSuite Field Extraction with Address/Vendor Support
Extracts ALL NetSuite PO fields including vendor, shipping, and billing addresses
Matches the data structure expected by RPA-based sync for seamless integration

CRITICAL ENHANCEMENTS:
- Added entity/vendor extraction at header level
- Added shipping address extraction (both raw text and structured)
- Added billing/vendor address extraction
- Structured output to match RPA/Stagehand format
"""

import re
import json
import requests
import time
import hashlib
import hmac
import base64
import random
import string
from datetime import datetime
from typing import Dict, Any, Optional, Tuple


class EnhancedPOExtractor:
    def __init__(
        self, account_id, consumer_key, consumer_secret, token_id, token_secret
    ):
        """
        Initialize with NetSuite credentials

        Args:
            account_id: NetSuite account ID (e.g., '7581852_SB1' for sandbox)
            consumer_key: OAuth consumer key
            consumer_secret: OAuth consumer secret
            token_id: Token ID
            token_secret: Token secret
        """
        self.config = {
            "account_id": account_id,
            "consumer_key": consumer_key,
            "consumer_secret": consumer_secret,
            "token_id": token_id,
            "token_secret": token_secret,
        }

        self.session = requests.Session()
        account_url = account_id.lower().replace("_", "-")
        self.endpoint = f"https://{account_url}.suitetalk.api.netsuite.com/services/NetSuitePort_2023_2"

    def print_header(self, po_number, internal_id):
        print("\n" + "=" * 100)
        print("🎯 ENHANCED PO EXTRACTOR - With Address/Vendor Support")
        print(f"🕐 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🏢 Account: {self.config['account_id']}")
        print(f"📋 Target: {po_number} (ID: {internal_id})")
        print("🎯 Extracting ALL fields including vendor/address data")
        print("=" * 100)

    def generate_oauth_signature(self):
        """Generate OAuth 1.0a signature for NetSuite authentication"""
        timestamp = str(int(time.time()))
        nonce = "".join(random.choices(string.ascii_letters + string.digits, k=20))

        base_string = f"{self.config['account_id']}&{self.config['consumer_key']}&{self.config['token_id']}&{nonce}&{timestamp}"
        key = f"{self.config['consumer_secret']}&{self.config['token_secret']}"

        signature = base64.b64encode(
            hmac.new(
                key.encode("utf-8"), base_string.encode("utf-8"), hashlib.sha256
            ).digest()
        ).decode("utf-8")

        return timestamp, nonce, signature

    def make_soap_request(self, action, body):
        """Make SOAP request to NetSuite API"""
        timestamp, nonce, signature = self.generate_oauth_signature()

        soap_request = f"""<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"
               xmlns:platformMsgs="urn:messages_2023_2.platform.webservices.netsuite.com"
               xmlns:platformCore="urn:core_2023_2.platform.webservices.netsuite.com"
               xmlns:platformCommon="urn:common_2023_2.platform.webservices.netsuite.com"
               xmlns:tranPurch="urn:purchases_2023_2.transactions.webservices.netsuite.com"
               xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <soap:Header>
        <platformMsgs:tokenPassport>
            <platformCore:account>{self.config['account_id']}</platformCore:account>
            <platformCore:consumerKey>{self.config['consumer_key']}</platformCore:consumerKey>
            <platformCore:token>{self.config['token_id']}</platformCore:token>
            <platformCore:nonce>{nonce}</platformCore:nonce>
            <platformCore:timestamp>{timestamp}</platformCore:timestamp>
            <platformCore:signature algorithm="HMAC-SHA256">{signature}</platformCore:signature>
        </platformMsgs:tokenPassport>
    </soap:Header>
    <soap:Body>
        {body}
    </soap:Body>
</soap:Envelope>"""

        headers = {"Content-Type": "text/xml; charset=utf-8", "SOAPAction": action}

        try:
            response = self.session.post(
                self.endpoint, data=soap_request, headers=headers, timeout=60
            )
            return response
        except Exception as e:
            print(f"❌ Request error: {e}")
            return None

    def get_po_xml(self, internal_id):
        """Retrieve PO XML from NetSuite"""
        print(f"📖 Retrieving PO XML (ID: {internal_id})...")

        body = f"""<platformMsgs:get>
            <platformMsgs:baseRef xsi:type="platformCore:RecordRef" type="purchaseOrder" internalId="{internal_id}">
            </platformMsgs:baseRef>
        </platformMsgs:get>"""

        response = self.make_soap_request("get", body)

        if not response or response.status_code != 200:
            print(
                f"❌ Failed to retrieve PO: {response.status_code if response else 'No response'}"
            )
            return None

        if "faultstring" in response.text.lower():
            print("❌ NetSuite error in response")
            fault_match = re.search(
                r"<faultstring>([^<]*)</faultstring>", response.text
            )
            if fault_match:
                print(f"Error: {fault_match.group(1)}")
            return None

        print("✅ PO XML retrieved successfully")
        return response.text

    def extract_entity_vendor(self, xml_text: str) -> Dict[str, Any]:
        """Extract entity/vendor information from PO XML"""
        vendor_data = {}

        # Extract entity reference (vendor)
        entity_match = re.search(
            r'<tranPurch:entity[^>]*internalId="([^"]*)"[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>',
            xml_text,
            re.DOTALL,
        )

        if entity_match:
            vendor_data["internal_id"] = entity_match.group(1)
            vendor_data["name"] = entity_match.group(2).strip()
            print(
                f"  ✅ Found vendor: {vendor_data['name']} (ID: {vendor_data['internal_id']})"
            )
        else:
            print("  ⚠️ No entity/vendor reference found")

        return vendor_data

    def extract_addresses(
        self, xml_text: str
    ) -> Tuple[Optional[str], Optional[Dict[str, Any]], Optional[Dict[str, Any]]]:
        """Extract shipping and billing addresses from PO XML"""

        # Extract raw ship_to text (for AI parsing)
        ship_to_raw = None
        ship_to_match = re.search(
            r"<tranPurch:shipTo>([^<]*)</tranPurch:shipTo>", xml_text
        )
        if ship_to_match:
            ship_to_raw = ship_to_match.group(1).strip()
            print(f"  ✅ Found raw ship_to text: {len(ship_to_raw)} characters")

        # Extract structured shipping address
        shipping_address = self._extract_structured_address(xml_text, "shippingAddress")
        if shipping_address:
            print(
                f"  ✅ Found structured shipping address: {shipping_address.get('city', 'Unknown city')}"
            )

        # Extract structured billing address
        billing_address = self._extract_structured_address(xml_text, "billAddress")
        if billing_address:
            print(
                f"  ✅ Found structured billing address: {billing_address.get('city', 'Unknown city')}"
            )

        return ship_to_raw, shipping_address, billing_address

    def _extract_structured_address(
        self, xml_text: str, address_type: str
    ) -> Optional[Dict[str, Any]]:
        """Extract structured address fields from XML"""
        address = {}

        # Find the address block
        address_match = re.search(
            f"<tranPurch:{address_type}>.*?</tranPurch:{address_type}>",
            xml_text,
            re.DOTALL,
        )

        if not address_match:
            return None

        address_xml = address_match.group(0)

        # Extract individual address fields
        address_fields = {
            "addr1": r"<platformCommon:addr1>([^<]*)</platformCommon:addr1>",
            "addr2": r"<platformCommon:addr2>([^<]*)</platformCommon:addr2>",
            "addr3": r"<platformCommon:addr3>([^<]*)</platformCommon:addr3>",
            "city": r"<platformCommon:city>([^<]*)</platformCommon:city>",
            "state": r"<platformCommon:state>([^<]*)</platformCommon:state>",
            "zip": r"<platformCommon:zip>([^<]*)</platformCommon:zip>",
            "country": r"<platformCommon:country>([^<]*)</platformCommon:country>",
            "attention": r"<platformCommon:attention>([^<]*)</platformCommon:attention>",
            "addressee": r"<platformCommon:addressee>([^<]*)</platformCommon:addressee>",
        }

        for field_name, pattern in address_fields.items():
            match = re.search(pattern, address_xml)
            if match and match.group(1).strip():
                address[field_name] = match.group(1).strip()

        # Also extract the raw address text if available
        addr_text_match = re.search(
            r"<platformCommon:addrText>([^<]*)</platformCommon:addrText>", address_xml
        )
        if addr_text_match:
            address["addrText"] = addr_text_match.group(1).strip()

        return address if address else None

    def format_vendor_address(
        self, vendor_data: Dict[str, Any], billing_address: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Format vendor address to match RPA structure"""
        vendor_address = {"company": vendor_data.get("name", "")}

        if billing_address:
            # Add address fields to vendor_address
            vendor_address.update(
                {
                    "address1": billing_address.get("addr1", ""),
                    "address2": billing_address.get("addr2", ""),
                    "city": billing_address.get("city", ""),
                    "state": billing_address.get("state", ""),
                    "zip": billing_address.get("zip", ""),
                    "country": billing_address.get("country", ""),
                    "attention": billing_address.get("attention", ""),
                    "full_address": billing_address.get("addrText", ""),
                }
            )

        return vendor_address

    def extract_all_fields_enhanced(self, xml_text, po_number, internal_id):
        """
        Extract ALL fields from PO XML including vendor and address data
        This enhanced version matches the RPA/Stagehand data structure
        """
        print("🚀 Extracting all fields with vendor and address support...")

        result = {
            "po_metadata": {
                "po_number": po_number,
                "internal_id": internal_id,
                "extraction_time": datetime.now().isoformat(),
                "extractor_version": "enhanced_v2.0",
                "account_id": self.config["account_id"],
            },
            "header_fields": {},
            "custom_header_fields": {},
            "vendor_data": {},
            "address_data": {},
            "line_items": [],
            "expense_lines": [],
            "summary": {},
        }

        # Extract header fields (same as before)
        print("  📋 Extracting header fields...")
        header_patterns = {
            "tranId": r"<tranPurch:tranId>([^<]*)</tranPurch:tranId>",
            "memo": r"<tranPurch:memo>([^<]*)</tranPurch:memo>",
            "status": r"<tranPurch:status>([^<]*)</tranPurch:status>",
            "total": r"<tranPurch:total>([^<]*)</tranPurch:total>",
            "email": r"<tranPurch:email>([^<]*)</tranPurch:email>",
            "currencyName": r"<tranPurch:currencyName>([^<]*)</tranPurch:currencyName>",
            "exchangeRate": r"<tranPurch:exchangeRate>([^<]*)</tranPurch:exchangeRate>",
            "createdDate": r"<tranPurch:createdDate>([^<]*)</tranPurch:createdDate>",
            "lastModifiedDate": r"<tranPurch:lastModifiedDate>([^<]*)</tranPurch:lastModifiedDate>",
            "tranDate": r"<tranPurch:tranDate>([^<]*)</tranPurch:tranDate>",
            "paymentTerms": r"<tranPurch:terms[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>",
            "shippingMethod": r"<tranPurch:shipMethod[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>",
        }

        for field_name, pattern in header_patterns.items():
            match = re.search(pattern, xml_text, re.DOTALL)
            if match and match.group(1).strip():
                result["header_fields"][field_name] = match.group(1).strip()

        print(f"    ✅ Extracted {len(result['header_fields'])} header fields")

        # Extract custom header fields (same as before)
        print("  🎯 Extracting header custom fields...")
        all_custom_lists = re.findall(
            r"<tranPurch:customFieldList[^>]*>(.*?)</tranPurch:customFieldList>",
            xml_text,
            re.DOTALL,
        )
        if all_custom_lists:
            header_custom_content = all_custom_lists[-1]
            header_custom_matches = re.findall(
                r'scriptId="([^"]+)"[^>]*>.*?<platformCore:value[^>]*>([^<]*)</platformCore:value>',
                header_custom_content,
                re.DOTALL,
            )

            for field, value in header_custom_matches:
                if value.strip():
                    result["custom_header_fields"][field] = value.strip()

            print(
                f"    ✅ Extracted {len(result['custom_header_fields'])} header custom fields"
            )

        # ENHANCED: Extract vendor/entity data
        print("  🏢 Extracting vendor/entity information...")
        vendor_data = self.extract_entity_vendor(xml_text)
        result["vendor_data"] = vendor_data

        # ENHANCED: Extract addresses
        print("  📍 Extracting address information...")
        ship_to_raw, shipping_address, billing_address = self.extract_addresses(
            xml_text
        )

        # Format addresses to match RPA structure
        result["address_data"] = {
            "ship_to": ship_to_raw,  # Raw text for AI parsing
            "shipping_address": shipping_address,  # Structured shipping address
            "billing_address": billing_address,  # Structured billing address
            "vendor_address": self.format_vendor_address(
                vendor_data, billing_address
            ),  # RPA-compatible vendor address
        }

        # Extract line items (same as before but with slight structure change)
        print("  🚀 Extracting line items...")
        itemlist_match = re.search(
            r"<tranPurch:itemList>(.*?)</tranPurch:itemList>", xml_text, re.DOTALL
        )
        if itemlist_match:
            itemlist_content = itemlist_match.group(1)

            # Process line items (same logic as before)
            item_starts = []
            for match in re.finditer(
                r"<tranPurch:item><tranPurch:item internalId=", itemlist_content
            ):
                item_starts.append(match.start())

            item_starts.append(len(itemlist_content))

            for i in range(len(item_starts) - 1):
                start_pos = item_starts[i]
                end_pos = item_starts[i + 1]

                full_item_content = itemlist_content[start_pos:end_pos]
                full_item_content = re.sub(
                    r"</tranPurch:item>\s*$", "", full_item_content
                )

                line_item = self._extract_line_item(full_item_content, i + 1)
                result["line_items"].append(line_item)

        # Generate enhanced summary
        result["summary"] = self._generate_enhanced_summary(result)

        return result

    def _extract_line_item(self, item_content: str, line_number: int) -> Dict[str, Any]:
        """Extract individual line item (same as original but cleaner)"""
        line_item = {
            "line_number": line_number,
            "item_info": {},
            "basic_fields": {},
            "quantities": {},
            "financials": {},
            "dates": {},
            "custom_fields": {},
            "departments": {},
        }

        # Extract item reference
        item_ref_match = re.search(
            r'<tranPurch:item internalId="([^"]*)"[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>',
            item_content,
            re.DOTALL,
        )
        if item_ref_match:
            line_item["item_info"] = {
                "internal_id": item_ref_match.group(1),
                "item_name": item_ref_match.group(2),
            }

        # Extract basic fields
        basic_patterns = {
            "line": r"<tranPurch:line>([^<]*)</tranPurch:line>",
            "vendorName": r"<tranPurch:vendorName>([^<]*)</tranPurch:vendorName>",
            "description": r"<tranPurch:description>([^<]*)</tranPurch:description>",
            "isClosed": r"<tranPurch:isClosed>([^<]*)</tranPurch:isClosed>",
        }

        for field, pattern in basic_patterns.items():
            match = re.search(pattern, item_content)
            if match and match.group(1).strip():
                line_item["basic_fields"][field] = match.group(1).strip()

        # Extract quantities
        quantity_patterns = {
            "quantity": r"<tranPurch:quantity>([^<]*)</tranPurch:quantity>",
            "quantityReceived": r"<tranPurch:quantityReceived>([^<]*)</tranPurch:quantityReceived>",
            "quantityBilled": r"<tranPurch:quantityBilled>([^<]*)</tranPurch:quantityBilled>",
        }

        for field, pattern in quantity_patterns.items():
            match = re.search(pattern, item_content)
            if match and match.group(1).strip():
                line_item["quantities"][field] = match.group(1).strip()

        # Extract financial fields
        financial_patterns = {
            "rate": r"<tranPurch:rate>([^<]*)</tranPurch:rate>",
            "amount": r"<tranPurch:amount>([^<]*)</tranPurch:amount>",
        }

        for field, pattern in financial_patterns.items():
            match = re.search(pattern, item_content)
            if match and match.group(1).strip():
                line_item["financials"][field] = match.group(1).strip()

        # Extract dates
        date_patterns = {
            "expectedReceiptDate": r"<tranPurch:expectedReceiptDate>([^<]*)</tranPurch:expectedReceiptDate>"
        }

        for field, pattern in date_patterns.items():
            match = re.search(pattern, item_content)
            if match and match.group(1).strip():
                line_item["dates"][field] = match.group(1).strip()

        # Extract custom fields
        custom_field_match = re.search(
            r"<tranPurch:customFieldList[^>]*>(.*?)</tranPurch:customFieldList>",
            item_content,
            re.DOTALL,
        )
        if custom_field_match:
            custom_content = custom_field_match.group(1)

            # Pattern 1: Value-based fields
            custom_value_matches = re.findall(
                r'scriptId="([^"]+)"[^>]*>.*?<platformCore:value[^>]*>([^<]*)</platformCore:value>',
                custom_content,
                re.DOTALL,
            )

            for field_name, value in custom_value_matches:
                if value.strip():
                    line_item["custom_fields"][field_name] = value.strip()

            # Pattern 2: Name-based fields
            custom_name_matches = re.findall(
                r'scriptId="([^"]+)"[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>',
                custom_content,
                re.DOTALL,
            )

            for field_name, value in custom_name_matches:
                if value.strip() and field_name not in line_item["custom_fields"]:
                    line_item["custom_fields"][field_name] = value.strip()

        return line_item

    def _generate_enhanced_summary(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Generate enhanced summary with vendor/address extraction status"""
        has_vendor = bool(result["vendor_data"].get("name"))
        has_ship_to = bool(result["address_data"].get("ship_to"))
        has_shipping_address = bool(result["address_data"].get("shipping_address"))
        has_billing_address = bool(result["address_data"].get("billing_address"))
        has_vendor_address = bool(
            result["address_data"].get("vendor_address", {}).get("company")
        )

        summary = {
            "extraction_timestamp": datetime.now().isoformat(),
            "header_fields": len(result["header_fields"]),
            "custom_header_fields": len(result["custom_header_fields"]),
            "line_items": len(result["line_items"]),
            "expense_lines": len(result.get("expense_lines", [])),
            "vendor_extraction": {
                "has_vendor": has_vendor,
                "vendor_name": result["vendor_data"].get("name"),
                "vendor_id": result["vendor_data"].get("internal_id"),
            },
            "address_extraction": {
                "has_ship_to_raw": has_ship_to,
                "has_shipping_address": has_shipping_address,
                "has_billing_address": has_billing_address,
                "has_vendor_address": has_vendor_address,
            },
            "ionq_target_fields_status": {
                "custbody_ionq_tracking_number": {
                    "found": "custbody_ionq_tracking_number"
                    in result["custom_header_fields"],
                    "value": result["custom_header_fields"].get(
                        "custbody_ionq_tracking_number"
                    ),
                    "location": "header",
                },
                "expectedReceiptDate": {
                    "found": any(
                        "expectedReceiptDate" in item.get("dates", {})
                        for item in result["line_items"]
                    ),
                    "occurrences": sum(
                        1
                        for item in result["line_items"]
                        if "expectedReceiptDate" in item.get("dates", {})
                    ),
                    "location": "line_items",
                },
            },
        }

        return summary

    def convert_to_rpa_format(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Convert extraction result to RPA/Stagehand-compatible format"""
        # Create flattened PO data structure
        po_data = {
            **result["header_fields"],
            **result["custom_header_fields"],
            "vendor": result["address_data"].get("ship_to", ""),  # Raw vendor text
            "vendor_address": result["address_data"].get("vendor_address", {}),
            "ship_to": result["address_data"].get(
                "ship_to", ""
            ),  # Raw shipping text for AI parsing
        }

        # Convert line items to RPA format
        line_items_data = []
        for item in result["line_items"]:
            line_item = {
                "item_number": item["item_info"].get("item_name", ""),
                "description": item["basic_fields"].get("description", ""),
                "quantity": float(item["quantities"].get("quantity", "0")),
                "unit_price_0": item["financials"].get("rate", "0"),
                "unit_price_currency": result["header_fields"].get(
                    "currencyName", "USD"
                ),
                "vendor_name": item["basic_fields"].get("vendorName", ""),
                **item["custom_fields"],
            }
            line_items_data.append(line_item)

        return {"purchase_order": po_data, "line_items": line_items_data}

    def extract_po_complete(self, po_number, internal_id):
        """
        Main method: Extract complete PO field data with vendor/address support

        Args:
            po_number: PO number (e.g., 'PO431')
            internal_id: NetSuite internal ID (e.g., '18816')

        Returns:
            dict: Complete extraction results in both native and RPA formats
        """
        self.print_header(po_number, internal_id)

        # Test connection
        # ... (connection test code same as before)

        # Get PO XML
        xml_text = self.get_po_xml(internal_id)
        if not xml_text:
            print("❌ Cannot proceed without PO XML")
            return None

        # Extract all fields with enhanced logic
        result = self.extract_all_fields_enhanced(xml_text, po_number, internal_id)

        # Convert to RPA format
        rpa_format = self.convert_to_rpa_format(result)
        result["rpa_format"] = rpa_format

        # Save results (enhanced version)
        json_file = self._save_results(result, po_number)

        # Print final summary
        self._print_enhanced_summary(result)

        return result

    def _save_results(self, result: Dict[str, Any], po_number: str) -> str:
        """Save enhanced results to JSON file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        json_file = f"ENHANCED_PO_{po_number}_extraction_{timestamp}.json"

        with open(json_file, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)

        return json_file

    def _print_enhanced_summary(self, result: Dict[str, Any]):
        """Print enhanced extraction summary"""
        summary = result["summary"]

        print("\n🎉 ENHANCED EXTRACTION COMPLETE!")
        print("📊 EXTRACTION SUMMARY:")
        print(f"  • Header fields: {summary['header_fields']}")
        print(f"  • Custom header fields: {summary['custom_header_fields']}")
        print(f"  • Line items: {summary['line_items']}")

        print("\n🏢 VENDOR/ADDRESS EXTRACTION:")
        print(
            f"  • Vendor found: {'✅' if summary['vendor_extraction']['has_vendor'] else '❌'}"
        )
        if summary["vendor_extraction"]["has_vendor"]:
            print(f"    - Name: {summary['vendor_extraction']['vendor_name']}")
            print(f"    - ID: {summary['vendor_extraction']['vendor_id']}")

        print(
            f"  • Ship-to raw text: {'✅' if summary['address_extraction']['has_ship_to_raw'] else '❌'}"
        )
        print(
            f"  • Shipping address: {'✅' if summary['address_extraction']['has_shipping_address'] else '❌'}"
        )
        print(
            f"  • Billing address: {'✅' if summary['address_extraction']['has_billing_address'] else '❌'}"
        )
        print(
            f"  • Vendor address: {'✅' if summary['address_extraction']['has_vendor_address'] else '❌'}"
        )

        print("\n🎯 RPA FORMAT CONVERSION: ✅ READY")
        print("🎯 Integration ready for Didero sync workflows!")


# Example usage
def main():
    """Main function with example usage"""

    # SANDBOX CREDENTIALS - REPLACE WITH YOUR ACTUAL CREDENTIALS
    credentials = {
        "account_id": "7581852_SB1",
        "consumer_key": "b2700a883f5bc5ddac8c462ca8e4d633deaa22cc68dec7fdc965451b419c9521",
        "consumer_secret": "98de89dc3317cbec7f3612661d7f7e99bb8525e63a9f754f9e0f029bbed661f0",
        "token_id": "5ec4bd683099249601f69808f2455a804e70b480c0801ea987a14b72f793e7c8",
        "token_secret": "87e6863c2192bf8c53f410e0271bc6ed245d94651e66ed1fe8f9a40dbf6c7272",
    }

    # TARGET PO
    target_po = {"po_number": "PO431", "internal_id": "18816"}

    # Create extractor and run
    extractor = EnhancedPOExtractor(**credentials)
    result = extractor.extract_po_complete(
        target_po["po_number"], target_po["internal_id"]
    )

    if result:
        print("\n🎯 EXTRACTION SUCCESS! Enhanced data ready for integration.")
    else:
        print("\n❌ EXTRACTION FAILED!")


if __name__ == "__main__":
    main()
