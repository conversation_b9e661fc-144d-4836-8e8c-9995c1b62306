#!/usr/bin/env python
"""
Simple test to check if Smart PO resolution is working
"""

import os
import sys

# Setup Django first
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")

try:
    import django

    django.setup()
except ImportError:
    print("Django not imported properly")
    sys.exit(1)

from didero.workflows.shared_activities.purchase_order_operations import (
    resolve_purchase_order_with_auto_creation,
    should_auto_create_po_for_team,
)

print("=== SMART PO RESOLUTION SIMPLE TEST ===")

# Test 1: Check if IonQ team should auto-create
ionq_team_id = 4
should_create = should_auto_create_po_for_team(ionq_team_id)
print(f"✅ IonQ team {ionq_team_id} should auto-create POs: {should_create}")

# Test 2: Check if non-IonQ team should NOT auto-create
non_ionq_team_id = 1
should_not_create = should_auto_create_po_for_team(non_ionq_team_id)
print(
    f"✅ Non-IonQ team {non_ionq_team_id} should auto-create POs: {should_not_create}"
)

# Test 3: Try to resolve a missing PO for IonQ (this should trigger auto-creation)
print(f"\n--- Testing PO resolution for missing PO 'PO999' ---")
try:
    result = resolve_purchase_order_with_auto_creation(
        po_number="PO999",
        team_id=ionq_team_id,
        source_email_id=None,
        source_context="test",
    )
    print(f"✅ PO resolution result: {result}")
except Exception as e:
    print(f"❌ PO resolution failed: {str(e)}")
    import traceback

    traceback.print_exc()

print("\n=== TEST COMPLETE ===")
