#!/usr/bin/env python
"""
Test script to verify Temporal queue routing is working correctly.

This script tests that workflows are being routed to the correct queues
based on their workflow type.

Usage:
    DJANGO_SETTINGS_MODULE=didero.settings.local python scripts/test_temporal_queue_routing.py
"""

import logging
import os
import sys
from typing import Dict

import django

# Setup Django environment
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.local")
django.setup()

from didero.workflows.queue_config import (
    DEFAULT_QUEUE,
    WORKFLOW_QUEUE_MAPPING,
    get_queue_for_workflow_type,
)
from didero.workflows.schemas import WorkflowType

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_queue_routing():
    """Test that each workflow type is routed to the correct queue."""
    logger.info("=" * 60)
    logger.info("TEMPORAL QUEUE ROUTING TEST")
    logger.info("=" * 60)

    # Test configured workflow types
    logger.info("\n1. Testing configured workflow types:")
    logger.info("-" * 40)

    for workflow_type, expected_queue in WORKFLOW_QUEUE_MAPPING.items():
        actual_queue = get_queue_for_workflow_type(workflow_type.value)
        status = "✅" if actual_queue == expected_queue else "❌"
        logger.info(
            f"{status} {workflow_type.value:40} -> {actual_queue:20} (expected: {expected_queue})"
        )

    # Test unconfigured workflow types (should use default)
    logger.info("\n2. Testing unconfigured workflow types (should use default):")
    logger.info("-" * 40)

    unconfigured_types = [
        WorkflowType.PURCHASE_ORDER_APPROVAL,
        WorkflowType.PURCHASE_ORDER_PLACED,
        WorkflowType.INVOICE_PROCESSING,
    ]

    for workflow_type in unconfigured_types:
        actual_queue = get_queue_for_workflow_type(workflow_type.value)
        status = "✅" if actual_queue == DEFAULT_QUEUE else "❌"
        logger.info(
            f"{status} {workflow_type.value:40} -> {actual_queue:20} (expected: {DEFAULT_QUEUE})"
        )

    # Show queue summary
    logger.info("\n3. Queue Summary:")
    logger.info("-" * 40)

    # Count workflows per queue
    queue_counts: Dict[str, int] = {DEFAULT_QUEUE: 0}
    for queue in WORKFLOW_QUEUE_MAPPING.values():
        queue_counts[queue] = 0

    # Count configured workflows
    for workflow_type, queue in WORKFLOW_QUEUE_MAPPING.items():
        queue_counts[queue] = queue_counts.get(queue, 0) + 1

    # Count unconfigured workflows
    all_workflow_types = [wf for wf in WorkflowType]
    configured_types = set(WORKFLOW_QUEUE_MAPPING.keys())
    unconfigured_count = len(all_workflow_types) - len(configured_types)
    queue_counts[DEFAULT_QUEUE] = unconfigured_count

    for queue, count in sorted(queue_counts.items()):
        logger.info(f"{queue:30} : {count} workflow type(s)")

    # Show configuration for local development
    logger.info("\n4. Local Development Configuration:")
    logger.info("-" * 40)
    logger.info("To run a worker listening to all queues:")
    logger.info("  make temporal-worker")
    logger.info("\nThis runs:")
    logger.info(
        '  python manage.py temporal_worker --queue "user_workflows,po_creation_queue,order_ack_queue,shipment_queue,follow_up_queue"'
    )

    # Show configuration for production
    logger.info("\n5. Production Configuration (ECS):")
    logger.info("-" * 40)
    logger.info("Each queue runs in a separate container with the same image:")
    logger.info("  - temporal-worker-po       : TEMPORAL_QUEUE=po_creation_queue")
    logger.info("  - temporal-worker-oa       : TEMPORAL_QUEUE=order_ack_queue")
    logger.info("  - temporal-worker-shipment : TEMPORAL_QUEUE=shipment_queue")
    logger.info("  - temporal-worker-follow-up: TEMPORAL_QUEUE=follow_up_queue")
    logger.info("  - temporal-worker-general  : TEMPORAL_QUEUE=user_workflows")


def test_workflow_execution():
    """Test that workflows can be triggered and show their queue assignment."""
    from didero.workflows.models import UserWorkflow

    logger.info("\n" + "=" * 60)
    logger.info("WORKFLOW EXECUTION TEST")
    logger.info("=" * 60)

    # Get all configured workflows
    workflows = UserWorkflow.objects.all().select_related("team")

    if not workflows:
        logger.warning("No workflows configured in the system")
        return

    logger.info(f"\nFound {len(workflows)} configured workflows:")
    logger.info("-" * 80)

    # Group by workflow type
    by_type: Dict[str, list] = {}
    for workflow in workflows:
        wf_type = workflow.workflow_type
        if wf_type not in by_type:
            by_type[wf_type] = []
        by_type[wf_type].append(workflow)

    # Show each type and its queue
    for workflow_type, workflow_list in sorted(by_type.items()):
        queue = get_queue_for_workflow_type(workflow_type)
        logger.info(f"\n{workflow_type} -> Queue: {queue}")
        for workflow in workflow_list:
            team_name = workflow.team.name if workflow.team else "Unknown"
            wf_impl = "Core" if workflow.uses_core_workflow else "DAG"
            logger.info(
                f"  - Team: {team_name:20} | Type: {wf_impl:4} | ID: {workflow.id}"
            )


def main():
    """Run all tests."""
    try:
        test_queue_routing()
        test_workflow_execution()

        logger.info("\n" + "=" * 60)
        logger.info("✅ Queue routing test completed successfully!")
        logger.info("=" * 60)

    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
