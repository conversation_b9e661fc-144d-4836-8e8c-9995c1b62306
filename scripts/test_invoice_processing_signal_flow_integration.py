#!/usr/bin/env python
"""
Integration test for invoice processing signal flow.

This script tests the actual signal flow without mocking:
1. Creates a test invoice email
2. Triggers the invoice processing workflow
3. Waits for upload task creation
4. Uploads a real PO document to properly test extraction
5. Verifies the signal is sent and workflow continues

Usage:
    DJANGO_SETTINGS_MODULE=didero.settings.local python scripts/test_invoice_processing_signal_flow_integration.py
"""

import logging
import os
import sys
import time
from datetime import datetime
from pathlib import Path

import django
from django.core.files.uploadedfile import SimpleUploadedFile
from django.utils import timezone

# Setup Django environment
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.local")
django.setup()

from didero.ai.purchase_order.schemas import PurchaseOrderDetails
from didero.documents.models import Document
from didero.documents.schemas import DocumentType
from didero.suppliers.models import Communication, CommunicationEmailRecipient, Supplier
from didero.tasks.models import Task, TaskStatus, TaskTypeV2
from didero.users.models.team_models import Team
from didero.workflows.models import UserWorkflow
from didero.workflows.schemas import WorkflowTrigger, WorkflowType
from didero.workflows.utils import trigger_workflow_if_exists_with_status

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def upload_real_test_document(team_id: str) -> Document:
    """Upload a real PO document for testing."""
    team = Team.objects.get(id=team_id)

    # Use one of the demo PO documents
    demo_po_path = (
        Path(__file__).parent.parent
        / "didero"
        / "demo"
        / "configs"
        / "documents"
        / "po_669382_original_po.pdf"
    )

    if not demo_po_path.exists():
        raise FileNotFoundError(f"Demo PO document not found at {demo_po_path}")

    # Read the actual PDF content
    with open(demo_po_path, "rb") as f:
        pdf_content = f.read()

    # Create a Document instance with the real PDF
    document = Document.objects.create(
        name="integration_test_po.pdf",
        document=SimpleUploadedFile(
            "integration_test_po.pdf", pdf_content, content_type="application/pdf"
        ),
        team=team,
        doc_type=DocumentType.PO.value,
        content_type="application/pdf",
        filesize_bytes=len(pdf_content),
    )

    logger.info(f"📄 Uploaded real PO document: {document.id}")
    logger.info(f"  Name: {document.name}")
    logger.info(f"  Size: {len(pdf_content):,} bytes")

    return document


def create_test_invoice_email(team_id: str) -> Communication:
    """Create a test invoice email."""
    import uuid

    team = Team.objects.get(id=team_id)

    # Get or create a test supplier
    supplier, _ = Supplier.objects.get_or_create(
        name="Integration Test Supplier",
        team=team,
        defaults={
            "website_url": "https://integrationtest.example.com",
        },
    )

    unique_id = str(uuid.uuid4())
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    invoice_number = f"INV-INT-{timestamp}"
    po_number = f"PO-INT-{timestamp}"

    invoice_content = f"""
INVOICE

Invoice Number: {invoice_number}
Invoice Date: {datetime.now().strftime('%B %d, %Y')}
Purchase Order: {po_number}

Bill To: Your Company
From: Integration Test Supplier

Items:
- Integration Test Service: $1,000.00

Total: $1,000.00
Payment Terms: Net 30
"""

    from didero.emails.models import EmailThread

    email_thread = EmailThread.objects.create(
        team=team,
        thread_id=f"thread-int-{unique_id}",
    )

    email = Communication.objects.create(
        team=team,
        supplier=supplier,
        email_subject=f"Invoice {invoice_number} - PO {po_number}",
        email_content=invoice_content,
        email_from="<EMAIL>",
        email_message_id=f"<invoice-int-{unique_id}@integrationtest.com>",
        direction=Communication.DIRECTION_INCOMING,
        comm_type=Communication.TYPE_EMAIL,
        comm_time=timezone.now(),
        email_thread=email_thread,
    )

    CommunicationEmailRecipient.objects.create(
        email_address="<EMAIL>", communication_to=email
    )

    logger.info(f"Created test invoice email: {email.id}")
    logger.info(f"  Subject: {email.email_subject}")
    logger.info(f"  PO Number: {po_number}")

    return email


def test_full_signal_flow_integration(team_id: str):
    """Test the complete signal flow integration."""
    try:
        team = Team.objects.get(id=team_id)
        logger.info(f"Testing signal flow integration for team: {team.name}")

        # Check if workflow exists
        workflow = UserWorkflow.objects.filter(
            workflow_type=WorkflowType.INVOICE_PROCESSING.value,
            trigger=WorkflowTrigger.ON_INVOICE_EMAIL_RECEIVED.value,
            team=team,
        ).first()

        if not workflow:
            logger.error("❌ No Invoice Processing workflow found for this team.")
            return False

        logger.info(f"Found workflow: {workflow.id}")

        # Step 1: Create test invoice email
        email = create_test_invoice_email(team_id)

        # Step 2: Trigger workflow
        logger.info("🚀 Triggering invoice processing workflow...")
        result = trigger_workflow_if_exists_with_status(
            WorkflowType.INVOICE_PROCESSING.value,
            WorkflowTrigger.ON_INVOICE_EMAIL_RECEIVED.value,
            team,
            context_object=email,
        )

        if not result.triggered:
            logger.error(f"❌ Failed to trigger workflow: {result.error_message}")
            return False

        logger.info("✅ Workflow triggered successfully!")
        logger.info(f"  Temporal ID: {result.temporal_id}")

        # Step 3: Wait for upload task creation
        logger.info("⏳ Waiting for document upload task to be created...")
        upload_task = None
        max_wait = 30  # seconds

        for i in range(max_wait):
            time.sleep(1)

            upload_task_type = TaskTypeV2.objects.filter(
                name="DOCUMENT_UPLOAD_REQUEST"
            ).first()

            if upload_task_type:
                # Find tasks of the upload type, then filter by team_id in task_config
                upload_tasks = Task.objects.filter(
                    task_type_v2=upload_task_type,
                    status=TaskStatus.PENDING.value,
                ).order_by("-created_at")

                # Filter by team_id in task_config
                for task in upload_tasks:
                    task_params = task.task_config.get("task_params", {})
                    if task_params.get("team_id") == team.id:
                        upload_task = task
                        break

                if upload_task:
                    logger.info(f"📋 Found upload task: {upload_task.id}")
                    break

            logger.info(f"  Waiting... ({i+1}/{max_wait})")

        if not upload_task:
            logger.warning("⚠️  No upload task found within timeout")
            logger.info("This may be normal if the workflow found an existing PO")
            return True

        # Step 4: Upload a real document and complete the task
        logger.info("📄 Uploading real PO document for end-to-end testing...")

        # Upload a real document
        real_document = upload_real_test_document(team_id)

        # Update task config to include real upload completion data
        upload_task.task_config = upload_task.task_config or {}
        upload_task.task_config["action_data"] = {
            "UPLOAD_DOCUMENT": {
                "uploaded_document_id": str(real_document.id),
                "document_type": "purchase_order",
            }
        }

        # Ensure workflow_id is in task params for signal targeting
        upload_task.task_config["task_params"] = upload_task.task_config.get(
            "task_params", {}
        )
        upload_task.task_config["task_params"]["workflow_id"] = result.temporal_id

        # Mark task as completed - this should trigger the signal
        upload_task.status = TaskStatus.COMPLETED.value
        upload_task.save()

        logger.info("✅ Task marked as completed with real document")
        logger.info(
            "📡 This should trigger the signal flow automatically via Django signals"
        )
        logger.info(
            f"🔄 The workflow should now extract data from document {real_document.id}"
        )

        # Step 5: Verify signal was sent (by checking logs)
        logger.info("🔍 Check the logs above for signal handling activity")
        logger.info(
            "Look for messages like 'Processing document upload task completion' and PO extraction logs"
        )

        return True

    except Exception as e:
        logger.error(f"Error during integration test: {str(e)}", exc_info=True)
        return False


def main():
    """Main function."""
    print("\n=== Invoice Processing Signal Flow Integration Test ===\n")

    # Test with team 1 (which has the workflow configured)
    success = test_full_signal_flow_integration(team_id="1")

    if success:
        print("\n✅ Integration test completed successfully!")
        print("Check the logs above for signal flow activity.")
        print("The workflow should have received the signal and continued processing.")
    else:
        print("\n❌ Integration test failed!")
        print("Check the error messages above for details.")

    print("\n" + "=" * 60)


if __name__ == "__main__":
    main()
