#!/usr/bin/env python
"""
Test script for triggering the Order Acknowledgement workflow.

This script can be used to test the Order Acknowledgement workflow with various scenarios:
1. Test with existing POs in the system
2. Inject validation errors (wrong amounts, missing items, etc.)
3. List all configured OA workflows across teams
4. Check workflow status (DAG vs Core implementation)

Usage:
    # Run with Django environment
    DJANGO_SETTINGS_MODULE=didero.settings.local python scripts/test_order_ack_workflow.py

    # Or use Django management command
    python manage.py runscript test_order_ack_workflow
"""

import logging
import os
import sys
from decimal import Decimal
from typing import Dict

import django
from django.utils import timezone

# Setup Django environment
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.local")
django.setup()


from didero.emails.models import EmailThread
from didero.orders.models import (
    LineItem,
    LineItemCategoryChoices,
    OrderItem,
    PurchaseOrder,
)
from didero.suppliers.models import Communication, CommunicationEmailRecipient
from didero.users.models.team_models import Team
from didero.workflows.core.nodes.purchase_orders.order_acknowledgement.order_ack import (
    get_purchase_order_acknowledgement_workflow_parameters,
)
from didero.workflows.models import UserWorkflow
from didero.workflows.schemas import (
    WorkflowTrigger,
    WorkflowType,
)
from didero.workflows.utils import trigger_workflow_if_exists

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Validation error scenarios
class ValidationScenario:
    NORMAL = "normal"  # Everything matches
    WRONG_TOTAL = "wrong_total"  # Total amount doesn't match
    MISSING_ITEMS = "missing_items"  # Some items missing from OA
    EXTRA_ITEMS = "extra_items"  # Extra items in OA
    WRONG_QUANTITIES = "wrong_quantities"  # Quantities don't match
    WRONG_PRICES = "wrong_prices"  # Unit prices don't match
    WRONG_FREIGHT = "wrong_freight"  # Freight cost doesn't match
    WRONG_SUPPLIER = "wrong_supplier"  # Different supplier name
    MISSING_PO = "missing_po"  # PO number doesn't exist


def get_po_details(po: PurchaseOrder) -> Dict:
    """Extract details from a PO for generating OA email."""
    items = OrderItem.objects.filter(purchase_order=po)

    # Calculate subtotal from items
    subtotal = sum(item.price.amount * Decimal(str(item.quantity)) for item in items)

    # Get tax amount from LineItems
    tax_line = LineItem.objects.filter(
        purchase_order=po, category=LineItemCategoryChoices.TAX.value
    ).first()
    tax_amount = tax_line.amount.amount if tax_line else Decimal("0.00")

    # Get freight amount from LineItems
    freight_line = LineItem.objects.filter(
        purchase_order=po, category=LineItemCategoryChoices.SHIPPING.value
    ).first()
    freight_amount = freight_line.amount.amount if freight_line else Decimal("0.00")

    return {
        "po_number": po.po_number,
        "supplier_name": po.supplier.name if po.supplier else "Unknown Supplier",
        "order_date": po.placement_time.strftime("%B %d, %Y")
        if po.placement_time
        else "N/A",
        "items": [
            {
                "sku": item.item.item_sku or item.item.item_number
                if item.item
                else f"ITEM-{item.id}",
                "description": item.item.description if item.item else "Product",
                "quantity": item.quantity,
                "unit_price": item.price.amount,
                "total_price": item.price.amount * Decimal(str(item.quantity)),
            }
            for item in items
        ],
        "subtotal": subtotal,
        "tax": tax_amount,
        "freight": freight_amount,
        "total": po.total_cost.amount if po.total_cost else Decimal("0.00"),
        "ship_to": po.shipping_address,
        "bill_to": po.sender_address,  # or None if not needed
    }


def generate_oa_email_with_scenario(
    po: PurchaseOrder, scenario: str = ValidationScenario.NORMAL
) -> str:
    """Generate an OA email based on PO details and validation scenario."""
    details = get_po_details(po)

    # Apply scenario modifications
    if scenario == ValidationScenario.WRONG_TOTAL:
        details["total"] = details["total"] + Decimal("100.00")
        logger.info("🔴 Scenario: Wrong total - Added $100 to total")

    elif scenario == ValidationScenario.MISSING_ITEMS:
        if len(details["items"]) > 1:
            details["items"] = details["items"][:-1]  # Remove last item
            logger.info("🔴 Scenario: Missing items - Removed last item from OA")

    elif scenario == ValidationScenario.EXTRA_ITEMS:
        details["items"].append(
            {
                "sku": "EXTRA-001",
                "description": "Extra Item Not in PO",
                "quantity": 5,
                "unit_price": Decimal("25.00"),
                "total_price": Decimal("125.00"),
            }
        )
        logger.info("🔴 Scenario: Extra items - Added item not in PO")

    elif scenario == ValidationScenario.WRONG_QUANTITIES:
        if details["items"]:
            details["items"][0]["quantity"] = details["items"][0]["quantity"] + 10
            logger.info(
                "🔴 Scenario: Wrong quantities - Added 10 to first item quantity"
            )

    elif scenario == ValidationScenario.WRONG_PRICES:
        if details["items"]:
            details["items"][0]["unit_price"] = details["items"][0][
                "unit_price"
            ] + Decimal("5.00")
            logger.info("🔴 Scenario: Wrong prices - Added $5 to first item unit price")

    elif scenario == ValidationScenario.WRONG_FREIGHT:
        details["freight"] = details["freight"] + Decimal("50.00")
        logger.info("🔴 Scenario: Wrong freight - Added $50 to freight cost")

    elif scenario == ValidationScenario.WRONG_SUPPLIER:
        details["supplier_name"] = "Different Supplier Inc."
        logger.info("🔴 Scenario: Wrong supplier - Changed supplier name")

    elif scenario == ValidationScenario.MISSING_PO:
        details["po_number"] = "NONEXISTENT-12345"
        logger.info("🔴 Scenario: Missing PO - Using non-existent PO number")

    # Generate email content
    email_content = f"""
Subject: Order Acknowledgement - PO #{details['po_number']}

Dear Customer,

We are pleased to confirm receipt of your purchase order:

Purchase Order Number: {details['po_number']}
Order Date: {details['order_date']}
Supplier: {details['supplier_name']}

Order Details:
-------------"""

    # Add items
    for item in details["items"]:
        email_content += f"""
Item: {item['description']} (SKU: {item['sku']})
Quantity: {item['quantity']}
Unit Price: ${item['unit_price']:.2f}
Line Total: ${item['total_price']:.2f}
"""

    # Add totals
    email_content += f"""
-------------
Subtotal: ${details['subtotal']:.2f}
Tax: ${details['tax']:.2f}
Freight: ${details['freight']:.2f}
-------------
Total Amount: ${details['total']:.2f}
"""

    # Add shipping info
    if details["ship_to"]:
        email_content += f"""
Shipping Information:
Ship To: {details['ship_to'].name or 'N/A'}
{details['ship_to'].line_1}"""
        if details["ship_to"].line_2:
            email_content += f"\n{details['ship_to'].line_2}"
        email_content += f"""
{details['ship_to'].city}, {details['ship_to'].state_or_province} {details['ship_to'].postal_code}
"""

    email_content += """
Expected Delivery: 7-10 business days
Tracking will be provided once items ship.

Thank you for your business!

Best regards,
""" + details["supplier_name"]

    return email_content


def list_recent_pos(team_id: str, limit: int = 10):
    """List recent POs for a team."""
    try:
        team = Team.objects.get(id=team_id)
        pos = PurchaseOrder.objects.filter(team=team).order_by("-created_at")[:limit]

        if not pos:
            logger.info(f"No POs found for team {team.name}")
            return

        logger.info(f"\nRecent POs for team {team.name}:")
        logger.info("-" * 80)

        for po in pos:
            items_count = OrderItem.objects.filter(purchase_order=po).count()
            oa_status = "✓ Has OA" if po.order_acknowledgements.exists() else "✗ No OA"

            logger.info(f"PO Number: {po.po_number}")
            logger.info(f"  Supplier: {po.supplier.name if po.supplier else 'N/A'}")
            logger.info(
                f"  Date: {po.placement_time.date() if po.placement_time else po.created_at.date()}"
            )
            logger.info(f"  Total: ${po.total_cost.amount:.2f}")
            logger.info(f"  Items: {items_count}")
            logger.info(f"  Status: {oa_status}")
            logger.info("-" * 40)

    except Team.DoesNotExist:
        logger.error(f"Team with ID {team_id} not found")


def test_with_po(
    team_id: str, po_number: str, scenario: str = ValidationScenario.NORMAL
):
    """
    Test Order Acknowledgement workflow with an existing PO.

    Args:
        team_id: The team ID
        po_number: The PO number to acknowledge
        scenario: Validation scenario to test
    """
    try:
        team = Team.objects.get(id=team_id)
        logger.info(f"Testing Order Acknowledgement workflow for team: {team.name}")

        # Check if workflow exists
        workflow = UserWorkflow.objects.filter(
            team=team,
            workflow_type=WorkflowType.PURCHASE_ORDER_ACKNOWLEDGEMENT,
            trigger=WorkflowTrigger.ON_PURCHASE_ORDER_ACKNOWLEDGEMENT_EMAIL_RECEIVED,
        ).first()

        if not workflow:
            logger.error("❌ No Order Acknowledgement workflow found for this team.")
            return None

        logger.info(
            f"Found workflow: {'Core' if workflow.uses_core_workflow else 'DAG'} implementation"
        )

        # Find the PO
        try:
            po = PurchaseOrder.objects.get(po_number=po_number, team=team)
            logger.info(f"✅ Found PO: {po_number}")
            logger.info(f"  Supplier: {po.supplier.name if po.supplier else 'N/A'}")
            logger.info(f"  Total: ${po.total_cost.amount:.2f}")
        except PurchaseOrder.DoesNotExist:
            logger.error(f"❌ PO {po_number} not found for team {team_id}")
            return None

        # Check if already acknowledged
        if po.order_acknowledgements.exists():
            logger.warning(f"⚠️  PO {po_number} already has an acknowledgement")
            confirm = input("Continue anyway? (y/n): ").strip().lower()
            if confirm != "y":
                return None

        # Generate OA email with scenario
        logger.info(f"\n📧 Generating OA email with scenario: {scenario}")
        oa_content = generate_oa_email_with_scenario(po, scenario)

        # Create the email
        import uuid

        unique_id = str(uuid.uuid4())

        # Create an EmailThread first
        email_thread = EmailThread.objects.create(
            team=team,
            thread_id=f"thread-oa-{unique_id}",  # Unique thread ID
        )

        email = Communication.objects.create(
            email_subject=f"Order Acknowledgement - PO #{po.po_number}",
            email_content=oa_content,
            email_from=f"orders@{po.supplier.name.lower().replace(' ', '')}.com"
            if po.supplier
            else "<EMAIL>",
            comm_time=timezone.now(),
            email_message_id=f"<test-oa-{unique_id}@example.com>",
            supplier=po.supplier,
            team=team,
            comm_type="email",
            direction="incoming",
            email_thread=email_thread,  # Associate with the email thread
        )

        # Add recipient
        CommunicationEmailRecipient.objects.create(
            email_address=f"orders@{team.name.lower().replace(' ', '')}.com",
            communication_to=email,
        )

        logger.info(f"✅ Created test OA email with ID: {email.id}")

        # Check if workflow uses core implementation
        is_core_workflow = workflow.uses_core_workflow

        # Show which queue will be used
        from didero.workflows.queue_config import get_queue_for_workflow_type

        queue_name = get_queue_for_workflow_type(
            WorkflowType.PURCHASE_ORDER_ACKNOWLEDGEMENT.value
        )
        logger.info(f"📍 Queue to be used: {queue_name}")

        # Trigger the workflow
        logger.info("\n🚀 Triggering Order Acknowledgement workflow...")
        workflow_run = trigger_workflow_if_exists(
            WorkflowType.PURCHASE_ORDER_ACKNOWLEDGEMENT.value,
            WorkflowTrigger.ON_PURCHASE_ORDER_ACKNOWLEDGEMENT_EMAIL_RECEIVED.value,
            team.id,
            get_purchase_order_acknowledgement_workflow_parameters(
                email, is_core_workflow
            ),
        )

        if workflow.uses_core_workflow:
            logger.info("✅ Core workflow triggered successfully!")
            logger.info("Check Temporal UI or logs for execution details")
        elif workflow_run:
            logger.info("✅ DAG workflow triggered successfully!")
            logger.info(f"Workflow run ID: {workflow_run.id}")
        else:
            logger.error("❌ Failed to trigger workflow")

        return email.id

    except Exception as e:
        logger.error(f"Error testing workflow: {str(e)}")
        import traceback

        traceback.print_exc()
        return None


def list_validation_scenarios():
    """List all available validation scenarios."""
    scenarios = [
        (ValidationScenario.NORMAL, "Everything matches correctly"),
        (ValidationScenario.WRONG_TOTAL, "Total amount doesn't match PO"),
        (ValidationScenario.MISSING_ITEMS, "Some items missing from OA"),
        (ValidationScenario.EXTRA_ITEMS, "Extra items in OA not in PO"),
        (ValidationScenario.WRONG_QUANTITIES, "Item quantities don't match"),
        (ValidationScenario.WRONG_PRICES, "Unit prices don't match"),
        (ValidationScenario.WRONG_FREIGHT, "Freight cost doesn't match"),
        (ValidationScenario.WRONG_SUPPLIER, "Different supplier name"),
        (ValidationScenario.MISSING_PO, "PO number doesn't exist"),
    ]

    logger.info("\nAvailable validation scenarios:")
    logger.info("-" * 60)
    for scenario, description in scenarios:
        logger.info(f"{scenario:20} - {description}")


def main():
    """Main function with interactive menu."""
    print("\n=== Order Acknowledgement Workflow Test Script ===")
    print("\nOptions:")
    print("1. List recent POs for a team")
    print("2. Test OA workflow with existing PO")
    print("3. Test with validation error scenario")
    print("4. List all validation scenarios")
    print("5. Exit")

    while True:
        choice = input("\nSelect an option (1-5): ").strip()

        if choice == "1":
            team_id = input("Enter team ID: ").strip()
            list_recent_pos(team_id)

        elif choice == "2":
            team_id = input("Enter team ID: ").strip()
            po_number = input("Enter PO number: ").strip()
            test_with_po(team_id, po_number, ValidationScenario.NORMAL)

        elif choice == "3":
            team_id = input("Enter team ID: ").strip()
            po_number = input("Enter PO number: ").strip()
            list_validation_scenarios()
            scenario = input("\nEnter scenario name: ").strip()
            test_with_po(team_id, po_number, scenario)

        elif choice == "4":
            list_validation_scenarios()

        elif choice == "5":
            print("Exiting...")
            break

        else:
            print("Invalid option. Please try again.")


if __name__ == "__main__":
    # If run with command line arguments, use them
    if len(sys.argv) > 1:
        if sys.argv[1] == "list-pos" and len(sys.argv) > 2:
            list_recent_pos(sys.argv[2])
        elif sys.argv[1] == "test" and len(sys.argv) > 3:
            team_id = sys.argv[2]
            po_number = sys.argv[3]
            scenario = sys.argv[4] if len(sys.argv) > 4 else ValidationScenario.NORMAL
            test_with_po(team_id, po_number, scenario)
        elif sys.argv[1] == "scenarios":
            list_validation_scenarios()
        else:
            print("Usage:")
            print("  python test_order_ack_workflow.py list-pos <team_id>")
            print(
                "  python test_order_ack_workflow.py test <team_id> <po_number> [scenario]"
            )
            print("  python test_order_ack_workflow.py scenarios")
    else:
        # Run interactive menu
        main()
