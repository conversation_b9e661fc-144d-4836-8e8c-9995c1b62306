#!/usr/bin/env python
"""
Test script for triggering the PO Creation workflow.

This script can be used to test the PO Creation workflow with various scenarios:
1. Generate and process test emails with PO information
2. Test with custom email content
3. List all configured PO workflows across teams

Usage:
    # Run with Django environment
    DJANGO_SETTINGS_MODULE=didero.settings.local python scripts/test_po_creation_workflow.py

    # Or use Django management command
    python manage.py runscript test_po_creation_workflow
"""

import logging
import os
import sys
from datetime import datetime
from typing import Optional

import django
from django.utils import timezone

# Setup Django environment
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.local")
django.setup()

from didero.ai.email_categorization.schemas import EmailCategory
from didero.suppliers.models import Communication
from didero.users.models.team_models import Team
from didero.workflows.core.nodes.purchase_orders.po_creation import (
    get_po_creation_workflow_parameters,
)
from didero.workflows.models import UserWorkflow
from didero.workflows.schemas import WorkflowTrigger, WorkflowType
from didero.workflows.utils import run_workflow, trigger_workflow_if_exists

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_with_email(team_id: str, po_content: Optional[str] = None):
    """
    Test PO Creation workflow by creating and processing a test email.

    Args:
        team_id: The team ID to test with
        po_content: Optional custom email content. If not provided, uses default PO email.
    """
    try:
        team = Team.objects.get(id=team_id)
        logger.info(
            f"Testing PO Creation workflow for team: {team.name} (ID: {team_id})"
        )

        # Get or create a test supplier
        from didero.suppliers.models import (
            Communication,
            CommunicationEmailRecipient,
            Supplier,
        )

        supplier, _ = Supplier.objects.get_or_create(
            name="Test Vendor Corp",
            team=team,
            defaults={
                "website_url": "https://testvendor.example.com",
            },
        )

        # Generate a unique timestamp and ID for this test
        import uuid

        unique_id = str(uuid.uuid4())
        timestamp = datetime.now().strftime("%Y%m%d-%H%M%S-%f")

        # Create a test email
        if not po_content:
            po_content = f"""
Dear Test Vendor Corp,

We would like to place the following purchase order with your company:

VENDOR INFORMATION:
Test Vendor Corp
456 Supplier Avenue
Industrial District
Commerce City, CA 90210
Phone: (*************
Contact: <EMAIL>

PO Number: PO-TEST-{timestamp}
Date: {datetime.now().strftime('%B %d, %Y')}

BILL TO:
Acme Corporation
789 Corporate Blvd
Business Park
New York, NY 10001
Accounts Payable: <EMAIL>

SHIP TO:
Acme Corporation - Warehouse
123 Main Street, Suite 100
Anytown, ST 12345
Receiving Dept: <EMAIL>

ITEMS ORDERED:
1. Widget A - SKU: WA-001 - Quantity: 100 units @ $25.00 each = $2,500.00
2. Widget B - SKU: WB-002 - Quantity: 50 units @ $15.00 each = $750.00
3. Widget C - SKU: WC-003 - Quantity: 200 units @ $5.00 each = $1,000.00

Subtotal: $4,250.00
Tax (8%): $340.00
Shipping: $50.00
Total: $4,640.00

Expected Delivery Date: {(datetime.now() + timezone.timedelta(days=30)).strftime('%B %d, %Y')}

Payment Terms: Net 30
Shipping Method: Standard Ground
FOB: Destination

Please deliver to our receiving dock between 8:00 AM - 4:00 PM Monday-Friday.
Please confirm receipt of this order and provide tracking information once shipped.

Best regards,
John Smith
Purchasing Manager
Acme Corporation
Direct: (555) 987-6543
<EMAIL>
"""

        # Import EmailThread for creating email threads
        from didero.emails.models import EmailThread

        # Create an EmailThread first
        email_thread = EmailThread.objects.create(
            team=team,
            thread_id=f"thread-{unique_id}",  # Unique thread ID
        )

        email = Communication.objects.create(
            team=team,
            supplier=supplier,
            email_subject=f"Purchase Order #PO-TEST-{timestamp}",
            email_content=po_content,
            email_from="<EMAIL>",
            email_message_id=f"<test-po-{unique_id}@testcompany.com>",  # Unique message ID
            direction=Communication.DIRECTION_OUTGOING,
            comm_type=Communication.TYPE_EMAIL,
            comm_time=timezone.now(),
            email_thread=email_thread,  # Associate with the email thread
        )

        # Create email recipients after the Communication is created
        CommunicationEmailRecipient.objects.create(
            email_address="<EMAIL>", communication_to=email
        )

        logger.info(f"Created test email with ID: {email.id}")
        logger.info(f"Email subject: {email.email_subject}")
        logger.info(f"Email from: {email.email_from}")

        # Get workflow parameters
        params = get_po_creation_workflow_parameters(email)
        logger.info(f"Workflow parameters: {params}")

        # Show which queue will be used
        from didero.workflows.queue_config import get_queue_for_workflow_type

        queue_name = get_queue_for_workflow_type(
            WorkflowType.PURCHASE_ORDER_CREATION.value
        )
        logger.info(f"Queue to be used: {queue_name}")

        # Trigger the workflow
        workflow_run = trigger_workflow_if_exists(
            WorkflowType.PURCHASE_ORDER_CREATION.value,
            WorkflowTrigger.ON_PURCHASE_ORDER_EMAIL_RECEIVED.value,
            team_id,
            params,
        )

        # Check if workflow exists first
        workflow = UserWorkflow.objects.filter(
            workflow_type=WorkflowType.PURCHASE_ORDER_CREATION.value,
            trigger=WorkflowTrigger.ON_PURCHASE_ORDER_EMAIL_RECEIVED.value,
            team_id=team_id,
        ).first()

        if not workflow:
            logger.error("❌ No PO Creation workflow found for this team.")
            logger.info(
                "Please configure the workflow first using the admin interface."
            )
            return None

        # For core workflows, workflow_run will be None (they manage their own state)
        if workflow.uses_core_workflow:
            logger.info("✅ Core workflow triggered successfully!")
            logger.info("Note: Core workflows manage their own state internally")
            logger.info("Check Temporal UI or logs for execution details")
            return email.id
        elif workflow_run:
            logger.info("✅ Workflow triggered successfully!")
            logger.info(f"Workflow run ID: {workflow_run}")
            return email.id
        else:
            logger.error("❌ Failed to trigger workflow")
            return None

    except Team.DoesNotExist:
        logger.error(f"Team with ID {team_id} not found")
    except Exception as e:
        logger.error(f"Error: {str(e)}", exc_info=True)
        return None


def list_po_creation_workflows():
    """List all configured PO Creation workflows."""
    workflows = UserWorkflow.objects.filter(
        workflow_type=WorkflowType.PURCHASE_ORDER_CREATION.value,
        trigger=WorkflowTrigger.ON_PURCHASE_ORDER_EMAIL_RECEIVED.value,
    ).select_related("team")

    if not workflows:
        logger.info("No PO Creation workflows configured")
        return

    logger.info(f"\nFound {workflows.count()} PO Creation workflows:")
    logger.info("-" * 60)

    for workflow in workflows:
        logger.info(f"Team: {workflow.team.name} (ID: {workflow.team.id})")
        logger.info(f"  Workflow ID: {workflow.id}")
        logger.info(f"  Type: {workflow.workflow_type}")
        logger.info(f"  Trigger: {workflow.trigger}")
        logger.info(f"  Core: {workflow.uses_core_workflow}")

        # Check behavior config
        if hasattr(workflow, "behavior_config"):
            config = workflow.behavior_config.get_config_as_pydantic()
            logger.info(f"  Human Validation: {config.require_human_validation}")
            logger.info(f"  Enabled: {config.enabled}")

        logger.info("-" * 60)


def main():
    """Main function with example usage."""
    print("\n=== PO Creation Workflow Test Script ===\n")

    # Example 1: List all configured workflows
    print("1. Listing all PO Creation workflows:")
    list_po_creation_workflows()

    # Example 2: Test with auto-generated email (uncomment and modify as needed)
    # print("\n2. Testing with auto-generated email:")
    test_with_email(team_id="2")  # Replace with your team ID

    # Example 3: Test with custom email content (uncomment and modify as needed)
    # print("\n3. Testing with custom email content:")
    # custom_po = """
    # Purchase Order: CUSTOM-PO-001
    # Supplier: ABC Corp
    # Items: 10x Widgets @ $50 each
    # Total: $500
    # """
    # test_with_email(team_id="2", po_content=custom_po)

    print("\n" + "=" * 40)
    print("To test a specific scenario, uncomment the relevant section in main()")
    print("Or import this module and call the functions directly:")
    print("  - test_with_email(team_id) -> email_id")
    print("  - test_with_email(team_id, po_content=custom_content) -> email_id")
    print("  - list_po_creation_workflows()")
    print("=" * 40 + "\n")


if __name__ == "__main__":
    main()
