#!/usr/bin/env python
"""
End-to-end test script for Stagehand-based carrier (ZIM, ONE, MSC, COSCO) shipment tracking with CSV import/export.

This script:
1. Creates a CSV file with carrier tracking codes (as BOL numbers)
2. Imports the CSV to create shipments
3. Runs the Celery tracking update task
4. Exports the results to CSV
5. Analyzes the results to show update statistics

Usage:
    # Test all carriers in full mode
    DJANGO_SETTINGS_MODULE=didero.settings.common uv run python scripts/shipping/test_stagehand_tracking.py

    # Quick mode (1-2 codes per carrier)
    DJANGO_SETTINGS_MODULE=didero.settings.common uv run python scripts/shipping/test_stagehand_tracking.py --quick

    # Clean up CSV files
    DJANGO_SETTINGS_MODULE=didero.settings.common uv run python scripts/shipping/test_stagehand_tracking.py --cleanup-csv
"""

import argparse
import csv
import glob
import io
import os
import signal
import sys
import time
from collections import defaultdict
from datetime import datetime

# Add the project directory to the Python path
project_root = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
)
sys.path.insert(0, project_root)

# Set up Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")

import django

django.setup()

from django.db import transaction

from didero.orders.models import Shipment
from didero.orders.schemas import CarrierType, ShipmentStatus
from didero.shipments.models import import_shipments_from_csv
from didero.shipments.serializers import ShipmentExportSerializer
from didero.shipments.tasks import process_carrier_shipments, update_slow_carriers
from didero.users.models import Team

# Carrier-specific test data (limited to ~10 tracking codes per carrier)
CARRIER_TEST_DATA = {
    "zim": {
        "name": "ZIM",
        "tracking_codes": [
            "ZIMUDMT800244171",
            "ZIMUDMT80024418",
            "ZIMUDMT80024702",
            "ZIMUDMT800248661",
            "ZIMUDMT80024867",
            "ZIMUHAI80148056",
            "ZIMUHAI80152969",
            "ZIMUHCM80532497",
            "ZIMUPKH003126085",
            "ZIMUSHH31725685",
        ],
        "api_delay": 30,  # seconds between API calls
    },
    "one": {
        "name": "ONE",
        "tracking_codes": [
            "ONEYDACF09757700",
            "ONEYDACF09757701",
            "ONEYDACF09757702",
            "ONEYDACF09758800",
            "ONEYDACF09758801",
            "ONEYDACF09758802",
            "ONEYDACF10202500",
            "ONEYDACF10202501",
            "ONEYDACF10202502",
            "ONEYDACF10804600",
        ],
        "api_delay": 30,  # Can be adjusted if ONE has different rate limits
    },
    "msc": {
        "name": "MSC",
        "tracking_codes": [
            "MEDUEV908912",
            "MEDUEV955319",
            "MEDUEV955343",
            "MEDUEV955350",
            "MEDUEV957000",
            "MEDUEV963289",
            "MEDUEV985241",
            "MEDUEV985274",
            "MEDUEV985282",
            "MEDUEV985290",
        ],
        "api_delay": 30,  # Can be adjusted if MSC has different rate limits
    },
    "cosco": {
        "name": "COSCO",
        "tracking_codes": [
            "COSU6417229150",
            "COSU6417354860",
            "COSU6417949420",
            "COSU6418142730",
            "COSU6418279380",
            "COSU6418403800",
            "COSU6418528550",
            "COSU6418531370",
            "COSU6418639930",
            "COSU6419088550",
        ],
        "api_delay": 30,  # Can be adjusted if COSCO has different rate limits
    },
}

terminate_flag = False
cleanup_tracking_codes = {}  # Dict of carrier -> tracking codes
cleanup_team = None


def signal_handler(signum, frame):
    """Handle termination signals gracefully."""
    global terminate_flag, cleanup_tracking_codes, cleanup_team
    print("\n\n⚠️  Received termination signal. Cleaning up...")
    terminate_flag = True

    # Perform cleanup if we have test data
    if cleanup_team and cleanup_tracking_codes:
        try:
            total_deleted = 0
            for carrier, tracking_codes in cleanup_tracking_codes.items():
                if tracking_codes:
                    deleted = Shipment.objects.filter(
                        team=cleanup_team, bol_number__in=tracking_codes
                    ).delete()[0]
                    total_deleted += deleted
            print(f"   Deleted {total_deleted} test shipments")
        except Exception as e:
            print(f"   Error during cleanup: {e}")

    print("   Exiting...")
    sys.exit(0)


def generate_multi_carrier_csv_content(carriers_data):
    """Generate CSV content for importing multiple carriers' shipments.

    Args:
        carriers_data: List of tuples (carrier_type, tracking_codes)
    """
    output = io.StringIO()
    writer = csv.writer(output)

    # Write header
    writer.writerow(
        [
            "tracking_number",
            "bol_number",
            "container_number",
            "carrier_type",
            "shipment_date",
            "estimated_delivery_date",
            "actual_delivery_date",
            "port_of_departure",
            "port_of_arrival",
        ]
    )

    # Write data for all carriers
    for carrier_type, tracking_codes in carriers_data:
        for code in tracking_codes:
            writer.writerow(
                [
                    "",  # tracking_number
                    code,  # bol_number (for ocean carriers)
                    "",  # container_number
                    carrier_type.upper(),  # carrier_type
                    "",  # shipment_date
                    "",  # estimated_delivery_date
                    "",  # actual_delivery_date
                    "",  # port_of_departure
                    "",  # port_of_arrival
                ]
            )

    return output.getvalue()


def export_shipments_to_csv(shipments):
    """Export shipments to CSV format."""
    output = io.StringIO()
    writer = csv.writer(output)

    # Write header
    writer.writerow(
        [
            "tracking_number",
            "bol_number",
            "container_number",
            "carrier_type",
            "shipment_date",
            "estimated_delivery_date",
            "actual_delivery_date",
            "port_of_departure",
            "port_of_arrival",
        ]
    )

    # Write data rows
    for shipment in shipments:
        serializer = ShipmentExportSerializer(shipment)
        data = serializer.data
        writer.writerow(
            [
                data.get("tracking_number", ""),
                data.get("bol_number", ""),
                data.get("container_number", ""),
                data.get("carrier_type", ""),
                data.get("shipment_date", ""),
                data.get("estimated_delivery_date", ""),
                data.get("actual_delivery_date", ""),
                data.get("port_of_departure", ""),
                data.get("port_of_arrival", ""),
            ]
        )

    return output.getvalue()


def analyze_results(initial_shipments, final_shipments):
    """Analyze the differences between initial and final shipment states."""
    results = {
        "total": len(final_shipments),
        "updated": 0,
        "with_eta": 0,
        "with_ports": 0,
        "delivered": 0,
        "failed": 0,
        "by_status": defaultdict(int),
    }

    # Create lookup for initial state
    initial_state = {s.id: s for s in initial_shipments}

    for shipment in final_shipments:
        initial = initial_state.get(shipment.id)

        # Count by status
        results["by_status"][shipment.status] += 1

        # Check if updated
        if initial and (
            shipment.estimated_delivery_date != initial.estimated_delivery_date
            or shipment.actual_delivery_date != initial.actual_delivery_date
            or shipment.metadata.get("port_of_departure")
            != initial.metadata.get("port_of_departure")
            or shipment.metadata.get("port_of_arrival")
            != initial.metadata.get("port_of_arrival")
            or shipment.status != initial.status
        ):
            results["updated"] += 1

        # Check specific fields
        if shipment.estimated_delivery_date:
            results["with_eta"] += 1

        if shipment.metadata and (
            shipment.metadata.get("port_of_departure")
            or shipment.metadata.get("port_of_arrival")
        ):
            results["with_ports"] += 1

        if shipment.status == ShipmentStatus.RECEIVED.value:
            results["delivered"] += 1

    results["failed"] = results["total"] - results["updated"]

    return results


def run_all_carriers_test(quick_mode=False):
    """Run tests for all slow carriers."""
    global cleanup_tracking_codes, cleanup_team

    # Get team
    team = Team.objects.first()
    if not team:
        print("❌ No team found in database. Please create a team first.")
        return

    cleanup_team = team

    # Define which carriers to test (slow carriers only)
    carriers_to_test = ["zim", "one", "msc", "cosco"]

    print("🚀 Starting multi-carrier tracking test")
    print(f"   Testing carriers: {', '.join([c.upper() for c in carriers_to_test])}")
    print(
        f"   Mode: {'QUICK (1-2 codes per carrier)' if quick_mode else 'FULL (up to 10 codes per carrier)'}"
    )
    print(f"   Using team: {team.name}")
    print("")

    # Collect all carrier data for CSV generation
    carriers_data = []
    all_tracking_codes = []

    for carrier_type in carriers_to_test:
        carrier_info = CARRIER_TEST_DATA[carrier_type]
        tracking_codes = carrier_info["tracking_codes"]

        # In quick mode, only use first 2 tracking codes
        if quick_mode:
            tracking_codes = tracking_codes[:2]

        carriers_data.append((carrier_type, tracking_codes))
        all_tracking_codes.extend(tracking_codes)
        cleanup_tracking_codes[carrier_type] = tracking_codes

        print(f"   {carrier_info['name']}: {len(tracking_codes)} tracking codes")

    # Clean up any existing test shipments
    print(f"\n🧹 Cleaning up any existing test shipments...")
    deleted = Shipment.objects.filter(
        team=team, bol_number__in=all_tracking_codes
    ).delete()[0]
    if deleted:
        print(f"   Deleted {deleted} existing test shipments")

    try:
        # Step 1: Generate single CSV for all carriers
        print(
            f"\n📝 Generating CSV with {len(all_tracking_codes)} tracking codes across {len(carriers_to_test)} carriers..."
        )
        csv_content = generate_multi_carrier_csv_content(carriers_data)

        # Save CSV for reference
        csv_filename = (
            f"stagehand_carriers_import_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        )
        with open(csv_filename, "w") as f:
            f.write(csv_content)
        print(f"   Saved import CSV as: {csv_filename}")

        # Step 2: Import CSV
        print("\n📥 Importing CSV to create shipments...")
        import_results = import_shipments_from_csv(csv_content, team)
        print(f"   Created: {import_results.get('created', 0)}")
        print(f"   Updated: {import_results.get('updated', 0)}")
        print(f"   Errors: {len(import_results.get('errors', []))}")

        if import_results.get("errors"):
            print("\n   Import errors:")
            for error in import_results["errors"][:5]:  # Show first 5 errors
                print(f"     Row {error['row']}: {error['errors']}")

        # Get all created shipments
        shipments = list(
            Shipment.objects.filter(
                team=team, bol_number__in=all_tracking_codes
            ).order_by("id")
        )

        if not shipments:
            print("\n❌ No shipments were created. Check the import errors above.")
            return

        print(f"\n✅ Successfully created {len(shipments)} shipments")

        # Store initial state
        initial_shipments = []
        for s in shipments:
            initial_shipments.append(Shipment.objects.get(pk=s.pk))

        # Step 3: Run tracking update task
        print(f"\n🚀 Running tracking update task for all carriers...")
        print(f"   Total shipments to process: {len(shipments)}")
        print("   Triggering Celery task...")

        start_time = time.time()

        # For testing without Celery workers, we'll call the carrier tasks directly
        try:
            # First, get the shipments grouped by carrier
            from django.db.models import Q

            # Build dynamic Q filter for slow carriers
            q_filter = Q()
            slow_carriers = ["zim", "one", "msc", "cosco"]
            for carrier in slow_carriers:
                q_filter |= Q(carrier_type=carrier.upper(), bol_number__isnull=False)

            # Get shipments grouped by carrier
            carrier_shipments = {}
            test_shipments = Shipment.objects.filter(
                team=team,
                bol_number__in=all_tracking_codes,
                status__in=[ShipmentStatus.SHIPPED],
            ).values("id", "carrier_type")

            for shipment in test_shipments:
                carrier_type = shipment["carrier_type"]
                if carrier_type not in carrier_shipments:
                    carrier_shipments[carrier_type] = []
                carrier_shipments[carrier_type].append(shipment["id"])

            if not carrier_shipments:
                print("\n   No shipments to process")
            else:
                print(f"\n   Processing {len(carrier_shipments)} carriers:")
                for carrier, ids in carrier_shipments.items():
                    print(f"     - {carrier}: {len(ids)} shipments")

                # Process each carrier's shipments directly
                # In production, these would run in parallel via Celery
                for carrier_type, shipment_ids in carrier_shipments.items():
                    print(f"\n   Processing {carrier_type}...")
                    result = process_carrier_shipments(carrier_type, shipment_ids)
                    print(f"     Result: {result}")

            print("\n   ✅ All carriers processed")

        except Exception as e:
            print(f"\n   ❌ Task failed: {e}")
            import traceback

            traceback.print_exc()

        total_time = time.time() - start_time
        print(f"   Total execution time: {total_time/60:.1f} minutes")

        # Step 4: Export results
        print("\n📤 Exporting results to CSV...")

        # Refresh shipments from DB
        final_shipments = []
        for s in shipments:
            s.refresh_from_db()
            final_shipments.append(s)

        export_csv = export_shipments_to_csv(final_shipments)

        # Save export CSV
        export_filename = (
            f"stagehand_carriers_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        )
        with open(export_filename, "w") as f:
            f.write(export_csv)
        print(f"   Saved export CSV as: {export_filename}")

        # Step 5: Analyze results by carrier
        print("\n📊 Analyzing results...")

        # Group shipments by carrier for analysis
        carrier_shipments = {}
        for shipment in final_shipments:
            carrier = shipment.carrier_type.lower()
            if carrier not in carrier_shipments:
                carrier_shipments[carrier] = []
            carrier_shipments[carrier].append(shipment)

        # Analyze each carrier
        all_results = {}
        total_shipments = 0
        total_updated = 0
        total_with_eta = 0
        total_with_ports = 0
        total_delivered = 0

        for carrier_type in carriers_to_test:
            if carrier_type in carrier_shipments:
                # Get initial and final shipments for this carrier
                carrier_initial = [
                    s
                    for s in initial_shipments
                    if s.carrier_type.lower() == carrier_type
                ]
                carrier_final = carrier_shipments[carrier_type]

                results = analyze_results(carrier_initial, carrier_final)
                all_results[carrier_type] = results

                total_shipments += results["total"]
                total_updated += results["updated"]
                total_with_eta += results["with_eta"]
                total_with_ports += results["with_ports"]
                total_delivered += results["delivered"]

        # Print combined summary
        print(f"\n\n{'='*60}")
        print("COMBINED RESULTS - ALL CARRIERS")
        print(f"{'='*60}")
        print(f"Carriers tested: {len(all_results)}")
        print(f"Total shipments: {total_shipments}")
        print(
            f"Successfully updated: {total_updated} ({total_updated/total_shipments*100:.1f}%)"
            if total_shipments > 0
            else "Successfully updated: 0"
        )
        print(
            f"Failed to update: {total_shipments - total_updated} ({(total_shipments - total_updated)/total_shipments*100:.1f}%)"
            if total_shipments > 0
            else "Failed to update: 0"
        )

        print("\nAcross all carriers:")
        print(f"  - Shipments with ETA: {total_with_eta}")
        print(f"  - Shipments with port info: {total_with_ports}")
        print(f"  - Delivered shipments: {total_delivered}")

        print("\nPer-carrier summary:")
        for carrier, results in all_results.items():
            carrier_name = CARRIER_TEST_DATA[carrier]["name"]
            print(f"\n  {carrier_name}:")
            print(f"    - Total: {results['total']}")
            print(
                f"    - Updated: {results['updated']} ({results['updated']/results['total']*100:.1f}%)"
                if results["total"] > 0
                else "    - Updated: 0"
            )
            print(f"    - With ETA: {results['with_eta']}")
            print(f"    - With ports: {results['with_ports']}")
            print(f"    - Delivered: {results['delivered']}")

    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Error during test: {e}")
        import traceback

        traceback.print_exc()
    finally:
        # Final cleanup
        if not terminate_flag:
            print("\n\n🧹 Cleaning up all test shipments...")
            total_deleted = 0
            for carrier, tracking_codes in cleanup_tracking_codes.items():
                if tracking_codes:
                    deleted = Shipment.objects.filter(
                        team=team, bol_number__in=tracking_codes
                    ).delete()[0]
                    total_deleted += deleted
            print(f"   Deleted {total_deleted} test shipments")

        print("\n✅ All tests complete!")


def main():
    """Main entry point."""
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Parse arguments
    parser = argparse.ArgumentParser(
        description="Test Stagehand-based carrier tracking (ZIM, ONE, MSC, COSCO)"
    )
    parser.add_argument(
        "--quick",
        action="store_true",
        help="Run quick test with only 1-2 tracking codes per carrier",
    )
    parser.add_argument(
        "--cleanup-csv",
        action="store_true",
        help="Clean up all CSV files generated by this script and exit",
    )

    args = parser.parse_args()

    # Handle CSV cleanup
    if args.cleanup_csv:
        print("🧹 Cleaning up CSV files...")

        # Find all CSV files matching our patterns
        patterns = [
            "stagehand_carriers_import_*.csv",
            "stagehand_carriers_export_*.csv",
        ]

        csv_files = []
        for pattern in patterns:
            csv_files.extend(glob.glob(pattern))

        # Remove duplicates
        csv_files = list(set(csv_files))

        if not csv_files:
            print("   No CSV files found to clean up")
        else:
            print(f"   Found {len(csv_files)} CSV files:")
            for file in sorted(csv_files):
                print(f"     - {file}")

            # Confirm deletion
            response = input("\n   Delete these files? (y/N): ").strip().lower()
            if response == "y":
                deleted_count = 0
                for file in csv_files:
                    try:
                        os.remove(file)
                        deleted_count += 1
                    except Exception as e:
                        print(f"     Error deleting {file}: {e}")
                print(f"\n   ✅ Deleted {deleted_count} CSV files")
            else:
                print("   Cleanup cancelled")

        return

    # Run the multi-carrier test
    print("\n💡 Press Ctrl+C to stop\n")
    run_all_carriers_test(quick_mode=args.quick)


if __name__ == "__main__":
    main()
