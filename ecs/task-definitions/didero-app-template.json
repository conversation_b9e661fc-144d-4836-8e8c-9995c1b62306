{"family": "${TASK_FAMILY}", "taskRoleArn": "${TASK_ROLE_ARN}", "executionRoleArn": "${EXECUTION_ROLE_ARN}", "networkMode": "awsvpc", "cpu": "16384", "memory": "40960", "requiresCompatibilities": ["FARGATE"], "runtimePlatform": {"cpuArchitecture": "ARM64", "operatingSystemFamily": "LINUX"}, "containerDefinitions": [{"name": "didero-api", "image": "${API_IMAGE}", "cpu": 4096, "memoryReservation": 16384, "portMappings": [{"containerPort": 8000, "hostPort": 8000, "protocol": "tcp", "name": "didero-api-8000-tcp"}], "essential": true, "environment": [{"name": "OTEL_RESOURCE_ATTRIBUTES", "value": "service.name=didero-api,deployment.environment=${ENVIRONMENT}"}], "environmentFiles": [{"value": "${ENV_FILE_ARN}", "type": "s3"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/${TASK_FAMILY}", "awslogs-create-group": "true", "awslogs-region": "${AWS_REGION}", "awslogs-stream-prefix": "ecs"}}}, {"name": "didero-worker1", "image": "${WORKER_IMAGE}", "cpu": 3072, "memoryReservation": 12288, "portMappings": [{"containerPort": 9001, "hostPort": 9001, "protocol": "tcp", "name": "didero-worker-supervisor-9001-tcp"}], "essential": false, "environment": [{"name": "OTEL_RESOURCE_ATTRIBUTES", "value": "service.name=didero-worker,deployment.environment=${ENVIRONMENT}"}], "environmentFiles": [{"value": "${ENV_FILE_ARN}", "type": "s3"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/${TASK_FAMILY}/workers", "awslogs-create-group": "true", "awslogs-region": "${AWS_REGION}", "awslogs-stream-prefix": "ecs"}}}, {"name": "email-consumer", "image": "${EMAIL_CONSUMER_IMAGE}", "cpu": 1024, "memoryReservation": 4096, "essential": false, "environment": [{"name": "OTEL_RESOURCE_ATTRIBUTES", "value": "service.name=email-consumer,deployment.environment=${ENVIRONMENT}"}], "environmentFiles": [{"value": "${ENV_FILE_ARN}", "type": "s3"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/${TASK_FAMILY}/email-consumer", "awslogs-create-group": "true", "awslogs-region": "${AWS_REGION}", "awslogs-stream-prefix": "ecs"}}}, {"name": "temporal-worker", "image": "${TEMPORAL_WORKER_IMAGE}", "cpu": 2048, "memoryReservation": 6144, "essential": false, "environment": [{"name": "TEMPORAL_QUEUE", "value": "po_creation_queue,order_ack_queue,shipment_queue,follow_up_queue,user_workflows,invoice_processing_queue"}, {"name": "TEMPORAL_MAX_WORKERS", "value": "20"}, {"name": "TEMPORAL_WORKER_NUMPROCS", "value": "3"}, {"name": "OTEL_RESOURCE_ATTRIBUTES", "value": "service.name=temporal-worker,deployment.environment=${ENVIRONMENT}"}], "environmentFiles": [{"value": "${ENV_FILE_ARN}", "type": "s3"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/${TASK_FAMILY}/temporal-worker", "awslogs-create-group": "true", "awslogs-region": "${AWS_REGION}", "awslogs-stream-prefix": "ecs"}}}]}