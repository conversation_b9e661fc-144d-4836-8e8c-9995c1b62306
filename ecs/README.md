# ECS Task Definitions

This directory contains ECS task definition templates and related configuration for the Didero API service.
NOTE - Task Definition is an ECS concept, don't confuse with Tasks in Didero.

## Overview

Instead of modifying task definitions through scripts, we now maintain them as code in this repository. This provides:
- Version control for infrastructure changes
- Reviewable changes in pull requests
- Consistent deployments across environments
- Foundation for future Infrastructure as Code migration

## Structure

```
ecs/
├── task-definitions/
│   └── didero-app-template.json     # Task definition template with placeholders
└── README.md
```

## How It Works

1. **Template**: `didero-app-template.json` contains the complete task definition with placeholder variables
2. **Generation**: `scripts/deploy/generate-task-definition.sh` substitutes variables to create environment-specific definitions
3. **Deployment**: The deployment scripts use the generated definition instead of fetching from AWS

## Placeholder Variables

The template uses these placeholders:
- `${TASK_FAMILY}`: Task definition family name
- `${API_IMAGE}`, `${WORKER_IMAGE}`, etc.: Container image URLs with tags
- `${ENV_FILE_ARN}`: S3 location of environment variables
- `${ENVIRONMENT}`: Environment name (staging/production)
- `${AWS_REGION}`: AWS region
- `${TASK_ROLE_ARN}`, `${EXECUTION_ROLE_ARN}`: IAM roles

## Usage

### Generate a Task Definition

```bash
# For staging
./scripts/deploy/generate-task-definition.sh staging <git-hash> output.json

# For production
./scripts/deploy/generate-task-definition.sh production <git-hash> output.json
```

### Deploy Using Generated Definition

```bash
# Deployment script that uses task definition from code
./scripts/deploy/deploy_generic_hash_to_prod.sh <git-hash>
```

### Update the Template

1. Edit `ecs/task-definitions/didero-app-template.json`
2. Test generation: `./scripts/deploy/generate-task-definition.sh staging test123`
3. Validate JSON and review changes
4. Commit and create PR

## Container Definitions

The task includes these containers:

### Core Services
- **didero-api**: Main Django application (4096 CPU, 16GB memory)
- **didero-worker1**: Celery workers (3072 CPU, 12GB memory)
- **email-consumer**: Email processing (1024 CPU, 4GB memory)

### Temporal Workers
- **temporal-worker**: Consolidated worker handling all workflow queues (2048 CPU, 6GB memory)
  - Listens to: po_creation_queue, order_ack_queue, shipment_queue, user_workflows
  - Maintains parallel processing across all queue types

### Supporting Services
- **otel-collector**: OpenTelemetry metrics collection (1024 memory)

## Migration Path

### Phase 1: Current State ✅
- Task definitions stored as code
- Generation script for variable substitution
- Optional use in deployment scripts

### Phase 2: Full Adoption
- Update all deployment scripts to use generated definitions
- Remove old scripts that fetch from AWS
- Add validation and testing

### Phase 3: Infrastructure as Code
- Migrate to Terraform/CDK
- Manage complete infrastructure stack
- GitOps deployment pipeline

## Best Practices

1. **Review Changes**: Always review the diff when updating the template
2. **Test Generation**: Generate and validate JSON before committing
3. **Update Documentation**: Keep this README current with changes
4. **Backwards Compatibility**: Maintain compatibility during migration

## Troubleshooting

**Invalid JSON after generation**
- Check placeholder syntax in template
- Ensure all variables are defined in generation script
- Validate with: `jq . generated-task-def.json`

**Deployment fails with new definition**
- Compare with current AWS definition for missing fields
- Check IAM roles and permissions
- Verify container resource allocations

**Need to check current AWS task definition?**
```bash
# View current production task definition
aws ecs describe-task-definition \
  --task-definition didero-app-prod-definition \
  --profile didero-deploy \
  --query 'taskDefinition'

# View current staging task definition
aws ecs describe-task-definition \
  --task-definition didero-app-staging-definition \
  --profile didero-deploy \
  --query 'taskDefinition'
```
