#!/usr/bin/env python
"""
Test ERP capability check directly.

Usage:
    uv run python test_erp_capability_check.py
"""

import os
import sys

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")

try:
    import django
    django.setup()
except ImportError:
    print("Django not imported properly, run with 'uv run python test_erp_capability_check.py'")
    sys.exit(1)

from didero.integrations.models import ERPIntegrationConfig
from didero.workflows.shared_activities.purchase_order_operations import (
    team_has_erp_auto_creation_capability,
    get_po_auto_creation_eligibility,
)

print("=" * 80)
print("ERP CAPABILITY CHECK - DIRECT TEST")
print("=" * 80)

# Test various teams
test_teams = [
    (1, "Default Team"),
    (2, "Didero"),
    (4, "IonQ"),
    (173, "IonQ Test"),
    (999, "Non-existent Team"),
]

for team_id, team_name in test_teams:
    print(f"\n{team_name} (Team {team_id}):")
    
    # Check if ERP config exists
    erp_configs = ERPIntegrationConfig.objects.filter(team_id=team_id)
    if erp_configs.exists():
        for config in erp_configs:
            print(f"  - ERP Config found: {config.erp_type}, enabled={config.enabled}")
    else:
        print(f"  - No ERP configuration found")
    
    # Check capability
    capability = team_has_erp_auto_creation_capability(team_id)
    print(f"  - Has auto-creation capability: {capability}")
    
    # Get detailed eligibility
    eligibility = get_po_auto_creation_eligibility(team_id)
    print(f"  - Eligibility details:")
    print(f"    - ERP configured: {eligibility.get('erp_configured')}")
    print(f"    - ERP type: {eligibility.get('erp_type')}")
    print(f"    - ERP enabled: {eligibility.get('erp_enabled')}")
    if eligibility.get('missing_requirements'):
        print(f"    - Missing requirements: {', '.join(eligibility['missing_requirements'])}")

print("\n" + "=" * 80)
print("TEST COMPLETED")
print("=" * 80)