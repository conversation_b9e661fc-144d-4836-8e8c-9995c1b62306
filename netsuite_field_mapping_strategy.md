# NetSuite Field Mapping Strategy for IonQ Integration

## Overview
This document defines the comprehensive field mapping strategy for NetSuite ↔ Didero integration, covering PO creation, order acknowledgement, and shipment workflows with special handling for the dynamic date field logic.

## Current Model Compatibility Analysis

### ✅ Existing Fields That Map Directly

| **NetSuite Field** | **Current Didero Field** | **Model** | **Type** | **Status** |
|-------------------|-------------------------|-----------|----------|------------|
| `tranId` | `po_number` | PurchaseOrder | CharField | ✅ Direct |
| `total` | `total_cost` | PurchaseOrder | MoneyField | ✅ Direct |
| `memo` | `internal_notes` | PurchaseOrder | TextField | ✅ Direct |
| `currencyName` | `total_cost.currency` | PurchaseOrder | MoneyField | ✅ Built-in |
| `tranDate` | `placement_time` | PurchaseOrder | DateTimeField | ✅ Direct |
| `expectedReceiptDate` | `requested_date` | PurchaseOrder | DateField | ✅ **KEY FIELD** |
| `item_name` | `item.item_number` | OrderItem | CharField | ✅ Via FK |
| `description` | `item.description` | OrderItem | TextField | ✅ Via FK |
| `quantity` | `quantity` | OrderItem | FloatField | ✅ Direct |
| `rate` | `price` | OrderItem | MoneyField | ✅ Direct |
| `units` | `unit_of_measure` | OrderItem | CharField | ✅ Direct |
| `custbody_ionq_tracking_number` | `tracking_number` | Shipment | CharField | ✅ Via Shipment |
| `promised_delivery_date` | `promised_delivery_date` | OrderAcknowledgementItem | DateField | ✅ **KEY FIELD** |
| `estimated_delivery_date` | `estimated_delivery_date` | Shipment | DateField | ✅ **KEY FIELD** |

### ⚠️ Fields Needing Address/Supplier Enhancement

| **NetSuite Field** | **Didero Mapping Strategy** | **Implementation** |
|-------------------|------------------------------|-------------------|
| `vendor` | `supplier` relationship + address formatting | Use existing Supplier model with address |
| `ship_to` | `shipping_address` FK | Use existing Address model |
| `contact` | `supplier.contact_info` or metadata | Extend Supplier or use JSONField |
| `location` | `metadata['location']` | Use existing JSONField |
| `subsidiary` | `metadata['subsidiary']` | Use existing JSONField |

### 🔧 Fields Using Flexible Storage

| **NetSuite Field** | **Storage Strategy** | **Location** |
|-------------------|---------------------|--------------|
| `status` | Status enum mapping | `order_status` with translation |
| `approval_status` | OrderApproval model | Existing approval workflow |
| `payment_terms` | `payment_terms` | Existing TextField |
| `shipping_method` | `shipping_method` | Existing TextField |
| `freight_terms` | `shipping_terms` | Existing CharField |
| `vendor_notes` | `vendor_notes` | Existing TextField |
| Custom NetSuite fields | `metadata` JSONField | Flexible custom field storage |

## Enhanced Field Mapping Configuration

### Updated Constants for Comprehensive Mapping

```python
# didero/integrations/erp/customers/ionq/constants.py

# === BASIC FIELD MAPPINGS ===
# NetSuite field → Didero internal field name

# Header Field Mappings
NETSUITE_HEADER_MAPPINGS = {
    "tranId": "po_number",
    "total": "total_cost", 
    "memo": "internal_notes",
    "currencyName": "currency",
    "tranDate": "placement_time",
    "status": "order_status",
    "email": "supplier_email",
    "createdDate": "created_at",
    "lastModifiedDate": "updated_at",
}

# Line Item Field Mappings  
NETSUITE_LINE_MAPPINGS = {
    "item_name": "item_number",
    "description": "description", 
    "quantity": "quantity",
    "rate": "unit_price",
    "amount": "line_total",
    "units": "unit_of_measure",
}

# === CRITICAL DATE FIELD LOGIC ===
# These fields have special workflow-dependent behavior

# PO Creation: Both fields start equal, map to PurchaseOrder.requested_date
NS_EXPECTED_RECEIPT_DATE = "expectedreceiptdate"
NS_SUPPLIER_PROMISED_DATE = "custcol_ionq_supplierpromisedatefield"

# During PO creation: expectedreceiptdate = custcol_ionq_supplierpromisedatefield = PurchaseOrder.requested_date
# During Order Ack: if OrderAcknowledgementItem.promised_delivery_date != PurchaseOrder.requested_date
#                   → update NetSuite expectedreceiptdate with promised_delivery_date
# During Shipment: if Shipment.estimated_delivery_date != OrderAcknowledgementItem.promised_delivery_date  
#                   → update NetSuite expectedreceiptdate with estimated_delivery_date

# === TEAM-SPECIFIC CONFIGURATIONS ===
IONQ_TEAM_CONFIGS = {
    4: {  # Test team
        "environment": "sandbox",
        "date_sync_enabled": True,
        "require_po_confirmation": True,
        "sync_custom_fields": True
    },
    173: {  # Production team
        "environment": "production", 
        "date_sync_enabled": True,
        "require_po_confirmation": False,
        "sync_custom_fields": True
    }
}

# === WORKFLOW-SPECIFIC FIELD MAPPINGS ===
WORKFLOW_FIELD_MAPPINGS = {
    "po_creation": {
        "required_fields": ["tranId", "total", "expectedreceiptdate"],
        "optional_fields": ["memo", "currencyName", "status", "email"],
        "custom_fields": ["custbody_ionq_tracking_number"],
        "date_field_logic": "initialize_both_dates"
    },
    "order_acknowledgement": {
        "update_fields": ["expectedreceiptdate"], 
        "comparison_field": "promised_delivery_date",
        "date_field_logic": "update_if_different"
    },
    "shipment_processing": {
        "update_fields": ["expectedreceiptdate", "custbody_ionq_tracking_number"],
        "comparison_field": "estimated_delivery_date", 
        "date_field_logic": "update_if_different"
    }
}
```

## Enhanced Field Mapper Implementation

### NetSuite → Didero Mapping (PO Creation)

```python
# didero/integrations/erp/field_mapper.py

class NetSuiteFieldMapper:
    """Enhanced field mapper supporting bidirectional NetSuite ↔ Didero mapping"""
    
    @staticmethod
    def map_netsuite_to_didero_po(netsuite_data: Dict[str, Any], team_id: int) -> Dict[str, Any]:
        """
        Map NetSuite PO extraction data to Didero PurchaseOrder creation fields.
        
        Args:
            netsuite_data: Complete NetSuite extraction result from ionq_api_extraction.py
            team_id: Team ID for configuration lookup
            
        Returns:
            Mapped data ready for PurchaseOrder.objects.create()
        """
        header_fields = netsuite_data.get("header_fields", {})
        custom_header_fields = netsuite_data.get("custom_header_fields", {})
        
        # Basic field mapping
        mapped_data = {}
        for ns_field, didero_field in NETSUITE_HEADER_MAPPINGS.items():
            if ns_field in header_fields:
                value = header_fields[ns_field]
                # Apply field-specific transformations
                if didero_field == "order_status":
                    value = map_netsuite_status_to_didero(value)
                elif didero_field == "currency":
                    value = value or "USD"
                elif didero_field in ["placement_time", "created_at", "updated_at"]:
                    value = parse_netsuite_datetime(value)
                
                mapped_data[didero_field] = value
        
        # Handle complex date field logic for PO creation
        expected_receipt = header_fields.get("expectedReceiptDate") 
        supplier_promised = custom_header_fields.get("custcol_ionq_supplierpromisedatefield")
        
        # PO Creation Logic: Both dates should be equal, map to requested_date
        if expected_receipt:
            mapped_data["requested_date"] = parse_netsuite_date(expected_receipt)
        elif supplier_promised:
            mapped_data["requested_date"] = parse_netsuite_date(supplier_promised)
            
        # Store NetSuite metadata
        mapped_data["metadata"] = {
            "netsuite_internal_id": netsuite_data["po_metadata"]["internal_id"],
            "extraction_metadata": netsuite_data["po_metadata"],
            "custom_fields": custom_header_fields,
            "source": "NETSUITE_ERP_EXTRACTION"
        }
        
        # Handle address mapping (if vendor address available)
        if "vendor_address" in netsuite_data:
            mapped_data["supplier_address_text"] = netsuite_data["vendor_address"]
            
        return mapped_data
    
    @staticmethod 
    def map_netsuite_to_didero_line_items(netsuite_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Map NetSuite line items to Didero OrderItem creation data"""
        
        line_items = []
        for line_data in netsuite_data.get("line_items", []):
            item_info = line_data.get("item_info", {})
            basic_fields = line_data.get("basic_fields", {})  
            quantities = line_data.get("quantities", {})
            financials = line_data.get("financials", {})
            dates = line_data.get("dates", {})
            custom_fields = line_data.get("custom_fields", {})
            
            # Map to OrderItem fields
            item_data = {
                "item_number": item_info.get("item_name", ""),
                "description": basic_fields.get("description", ""),
                "quantity": float(quantities.get("quantity", "0")),
                "unit_price": Decimal(financials.get("rate", "0")),
                "unit_of_measure": basic_fields.get("units", "Each"),
                "line_total": Decimal(financials.get("amount", "0")),
                
                # NetSuite-specific metadata
                "metadata": {
                    "netsuite_line_number": int(basic_fields.get("line", "0")),
                    "netsuite_item_internal_id": item_info.get("internal_id", ""),
                    "vendor_item_name": basic_fields.get("vendorName", ""),
                    "expected_receipt_date": dates.get("expectedReceiptDate"),
                    "custom_fields": custom_fields
                }
            }
            
            line_items.append(item_data)
            
        return line_items

    @staticmethod
    def prepare_date_sync_update(
        workflow_type: str,
        current_date: date,
        comparison_date: date,
        po_number: str,
        line_items_data: Optional[List[Dict]] = None
    ) -> Optional[ERPUpdateRequest]:
        """
        Prepare NetSuite update for dynamic date field synchronization.
        
        This handles the complex date field logic:
        1. Order Ack: Update expectedreceiptdate if promised_delivery_date != requested_date
        2. Shipment: Update expectedreceiptdate if estimated_delivery_date != promised_delivery_date
        
        Args:
            workflow_type: "order_acknowledgement" or "shipment_processing"
            current_date: The new date to potentially sync (promised_delivery_date or estimated_delivery_date)
            comparison_date: The existing date to compare against (requested_date or promised_delivery_date)
            po_number: PO number for logging
            line_items_data: Optional line-specific data for granular updates
            
        Returns:
            ERPUpdateRequest if update needed, None if dates match
        """
        if not current_date or current_date == comparison_date:
            logger.info(f"No date sync needed for {workflow_type}",
                       po_number=po_number,
                       current_date=current_date, 
                       comparison_date=comparison_date)
            return None
            
        logger.info(f"Date sync required for {workflow_type}",
                   po_number=po_number,
                   old_date=comparison_date,
                   new_date=current_date)
        
        # Create line-specific updates if line data provided
        line_items = None
        if line_items_data:
            line_items = [
                LineItemUpdate(
                    line=item["line"],
                    item_number=item["item_number"],
                    quantity=item["quantity"],
                    estimated_delivery_date=current_date  # Update expectedreceiptdate
                )
                for item in line_items_data
            ]
        
        return ERPUpdateRequest(
            estimated_delivery_date=current_date,  # Maps to expectedreceiptdate
            line_items=line_items
        )
```

## Workflow Integration Strategy

### 1. PO Creation Workflow Enhancement

```python
# didero/workflows/core/nodes/purchase_orders/po_creation/activities.py

@activity.defn(name="create_po_from_netsuite")
async def create_po_from_netsuite(
    po_number: str,
    team_id: int,
    source_context: Dict[str, Any]
) -> Dict[str, Any]:
    """Enhanced PO creation from NetSuite with comprehensive field mapping"""
    
    try:
        # Extract complete PO data using existing proven logic
        netsuite_data = await extract_complete_po_from_netsuite(po_number, team_id)
        
        # Map NetSuite data to Didero fields  
        po_data = NetSuiteFieldMapper.map_netsuite_to_didero_po(netsuite_data, team_id)
        line_items_data = NetSuiteFieldMapper.map_netsuite_to_didero_line_items(netsuite_data)
        
        # Create supplier if not exists (handle vendor address)
        supplier = await get_or_create_supplier_from_netsuite(netsuite_data, team_id)
        
        # Create PO with mapped data
        po = PurchaseOrder.objects.create(
            supplier=supplier,
            team_id=team_id,
            source=PurchaseOrder.SOURCE_EXTERNAL_PO_IMPORT,
            **po_data
        )
        
        # Create line items with metadata
        for item_data in line_items_data:
            # Get or create Item
            item = await get_or_create_item_from_netsuite(item_data, team_id)
            
            OrderItem.objects.create(
                purchase_order=po,
                item=item,
                **{k: v for k, v in item_data.items() if k != 'metadata'}
            )
        
        logger.info("Successfully created PO from NetSuite",
                   po_id=po.id, po_number=po.po_number,
                   total_lines=len(line_items_data))
        
        return {
            "success": True,
            "po_id": po.id,
            "po_number": po.po_number,
            "requested_date": po.requested_date.isoformat() if po.requested_date else None,
            "metadata": po.metadata
        }
        
    except Exception as e:
        logger.error("NetSuite PO creation failed", 
                     po_number=po_number, error=str(e))
        return {"success": False, "error": str(e)}
```

### 2. Order Acknowledgement Workflow Integration

```python  
# didero/workflows/core_workflows/order_acknowledgement/activities.py

@activity.defn(name="sync_promised_dates_to_netsuite")
async def sync_promised_dates_to_netsuite(
    order_ack_id: int,
    team_id: int
) -> Dict[str, Any]:
    """
    Sync promised delivery dates from OA to NetSuite expectedreceiptdate.
    Only updates if promised_delivery_date != PurchaseOrder.requested_date
    """
    
    try:
        oa = OrderAcknowledgement.objects.get(id=order_ack_id)
        po = oa.purchase_order
        
        # Check if team has NetSuite sync enabled
        if not await should_sync_dates_to_netsuite(team_id, "order_acknowledgement"):
            return {"success": True, "skipped": "NetSuite sync disabled"}
        
        # Get line items with promised dates that differ from PO requested date
        sync_updates = []
        for oa_item in oa.items.exclude(promised_delivery_date__isnull=True):
            if oa_item.promised_delivery_date != po.requested_date:
                sync_updates.append({
                    "line": oa_item.order_item.metadata.get("netsuite_line_number", 1),
                    "item_number": oa_item.item.item_number,
                    "quantity": oa_item.quantity,
                    "new_promised_date": oa_item.promised_delivery_date
                })
        
        if not sync_updates:
            return {"success": True, "skipped": "No date differences found"}
        
        # Prepare NetSuite update
        update_request = NetSuiteFieldMapper.prepare_date_sync_update(
            workflow_type="order_acknowledgement",
            current_date=sync_updates[0]["new_promised_date"],  # Use first item's date
            comparison_date=po.requested_date,
            po_number=po.po_number,
            line_items_data=sync_updates
        )
        
        if update_request:
            # Execute NetSuite update
            erp_client = await get_erp_client(team_id)
            result = erp_client.update_purchase_order(po.po_number, update_request)
            
            logger.info("Updated NetSuite expectedreceiptdate from order acknowledgement",
                       po_number=po.po_number,
                       old_date=po.requested_date,
                       new_date=sync_updates[0]["new_promised_date"],
                       lines_updated=len(sync_updates))
            
            return {"success": True, "lines_updated": len(sync_updates)}
        
        return {"success": True, "skipped": "No sync needed"}
        
    except Exception as e:
        logger.error("Failed to sync promised dates to NetSuite",
                     order_ack_id=order_ack_id, error=str(e))
        return {"success": False, "error": str(e)}
```

### 3. Shipment Workflow Integration  

```python
# didero/workflows/core_workflows/shipment_processing/activities.py

@activity.defn(name="sync_delivery_dates_to_netsuite")
async def sync_delivery_dates_to_netsuite(
    shipment_id: int,
    team_id: int  
) -> Dict[str, Any]:
    """
    Sync estimated delivery dates from Shipment to NetSuite expectedreceiptdate.
    Only updates if estimated_delivery_date != OrderAcknowledgementItem.promised_delivery_date
    """
    
    try:
        shipment = Shipment.objects.get(id=shipment_id)
        po = shipment.purchase_order
        
        if not shipment.estimated_delivery_date:
            return {"success": True, "skipped": "No estimated delivery date"}
        
        # Check if team has NetSuite sync enabled
        if not await should_sync_dates_to_netsuite(team_id, "shipment_processing"):
            return {"success": True, "skipped": "NetSuite sync disabled"}
        
        # Get latest promised delivery date from order acknowledgements
        latest_promised_date = None
        oa_item = OrderAcknowledgementItem.objects.filter(
            order_acknowledgement__purchase_order=po,
            promised_delivery_date__isnull=False
        ).order_by("-order_acknowledgement__created_at").first()
        
        if oa_item:
            latest_promised_date = oa_item.promised_delivery_date
        
        # Only sync if shipment date differs from promised date
        if shipment.estimated_delivery_date == latest_promised_date:
            return {"success": True, "skipped": "Delivery dates match"}
        
        # Prepare comprehensive update (tracking number + delivery date)
        update_request = ERPUpdateRequest(
            tracking_number=shipment.tracking_number,
            estimated_delivery_date=shipment.estimated_delivery_date
        )
        
        # Execute NetSuite update
        erp_client = await get_erp_client(team_id)
        result = erp_client.update_purchase_order(po.po_number, update_request)
        
        logger.info("Updated NetSuite from shipment",
                   po_number=po.po_number,
                   tracking_number=shipment.tracking_number,
                   old_promised_date=latest_promised_date,
                   new_delivery_date=shipment.estimated_delivery_date)
        
        return {"success": True, "fields_updated": ["tracking_number", "expectedreceiptdate"]}
        
    except Exception as e:
        logger.error("Failed to sync delivery dates to NetSuite",
                     shipment_id=shipment_id, error=str(e))
        return {"success": False, "error": str(e)}
```

## Team Configuration Enhancement

### Enhanced ERPIntegrationConfig

```python
# didero/integrations/models.py - Enhanced workflow settings

ENHANCED_WORKFLOW_SETTINGS_EXAMPLE = {
    "global_settings": {
        "date_sync_enabled": True,
        "custom_field_sync": True,
        "fallback_on_failure": True
    },
    "po_creation": {
        "enabled": True,
        "require_confirmation": False,
        "field_mappings": {
            "use_all_header_fields": True,
            "use_all_line_fields": True,
            "custom_field_storage": "metadata"
        }
    },
    "order_acknowledgement": { 
        "enabled": True,
        "date_sync_enabled": True,
        "sync_only_on_differences": True,  # Only sync if promised != requested
        "require_confirmation": True
    },
    "shipment_processing": {
        "enabled": True,
        "date_sync_enabled": True,
        "sync_tracking_number": True,
        "sync_only_on_differences": True  # Only sync if estimated != promised
    }
}
```

## Implementation Phases

### Phase 1: Enhanced Field Mapping
- [ ] Update constants with comprehensive field mappings
- [ ] Enhance NetSuiteFieldMapper for bidirectional mapping
- [ ] Add date field logic for workflow-specific updates
- [ ] Test with existing IonQ NetSuite data

### Phase 2: PO Creation Integration
- [ ] Implement `create_po_from_netsuite` activity
- [ ] Add supplier/address creation logic
- [ ] Integrate with existing PO creation workflow
- [ ] Add team configuration for PO creation settings

### Phase 3: Multi-Workflow Date Synchronization
- [ ] Implement order acknowledgement date sync
- [ ] Implement shipment date sync  
- [ ] Add team configuration for sync behavior
- [ ] Add comprehensive logging and monitoring

### Phase 4: Production Rollout
- [ ] Test with IonQ teams (4, 173)
- [ ] Monitor sync success rates and error patterns
- [ ] Add configuration UI for workflow settings
- [ ] Full deployment with monitoring

## Success Metrics

### Technical Metrics
- **Field Mapping Completeness**: 100% of required fields mapped correctly
- **Date Sync Accuracy**: expectedreceiptdate correctly updated in >95% of cases
- **Integration Reliability**: <2% ERP sync failures
- **Performance**: End-to-end PO creation <30s, date syncs <10s

### Business Metrics  
- **PO Coverage**: 100% of NetSuite POs can be created in Didero
- **Date Accuracy**: Delivery date estimates improve through workflow progression
- **Manual Intervention**: 80% reduction in manual PO creation and date corrections
- **User Experience**: Transparent integration with existing workflows

This comprehensive strategy leverages the existing robust integration architecture while adding sophisticated field mapping and multi-workflow date synchronization capabilities.