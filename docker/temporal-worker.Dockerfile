# Single Temporal worker image that can be configured via environment variables
FROM didero-base:latest

# Install supervisor
RUN apt-get update && apt-get install -y supervisor

# Create a directory for supervisor logs
RUN mkdir -p /var/logs

# Set default environment variables
ENV TEMPORAL_QUEUE=user_workflows
ENV TEMPORAL_MAX_WORKERS=10
ENV TEMPORAL_WORKER_NUMPROCS=1
ENV SUPERVISOR_USERNAME=admin
ENV SUPERVISOR_PASSWORD=changeme

# Copy the supervisord configuration
COPY docker/supervisord-temporal-worker.conf /etc/supervisord.conf

# Start supervisord
CMD ["supervisord", "-c", "/etc/supervisord.conf"]