# Supervisord configuration for Email Consumer
# This file is used by the email-consumer container to run the email consumer process

[supervisord]
nodaemon=true
user=root

[program:email_consumer]
command=uv run python -m didero.email_consumer.main
autostart=true
autorestart=true
stdout_logfile=/dev/fd/1
stdout_logfile_maxbytes=0
stderr_logfile=/dev/fd/2
stderr_logfile_maxbytes=0
user=root
# Restart the process if it exits with any code
exitcodes=0
# Number of seconds to wait before considering the process as running
startsecs=10
# Number of restart attempts before giving up
startretries=3
# Kill the process group to ensure all child processes are terminated
killasgroup=true
stopasgroup=true