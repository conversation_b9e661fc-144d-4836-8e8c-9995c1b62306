#!/usr/bin/env python3
"""
Experiment 3: Hybrid approach - Extract full data like ionq_api_extraction_enhanced_fixed.py
but structure it for easy conversion to both Stagehand format and direct model creation
"""

import re
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime


class ExperimentV3:
    """Comprehensive extraction with flexible output format"""

    def extract_complete_po(self, xml_text: str, po_number: str) -> Dict[str, Any]:
        """Extract complete PO data with multiple format options"""

        result = {
            "metadata": {
                "po_number": po_number,
                "extraction_time": datetime.now().isoformat(),
                "format": "comprehensive_v3",
            },
            "raw_extraction": {
                "header": {},
                "custom_fields": {},
                "vendor": {},
                "addresses": {},
                "line_items": [],
            },
            "stagehand_format": {},
            "didero_format": {},
        }

        # Step 1: Raw extraction (like ionq_api_extraction)
        self._extract_raw_data(xml_text, result["raw_extraction"])

        # Step 2: Convert to Stagehand format (for compatibility)
        self._convert_to_stagehand(result["raw_extraction"], result["stagehand_format"])

        # Step 3: Convert to Didero format (for direct creation)
        self._convert_to_didero(result["raw_extraction"], result["didero_format"])

        return result

    def _extract_raw_data(self, xml_text: str, raw_data: Dict[str, Any]):
        """Extract all raw data from XML"""

        # Extract header fields
        header_patterns = {
            "tranId": r"<tranPurch:tranId>([^<]*)</tranPurch:tranId>",
            "memo": r"<tranPurch:memo>([^<]*)</tranPurch:memo>",
            "status": r"<tranPurch:status>([^<]*)</tranPurch:status>",
            "total": r"<tranPurch:total>([^<]*)</tranPurch:total>",
            "email": r"<tranPurch:email>([^<]*)</tranPurch:email>",
            "currencyName": r"<tranPurch:currencyName>([^<]*)</tranPurch:currencyName>",
            "tranDate": r"<tranPurch:tranDate>([^<]*)</tranPurch:tranDate>",
            "createdDate": r"<tranPurch:createdDate>([^<]*)</tranPurch:createdDate>",
        }

        for field, pattern in header_patterns.items():
            match = re.search(pattern, xml_text, re.DOTALL)
            if match:
                raw_data["header"][field] = match.group(1).strip()

        # Extract payment terms and shipping method
        terms_match = re.search(
            r"<tranPurch:terms[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>",
            xml_text,
            re.DOTALL,
        )
        if terms_match:
            raw_data["header"]["paymentTerms"] = terms_match.group(1).strip()

        # Extract custom fields
        custom_fields_match = re.search(
            r"<tranPurch:customFieldList[^>]*>(.*?)</tranPurch:customFieldList>",
            xml_text,
            re.DOTALL,
        )
        if custom_fields_match:
            custom_xml = custom_fields_match.group(1)
            custom_pattern = r'scriptId="([^"]+)"[^>]*>.*?<platformCore:value[^>]*>([^<]*)</platformCore:value>'
            for match in re.finditer(custom_pattern, custom_xml, re.DOTALL):
                if match.group(2).strip():
                    raw_data["custom_fields"][match.group(1)] = match.group(2).strip()

        # Extract vendor
        vendor_match = re.search(
            r'<tranPurch:entity[^>]*internalId="([^"]*)"[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>',
            xml_text,
            re.DOTALL,
        )
        if vendor_match:
            raw_data["vendor"] = {
                "internal_id": vendor_match.group(1),
                "name": vendor_match.group(2).strip(),
            }

        # Extract addresses
        raw_data["addresses"]["billing"] = self._extract_address_raw(
            xml_text, "billingAddress"
        )
        raw_data["addresses"]["shipping"] = self._extract_address_raw(
            xml_text, "shippingAddress"
        )

        # Extract line items
        self._extract_line_items_raw(xml_text, raw_data["line_items"])

    def _extract_address_raw(self, xml_text: str, address_type: str) -> Dict[str, Any]:
        """Extract raw address data"""
        address = {}

        pattern = f"<tranPurch:{address_type}[^>]*>(.*?)</tranPurch:{address_type}>"
        match = re.search(pattern, xml_text, re.DOTALL)
        if not match:
            return address

        addr_xml = match.group(1)

        # Extract all address fields
        address_fields = {
            "country": r"<platformCommon:country>([^<]*)</platformCommon:country>",
            "addressee": r"<platformCommon:addressee>([^<]*)</platformCommon:addressee>",
            "addr1": r"<platformCommon:addr1>([^<]*)</platformCommon:addr1>",
            "addr2": r"<platformCommon:addr2>([^<]*)</platformCommon:addr2>",
            "city": r"<platformCommon:city>([^<]*)</platformCommon:city>",
            "state": r"<platformCommon:state>([^<]*)</platformCommon:state>",
            "zip": r"<platformCommon:zip>([^<]*)</platformCommon:zip>",
            "addrText": r"<platformCommon:addrText>([^<]*)</platformCommon:addrText>",
        }

        for field, pattern in address_fields.items():
            field_match = re.search(pattern, addr_xml, re.DOTALL)
            if field_match:
                address[field] = field_match.group(1).strip()

        return address

    def _extract_line_items_raw(self, xml_text: str, line_items: List[Dict[str, Any]]):
        """Extract raw line item data"""
        itemlist_match = re.search(
            r"<tranPurch:itemList>(.*?)</tranPurch:itemList>", xml_text, re.DOTALL
        )
        if not itemlist_match:
            return

        items_xml = itemlist_match.group(1)
        item_pattern = r"<tranPurch:item>(.*?)</tranPurch:item>"

        for idx, item_match in enumerate(
            re.finditer(item_pattern, items_xml, re.DOTALL)
        ):
            item_xml = item_match.group(1)
            item = {
                "line_number": idx + 1,
                "item_ref": {},
                "fields": {},
                "custom_fields": {},
            }

            # Extract item reference
            ref_match = re.search(
                r'<tranPurch:item[^>]*internalId="([^"]*)"[^>]*>.*?<platformCore:name>([^<]*)</platformCore:name>',
                item_xml,
                re.DOTALL,
            )
            if ref_match:
                item["item_ref"] = {
                    "internal_id": ref_match.group(1),
                    "name": ref_match.group(2).strip(),
                }

            # Extract standard fields
            field_patterns = {
                "description": r"<tranPurch:description>([^<]*)</tranPurch:description>",
                "vendorName": r"<tranPurch:vendorName>([^<]*)</tranPurch:vendorName>",
                "quantity": r"<tranPurch:quantity>([^<]*)</tranPurch:quantity>",
                "rate": r"<tranPurch:rate>([^<]*)</tranPurch:rate>",
                "amount": r"<tranPurch:amount>([^<]*)</tranPurch:amount>",
                "expectedReceiptDate": r"<tranPurch:expectedReceiptDate>([^<]*)</tranPurch:expectedReceiptDate>",
            }

            for field, pattern in field_patterns.items():
                match = re.search(pattern, item_xml)
                if match:
                    item["fields"][field] = match.group(1).strip()

            # Extract custom fields
            custom_match = re.search(
                r"<tranPurch:customFieldList[^>]*>(.*?)</tranPurch:customFieldList>",
                item_xml,
                re.DOTALL,
            )
            if custom_match:
                custom_xml = custom_match.group(1)
                for cm in re.finditer(
                    r'scriptId="([^"]+)"[^>]*>.*?<platformCore:value[^>]*>([^<]*)</platformCore:value>',
                    custom_xml,
                    re.DOTALL,
                ):
                    if cm.group(2).strip():
                        item["custom_fields"][cm.group(1)] = cm.group(2).strip()

            line_items.append(item)

    def _convert_to_stagehand(
        self, raw_data: Dict[str, Any], stagehand_data: Dict[str, Any]
    ):
        """Convert to Stagehand/RPA format"""
        # Purchase order data
        po_data = {}

        # Map header fields
        po_data.update(raw_data["header"])
        po_data.update(raw_data["custom_fields"])

        # Add vendor_address in Stagehand format
        if raw_data["vendor"]:
            po_data["vendor_address"] = {"company": raw_data["vendor"]["name"]}

            # Add billing address fields to vendor_address
            if raw_data["addresses"]["billing"]:
                billing = raw_data["addresses"]["billing"]
                po_data["vendor_address"].update(
                    {
                        "address1": billing.get("addr1", ""),
                        "address2": billing.get("addr2", ""),
                        "city": billing.get("city", ""),
                        "state": billing.get("state", ""),
                        "zip": billing.get("zip", ""),
                        "country": billing.get("country", ""),
                    }
                )

        # Add ship_to as raw text
        if (
            raw_data["addresses"]["shipping"]
            and "addrText" in raw_data["addresses"]["shipping"]
        ):
            po_data["ship_to"] = raw_data["addresses"]["shipping"]["addrText"]

        # Convert total to Stagehand format
        if "total" in raw_data["header"]:
            po_data["total_cost_0"] = raw_data["header"]["total"]
            po_data["total_cost_currency"] = raw_data["header"].get(
                "currencyName", "USD"
            )

        stagehand_data["purchase_order"] = po_data

        # Convert line items
        line_items = []
        for item in raw_data["line_items"]:
            line_item = {}

            if item["item_ref"]:
                line_item["item_number"] = item["item_ref"]["name"]

            fields = item["fields"]
            line_item["description"] = fields.get("description", "")
            line_item["vendor_name"] = fields.get("vendorName", "")

            # Convert quantity to float
            if "quantity" in fields:
                try:
                    line_item["quantity"] = float(fields["quantity"])
                except:
                    line_item["quantity"] = 1.0

            # Price in Stagehand format
            if "rate" in fields:
                line_item["unit_price_0"] = fields["rate"]
                line_item["unit_price_currency"] = raw_data["header"].get(
                    "currencyName", "USD"
                )

            # Add custom fields
            line_item.update(item["custom_fields"])

            line_items.append(line_item)

        stagehand_data["line_items"] = line_items

    def _convert_to_didero(self, raw_data: Dict[str, Any], didero_data: Dict[str, Any]):
        """Convert to format optimized for Didero model creation"""
        # PurchaseOrder fields
        didero_data["purchase_order"] = {
            "po_number": raw_data["header"].get("tranId"),
            "vendor_notes": raw_data["header"].get("memo"),
            "payment_terms": raw_data["header"].get("paymentTerms"),
            "placement_time": raw_data["header"].get("tranDate"),
            "source": "EXTERNAL_PO_IMPORT",
            "is_po_editable": True,
        }

        # Supplier data for matching
        if raw_data["vendor"]:
            didero_data["supplier"] = {
                "name": raw_data["vendor"]["name"],
                "netsuite_id": raw_data["vendor"]["internal_id"],
            }

        # Addresses
        if raw_data["addresses"]["billing"]:
            billing = raw_data["addresses"]["billing"]
            didero_data["sender_address"] = {
                "line_1": billing.get("addr1", ""),
                "line_2": billing.get("addr2", ""),
                "city": billing.get("city", ""),
                "state_or_province": billing.get("state", ""),
                "postal_code": billing.get("zip", ""),
                "country": self._normalize_country(billing.get("country", "")),
                "name": billing.get("addressee", ""),
            }

        if raw_data["addresses"]["shipping"]:
            shipping = raw_data["addresses"]["shipping"]
            didero_data["shipping_address"] = {
                "raw_text": shipping.get("addrText", ""),
                "name": shipping.get("addressee", ""),
                # Minimal data - will need AI parsing
                "requires_ai_parsing": True,
            }

        # Order items
        order_items = []
        for item in raw_data["line_items"]:
            order_item = {
                "item": {
                    "item_number": item["item_ref"].get("name", ""),
                    "description": item["fields"].get("description", ""),
                    "supplier_part_number": item["fields"].get("vendorName", ""),
                },
                "order_item": {
                    "quantity": float(item["fields"].get("quantity", "1.0")),
                    "price": item["fields"].get("rate", "0.00"),
                    "price_currency": raw_data["header"].get("currencyName", "USD"),
                    "requested_date": item["fields"].get("expectedReceiptDate"),
                },
            }
            order_items.append(order_item)

        didero_data["order_items"] = order_items

        # Metadata
        didero_data["metadata"] = {
            "total": raw_data["header"].get("total"),
            "currency": raw_data["header"].get("currencyName", "USD"),
            "custom_fields": raw_data["custom_fields"],
        }

    def _normalize_country(self, country: str) -> str:
        """Normalize NetSuite country codes"""
        country_map = {
            "_unitedStates": "US",
            "_unitedKingdom": "GB",
            "_canada": "CA",
            # Add more as needed
        }
        return country_map.get(country, country)


def test_comprehensive_extraction():
    """Test comprehensive extraction approach"""
    sample_xml = """
    <tranPurch:purchaseOrder xmlns:tranPurch="urn:purchases_2023_2.transactions.webservices.netsuite.com"
                             xmlns:platformCore="urn:core_2023_2.platform.webservices.netsuite.com"
                             xmlns:platformCommon="urn:common_2023_2.platform.webservices.netsuite.com">
        <tranPurch:tranId>PO431</tranPurch:tranId>
        <tranPurch:memo>WEB NUW1334695</tranPurch:memo>
        <tranPurch:status>Fully Billed</tranPurch:status>
        <tranPurch:total>7291.42</tranPurch:total>
        <tranPurch:currencyName>USD</tranPurch:currencyName>
        <tranPurch:tranDate>2022-10-03T21:00:00.000-07:00</tranPurch:tranDate>
        <tranPurch:terms internalId="4">
            <platformCore:name>Net 30</platformCore:name>
        </tranPurch:terms>
        <tranPurch:customFieldList>
            <tranPurch:customField scriptId="custbody_ionq_tracking_number">
                <platformCore:value>1Z07X6270360947105</platformCore:value>
            </tranPurch:customField>
        </tranPurch:customFieldList>
        <tranPurch:entity internalId="550">
            <platformCore:name>V10072 ThorLabs</platformCore:name>
        </tranPurch:entity>
        <tranPurch:billingAddress>
            <platformCommon:addressee>ThorLabs</platformCommon:addressee>
            <platformCommon:addr1>56 Sparta Avenue</platformCommon:addr1>
            <platformCommon:city>Newton</platformCommon:city>
            <platformCommon:state>NJ</platformCommon:state>
            <platformCommon:zip>07860</platformCommon:zip>
            <platformCommon:country>_unitedStates</platformCommon:country>
        </tranPurch:billingAddress>
        <tranPurch:shippingAddress>
            <platformCommon:addressee>CP Tooling (NI)</platformCommon:addressee>
            <platformCommon:addrText>CP Tooling (NI)\r\nUnited States</platformCommon:addrText>
            <platformCommon:country>_unitedStates</platformCommon:country>
        </tranPurch:shippingAddress>
        <tranPurch:itemList>
            <tranPurch:item>
                <tranPurch:item internalId="2761">
                    <platformCore:name>502-00097</platformCore:name>
                </tranPurch:item>
                <tranPurch:description>Compact Power and Energy Meter Console</tranPurch:description>
                <tranPurch:quantity>1.0</tranPurch:quantity>
                <tranPurch:rate>1220.57</tranPurch:rate>
                <tranPurch:vendorName>PM100D</tranPurch:vendorName>
                <tranPurch:expectedReceiptDate>2022-10-03T21:00:00.000-07:00</tranPurch:expectedReceiptDate>
                <tranPurch:customFieldList>
                    <tranPurch:customField scriptId="custcol_ionq_supplierpromisedatefield">
                        <platformCore:value>2022-11-03T21:00:00.000-07:00</platformCore:value>
                    </tranPurch:customField>
                </tranPurch:customFieldList>
            </tranPurch:item>
        </tranPurch:itemList>
    </tranPurch:purchaseOrder>
    """

    extractor = ExperimentV3()
    result = extractor.extract_complete_po(sample_xml, "PO431")

    print("=== Experiment V3 Results ===")
    print("\n--- Raw Extraction ---")
    print(f"Header fields: {list(result['raw_extraction']['header'].keys())}")
    print(f"Custom fields: {result['raw_extraction']['custom_fields']}")
    print(f"Vendor: {result['raw_extraction']['vendor']}")

    print("\n--- Stagehand Format ---")
    print(f"PO keys: {list(result['stagehand_format']['purchase_order'].keys())}")
    print(
        f"Has vendor_address: {'vendor_address' in result['stagehand_format']['purchase_order']}"
    )
    print(f"Has ship_to: {'ship_to' in result['stagehand_format']['purchase_order']}")

    print("\n--- Didero Format ---")
    print(f"PO fields: {result['didero_format']['purchase_order']}")
    print(f"Supplier: {result['didero_format'].get('supplier', {})}")
    print(
        f"Shipping needs AI: {result['didero_format'].get('shipping_address', {}).get('requires_ai_parsing', False)}"
    )

    return result


if __name__ == "__main__":
    test_comprehensive_extraction()
