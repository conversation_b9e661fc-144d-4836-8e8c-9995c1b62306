#!/usr/bin/env python
"""
Comprehensive analysis of ERP-created PO
"""

import os
import sys
import json

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")

try:
    import django

    django.setup()
except ImportError:
    print("Django not imported properly")
    sys.exit(1)

from didero.orders.models import PurchaseOrder, OrderItem
from didero.suppliers.models import Supplier
from didero.addresses.models import Address
from didero.emails.models import EmailThreadToPurchaseOrderLink
from didero.tasks.models import Task
from django.contrib.contenttypes.models import ContentType

# Get the PO
po = PurchaseOrder.objects.filter(po_number="PO431", team_id=4).first()

if po:
    print("=" * 80)
    print("COMPREHENSIVE PO ANALYSIS - ERP EXTRACTED DATA")
    print("=" * 80)

    print("\n📋 PO BASIC INFO:")
    print(f"   - PO ID: {po.pk}")
    print(f"   - PO Number: {po.po_number}")
    print(f"   - Team ID: {po.team_id}")
    print(f"   - Source: {po.source}")
    print(f"   - Order Status: {po.order_status}")
    print(f"   - Created: {po.placement_time}")
    print(f"   - Is Editable: {po.is_po_editable}")
    print(f'   - Placed By: {po.placed_by.email if po.placed_by else "None"}')

    print("\n💰 FINANCIAL INFO:")
    print(f"   - Total Cost: {po.total_cost}")
    print(
        f'   - Currency: {getattr(po.total_cost, "currency", "N/A") if po.total_cost else "N/A"}'
    )
    print(f'   - Payment Terms: {po.payment_terms or "Not specified"}')
    print(f'   - Shipping Method: {po.shipping_method or "Not specified"}')

    print("\n🏢 SUPPLIER INFO:")
    if po.supplier:
        print(f"   - Supplier ID: {po.supplier.pk}")
        print(f"   - Supplier Name: {po.supplier.name}")
        print(f'   - Website: {po.supplier.website_url or "Not specified"}')
        print(f"   - Created: {po.supplier.created_at}")
    else:
        print("   - No supplier assigned")

    print("\n🚚 SHIPPING ADDRESS:")
    if po.shipping_address:
        addr = po.shipping_address
        print(f"   - Address ID: {addr.pk}")
        print(f'   - Line 1: "{addr.line_1 or "Empty"}"')
        print(f'   - Line 2: "{addr.line_2 or "Empty"}"')
        print(f'   - City: "{addr.city or "Empty"}"')
        print(f'   - State: "{addr.state_or_province or "Empty"}"')
        print(f'   - Postal Code: "{addr.postal_code or "Empty"}"')
        print(f'   - Country: "{addr.country or "Empty"}"')
    else:
        print("   - No shipping address")

    print("\n🏭 SUPPLIER ADDRESS:")
    if po.sender_address:
        addr = po.sender_address
        print(f"   - Address ID: {addr.pk}")
        print(f'   - Line 1: "{addr.line_1 or "Empty"}"')
        print(f'   - Line 2: "{addr.line_2 or "Empty"}"')
        print(f'   - City: "{addr.city or "Empty"}"')
        print(f'   - State: "{addr.state_or_province or "Empty"}"')
        print(f'   - Postal Code: "{addr.postal_code or "Empty"}"')
        print(f'   - Country: "{addr.country or "Empty"}"')
    else:
        print("   - No supplier address")

    print(f"\n📦 ORDER ITEMS ({po.items.count()} total):")
    if po.items.exists():
        for i, order_item in enumerate(po.items.all()[:10], 1):  # Show first 10 items
            item = order_item.item
            description = item.description or "No description"
            if len(description) > 100:
                description = description[:100] + "..."

            print(f"   {i}. Item ID: {item.pk}")
            print(f"      - Item Number: {item.item_number}")
            print(f"      - Description: {description}")
            print(
                f'      - Quantity: {order_item.quantity} {order_item.unit_of_measure or "units"}'
            )
            print(f"      - Unit Price: {order_item.price}")

            if order_item.price and order_item.quantity:
                # Handle Money object and Decimal
                price_amount = (
                    order_item.price.amount
                    if hasattr(order_item.price, "amount")
                    else order_item.price
                )
                total = float(order_item.quantity) * float(price_amount)
                print(f"      - Line Total: ${total:.2f}")
            else:
                print("      - Line Total: N/A")

            print(
                f'      - Requested Date: {order_item.requested_date or "Not specified"}'
            )
            print(f'      - Item Source: {getattr(item, "source", "Not specified")}')
            print()

        if po.items.count() > 10:
            print(f"   ... and {po.items.count() - 10} more items")

        # Calculate totals
        total_items = po.items.count()
        items_with_price = po.items.exclude(price__isnull=True).count()

        estimated_total = 0
        for oi in po.items.all():
            if oi.price and oi.quantity:
                # Handle Money object and Decimal
                price_amount = (
                    oi.price.amount if hasattr(oi.price, "amount") else oi.price
                )
                estimated_total += float(oi.quantity) * float(price_amount)

        print("\n   📊 ITEM SUMMARY:")
        print(f"      - Total Items: {total_items}")
        print(f"      - Items with Price: {items_with_price}")
        print(f"      - Estimated Total: ${estimated_total:.2f}")
    else:
        print("   - No items found")

    print("\n📧 EMAIL LINKS:")
    links = EmailThreadToPurchaseOrderLink.objects.filter(purchase_order=po)
    if links.exists():
        for link in links:
            print(f"   - Link ID: {link.pk}")
            print(
                f'   - Email Thread ID: {link.email_thread.pk if link.email_thread else "None"}'
            )
            print(f"   - Created: {link.created_at}")
    else:
        print("   - No email links found")

    print("\n🎯 TASKS:")
    tasks = Task.objects.filter(
        model_type=ContentType.objects.get_for_model(PurchaseOrder), model_id=str(po.pk)
    ).order_by("-created_at")

    if tasks.exists():
        for task in tasks:
            print(f"   - Task ID: {task.pk}")
            print(
                f'   - Type: {task.task_type_v2.name if task.task_type_v2 else "Legacy Task"}'
            )
            print(f"   - Status: {task.status}")
            print(f'   - Assigned to: {task.user.email if task.user else "Unassigned"}')
            print(f"   - Created: {task.created_at}")
    else:
        print("   - No tasks found")

    print("\n📝 NOTES:")
    vendor_notes = po.vendor_notes or "None"
    if len(vendor_notes) > 200:
        vendor_notes = vendor_notes[:200] + "..."

    internal_notes = po.internal_notes or "None"
    if len(internal_notes) > 200:
        internal_notes = internal_notes[:200] + "..."

    print(f"   - Vendor Notes: {vendor_notes}")
    print(f"   - Internal Notes: {internal_notes}")

    print("\n" + "=" * 80)
    print("ERP EXTRACTION QUALITY ANALYSIS")
    print("=" * 80)

    # Analyze data quality
    quality_score = 0
    total_checks = 0

    print("\n🔍 DATA QUALITY ASSESSMENT:")

    # Check 1: PO Number
    total_checks += 1
    if po.po_number:
        quality_score += 1
        print(f"   ✅ PO Number: Present ({po.po_number})")
    else:
        print("   ❌ PO Number: Missing")

    # Check 2: Supplier
    total_checks += 1
    if po.supplier:
        quality_score += 1
        print(f"   ✅ Supplier: Present ({po.supplier.name})")
    else:
        print("   ❌ Supplier: Missing")

    # Check 3: Items
    total_checks += 1
    if po.items.exists():
        quality_score += 1
        print(f"   ✅ Items: Present ({po.items.count()} items)")
    else:
        print("   ❌ Items: Missing")

    # Check 4: Pricing
    total_checks += 1
    items_with_pricing = po.items.exclude(price__isnull=True).count()
    if items_with_pricing > 0:
        quality_score += 1
        print(
            f"   ✅ Pricing: Present ({items_with_pricing}/{po.items.count()} items have prices)"
        )
    else:
        print("   ❌ Pricing: Missing")

    # Check 5: Addresses
    total_checks += 1
    if po.shipping_address or po.sender_address:
        quality_score += 1
        addresses = []
        if po.shipping_address:
            addresses.append("shipping")
        if po.sender_address:
            addresses.append("supplier")
        print(f'   ✅ Addresses: Present ({", ".join(addresses)})')
    else:
        print("   ❌ Addresses: Missing")

    quality_percentage = (quality_score / total_checks) * 100
    print(
        f"\n📊 OVERALL DATA QUALITY: {quality_score}/{total_checks} ({quality_percentage:.1f}%)"
    )

    if quality_percentage >= 80:
        print("   🎉 EXCELLENT: ERP extraction provided high-quality data")
    elif quality_percentage >= 60:
        print("   👍 GOOD: ERP extraction provided adequate data")
    else:
        print("   ⚠️  POOR: ERP extraction needs improvement")

else:
    print("No PO found with number PO431 for team 4")
    print("\nAvailable POs for team 4:")
    pos = PurchaseOrder.objects.filter(team_id=4).order_by("-placement_time")[:5]
    for po in pos:
        print(f"   - {po.po_number} (ID: {po.pk}, Created: {po.placement_time})")
