#!/usr/bin/env python
"""
Comprehensive test script for Smart PO Auto-Creation feature.

This script tests:
1. Direct PO creation via POCreationWorkflow with direct PO number
2. Auto-creation triggered by Order Acknowledgement workflow
3. Auto-creation triggered by Shipment workflow
4. Permission vs capability separation

Usage:
    uv run python test_smart_po_auto_creation_full.py
"""

import asyncio
import json
import os
import sys
import time
import uuid
from datetime import datetime

# Setup Django first before importing any Django models
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")

try:
    import django
    django.setup()
except ImportError:
    print("Django not imported properly, make sure you're running this with 'uv run python test_smart_po_auto_creation_full.py'")
    sys.exit(1)

# Now import Django models
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone

from didero.addresses.models import Address
from didero.emails.models import EmailThread, EmailThreadToPurchaseOrderLink
from didero.integrations.models import ERPIntegrationConfig
from didero.orders.models import PurchaseOrder, OrderAcknowledgement
from didero.suppliers.models import Communication, Supplier
from didero.tasks.models import Task
from didero.users.models.team_models import Team
from didero.workflows.models import UserWorkflow, WorkflowBehaviorConfig
from didero.workflows.schemas import WorkflowType, WorkflowTrigger

# Import Temporal client
from temporalio.client import Client

# Configuration
TEAM_ID = 4  # Using Team ID 4 (IonQ) which has ERP configured
TEMPORAL_HOST = os.environ.get("TEMPORAL_HOST", "localhost:7233")

print("=" * 80)
print("SMART PO AUTO-CREATION COMPREHENSIVE TEST")
print("=" * 80)

# Get the team
team = Team.objects.get(id=TEAM_ID)
print(f"\nUsing team: {team.name} (ID: {team.id})")

# Check ERP configuration
from didero.workflows.shared_activities.purchase_order_operations import (
    team_has_erp_auto_creation_capability,
    get_po_auto_creation_eligibility
)

print("\n" + "-" * 40)
print("CHECKING TEAM CAPABILITY")
print("-" * 40)

capability = team_has_erp_auto_creation_capability(TEAM_ID)
eligibility = get_po_auto_creation_eligibility(TEAM_ID)

print(f"Team has ERP auto-creation capability: {capability}")
print(f"Eligibility details: {json.dumps(eligibility, indent=2)}")

if not capability:
    print("\n⚠️  WARNING: Team does not have ERP auto-creation capability!")
    print("The test will continue but auto-creation will not trigger.")

# Generate unique identifiers for this test run
test_run_id = uuid.uuid4().hex[:8]
po_number_direct = f"TEST-PO-DIRECT-{test_run_id}"
po_number_oa = f"TEST-PO-OA-{test_run_id}"
po_number_shipment = f"TEST-PO-SHIP-{test_run_id}"

# Clean up any existing test POs
print("\n" + "-" * 40)
print("CLEANING UP PREVIOUS TEST POS")
print("-" * 40)
deleted_pos, _ = PurchaseOrder.objects.filter(
    po_number__startswith="TEST-PO-", 
    team=team
).delete()
print(f"Deleted {deleted_pos} existing test POs")

# Get or create a test supplier
unique_website = f"https://testsupplier-{test_run_id}.example.com"
supplier, created = Supplier.objects.get_or_create(
    name="Test Supplier Inc.",
    team=team,
    defaults={
        "website_url": unique_website,
        "description": "Test supplier for automated testing",
    }
)
print(f"\n{'Created' if created else 'Using existing'} supplier: {supplier.name} (ID: {supplier.id})")

# Create addresses if needed
supplier_address = Address.objects.filter(supplier=supplier).first()
if not supplier_address:
    supplier_address = Address.objects.create(
        supplier=supplier,
        team=team,
        line_1="123 Test Street",
        city="New York",
        state_or_province="NY",
        postal_code="10001",
        country="US",
        is_default=True,
    )
    print(f"Created supplier address: {supplier_address}")

async def run_temporal_workflow(workflow_class, workflow_id, params, task_queue="user_workflows"):
    """Helper to run a Temporal workflow"""
    client = await Client.connect(TEMPORAL_HOST)
    
    handle = await client.start_workflow(
        workflow_class.run,
        args=["", params],  # Empty workflow_id as first arg
        id=workflow_id,
        task_queue=task_queue,
    )
    
    print(f"Started workflow with Temporal ID: {handle.id}")
    result = await handle.result()
    return result

def create_po_email(po_number: str, supplier: Supplier, team: Team) -> Communication:
    """Create a test email with PO details"""
    email_body = f"""
Purchase Order: {po_number}
Date: {timezone.now().strftime("%Y-%m-%d")}

SUPPLIER ADDRESS:
{supplier.name}
123 Test Street
New York, NY 10001
USA

Ship To ADDRESS:
Test Customer
456 Main Avenue
Floor 12
New York, NY 10002
USA

Items:
1. Item Number: TEST-ITEM-1
   Description: Test Product
   Quantity: 5
   Unit Price: $100.00
   Total: $500.00

2. Item Number: TEST-ITEM-2
   Description: Another Test Product
   Quantity: 2
   Unit Price: $150.00
   Total: $300.00

Subtotal: $800.00
Tax: $64.00
Shipping: $35.00
Total: $899.00

Payment Terms: Net 30
Shipping Method: Ground
"""

    email_thread = EmailThread.objects.create(
        team=team, 
        thread_id=f"thread-{uuid.uuid4()}"
    )
    
    email = Communication.objects.create(
        team=team,
        supplier=supplier,
        email_thread=email_thread,
        email_subject=f"Purchase Order {po_number}",
        email_content=email_body,
        direction="INBOUND",
        email_from="<EMAIL>",
        email_message_id=f"test-{uuid.uuid4()}@example.com",
        comm_time=timezone.now(),
    )
    
    return email

def create_oa_email(po_number: str, supplier: Supplier, team: Team) -> Communication:
    """Create a test Order Acknowledgement email"""
    email_body = f"""
Order Acknowledgement
Order Number: OA-{uuid.uuid4().hex[:8]}
PO Reference: {po_number}
Date: {timezone.now().strftime("%Y-%m-%d")}

Dear Customer,

We acknowledge receipt of your purchase order {po_number}.

Items:
1. TEST-ITEM-1 - Test Product - Qty: 5 - Unit Price: $100.00
2. TEST-ITEM-2 - Another Test Product - Qty: 2 - Unit Price: $150.00

Total: $899.00

Estimated Delivery: 7-10 business days

Thank you for your order.
"""

    email_thread = EmailThread.objects.create(
        team=team, 
        thread_id=f"thread-oa-{uuid.uuid4()}"
    )
    
    email = Communication.objects.create(
        team=team,
        supplier=supplier,
        email_thread=email_thread,
        email_subject=f"Order Acknowledgement for PO {po_number}",
        email_content=email_body,
        direction="INBOUND",
        email_from="<EMAIL>",
        email_message_id=f"oa-{uuid.uuid4()}@example.com",
        comm_time=timezone.now(),
    )
    
    return email

def create_shipment_email(po_number: str, supplier: Supplier, team: Team) -> Communication:
    """Create a test Shipment notification email"""
    email_body = f"""
Shipment Notification

Your order has been shipped!

PO Number: {po_number}
Tracking Number: TRACK-{uuid.uuid4().hex[:8]}
Carrier: FedEx Ground
Ship Date: {timezone.now().strftime("%Y-%m-%d")}

Items Shipped:
- TEST-ITEM-1: Test Product (Qty: 5)
- TEST-ITEM-2: Another Test Product (Qty: 2)

Estimated Delivery: 3-5 business days

Track your package: https://fedex.com/track
"""

    email_thread = EmailThread.objects.create(
        team=team, 
        thread_id=f"thread-ship-{uuid.uuid4()}"
    )
    
    email = Communication.objects.create(
        team=team,
        supplier=supplier,
        email_thread=email_thread,
        email_subject=f"Shipment Confirmation - PO {po_number}",
        email_content=email_body,
        direction="INBOUND",
        email_from="<EMAIL>",
        email_message_id=f"ship-{uuid.uuid4()}@example.com",
        comm_time=timezone.now(),
    )
    
    return email

def setup_workflow_config(workflow_type: str, enable_auto_creation: bool = False) -> UserWorkflow:
    """Setup workflow configuration"""
    workflow, created = UserWorkflow.objects.get_or_create(
        workflow_type=workflow_type,
        trigger=WorkflowTrigger.ON_PURCHASE_ORDER_EMAIL_RECEIVED.value 
            if workflow_type == WorkflowType.PURCHASE_ORDER_CREATION.value
            else WorkflowTrigger.INBOUND_EMAIL.value,
        team=team,
        defaults={
            "current_snapshot": None,
        },
    )
    
    # Create or update behavior config
    config_data = {
        "enabled": True,
        "require_human_validation": False,
        "max_retry_attempts": 3,
        "retry_backoff_seconds": 10,
    }
    
    if enable_auto_creation:
        config_data["enable_po_auto_creation"] = True
    
    behavior_config, _ = WorkflowBehaviorConfig.objects.update_or_create(
        workflow=workflow,
        defaults={"config": config_data}
    )
    
    print(f"Configured {workflow_type}: enable_po_auto_creation = {config_data.get('enable_po_auto_creation', False)}")
    
    return workflow

# Test 1: Direct PO Creation with PO Number
print("\n" + "=" * 60)
print("TEST 1: DIRECT PO CREATION WITH PO NUMBER")
print("=" * 60)

po_email = create_po_email(po_number_direct, supplier, team)
print(f"Created PO email with ID: {po_email.pk}")

workflow = setup_workflow_config(WorkflowType.PURCHASE_ORDER_CREATION.value)

from didero.workflows.core_workflows.po_creation.workflow import POCreationWorkflow, POCreationParams

params = {
    "team_id": str(team.id),
    "email_id": str(po_email.pk),
    "workflow_id": str(workflow.id),
    "po_number": po_number_direct,  # Direct PO number to skip AI extraction
}

print(f"\nTesting direct PO creation with po_number: {po_number_direct}")
result = asyncio.run(run_temporal_workflow(
    POCreationWorkflow,
    f"test-direct-po-{test_run_id}",
    params,
))

print(f"\nDirect PO Creation Result: {json.dumps(result, indent=2)}")

# Verify the PO was created
if result.get("success"):
    po = PurchaseOrder.objects.get(id=result["po_id"])
    print(f"✓ PO created successfully: {po.po_number} (ID: {po.pk})")
else:
    print("✗ Direct PO creation failed!")

# Test 2: Order Acknowledgement with Auto-Creation DISABLED
print("\n" + "=" * 60)
print("TEST 2: ORDER ACK WITH AUTO-CREATION DISABLED")
print("=" * 60)

oa_email = create_oa_email(po_number_oa, supplier, team)
print(f"Created OA email with ID: {oa_email.pk}")

# Setup OA workflow WITHOUT auto-creation
oa_workflow = setup_workflow_config(
    WorkflowType.ORDER_ACKNOWLEDGEMENT_CORE.value, 
    enable_auto_creation=False
)

from didero.workflows.core_workflows.order_ack.workflow import (
    OrderAcknowledgementWorkflow,
    OrderAcknowledgementParams
)

oa_params = {
    "email_id": str(oa_email.pk),
    "team_id": str(team.id),
    "workflow_id": str(oa_workflow.id),
}

print(f"\nTesting OA for non-existent PO: {po_number_oa} (auto-creation DISABLED)")
try:
    oa_result = asyncio.run(run_temporal_workflow(
        OrderAcknowledgementWorkflow,
        f"test-oa-disabled-{test_run_id}",
        oa_params,
    ))
    print(f"Unexpected success: {oa_result}")
except Exception as e:
    print(f"✓ Expected failure: {str(e)}")
    if "Purchase order not found" in str(e):
        print("✓ Correctly failed with 'Purchase order not found' error")

# Test 3: Order Acknowledgement with Auto-Creation ENABLED
print("\n" + "=" * 60)
print("TEST 3: ORDER ACK WITH AUTO-CREATION ENABLED")
print("=" * 60)

# Update workflow config to enable auto-creation
oa_workflow_config = WorkflowBehaviorConfig.objects.get(workflow=oa_workflow)
oa_workflow_config.config["enable_po_auto_creation"] = True
oa_workflow_config.save()
print("Updated OA workflow config: enable_po_auto_creation = True")

print(f"\nTesting OA for non-existent PO: {po_number_oa} (auto-creation ENABLED)")
try:
    oa_result = asyncio.run(run_temporal_workflow(
        OrderAcknowledgementWorkflow,
        f"test-oa-enabled-{test_run_id}",
        oa_params,
    ))
    print(f"\nOA Workflow Result: {json.dumps(oa_result, indent=2)}")
    
    # Check if PO was auto-created
    po_oa = PurchaseOrder.objects.filter(po_number=po_number_oa).first()
    if po_oa:
        print(f"✓ PO auto-created successfully: {po_oa.po_number} (ID: {po_oa.pk})")
        print(f"  Source: {po_oa.source}")
        
        # Check if OA was created
        oas = OrderAcknowledgement.objects.filter(purchase_order=po_oa)
        if oas.exists():
            print(f"✓ OA created and linked to auto-created PO")
    else:
        print("✗ PO was not auto-created!")
        
except Exception as e:
    print(f"✗ OA workflow failed: {str(e)}")

# Test 4: Shipment with Auto-Creation
print("\n" + "=" * 60)
print("TEST 4: SHIPMENT WITH AUTO-CREATION ENABLED")
print("=" * 60)

ship_email = create_shipment_email(po_number_shipment, supplier, team)
print(f"Created Shipment email with ID: {ship_email.pk}")

# Setup Shipment workflow with auto-creation
ship_workflow = setup_workflow_config(
    WorkflowType.SHIPMENTS_CORE.value,
    enable_auto_creation=True
)

from didero.workflows.core_workflows.shipments.workflow import (
    ShipmentWorkflow,
    ShipmentWorkflowParams
)

ship_params = {
    "email_id": str(ship_email.pk),
    "team_id": str(team.id),
    "workflow_id": str(ship_workflow.id),
}

print(f"\nTesting Shipment for non-existent PO: {po_number_shipment} (auto-creation ENABLED)")
try:
    ship_result = asyncio.run(run_temporal_workflow(
        ShipmentWorkflow,
        f"test-ship-{test_run_id}",
        ship_params,
    ))
    print(f"\nShipment Workflow Result: {json.dumps(ship_result, indent=2)}")
    
    # Check if PO was auto-created
    po_ship = PurchaseOrder.objects.filter(po_number=po_number_shipment).first()
    if po_ship:
        print(f"✓ PO auto-created successfully: {po_ship.po_number} (ID: {po_ship.pk})")
        print(f"  Source: {po_ship.source}")
    else:
        print("✗ PO was not auto-created!")
        
except Exception as e:
    print(f"✗ Shipment workflow failed: {str(e)}")

# Summary
print("\n" + "=" * 60)
print("TEST SUMMARY")
print("=" * 60)

# Count created POs
test_pos = PurchaseOrder.objects.filter(
    po_number__in=[po_number_direct, po_number_oa, po_number_shipment],
    team=team
)

print(f"\nPOs created during test: {test_pos.count()}")
for po in test_pos:
    print(f"- {po.po_number} (Source: {po.source})")

print("\nCapability Check:")
print(f"- Team has ERP capability: {capability}")
print(f"- ERP configured: {eligibility.get('erp_configured', False)}")
print(f"- ERP type: {eligibility.get('erp_type', 'None')}")
print(f"- ERP enabled: {eligibility.get('erp_enabled', False)}")

print("\n" + "=" * 60)
print("TEST COMPLETED")
print("=" * 60)